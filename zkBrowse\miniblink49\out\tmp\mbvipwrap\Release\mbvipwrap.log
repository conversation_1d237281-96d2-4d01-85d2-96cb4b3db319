﻿E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\mbvipwrap\mbvipwrap.vcxproj(88,5): warning MSB4011: 无法再次导入“C:\Users\<USER>\AppData\Local\Microsoft\MSBuild\v4.0\Microsoft.Cpp.Win32.user.props”。可能已在“E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\mbvipwrap\mbvipwrap.vcxproj (82,5)”处导入过它。这很可能是生成创作错误。将忽略此后续导入。
C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  wrap_main.cpp
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧
C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Microsoft.CppBuild.targets(1216,5): warning MSB8012: TargetPath(E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\mbvipwrap.dll) 与 Linker 的 OutputFile 属性值(E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\out\Release\mb.dll)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Microsoft.CppBuild.targets(1218,5): warning MSB8012: TargetName(mbvipwrap) 与 Linker 的 OutputFile 属性值(mb)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
    正在创建库 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\mbvipwrap.lib 和对象 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\mbvipwrap.exp
mbvipwrap.exp : warning LNK4070: .EXP 中的 /OUT:miniblink_4975_x32.dll 指令与输出文件名“E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\mb.dll”不同；忽略指令
  正在生成代码
  已完成代码的生成
mbvipwrap.exp : warning LNK4070: .EXP 中的 /OUT:miniblink_4975_x32.dll 指令与输出文件名“E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\mb.dll”不同；忽略指令
  mbvipwrap.vcxproj -> E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\mbvipwrap.dll
