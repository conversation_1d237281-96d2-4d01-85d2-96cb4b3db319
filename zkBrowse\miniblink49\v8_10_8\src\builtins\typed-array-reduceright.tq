// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-typed-array-gen.h'

namespace typed_array {
const kBuiltinNameReduceRight: constexpr string =
    '%TypedArray%.prototype.reduceRight';

// https://tc39.github.io/ecma262/#sec-%typedarray%.prototype.reduceright
transitioning macro ReduceRightAllElements(implicit context: Context)(
    attachedArrayAndLength: typed_array::AttachedJSTypedArrayAndLength,
    callbackfn: Callable, initialValue: JSAny|TheHole): JSAny {
  let witness =
      typed_array::NewAttachedJSTypedArrayWitness(attachedArrayAndLength.array);
  let accumulator = initialValue;
  for (let k: uintptr = attachedArrayAndLength.length; k-- > 0;) {
    let value: JSAny;
    try {
      witness.RecheckIndex(k)
          otherwise goto IsDetachedOrOutOfBounds;
      value = witness.Load(k);
    } label IsDetachedOrOutOfBounds deferred {
      value = Undefined;
    }
    typeswitch (accumulator) {
      case (TheHole): {
        accumulator = value;
      }
      case (accumulatorNotHole: JSAny): {
        // TODO(v8:4153): Consider versioning this loop for Smi and non-Smi
        // indices to optimize Convert<Number>(k) for the most common case.
        accumulator = Call(
            context, callbackfn, Undefined, accumulatorNotHole, value,
            Convert<Number>(k), witness.GetStable());
      }
    }
  }
  typeswitch (accumulator) {
    case (TheHole): {
      ThrowTypeError(
          MessageTemplate::kReduceNoInitial, kBuiltinNameReduceRight);
    }
    case (accumulator: JSAny): {
      return accumulator;
    }
  }
}

// https://tc39.github.io/ecma262/#sec-%typedarray%.prototype.reduceright
transitioning javascript builtin
TypedArrayPrototypeReduceRight(
    js-implicit context: NativeContext, receiver: JSAny)(...arguments): JSAny {
  // arguments[0] = callback
  // arguments[1] = initialValue.
  try {
    // 1. Let O be the this value.
    // 2. Perform ? ValidateTypedArray(O).
    // 3. Let len be IntegerIndexedObjectLength(O).
    const array: JSTypedArray = Cast<JSTypedArray>(receiver)
        otherwise NotTypedArray;
    const attachedArrayAndLength = EnsureAttachedAndReadLength(array)
        otherwise IsDetachedOrOutOfBounds;
    // 4. If IsCallable(callbackfn) is false, throw a TypeError exception.
    const callbackfn = Cast<Callable>(arguments[0]) otherwise NotCallable;
    const initialValue = arguments.length >= 2 ? arguments[1] : TheHole;

    return ReduceRightAllElements(
        attachedArrayAndLength, callbackfn, initialValue);
  } label NotCallable deferred {
    ThrowTypeError(MessageTemplate::kCalledNonCallable, arguments[0]);
  } label NotTypedArray deferred {
    ThrowTypeError(MessageTemplate::kNotTypedArray, kBuiltinNameReduceRight);
  } label IsDetachedOrOutOfBounds deferred {
    ThrowTypeError(
        MessageTemplate::kDetachedOperation, kBuiltinNameReduceRight);
  }
}
}
