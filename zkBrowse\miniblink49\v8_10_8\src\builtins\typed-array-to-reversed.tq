// Copyright 2022 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace typed_array {
// https://tc39.es/proposal-change-array-by-copy/#sec-%typedarray%.prototype.toReversed
transitioning javascript builtin TypedArrayPrototypeToReversed(
    js-implicit context: NativeContext, receiver: JSAny)(...arguments): JSAny {
  // 1. Let O be the this value.
  // 2. Perform ? ValidateTypedArray(O).
  // 3. Let length be O.[[ArrayLength]].
  const len = ValidateTypedArrayAndGetLength(
      context, receiver, '%TypedArray%.prototype.toReversed');
  const src: JSTypedArray = UnsafeCast<JSTypedArray>(receiver);

  // 4. Let A be ? TypedArrayCreateSameType(O, « 𝔽(length) »).
  const copy = TypedArrayCreateSameType(src, len);
  const accessor: TypedArrayAccessor =
      GetTypedArrayAccessor(copy.elements_kind);

  // 5. Let k be 0.
  let k: uintptr = 0;

  // 6. Repeat, while k < length,
  while (k < len) {
    // a. Let from be ! ToString(𝔽(length - k - 1)).
    // b. Let Pk be ! ToString(𝔽(k)).
    const from = len - k - 1;

    // c. Let fromValue be ! Get(O, from).
    const fromValue = accessor.LoadNumeric(src, from);

    // d. Perform ! Set(A, Pk, kValue, true).
    accessor.StoreNumeric(context, copy, k, fromValue);

    // e. Set k to k + 1.
    ++k;
  }

  // 7. Return A.
  return copy;
}
}
