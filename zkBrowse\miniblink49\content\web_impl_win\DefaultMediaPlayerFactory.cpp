#include "DefaultMediaPlayerFactory.h"
#include "wke/wkeGlobalVar.h"
#include "wke/wkedefine.h"
#include "wke/wkeMediaPlayer.h"
#include <string>

// Simple log macro
#define SIMPLE_LOG(msg) OutputDebugStringA(msg)

namespace content {

// Simple media player implementation
class SimpleWkeMediaPlayer : public wke::WkeMediaPlayer {
public:
    SimpleWkeMediaPlayer(wkeWebView webView, wke::WkeMediaPlayerClient* client)
        : m_webView(webView), m_client(client), m_paused(true), m_currentTime(0.0), m_duration(0.0) {
        SIMPLE_LOG("SimpleWkeMediaPlayer created\n");
    }
    
    virtual ~SimpleWkeMediaPlayer() {
        SIMPLE_LOG("SimpleWkeMediaPlayer destroyed\n");
    }
    
    virtual void destroy() override {
        delete this;
    }

    // Implement WkeMediaPlayer interface
    virtual void load(LoadType type, const char* url, CORSMode mode, bool isAudio) override {
        SIMPLE_LOG("SimpleWkeMediaPlayer::load called\n");
        m_url = url ? url : "";
    }
    
    virtual void play() override {
        SIMPLE_LOG("SimpleWkeMediaPlayer::play\n");
        m_paused = false;
    }
    
    virtual void pause() override {
        SIMPLE_LOG("SimpleWkeMediaPlayer::pause\n");
        m_paused = true;
    }
    
    virtual bool supportsSave() const override { return false; }
    virtual void seek(double seconds) override { m_currentTime = seconds; }
    virtual void setRate(double rate) override {}
    virtual void setVolume(double volume) override {}
    virtual void setPreload(Preload preload) override {}
    
    virtual wkeMemBuf* buffered() const override { return nullptr; }
    virtual TimeRanges* seekable() const override { return nullptr; }
    
    virtual void setSinkId(const char* deviceId, void* callbacks) override {}
    
    virtual bool hasVideo() const override { return true; }
    virtual bool hasAudio() const override { return true; }
    virtual wkePoint naturalSize() const override { 
        wkePoint size = {640, 480}; 
        return size; 
    }
    virtual bool paused() const override { return m_paused; }
    virtual bool seeking() const override { return false; }
    virtual double duration() const override { return m_duration; }
    virtual double currentTime() const override { return m_currentTime; }
    
    virtual NetworkState networkState() const override { return kWkeNetworkStateLoaded; }
    virtual ReadyState readyState() const override { return kWkeReadyStateHaveEnoughData; }
    
    virtual bool didLoadingProgress() override { return false; }
    virtual bool hasSingleSecurityOrigin() const override { return true; }
    virtual bool didPassCORSAccessCheck() const override { return true; }
    virtual double mediaTimeForTimeValue(double timeValue) const override { return timeValue; }
    
    virtual unsigned decodedFrameCount() const override { return 0; }
    virtual unsigned droppedFrameCount() const override { return 0; }
    virtual unsigned audioDecodedByteCount() const override { return 0; }
    virtual unsigned videoDecodedByteCount() const override { return 0; }
    
    virtual void paint(HDC hdc, const wkeRect& r, unsigned char alpha, int mode) override {}
    
    virtual bool copyVideoTextureToPlatformTexture(void* context, unsigned texture, unsigned internalFormat, unsigned type, bool premultiplyAlpha, bool flipY) override { 
        return false; 
    }
    
    virtual void* audioSourceProvider() override { return nullptr; }
    
    virtual MediaKeyException generateKeyRequest(const char* keySystem, const unsigned char* initData, unsigned initDataLength) override { 
        return kWkeMediaKeyExceptionKeySystemNotSupported; 
    }
    
    virtual MediaKeyException addKey(const char* keySystem, const unsigned char* key, unsigned keyLength, const unsigned char* initData, unsigned initDataLength, const char* sessionId) override { 
        return kWkeMediaKeyExceptionKeySystemNotSupported; 
    }
    
    virtual MediaKeyException cancelKeyRequest(const char* keySystem, const char* sessionId) override { 
        return kWkeMediaKeyExceptionKeySystemNotSupported; 
    }
    
    virtual void setContentDecryptionModule(void* cdm, void* result) override {}
    virtual void setPoster(const char* poster) override {}
    virtual void enterFullscreen() override {}
    virtual void enabledAudioTracksChanged(const void* enabledTrackIds) override {}
    virtual void selectedVideoTrackChanged(TrackId* selectedTrackId) override {}
    
    virtual void setContentsToNativeWindowOffset(int x, int y) override {}
    virtual bool handleMouseEvent(unsigned msg, unsigned wParam, unsigned lParam) override { return false; }
    virtual bool handleKeyboardEvent(unsigned msg, unsigned wParam, unsigned lParam) override { return false; }
    virtual void showMediaControls() override {}
    virtual void hideMediaControls() override {}

private:
    wkeWebView m_webView;
    wke::WkeMediaPlayerClient* m_client;
    std::string m_url;
    bool m_paused;
    double m_currentTime;
    double m_duration;
};

// Default media player factory function
wkeMediaPlayer WKE_CALL_TYPE DefaultMediaPlayerFactory(wkeWebView webView, 
                                                       wkeMediaPlayerClient client, 
                                                       void* npBrowserFuncs, 
                                                       void* npPluginFuncs) {
    SIMPLE_LOG("DefaultMediaPlayerFactory called\n");
    return new SimpleWkeMediaPlayer(webView, client);
}

// Default MIME type support check function
bool WKE_CALL_TYPE DefaultIsMediaPlayerSupportsMIMEType(const char* mime) {
    if (!mime) return false;
    
    // Support common video and audio formats
    std::string mimeType(mime);
    
    // Video formats
    if (mimeType.find("video/mp4") != std::string::npos ||
        mimeType.find("video/webm") != std::string::npos ||
        mimeType.find("video/ogg") != std::string::npos ||
        mimeType.find("video/avi") != std::string::npos ||
        mimeType.find("video/quicktime") != std::string::npos) {
        return true;
    }
    
    // Audio formats
    if (mimeType.find("audio/mp3") != std::string::npos ||
        mimeType.find("audio/mpeg") != std::string::npos ||
        mimeType.find("audio/ogg") != std::string::npos ||
        mimeType.find("audio/wav") != std::string::npos ||
        mimeType.find("audio/aac") != std::string::npos ||
        mimeType.find("audio/flac") != std::string::npos) {
        return true;
    }
    
    return false;
}

// Initialize default media player factory
void InitializeDefaultMediaPlayerFactory() {
    if (!wke::g_wkeMediaPlayerFactory) {
        SIMPLE_LOG("Initializing default media player factory\n");
        wke::g_wkeMediaPlayerFactory = DefaultMediaPlayerFactory;
        wke::g_onIsMediaPlayerSupportsMIMETypeCallback = DefaultIsMediaPlayerSupportsMIMEType;
    } else {
        SIMPLE_LOG("Media player factory already initialized\n");
    }
}

} // namespace content
