// Copyright 2008 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

var x;

// Converts a number to string respecting -0.
function stringify(n) {
  if ((1 / n) === -Infinity) return "-0";
  return String(n);
}

function f(expected, y) {
  function testEval(string, x, y) {
    var mulFunction = Function("x, y", "return " + string);
    return mulFunction(x, y);
  }
  function mulTest(expected, x, y) {
    assertEquals(expected, x * y);
    assertEquals(expected, testEval(stringify(x) + " * y", x, y));
    assertEquals(expected, testEval("x * " + stringify(y), x, y));
    assertEquals(expected, testEval(stringify(x) + " * " + stringify(y), x, y));
  }
  mulTest(expected, x, y);
  mulTest(-expected, -x, y);
  mulTest(-expected, x, -y);
  mulTest(expected, -x, -y);
  if (x === y) return;  // Symmetric cases not necessary.
  mulTest(expected, y, x);
  mulTest(-expected, -y, x);
  mulTest(-expected, y, -x);
  mulTest(expected, -y, -x);
}

x = 536870913;
f(0, 0);
f(536870913, 1);
f(1073741826, 2);
f(1610612739, 3);
f(2147483652, 4);
f(2684354565, 5);
f(3758096391, 7);
f(4294967304, 8);
f(4831838217, 9);
f(8053063695, 15);
f(8589934608, 16);
f(9126805521, 17);
f(16642998303, 31);
f(17179869216, 32);
f(17716740129, 33);
f(33822867519, 63);
f(34359738432, 64);
f(34896609345, 65);
f(68182605951, 127);
f(68719476864, 128);
f(69256347777, 129);
f(136902082815, 255);
f(137438953728, 256);
f(137975824641, 257);
f(274341036543, 511);
f(274877907456, 512);
f(275414778369, 513);
f(549218943999, 1023);
f(549755814912, 1024);
f(550292685825, 1025);
f(1098974758911, 2047);
f(1099511629824, 2048);
f(1100048500737, 2049);
f(2198486388735, 4095);
f(2199023259648, 4096);
f(2199560130561, 4097);
f(4397509648383, 8191);
f(4398046519296, 8192);
f(4398583390209, 8193);
f(8795556167679, 16383);
f(8796093038592, 16384);
f(8796629909505, 16385);
f(17591649206271, 32767);
f(17592186077184, 32768);
f(17592722948097, 32769);
f(35183835283455, 65535);
f(35184372154368, 65536);
f(35184909025281, 65537);
f(70368207437823, 131071);
f(70368744308736, 131072);
f(70369281179649, 131073);
f(140736951746559, 262143);
f(140737488617472, 262144);
f(140738025488385, 262145);
f(281474440364031, 524287);
f(281474977234944, 524288);
f(281475514105857, 524289);
f(562949417598975, 1048575);
f(562949954469888, 1048576);
f(562950491340801, 1048577);
f(1125899372068863, 2097151);
f(1125899908939776, 2097152);
f(1125900445810689, 2097153);
x = 1073741823;
f(0, 0);
f(1073741823, 1);
f(2147483646, 2);
f(3221225469, 3);
f(4294967292, 4);
f(5368709115, 5);
f(7516192761, 7);
f(8589934584, 8);
f(9663676407, 9);
f(16106127345, 15);
f(17179869168, 16);
f(18253610991, 17);
f(33285996513, 31);
f(34359738336, 32);
f(35433480159, 33);
f(67645734849, 63);
f(68719476672, 64);
f(69793218495, 65);
f(136365211521, 127);
f(137438953344, 128);
f(138512695167, 129);
f(273804164865, 255);
f(274877906688, 256);
f(275951648511, 257);
f(548682071553, 511);
f(549755813376, 512);
f(550829555199, 513);
f(1098437884929, 1023);
f(1099511626752, 1024);
f(1100585368575, 1025);
f(2197949511681, 2047);
f(2199023253504, 2048);
f(2200096995327, 2049);
f(4396972765185, 4095);
f(4398046507008, 4096);
f(4399120248831, 4097);
f(8795019272193, 8191);
f(8796093014016, 8192);
f(8797166755839, 8193);
f(17591112286209, 16383);
f(17592186028032, 16384);
f(17593259769855, 16385);
f(35183298314241, 32767);
f(35184372056064, 32768);
f(35185445797887, 32769);
f(70367670370305, 65535);
f(70368744112128, 65536);
f(70369817853951, 65537);
f(140736414482433, 131071);
f(140737488224256, 131072);
f(140738561966079, 131073);
f(281473902706689, 262143);
f(281474976448512, 262144);
f(281476050190335, 262145);
f(562948879155201, 524287);
f(562949952897024, 524288);
f(562951026638847, 524289);
f(1125898832052225, 1048575);
f(1125899905794048, 1048576);
f(1125900979535871, 1048577);
x = 1073741824;
f(0, 0);
f(1073741824, 1);
f(2147483648, 2);
f(3221225472, 3);
f(4294967296, 4);
f(5368709120, 5);
f(7516192768, 7);
f(8589934592, 8);
f(9663676416, 9);
f(16106127360, 15);
f(17179869184, 16);
f(18253611008, 17);
f(33285996544, 31);
f(34359738368, 32);
f(35433480192, 33);
f(67645734912, 63);
f(68719476736, 64);
f(69793218560, 65);
f(136365211648, 127);
f(137438953472, 128);
f(138512695296, 129);
f(273804165120, 255);
f(274877906944, 256);
f(275951648768, 257);
f(548682072064, 511);
f(549755813888, 512);
f(550829555712, 513);
f(1098437885952, 1023);
f(1099511627776, 1024);
f(1100585369600, 1025);
f(2197949513728, 2047);
f(2199023255552, 2048);
f(2200096997376, 2049);
f(4396972769280, 4095);
f(4398046511104, 4096);
f(4399120252928, 4097);
f(8795019280384, 8191);
f(8796093022208, 8192);
f(8797166764032, 8193);
f(17591112302592, 16383);
f(17592186044416, 16384);
f(17593259786240, 16385);
f(35183298347008, 32767);
f(35184372088832, 32768);
f(35185445830656, 32769);
f(70367670435840, 65535);
f(70368744177664, 65536);
f(70369817919488, 65537);
f(140736414613504, 131071);
f(140737488355328, 131072);
f(140738562097152, 131073);
f(281473902968832, 262143);
f(281474976710656, 262144);
f(281476050452480, 262145);
f(562948879679488, 524287);
f(562949953421312, 524288);
f(562951027163136, 524289);
f(1125898833100800, 1048575);
f(1125899906842624, 1048576);
f(1125900980584448, 1048577);
x = 1073741825;
f(0, 0);
f(1073741825, 1);
f(2147483650, 2);
f(3221225475, 3);
f(4294967300, 4);
f(5368709125, 5);
f(7516192775, 7);
f(8589934600, 8);
f(9663676425, 9);
f(16106127375, 15);
f(17179869200, 16);
f(18253611025, 17);
f(33285996575, 31);
f(34359738400, 32);
f(35433480225, 33);
f(67645734975, 63);
f(68719476800, 64);
f(69793218625, 65);
f(136365211775, 127);
f(137438953600, 128);
f(138512695425, 129);
f(273804165375, 255);
f(274877907200, 256);
f(275951649025, 257);
f(548682072575, 511);
f(549755814400, 512);
f(550829556225, 513);
f(1098437886975, 1023);
f(1099511628800, 1024);
f(1100585370625, 1025);
f(2197949515775, 2047);
f(2199023257600, 2048);
f(2200096999425, 2049);
f(4396972773375, 4095);
f(4398046515200, 4096);
f(4399120257025, 4097);
f(8795019288575, 8191);
f(8796093030400, 8192);
f(8797166772225, 8193);
f(17591112318975, 16383);
f(17592186060800, 16384);
f(17593259802625, 16385);
f(35183298379775, 32767);
f(35184372121600, 32768);
f(35185445863425, 32769);
f(70367670501375, 65535);
f(70368744243200, 65536);
f(70369817985025, 65537);
f(140736414744575, 131071);
f(140737488486400, 131072);
f(140738562228225, 131073);
f(281473903230975, 262143);
f(281474976972800, 262144);
f(281476050714625, 262145);
f(562948880203775, 524287);
f(562949953945600, 524288);
f(562951027687425, 524289);
f(1125898834149375, 1048575);
f(1125899907891200, 1048576);
f(1125900981633025, 1048577);
x = 2147483647;
f(0, 0);
f(2147483647, 1);
f(4294967294, 2);
f(6442450941, 3);
f(8589934588, 4);
f(10737418235, 5);
f(15032385529, 7);
f(17179869176, 8);
f(19327352823, 9);
f(32212254705, 15);
f(34359738352, 16);
f(36507221999, 17);
f(66571993057, 31);
f(68719476704, 32);
f(70866960351, 33);
f(135291469761, 63);
f(137438953408, 64);
f(139586437055, 65);
f(272730423169, 127);
f(274877906816, 128);
f(277025390463, 129);
f(547608329985, 255);
f(549755813632, 256);
f(551903297279, 257);
f(1097364143617, 511);
f(1099511627264, 512);
f(1101659110911, 513);
f(2196875770881, 1023);
f(2199023254528, 1024);
f(2201170738175, 1025);
f(4395899025409, 2047);
f(4398046509056, 2048);
f(4400193992703, 2049);
f(8793945534465, 4095);
f(8796093018112, 4096);
f(8798240501759, 4097);
f(17590038552577, 8191);
f(17592186036224, 8192);
f(17594333519871, 8193);
f(35182224588801, 16383);
f(35184372072448, 16384);
f(35186519556095, 16385);
f(70366596661249, 32767);
f(70368744144896, 32768);
f(70370891628543, 32769);
f(140735340806145, 65535);
f(140737488289792, 65536);
f(140739635773439, 65537);
f(281472829095937, 131071);
f(281474976579584, 131072);
f(281477124063231, 131073);
f(562947805675521, 262143);
f(562949953159168, 262144);
f(562952100642815, 262145);
f(1125897758834689, 524287);
f(1125899906318336, 524288);
f(1125902053801983, 524289);
x = 2147483648;
f(0, 0);
f(2147483648, 1);
f(4294967296, 2);
f(6442450944, 3);
f(8589934592, 4);
f(10737418240, 5);
f(15032385536, 7);
f(17179869184, 8);
f(19327352832, 9);
f(32212254720, 15);
f(34359738368, 16);
f(36507222016, 17);
f(66571993088, 31);
f(68719476736, 32);
f(70866960384, 33);
f(135291469824, 63);
f(137438953472, 64);
f(139586437120, 65);
f(272730423296, 127);
f(274877906944, 128);
f(277025390592, 129);
f(547608330240, 255);
f(549755813888, 256);
f(551903297536, 257);
f(1097364144128, 511);
f(1099511627776, 512);
f(1101659111424, 513);
f(2196875771904, 1023);
f(2199023255552, 1024);
f(2201170739200, 1025);
f(4395899027456, 2047);
f(4398046511104, 2048);
f(4400193994752, 2049);
f(8793945538560, 4095);
f(8796093022208, 4096);
f(8798240505856, 4097);
f(17590038560768, 8191);
f(17592186044416, 8192);
f(17594333528064, 8193);
f(35182224605184, 16383);
f(35184372088832, 16384);
f(35186519572480, 16385);
f(70366596694016, 32767);
f(70368744177664, 32768);
f(70370891661312, 32769);
f(140735340871680, 65535);
f(140737488355328, 65536);
f(140739635838976, 65537);
f(281472829227008, 131071);
f(281474976710656, 131072);
f(281477124194304, 131073);
f(562947805937664, 262143);
f(562949953421312, 262144);
f(562952100904960, 262145);
f(1125897759358976, 524287);
f(1125899906842624, 524288);
f(1125902054326272, 524289);
x = 2147483649;
f(0, 0);
f(2147483649, 1);
f(4294967298, 2);
f(6442450947, 3);
f(8589934596, 4);
f(10737418245, 5);
f(15032385543, 7);
f(17179869192, 8);
f(19327352841, 9);
f(32212254735, 15);
f(34359738384, 16);
f(36507222033, 17);
f(66571993119, 31);
f(68719476768, 32);
f(70866960417, 33);
f(135291469887, 63);
f(137438953536, 64);
f(139586437185, 65);
f(272730423423, 127);
f(274877907072, 128);
f(277025390721, 129);
f(547608330495, 255);
f(549755814144, 256);
f(551903297793, 257);
f(1097364144639, 511);
f(1099511628288, 512);
f(1101659111937, 513);
f(2196875772927, 1023);
f(2199023256576, 1024);
f(2201170740225, 1025);
f(4395899029503, 2047);
f(4398046513152, 2048);
f(4400193996801, 2049);
f(8793945542655, 4095);
f(8796093026304, 4096);
f(8798240509953, 4097);
f(17590038568959, 8191);
f(17592186052608, 8192);
f(17594333536257, 8193);
f(35182224621567, 16383);
f(35184372105216, 16384);
f(35186519588865, 16385);
f(70366596726783, 32767);
f(70368744210432, 32768);
f(70370891694081, 32769);
f(140735340937215, 65535);
f(140737488420864, 65536);
f(140739635904513, 65537);
f(281472829358079, 131071);
f(281474976841728, 131072);
f(281477124325377, 131073);
f(562947806199807, 262143);
f(562949953683456, 262144);
f(562952101167105, 262145);
f(1125897759883263, 524287);
f(1125899907366912, 524288);
f(1125902054850561, 524289);
x = 4294967295;
f(0, 0);
f(4294967295, 1);
f(8589934590, 2);
f(12884901885, 3);
f(17179869180, 4);
f(21474836475, 5);
f(30064771065, 7);
f(34359738360, 8);
f(38654705655, 9);
f(64424509425, 15);
f(68719476720, 16);
f(73014444015, 17);
f(133143986145, 31);
f(137438953440, 32);
f(141733920735, 33);
f(270582939585, 63);
f(274877906880, 64);
f(279172874175, 65);
f(545460846465, 127);
f(549755813760, 128);
f(554050781055, 129);
f(1095216660225, 255);
f(1099511627520, 256);
f(1103806594815, 257);
f(2194728287745, 511);
f(2199023255040, 512);
f(2203318222335, 513);
f(4393751542785, 1023);
f(4398046510080, 1024);
f(4402341477375, 1025);
f(8791798052865, 2047);
f(8796093020160, 2048);
f(8800387987455, 2049);
f(17587891073025, 4095);
f(17592186040320, 4096);
f(17596481007615, 4097);
f(35180077113345, 8191);
f(35184372080640, 8192);
f(35188667047935, 8193);
f(70364449193985, 16383);
f(70368744161280, 16384);
f(70373039128575, 16385);
f(140733193355265, 32767);
f(140737488322560, 32768);
f(140741783289855, 32769);
f(281470681677825, 65535);
f(281474976645120, 65536);
f(281479271612415, 65537);
f(562945658322945, 131071);
f(562949953290240, 131072);
f(562954248257535, 131073);
f(1125895611613185, 262143);
f(1125899906580480, 262144);
f(1125904201547775, 262145);
