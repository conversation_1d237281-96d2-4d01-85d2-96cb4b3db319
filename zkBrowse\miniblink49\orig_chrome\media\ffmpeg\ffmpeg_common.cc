﻿// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "media/ffmpeg/ffmpeg_common.h"

#include "base/basictypes.h"
#include "base/logging.h"
#include "base/memory/scoped_ptr.h"
#include "base/sha1.h"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "media/base/decoder_buffer.h"
#include "media/base/video_decoder_config.h"
#include "media/base/video_util.h"

namespace media {

// Convert an FFmpeg codec ID into an AudioCodec enum.
static AudioCodec CodecIDToAudioCodec(AVCodecID codec_id) {
    switch (codec_id) {
    case AV_CODEC_ID_AAC:
        return kCodecAAC;
    case AV_CODEC_ID_MP3:
        return kCodecMP3;
    case AV_CODEC_ID_VORBIS:
        return kCodecVorbis;
    case AV_CODEC_ID_PCM_U8:
    case AV_CODEC_ID_PCM_S16LE:
    case AV_CODEC_ID_PCM_S24LE:
    case AV_CODEC_ID_PCM_F32LE:
        return kCodecPCM;
    case AV_CODEC_ID_PCM_S16BE:
        return kCodecPCM_S16BE;
    case AV_CODEC_ID_PCM_S24BE:
        return kCodecPCM_S24BE;
    case AV_CODEC_ID_FLAC:
        return kCodecFLAC;
    case AV_CODEC_ID_AMR_NB:
        return kCodecAMR_NB;
    case AV_CODEC_ID_AMR_WB:
        return kCodecAMR_WB;
    case AV_CODEC_ID_GSM_MS:
        return kCodecGSM_MS;
    case AV_CODEC_ID_PCM_ALAW:
        return kCodecPCM_ALAW;
    case AV_CODEC_ID_PCM_MULAW:
        return kCodecPCM_MULAW;
    case AV_CODEC_ID_OPUS:
        return kCodecOpus;
    case AV_CODEC_ID_ALAC:
        return kCodecALAC;
    default:
        DVLOG(1) << "Unknown audio CodecID: " << codec_id;
    }
    return kUnknownAudioCodec;
}

static AVCodecID AudioCodecToCodecID(AudioCodec audio_codec,
    SampleFormat sample_format)
{
    switch (audio_codec) {
    case kCodecAAC:
        return AV_CODEC_ID_AAC;
    case kCodecALAC:
        return AV_CODEC_ID_ALAC;
    case kCodecMP3:
        return AV_CODEC_ID_MP3;
    case kCodecPCM:
        switch (sample_format) {
        case kSampleFormatU8:
            return AV_CODEC_ID_PCM_U8;
        case kSampleFormatS16:
            return AV_CODEC_ID_PCM_S16LE;
        case kSampleFormatS32:
            return AV_CODEC_ID_PCM_S24LE;
        case kSampleFormatF32:
            return AV_CODEC_ID_PCM_F32LE;
        default:
            DVLOG(1) << "Unsupported sample format: " << sample_format;
        }
        break;
    case kCodecPCM_S16BE:
        return AV_CODEC_ID_PCM_S16BE;
    case kCodecPCM_S24BE:
        return AV_CODEC_ID_PCM_S24BE;
    case kCodecVorbis:
        return AV_CODEC_ID_VORBIS;
    case kCodecFLAC:
        return AV_CODEC_ID_FLAC;
    case kCodecAMR_NB:
        return AV_CODEC_ID_AMR_NB;
    case kCodecAMR_WB:
        return AV_CODEC_ID_AMR_WB;
    case kCodecGSM_MS:
        return AV_CODEC_ID_GSM_MS;
    case kCodecPCM_ALAW:
        return AV_CODEC_ID_PCM_ALAW;
    case kCodecPCM_MULAW:
        return AV_CODEC_ID_PCM_MULAW;
    case kCodecOpus:
        return AV_CODEC_ID_OPUS;
    default:
        DVLOG(1) << "Unknown AudioCodec: " << audio_codec;
    }
    return AV_CODEC_ID_NONE;
}

// Converts an FFmpeg video codec ID into its corresponding supported codec id.
static VideoCodec CodecIDToVideoCodec(AVCodecID codec_id)
{
    switch (codec_id) {
    case AV_CODEC_ID_H264:
        return kCodecH264;
#if defined(ENABLE_HEVC_DEMUXING)
    case AV_CODEC_ID_HEVC:
        return kCodecHEVC;
#endif
    case AV_CODEC_ID_THEORA:
        return kCodecTheora;
    case AV_CODEC_ID_MPEG4:
        return kCodecMPEG4;
    case AV_CODEC_ID_VP8:
        return kCodecVP8;
    case AV_CODEC_ID_VP9:
        return kCodecVP9;
    default:
        DVLOG(1) << "Unknown video CodecID: " << codec_id;
    }
    return kUnknownVideoCodec;
}

AVCodecID VideoCodecToCodecID(VideoCodec video_codec)
{
    switch (video_codec) {
    case kCodecH264:
        return AV_CODEC_ID_H264;
#if defined(ENABLE_HEVC_DEMUXING)
    case kCodecHEVC:
        return AV_CODEC_ID_HEVC;
#endif
    case kCodecTheora:
        return AV_CODEC_ID_THEORA;
    case kCodecMPEG4:
        return AV_CODEC_ID_MPEG4;
    case kCodecVP8:
        return AV_CODEC_ID_VP8;
    case kCodecVP9:
        return AV_CODEC_ID_VP9;
    default:
        DVLOG(1) << "Unknown VideoCodec: " << video_codec;
    }
    return AV_CODEC_ID_NONE;
}

static VideoCodecProfile ProfileIDToVideoCodecProfile(int profile)
{
    // Clear out the CONSTRAINED & INTRA flags which are strict subsets of the
    // corresponding profiles with which they're used.
    profile &= ~FF_PROFILE_H264_CONSTRAINED;
    profile &= ~FF_PROFILE_H264_INTRA;
    switch (profile) {
    case FF_PROFILE_H264_BASELINE:
        return H264PROFILE_BASELINE;
    case FF_PROFILE_H264_MAIN:
        return H264PROFILE_MAIN;
    case FF_PROFILE_H264_EXTENDED:
        return H264PROFILE_EXTENDED;
    case FF_PROFILE_H264_HIGH:
        return H264PROFILE_HIGH;
    case FF_PROFILE_H264_HIGH_10:
        return H264PROFILE_HIGH10PROFILE;
    case FF_PROFILE_H264_HIGH_422:
        return H264PROFILE_HIGH422PROFILE;
    case FF_PROFILE_H264_HIGH_444_PREDICTIVE:
        return H264PROFILE_HIGH444PREDICTIVEPROFILE;
    default:
        DVLOG(1) << "Unknown profile id: " << profile;
    }
    return VIDEO_CODEC_PROFILE_UNKNOWN;
}

static int VideoCodecProfileToProfileID(VideoCodecProfile profile)
{
    switch (profile) {
    case H264PROFILE_BASELINE:
        return FF_PROFILE_H264_BASELINE;
    case H264PROFILE_MAIN:
        return FF_PROFILE_H264_MAIN;
    case H264PROFILE_EXTENDED:
        return FF_PROFILE_H264_EXTENDED;
    case H264PROFILE_HIGH:
        return FF_PROFILE_H264_HIGH;
    case H264PROFILE_HIGH10PROFILE:
        return FF_PROFILE_H264_HIGH_10;
    case H264PROFILE_HIGH422PROFILE:
        return FF_PROFILE_H264_HIGH_422;
    case H264PROFILE_HIGH444PREDICTIVEPROFILE:
        return FF_PROFILE_H264_HIGH_444_PREDICTIVE;
    default:
        DVLOG(1) << "Unknown VideoCodecProfile: " << profile;
    }
    return FF_PROFILE_UNKNOWN;
}

SampleFormat AVSampleFormatToSampleFormat(AVSampleFormat sample_format)
{
    switch (sample_format) {
    case AV_SAMPLE_FMT_U8:
        return kSampleFormatU8;
    case AV_SAMPLE_FMT_S16:
        return kSampleFormatS16;
    case AV_SAMPLE_FMT_S32:
        return kSampleFormatS32;
    case AV_SAMPLE_FMT_FLT:
        return kSampleFormatF32;
    case AV_SAMPLE_FMT_S16P:
        return kSampleFormatPlanarS16;
    case AV_SAMPLE_FMT_S32P:
        return kSampleFormatPlanarS32;
    case AV_SAMPLE_FMT_FLTP:
        return kSampleFormatPlanarF32;
    default:
        DVLOG(1) << "Unknown AVSampleFormat: " << sample_format;
    }
    return kUnknownSampleFormat;
}

static AVSampleFormat SampleFormatToAVSampleFormat(SampleFormat sample_format)
{
    switch (sample_format) {
    case kSampleFormatU8:
        return AV_SAMPLE_FMT_U8;
    case kSampleFormatS16:
        return AV_SAMPLE_FMT_S16;
    case kSampleFormatS32:
        return AV_SAMPLE_FMT_S32;
    case kSampleFormatF32:
        return AV_SAMPLE_FMT_FLT;
    case kSampleFormatPlanarS16:
        return AV_SAMPLE_FMT_S16P;
    case kSampleFormatPlanarF32:
        return AV_SAMPLE_FMT_FLTP;
    default:
        DVLOG(1) << "Unknown SampleFormat: " << sample_format;
    }
    return AV_SAMPLE_FMT_NONE;
}

bool AVCodecContextToAudioDecoderConfig(const AVCodecContext* codec_context,
    bool is_encrypted,
    AudioDecoderConfig* config)
{
    AudioCodec codec = CodecIDToAudioCodec(codec_context->codec_id);

    // Get the channel count from the new ch_layout
    int channel_count = codec_context->ch_layout.nb_channels;
    ChannelLayout channel_layout = media::CHANNEL_LAYOUT_NONE;

    // Convert FFmpeg channel layout to Chrome's channel layout
    if (channel_count == 1) {
        channel_layout = media::CHANNEL_LAYOUT_MONO;
    } else if (channel_count == 2) {
        channel_layout = media::CHANNEL_LAYOUT_STEREO;
    }

    SampleFormat sample_format = AVSampleFormatToSampleFormat(
        codec_context->sample_fmt);
    int sample_rate = codec_context->sample_rate;

    std::vector<uint8_t> extra_data;
    if (codec_context->extradata_size > 0) {
        extra_data.assign(codec_context->extradata,
            codec_context->extradata + codec_context->extradata_size);
    }

    base::TimeDelta seek_preroll;
    if (codec_context->seek_preroll > 0) {
        seek_preroll = base::TimeDelta::FromMicroseconds(
            codec_context->seek_preroll * 1000000.0 / codec_context->sample_rate);
    }

    config->Initialize(codec,
        sample_format,
        channel_layout,
        sample_rate,
        extra_data,
        is_encrypted,
        seek_preroll,
        codec_context->delay);

    return (codec != kUnknownAudioCodec);
}

bool AVStreamToAudioDecoderConfig(const AVStream* stream,
    AudioDecoderConfig* config)
{
    bool is_encrypted = false;
    AVDictionaryEntry* key = av_dict_get(stream->metadata, "enc_key_id", nullptr, 0);
    if (key)
        is_encrypted = true;

#if LIBAVCODEC_VERSION_MAJOR >= 59
    // For FFmpeg 6.0+, use codecpar instead of codec
    AVCodecContext* codec_context = avcodec_alloc_context3(NULL);
    if (!codec_context)
        return false;

    int ret = avcodec_parameters_to_context(codec_context, stream->codecpar);
    if (ret < 0) {
        avcodec_free_context(&codec_context);
        return false;
    }

    bool result = AVCodecContextToAudioDecoderConfig(codec_context, is_encrypted, config);
    avcodec_free_context(&codec_context);
    return result;
#else
    return AVCodecContextToAudioDecoderConfig(stream->codec, is_encrypted, config);
#endif
}

void AudioDecoderConfigToAVCodecContext(const AudioDecoderConfig& config,
    AVCodecContext* codec_context)
{
    codec_context->codec_type = AVMEDIA_TYPE_AUDIO;
    codec_context->codec_id = AudioCodecToCodecID(config.codec(),
        config.sample_format());
    codec_context->sample_fmt = SampleFormatToAVSampleFormat(
        config.sample_format());

#if LIBAVCODEC_VERSION_MAJOR >= 59
    // Update channel layout using AVChannelLayout
    av_channel_layout_default(&codec_context->ch_layout, ChannelLayoutToChannelCount(config.channel_layout()));
#else
    // Use old channel layout API
    codec_context->channels = ChannelLayoutToChannelCount(config.channel_layout());
    codec_context->channel_layout = ChannelLayoutToAVChannelLayout(config.channel_layout());
#endif

    codec_context->sample_rate = config.samples_per_second();

    if (config.extra_data().empty()) {
        codec_context->extradata = nullptr;
        codec_context->extradata_size = 0;
    } else {
        codec_context->extradata_size = config.extra_data().size();
        codec_context->extradata = reinterpret_cast<uint8_t*>(
            av_malloc(config.extra_data().size() + AV_INPUT_BUFFER_PADDING_SIZE));
        memcpy(codec_context->extradata, &config.extra_data()[0],
            config.extra_data().size());
        memset(codec_context->extradata + config.extra_data().size(), 0,
            AV_INPUT_BUFFER_PADDING_SIZE);
    }
}
bool AVStreamToVideoDecoderConfig(const AVStream* stream,
    VideoDecoderConfig* config)
{
#if LIBAVCODEC_VERSION_MAJOR >= 59
    AVCodecParameters* codecpar = stream->codecpar;
    gfx::Size coded_size(codecpar->width, codecpar->height);

    // TODO(vrk): This assumes decoded frame data starts at (0, 0), which is true
    // for now, but may not always be true forever. Fix this in the future.
    gfx::Rect visible_rect(codecpar->width, codecpar->height);

    AVRational aspect_ratio = { 1, 1 };
    if (stream->sample_aspect_ratio.num)
        aspect_ratio = stream->sample_aspect_ratio;

    VideoCodec codec = CodecIDToVideoCodec(codecpar->codec_id);

    VideoCodecProfile profile = VIDEO_CODEC_PROFILE_UNKNOWN;
    if (codec == kCodecVP8)
        profile = VP8PROFILE_ANY;
    else if (codec == kCodecVP9)
        profile = VP9PROFILE_ANY;
    // Note: profile information is not available in codecpar for newer FFmpeg

        // Without the FFmpeg h264 decoder, AVFormat is unable to get the profile, so
        // default to baseline and let the VDA fail later if it doesn't support the
        // real profile. This is alright because if the FFmpeg h264 decoder isn't
        // enabled, there is no fallback if the VDA fails.
#if defined(DISABLE_FFMPEG_VIDEO_DECODERS)
    if (codec == kCodecH264)
        profile = H264PROFILE_BASELINE;
#endif

    gfx::Size natural_size = GetNaturalSize(
        visible_rect.size(), aspect_ratio.num, aspect_ratio.den);

    VideoPixelFormat format = AVPixelFormatToVideoPixelFormat(static_cast<AVPixelFormat>(codecpar->format));
#else
    gfx::Size coded_size(stream->codec->coded_width, stream->codec->coded_height);

    // TODO(vrk): This assumes decoded frame data starts at (0, 0), which is true
    // for now, but may not always be true forever. Fix this in the future.
    gfx::Rect visible_rect(stream->codec->width, stream->codec->height);

    AVRational aspect_ratio = { 1, 1 };
    if (stream->sample_aspect_ratio.num)
        aspect_ratio = stream->sample_aspect_ratio;
    else if (stream->codec->sample_aspect_ratio.num)
        aspect_ratio = stream->codec->sample_aspect_ratio;

    VideoCodec codec = CodecIDToVideoCodec(stream->codec->codec_id);

    VideoCodecProfile profile = VIDEO_CODEC_PROFILE_UNKNOWN;
    if (codec == kCodecVP8)
        profile = VP8PROFILE_ANY;
    else if (codec == kCodecVP9)
        profile = VP9PROFILE_ANY;
    else
        profile = ProfileIDToVideoCodecProfile(stream->codec->profile);

        // Without the FFmpeg h264 decoder, AVFormat is unable to get the profile, so
        // default to baseline and let the VDA fail later if it doesn't support the
        // real profile. This is alright because if the FFmpeg h264 decoder isn't
        // enabled, there is no fallback if the VDA fails.
#if defined(DISABLE_FFMPEG_VIDEO_DECODERS)
    if (codec == kCodecH264)
        profile = H264PROFILE_BASELINE;
#endif

    gfx::Size natural_size = GetNaturalSize(
        visible_rect.size(), aspect_ratio.num, aspect_ratio.den);

    VideoPixelFormat format = AVPixelFormatToVideoPixelFormat(stream->codec->pix_fmt);
#endif
    // The format and coded size may be unknown if FFmpeg is compiled without
    // video decoders.
#if defined(DISABLE_FFMPEG_VIDEO_DECODERS)
    if (format == PIXEL_FORMAT_UNKNOWN)
        format = PIXEL_FORMAT_YV12;
    if (coded_size == gfx::Size(0, 0))
        coded_size = visible_rect.size();
#endif

    if (codec == kCodecVP9) {
        // TODO(tomfinegan): libavcodec doesn't know about VP9.
        format = PIXEL_FORMAT_YV12;
        coded_size = visible_rect.size();
    }

    // Pad out |coded_size| for subsampled YUV formats.
    if (format != PIXEL_FORMAT_YV24) {
        coded_size.set_width((coded_size.width() + 1) / 2 * 2);
        if (format != PIXEL_FORMAT_YV16)
            coded_size.set_height((coded_size.height() + 1) / 2 * 2);
    }

    bool is_encrypted = false;
    AVDictionaryEntry* key = av_dict_get(stream->metadata, "enc_key_id", nullptr, 0);
    if (key)
        is_encrypted = true;

    AVDictionaryEntry* webm_alpha = av_dict_get(stream->metadata, "alpha_mode", nullptr, 0);
    if (webm_alpha && !strcmp(webm_alpha->value, "1")) {
        format = PIXEL_FORMAT_YV12A;
    }

    // Prefer the color space found by libavcodec if available.
#if LIBAVCODEC_VERSION_MAJOR >= 59
    // In newer FFmpeg, color space info is in codecpar
    ColorSpace color_space = COLOR_SPACE_UNSPECIFIED;
#else
    ColorSpace color_space = AVColorSpaceToColorSpace(stream->codec->colorspace,
        stream->codec->color_range);
#endif
    if (color_space == COLOR_SPACE_UNSPECIFIED) {
        // Otherwise, assume that SD video is usually Rec.601, and HD is usually
        // Rec.709.
        color_space = (natural_size.height() < 720) ? COLOR_SPACE_SD_REC601
                                                    : COLOR_SPACE_HD_REC709;
    }

    // AVStream occasionally has invalid extra data. See http://crbug.com/517163
#if LIBAVCODEC_VERSION_MAJOR >= 59
    if ((codecpar->extradata_size == 0) != (codecpar->extradata == nullptr)) {
        LOG(ERROR) << __FUNCTION__
                   << (codecpar->extradata == nullptr ? " NULL" : " Non-Null")
                   << " extra data cannot have size of "
                   << codecpar->extradata_size << ".";
        return false;
    }

    std::vector<uint8_t> extra_data;
    if (codecpar->extradata_size > 0) {
        extra_data.assign(codecpar->extradata,
            codecpar->extradata + codecpar->extradata_size);
    }
#else
    if ((stream->codec->extradata_size == 0) != (stream->codec->extradata == nullptr)) {
        LOG(ERROR) << __FUNCTION__
                   << (stream->codec->extradata == nullptr ? " NULL" : " Non-Null")
                   << " extra data cannot have size of "
                   << stream->codec->extradata_size << ".";
        return false;
    }

    std::vector<uint8_t> extra_data;
    if (stream->codec->extradata_size > 0) {
        extra_data.assign(stream->codec->extradata,
            stream->codec->extradata + stream->codec->extradata_size);
    }
#endif
    config->Initialize(codec, profile, format, color_space, coded_size,
        visible_rect, natural_size, extra_data, is_encrypted);
    return true;
}

void VideoDecoderConfigToAVCodecContext(
    const VideoDecoderConfig& config,
    AVCodecContext* codec_context)
{
    codec_context->codec_type = AVMEDIA_TYPE_VIDEO;
    codec_context->codec_id = VideoCodecToCodecID(config.codec());
    codec_context->profile = VideoCodecProfileToProfileID(config.profile());
    codec_context->coded_width = config.coded_size().width();
    codec_context->coded_height = config.coded_size().height();
    codec_context->pix_fmt = VideoPixelFormatToAVPixelFormat(config.format());
    if (config.color_space() == COLOR_SPACE_JPEG)
        codec_context->color_range = AVCOL_RANGE_JPEG;

    if (config.extra_data().empty()) {
        codec_context->extradata = nullptr;
        codec_context->extradata_size = 0;
    } else {
        codec_context->extradata_size = config.extra_data().size();
        codec_context->extradata = reinterpret_cast<uint8_t*>(
            av_malloc(config.extra_data().size() + AV_INPUT_BUFFER_PADDING_SIZE));
        memcpy(codec_context->extradata, &config.extra_data()[0],
            config.extra_data().size());
        memset(codec_context->extradata + config.extra_data().size(), '\0',
            AV_INPUT_BUFFER_PADDING_SIZE);
    }
}
ChannelLayout ChannelLayoutToChromeChannelLayout(int64_t layout, int channels)
{
    switch (layout) {
    case AV_CH_LAYOUT_MONO:
        return CHANNEL_LAYOUT_MONO;
    case AV_CH_LAYOUT_STEREO:
        return CHANNEL_LAYOUT_STEREO;
    case AV_CH_LAYOUT_2_1:
        return CHANNEL_LAYOUT_2_1;
    case AV_CH_LAYOUT_SURROUND:
        return CHANNEL_LAYOUT_SURROUND;
    case AV_CH_LAYOUT_4POINT0:
        return CHANNEL_LAYOUT_4_0;
    case AV_CH_LAYOUT_2_2:
        return CHANNEL_LAYOUT_2_2;
    case AV_CH_LAYOUT_QUAD:
        return CHANNEL_LAYOUT_QUAD;
    case AV_CH_LAYOUT_5POINT0:
        return CHANNEL_LAYOUT_5_0;
    case AV_CH_LAYOUT_5POINT1:
        return CHANNEL_LAYOUT_5_1;
    case AV_CH_LAYOUT_5POINT0_BACK:
        return CHANNEL_LAYOUT_5_0_BACK;
    case AV_CH_LAYOUT_5POINT1_BACK:
        return CHANNEL_LAYOUT_5_1_BACK;
    case AV_CH_LAYOUT_7POINT0:
        return CHANNEL_LAYOUT_7_0;
    case AV_CH_LAYOUT_7POINT1:
        return CHANNEL_LAYOUT_7_1;
    case AV_CH_LAYOUT_7POINT1_WIDE:
        return CHANNEL_LAYOUT_7_1_WIDE;
    case AV_CH_LAYOUT_STEREO_DOWNMIX:
        return CHANNEL_LAYOUT_STEREO_DOWNMIX;
    case AV_CH_LAYOUT_2POINT1:
        return CHANNEL_LAYOUT_2POINT1;
    case AV_CH_LAYOUT_3POINT1:
        return CHANNEL_LAYOUT_3_1;
    case AV_CH_LAYOUT_4POINT1:
        return CHANNEL_LAYOUT_4_1;
    case AV_CH_LAYOUT_6POINT0:
        return CHANNEL_LAYOUT_6_0;
    case AV_CH_LAYOUT_6POINT0_FRONT:
        return CHANNEL_LAYOUT_6_0_FRONT;
    case AV_CH_LAYOUT_HEXAGONAL:
        return CHANNEL_LAYOUT_HEXAGONAL;
    case AV_CH_LAYOUT_6POINT1:
        return CHANNEL_LAYOUT_6_1;
    case AV_CH_LAYOUT_6POINT1_BACK:
        return CHANNEL_LAYOUT_6_1_BACK;
    case AV_CH_LAYOUT_6POINT1_FRONT:
        return CHANNEL_LAYOUT_6_1_FRONT;
    case AV_CH_LAYOUT_7POINT0_FRONT:
        return CHANNEL_LAYOUT_7_0_FRONT;
#ifdef AV_CH_LAYOUT_7POINT1_WIDE_BACK
    case AV_CH_LAYOUT_7POINT1_WIDE_BACK:
        return CHANNEL_LAYOUT_7_1_WIDE_BACK;
#endif
    case AV_CH_LAYOUT_OCTAGONAL:
        return CHANNEL_LAYOUT_OCTAGONAL;
    default:
        // FFmpeg channel_layout is 0 for .wav and .mp3.  Attempt to guess layout
        // based on the channel count.
        return GuessChannelLayout(channels);
    }
}

VideoPixelFormat AVPixelFormatToVideoPixelFormat(AVPixelFormat pixel_format)
{
    // The YUVJ alternatives are FFmpeg's (deprecated, but still in use) way to
    // specify a pixel format and full range color combination.
    switch (pixel_format) {
    case AV_PIX_FMT_YUV422P:
    case AV_PIX_FMT_YUVJ422P:
        return PIXEL_FORMAT_YV16;
    case AV_PIX_FMT_YUV444P:
    case AV_PIX_FMT_YUVJ444P:
        return PIXEL_FORMAT_YV24;
    case AV_PIX_FMT_YUV420P:
    case AV_PIX_FMT_YUVJ420P:
        return PIXEL_FORMAT_YV12;
    case AV_PIX_FMT_YUVA420P:
        return PIXEL_FORMAT_YV12A;
    default:
        DVLOG(1) << "Unsupported AVPixelFormat: " << pixel_format;
    }
    return PIXEL_FORMAT_UNKNOWN;
}

AVPixelFormat VideoPixelFormatToAVPixelFormat(VideoPixelFormat video_format)
{
    switch (video_format) {
    case PIXEL_FORMAT_YV16:
        return AV_PIX_FMT_YUV422P;
    case PIXEL_FORMAT_YV12:
        return AV_PIX_FMT_YUV420P;
    case PIXEL_FORMAT_YV12A:
        return AV_PIX_FMT_YUVA420P;
    case PIXEL_FORMAT_YV24:
        return AV_PIX_FMT_YUV444P;
    default:
        DVLOG(1) << "Unsupported Format: " << video_format;
    }
    return AV_PIX_FMT_NONE;
}

ColorSpace AVColorSpaceToColorSpace(AVColorSpace color_space,
    AVColorRange color_range)
{
    if (color_range == AVCOL_RANGE_JPEG)
        return COLOR_SPACE_JPEG;

    switch (color_space) {
    case AVCOL_SPC_UNSPECIFIED:
        break;
    case AVCOL_SPC_BT709:
        return COLOR_SPACE_HD_REC709;
    case AVCOL_SPC_SMPTE170M:
    case AVCOL_SPC_BT470BG:
        return COLOR_SPACE_SD_REC601;
    default:
        DVLOG(1) << "Unknown AVColorSpace: " << color_space;
    }
    return COLOR_SPACE_UNSPECIFIED;
}

bool FFmpegUTCDateToTime(const char* date_utc, base::Time* out)
{
    DCHECK(date_utc);
    DCHECK(out);

    std::vector<base::StringPiece> fields = base::SplitStringPiece(
        date_utc, " ", base::KEEP_WHITESPACE, base::SPLIT_WANT_NONEMPTY);
    if (fields.size() != 2)
        return false;

    std::vector<base::StringPiece> date_fields = base::SplitStringPiece(
        fields[0], "-", base::KEEP_WHITESPACE, base::SPLIT_WANT_NONEMPTY);
    if (date_fields.size() != 3)
        return false;

    // TODO(acolwell): Update this parsing code when FFmpeg returns sub-second
    // information.
    std::vector<base::StringPiece> time_fields = base::SplitStringPiece(
        fields[1], ":", base::KEEP_WHITESPACE, base::SPLIT_WANT_NONEMPTY);
    if (time_fields.size() != 3)
        return false;

    base::Time::Exploded exploded;
    exploded.millisecond = 0;
    if (base::StringToInt(date_fields[0], &exploded.year) && base::StringToInt(date_fields[1], &exploded.month) && base::StringToInt(date_fields[2], &exploded.day_of_month) && base::StringToInt(time_fields[0], &exploded.hour) && base::StringToInt(time_fields[1], &exploded.minute) && base::StringToInt(time_fields[2], &exploded.second)) {
        base::Time parsed_time = base::Time::FromUTCExploded(exploded);
        if (parsed_time.is_null())
            return false;

        *out = parsed_time;
        return true;
    }

    return false;
}

int32_t HashCodecName(const char* codec_name)
{
    // Use the first 32-bits from the SHA1 hash as the identifier.
    int32_t hash;
    //memcpy(&hash, base::SHA1HashString(codec_name).substr(0, 4).c_str(), 4);
    hash = base::Hash(codec_name, strlen(codec_name));
    return hash;
}

#if LIBAVCODEC_VERSION_MAJOR >= 59
// New decode functions for FFmpeg 6.0+
int decode_audio_new(AVCodecContext* ctx, AVFrame* frame, int* got_frame, AVPacket* pkt)
{
    int ret = 0;
    *got_frame = 0;

    if (pkt) {
        ret = avcodec_send_packet(ctx, pkt);
        if (ret < 0 && ret != AVERROR(EAGAIN) && ret != AVERROR_EOF)
            return ret;
    }

    ret = avcodec_receive_frame(ctx, frame);
    if (ret >= 0) {
        *got_frame = 1;
        return pkt ? pkt->size : 0;
    } else if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
        return 0;
    }

    return ret;
}

int decode_video_new(AVCodecContext* ctx, AVFrame* frame, int* got_frame, AVPacket* pkt)
{
    int ret = 0;
    *got_frame = 0;

    if (pkt) {
        ret = avcodec_send_packet(ctx, pkt);
        if (ret < 0 && ret != AVERROR(EAGAIN) && ret != AVERROR_EOF)
            return ret;
    }

    ret = avcodec_receive_frame(ctx, frame);
    if (ret >= 0) {
        *got_frame = 1;
        return pkt ? pkt->size : 0;
    } else if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
        return 0;
    }

    return ret;
}
#endif

// Time base conversion functions
base::TimeDelta ConvertFromTimeBase(const AVRational& time_base, int64_t timestamp)
{
    int64_t microseconds = av_rescale_q(timestamp, time_base, AV_TIME_BASE_Q);
    return base::TimeDelta::FromMicroseconds(microseconds);
}

int64_t ConvertToTimeBase(const AVRational& time_base, const base::TimeDelta& timestamp)
{
    return av_rescale_q(timestamp.InMicroseconds(), AV_TIME_BASE_Q, time_base);
}

// Channel layout conversion function
ChannelLayout ChannelLayoutToChromeChannelLayout(uint64_t channel_layout, int channels)
{
    switch (channels) {
    case 1:
        return CHANNEL_LAYOUT_MONO;
    case 2:
        return CHANNEL_LAYOUT_STEREO;
    case 3:
        return CHANNEL_LAYOUT_2_1;
    case 4:
        return CHANNEL_LAYOUT_4_0;
    case 5:
        return CHANNEL_LAYOUT_5_0;
    case 6:
        return CHANNEL_LAYOUT_5_1;
    case 7:
        return CHANNEL_LAYOUT_6_1;
    case 8:
        return CHANNEL_LAYOUT_7_1;
    default:
        return CHANNEL_LAYOUT_UNSUPPORTED;
    }
}

#if LIBAVCODEC_VERSION_MAJOR >= 59
// Compatibility function for av_channel_layout_default
extern "C" {
void av_channel_layout_default(AVChannelLayout* ch_layout, int nb_channels)
{
    ch_layout->order = AV_CHANNEL_ORDER_NATIVE;
    ch_layout->nb_channels = nb_channels;

    switch (nb_channels) {
    case 1:
        ch_layout->u.mask = AV_CH_LAYOUT_MONO;
        break;
    case 2:
        ch_layout->u.mask = AV_CH_LAYOUT_STEREO;
        break;
    case 3:
        ch_layout->u.mask = AV_CH_LAYOUT_2_1;
        break;
    case 4:
        ch_layout->u.mask = AV_CH_LAYOUT_4POINT0;
        break;
    case 5:
        ch_layout->u.mask = AV_CH_LAYOUT_5POINT0;
        break;
    case 6:
        ch_layout->u.mask = AV_CH_LAYOUT_5POINT1;
        break;
    case 7:
        ch_layout->u.mask = AV_CH_LAYOUT_6POINT1;
        break;
    case 8:
        ch_layout->u.mask = AV_CH_LAYOUT_7POINT1;
        break;
    default:
        ch_layout->u.mask = 0;
        break;
    }
}
}
#endif



} // namespace media
