// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef MEDIA_FFMPEG_FFMPEG_COMMON_H_
#define MEDIA_FFMPEG_FFMPEG_COMMON_H_

#include <stdint.h>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/channel_layout.h>
#include <libavutil/error.h>
#include <libavutil/imgutils.h>
}

#include "base/compiler_specific.h"
#include "media/base/channel_layout.h"
#include "media/base/video_codecs.h"
#include "media/base/video_frame.h"
//#include "media/base/audio_codecs.h"
#include "media/base/audio_decoder_config.h"

// FFmpeg forward declarations for types used in this header.
struct AVCodecContext;
struct AVFormatContext;
struct AVFrame;
struct AVPacket;
struct AVRational;
struct AVStream;

namespace media {

// Remove existing definitions to avoid conflicts
#undef AV_CODEC_CAP_DELAY
#undef AV_INPUT_BUFFER_PADDING_SIZE

// FFmpeg version compatibility macros
#if LIBAVCODEC_VERSION_MAJOR >= 59
#define GET_CODEC_PARAMS(stream) ((stream)->codecpar)
#define GET_CODEC_CTX(stream) avcodec_alloc_context3(NULL)
#define SET_CODEC_CTX(stream, ctx) avcodec_parameters_to_context(ctx, (stream)->codecpar)
#else
#define GET_CODEC_PARAMS(stream) ((stream)->codec)
#define GET_CODEC_CTX(stream) ((stream)->codec)
#define SET_CODEC_CTX(stream, ctx) (0)
#endif

#if LIBAVCODEC_VERSION_MAJOR >= 59
#define GET_CHANNEL_COUNT(ctx) ((ctx)->ch_layout.nb_channels)
#define GET_CHANNEL_LAYOUT(ctx) ((ctx)->ch_layout.u.mask)
#define SET_CHANNEL_LAYOUT(ctx, count, layout) av_channel_layout_default(&(ctx)->ch_layout, count)
#define GET_FRAME_CHANNELS(frame) ((frame)->ch_layout.nb_channels)
#else
#define GET_CHANNEL_COUNT(ctx) ((ctx)->channels)
#define GET_CHANNEL_LAYOUT(ctx) ((ctx)->channel_layout)
#define SET_CHANNEL_LAYOUT(ctx, count, layout) do { \
    (ctx)->channels = count; \
    (ctx)->channel_layout = layout; \
} while (0)
#define GET_FRAME_CHANNELS(frame) ((frame)->channels)
#endif

// Decode function compatibility
#if LIBAVCODEC_VERSION_MAJOR >= 59
#define DECODE_AUDIO(ctx, frame, got_frame, pkt) \
    decode_audio_new(ctx, frame, got_frame, pkt)
#define DECODE_VIDEO(ctx, frame, got_frame, pkt) \
    decode_video_new(ctx, frame, got_frame, pkt)
#else
#define DECODE_AUDIO(ctx, frame, got_frame, pkt) \
    avcodec_decode_audio4(ctx, frame, got_frame, pkt)
#define DECODE_VIDEO(ctx, frame, got_frame, pkt) \
    avcodec_decode_video2(ctx, frame, got_frame, pkt)
#endif

#ifndef AV_CODEC_CAP_DELAY
#define AV_CODEC_CAP_DELAY 0x0020
#endif

#ifndef AV_INPUT_BUFFER_PADDING_SIZE
#define AV_INPUT_BUFFER_PADDING_SIZE 64
#endif

// Helper functions for extracting codec parameters and contexts
inline AVCodecParameters* GetCodecParameters(const AVStream* stream) {
    return GET_CODEC_PARAMS(stream);
}

inline AVCodecContext* CreateCodecContext(const AVStream* stream) {
    AVCodecContext* ctx = GET_CODEC_CTX(stream);
    if (ctx)
        SET_CODEC_CTX(stream, ctx);
    return ctx;
}

// New decode functions for FFmpeg 6.0+
#if LIBAVCODEC_VERSION_MAJOR >= 59
int decode_audio_new(AVCodecContext* ctx, AVFrame* frame, int* got_frame, AVPacket* pkt);
int decode_video_new(AVCodecContext* ctx, AVFrame* frame, int* got_frame, AVPacket* pkt);
#endif

// Channel layout conversion function
ChannelLayout ChannelLayoutToChromeChannelLayout(uint64_t channel_layout, int channels);

// Time base conversion functions
base::TimeDelta ConvertFromTimeBase(const AVRational& time_base, int64_t timestamp);
int64_t ConvertToTimeBase(const AVRational& time_base, const base::TimeDelta& timestamp);

// Audio/Video configuration functions
bool AVStreamToAudioDecoderConfig(const AVStream* stream, AudioDecoderConfig* config);
bool AVStreamToVideoDecoderConfig(const AVStream* stream, VideoDecoderConfig* config);
void AudioDecoderConfigToAVCodecContext(const AudioDecoderConfig& config, AVCodecContext* codec_context);
void VideoDecoderConfigToAVCodecContext(const VideoDecoderConfig& config, AVCodecContext* codec_context);

// Codec conversion functions
VideoCodec CodecIDToVideoCodec(AVCodecID codec_id);
VideoCodecProfile ProfileIDToVideoCodecProfile(AVCodecID codec_id, int profile_id);
AVCodecID VideoCodecToCodecID(VideoCodec video_codec);
AVCodecID AudioCodecToCodecID(AudioCodec audio_codec, SampleFormat sample_format);

// Pixel format conversion functions
VideoPixelFormat AVPixelFormatToVideoPixelFormat(AVPixelFormat pixel_format);
AVPixelFormat VideoPixelFormatToAVPixelFormat(VideoPixelFormat video_format);

// Color space conversion functions
ColorSpace AVColorSpaceToColorSpace(AVColorSpace color_space, AVColorRange color_range);

// Sample format conversion functions
AVSampleFormat SampleFormatToAVSampleFormat(SampleFormat sample_format);

// Utility functions
bool FFmpegUTCDateToTime(const char* date_utc, base::Time* out);
int32_t HashCodecName(const char* codec_name);

// Channel layout functions
uint64_t ChannelLayoutToAVChannelLayout(ChannelLayout channel_layout);
int ChannelLayoutToChannelCount(ChannelLayout channel_layout);

} // namespace media

#endif // MEDIA_FFMPEG_FFMPEG_COMMON_H_
