// Copyright 2011 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

"use strict";

var code1 = "function f(eval) {}";
var code2 = "function f(a, a) {}";
var code3 = "var x = '\\020;'";
var code4 = "function arguments() {}";

// Verify the code compiles just fine in non-strict mode
// (using aliased eval to force non-strict mode)
var eval_alias = eval;

eval_alias(code1);
eval_alias(code2);
eval_alias(code3);
eval_alias(code4);

function strict1() {
  var exception = false;
  try {
    eval(code1);
  } catch (e) {
    exception = true;
    assertInstanceof(e, SyntaxError);
  }
  assertTrue(exception);

  function strict2() {
    var exception = false;
    try {
      eval(code2);
    } catch (e) {
      exception = true;
      assertInstanceof(e, SyntaxError);
    }
    assertTrue(exception);

    function strict3() {
      var exception = false;
      try {
        eval(code3);
      } catch (e) {
        exception = true;
        assertInstanceof(e, SyntaxError);
      }
      assertTrue(exception);

      function strict4() {
        var exception = false;
        try {
          eval(code4);
        } catch (e) {
          exception = true;
          assertInstanceof(e, SyntaxError);
        }
        assertTrue(exception);
      }
      strict4();
    }
    strict3();
  }
  strict2();
}
strict1();
