// Copyright 2011 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

function f() {
  var x = -962609355;
  var tmp = 0;
  assertEquals(-114583755, x -= (((-2806715240)&(((1961136061.0329285)>>>((2087162059)*x))+((tmp = -1890084022.7631018, tmp)%(tmp = 2137514142.358262, tmp))))+(x<<(tmp = 2991240918, tmp))));
  assertEquals(-425721856, x <<= x);
  assertEquals(3778560, x >>>= ((x|(3198503572))>>(1158434541.1099558)));
  assertEquals(3778560, x %= (tmp = -2592585378.9592104, tmp));
  assertEquals(624640, x &= (tmp = 2261638192.9864054, tmp));
  assertEquals(1249280, x += x);
  assertEquals(1048576, x &= ((tmp = -2144301819.9892588, tmp)^((x-x)<<x)));
  assertEquals(2097152, x <<= (x/x));
  assertEquals(5069061551149729, x *= (tmp = 2417116904.8069615, tmp));
  assertEquals(1.4836296666029616e+25, x += ((tmp = 2926833006.7121572, tmp)*x));
  assertEquals(-256, x >>= ((-469330345.3589895)%((x^(((2554170843.4978285)/(2495676674.815263))>>>x))*(-918892963))));
  assertEquals(-134217728, x <<= (x|(((((1687450853.1321645)+(tmp = 2369533014.5803776, tmp))+(tmp = -2613779445, tmp))+(tmp = -2488826226.3733397, tmp))>>(tmp = -220646936.41245174, tmp))));
  assertEquals(704164545131708400, x *= ((-2632786741)+(-2613647956)));
  assertEquals(9216, x >>>= (-1925405359.657349));
  assertEquals(4491403261551.008, x *= (tmp = 487348444.1787118, tmp));
  assertEquals(4490606381829.008, x -= (tmp = 796879722, tmp));
  assertEquals(-60294056, x >>= x);
  assertEquals(-3193966580.494005, x += (tmp = -3133672524.494005, tmp));
  assertEquals(550500358, x >>>= ((tmp = -2779637628.390116, tmp)-((tmp = 29230786.984039664, tmp)%(tmp = -310649504.7704866, tmp))));
  assertEquals(68812544, x >>= (-1347584797));
  assertEquals(1.2120221595741834e-11, x /= ((2791020260)*((((1964870148.6358237)^x)|(-3082869417))-((x^x)&((1234292117.8790703)<<(-1792461937.2469518))))));
  assertEquals(1.2120221595741834e-11, x %= (x-(2780439348)));
  assertEquals(-1421552183, x |= (tmp = -1421552183.5930738, tmp));
  assertEquals(-1420954119, x |= ((((-2547788562.5735893)<<x)%(435385623))>>(x|x)));
  assertEquals(1, x /= x);
  assertEquals(1, x >>= (x>>>(((2975715011.501709)-(tmp = -1473273552.981069, tmp))/(1654883913.042487))));
  assertEquals(-65382, x ^= ((x/((tmp = -2780026200, tmp)<<x))^(((-2683084424)<<x)>>(-1716245874))));
  assertEquals(1530921106, x &= (1530940914));
  assertEquals(1, x /= x);
  assertEquals(0, x >>= x);
  assertEquals(0, x /= (tmp = 773741434.1972584, tmp));
  assertEquals(0, x |= x);
  assertEquals(0, x <<= (-67977514.99888301));
  assertEquals(0, x %= (2496550482.524729));
  assertEquals(-0, x /= (tmp = -515040417, tmp));
  assertEquals(0, x <<= (-1673460935.2858837));
  assertEquals(-2638209488, x += (-2638209488));
  assertEquals(-2400951839498683400, x *= (910068685));
  assertEquals(1600582036, x ^= (((-1247602308.4812562)>>(((-2393714444.179732)>>>x)%(-778140635.7165127)))+(-1933914727.2268424)));
  assertEquals(0, x *= ((x-x)>>(-1270234575)));
  assertEquals(0, x >>>= (tmp = 3193676327.493656, tmp));
  assertEquals(0, x ^= (x>>>(1148676785.389884)));
  assertEquals(0, x >>= (tmp = -2269181763.8663893, tmp));
  assertEquals(0, x >>= (3149450221));
  assertEquals(0, x >>= (1069630750));
  assertEquals(-625009654, x ^= ((-2143499112)%(-759244728.6214335)));
  assertEquals(3583943, x >>>= (-2942645558.1204453));
  assertEquals(1791971, x >>= (x/x));
  assertEquals(223996, x >>= x);
  assertEquals(6999, x >>= (tmp = -1051883611.9443719, tmp));
  assertEquals(1459617792, x <<= (-1572314984));
  assertEquals(2622356453.269262, x -= (tmp = -1162738661.2692618, tmp));
  assertEquals(5103676461.269262, x += (2481320008));
  assertEquals(823989684.2692623, x %= (x^(((((1048362966)*((tmp = -2423040747.6233954, tmp)>>>x))*((tmp = 2330818588.4081, tmp)>>(tmp = 103312020.98346841, tmp)))+(tmp = 2264492857.144133, tmp))>>>((tmp = 2523442834, tmp)<<x))));
  assertEquals(0, x >>>= (tmp = -2018700898.531027, tmp));
  assertEquals(NaN, x /= x);
  assertEquals(0, x <<= (tmp = -2489442223, tmp));
  assertEquals(0, x >>= ((3045836220)>>>x));
  assertEquals(-1156905149, x ^= (3138062147));
  assertEquals(-0, x %= x);
  assertEquals(-3118433907.512866, x -= ((tmp = 1338611238, tmp)-(-1779822669.5128663)));
  assertEquals(100679693, x &= (1040565279));
  assertEquals(10136400582574248, x *= x);
  assertEquals(0, x %= x);
  assertEquals(2400318405, x += (2400318405));
  assertEquals(1.0036190808578471, x /= (((tmp = -2313492253.9889445, tmp)|(x-((tmp = -205459123, tmp)>>x)))+x));
  assertEquals(0, x >>>= (tmp = 882343227.1675215, tmp));
  assertEquals(0, x &= ((tmp = 2307828832.2706165, tmp)^((((((1404388047)<<((807879382)-(-2862921873)))-x)*(tmp = -1897734732, tmp))>>(tmp = 1981888881.2306776, tmp))%x)));
  assertEquals(0, x <<= x);
  assertEquals(0, x *= (((x*x)*((((2764801384.171454)%(x>>>x))&(384818815))+(x>>(tmp = -1481683516, tmp))))&x));
  assertEquals(0, x >>= (tmp = -2202536436, tmp));
  assertEquals(0, x ^= x);
  assertEquals(0, x &= (tmp = 15161124, tmp));
  assertEquals(-1586110900, x ^= (-1586110900));
  assertEquals(-1586127952, x -= ((tmp = 560737212, tmp)%((1349529668)>>>(tmp = -1956656528, tmp))));
  assertEquals(-1174945870, x -= ((1178456190)|x));
  assertEquals(1335167624.3422346, x -= (tmp = -2510113494.3422346, tmp));
  assertEquals(1329952126.3422346, x -= (x>>x));
  assertEquals(1, x >>= x);
  assertEquals(3, x |= (x<<x));
  assertEquals(3, x -= (x-x));
  assertEquals(-1938525669, x |= (tmp = 2356441625.5128202, tmp));
  assertEquals(-1938525669, x ^= ((tmp = -197149141.3622346, tmp)/(2833823156)));
  assertEquals(-2.6292393147661324, x /= (737295254.2254335));
  assertEquals(2925975987.370761, x -= (-2925975990));
  assertEquals(2925975987.370761, x %= (tmp = 3041184582.8197603, tmp));
  assertEquals(-1908068660, x ^= ((tmp = -1380575181, tmp)-(2375164084.8366547)));
  assertEquals(-477017165, x >>= (tmp = 2420877826.353099, tmp));
  assertEquals(-477017165, x %= ((tmp = -2919204062.3683634, tmp)-(tmp = -2263328990, tmp)));
  assertEquals(-2105539936, x &= ((tmp = -1630795440, tmp)-(x&((933423833)>>(-475069901)))));
  assertEquals(-4979480720, x -= (tmp = 2873940784, tmp));
  assertEquals(-4190953472, x -= (x&(tmp = -645918862.9001305, tmp)));
  assertEquals(17564091004468855000, x *= x);
  assertEquals(-857277134, x |= (tmp = 2363948338, tmp));
  assertEquals(1015632515, x -= (-1872909649));
  assertEquals(-1150380043, x ^= (tmp = -2014853770, tmp));
  assertEquals(1607729152, x <<= ((2194449589)+(x|(tmp = -1470075256.4605722, tmp))));
  assertEquals(1608356496, x |= ((((x|(670426524))<<((-2415862218)>>(tmp = 1572561529.9213061, tmp)))^((-1989566800.3681061)|x))&(2170270618.3401785)));
  assertEquals(-1836056576, x <<= (tmp = 2906301296.540217, tmp));
  assertEquals(-2952415961567723500, x *= (tmp = 1608020145, tmp));
  assertEquals(1435500544, x <<= x);
  assertEquals(700928, x >>>= (tmp = 2924829771.1804566, tmp));
  assertEquals(0, x <<= ((x^(2410009094))|(((-164334714.18698573)%(x*x))|(tmp = 2182431441.2575436, tmp))));
  assertEquals(-143321285, x ^= (tmp = -143321285, tmp));
  assertEquals(-2, x >>= x);
  assertEquals(-1, x >>= (x&(1109737404)));
  assertEquals(1, x >>>= x);
  assertEquals(0, x ^= x);
  assertEquals(-2463707358.165766, x += (-2463707358.165766));
  assertEquals(1831259938, x >>= (((((x-(tmp = 1359448920.5452857, tmp))%(tmp = -104541523, tmp))/((3133289055.9780197)*x))>>x)%x));
  assertEquals(1858895646, x ^= ((tmp = 131424376, tmp)>>(tmp = -396761023, tmp)));
  assertEquals(1, x >>= x);
  assertEquals(-1888369021, x |= ((tmp = -2038869285.046599, tmp)^((tmp = -1318286592.4250565, tmp)-(tmp = 2825123496, tmp))));
  assertEquals(1036458508, x <<= ((tmp = 2722401450, tmp)/((tmp = 1090712291, tmp)>>((tmp = -2155694696.9755683, tmp)*(tmp = 1661107340, tmp)))));
  assertEquals(1, x /= (x%((tmp = -1716050484, tmp)+(tmp = -1683833551.797319, tmp))));
  assertEquals(0, x >>= (tmp = -2899315628, tmp));
  assertEquals(0, x |= x);
  assertEquals(0, x >>>= x);
  assertEquals(0, x <<= x);
  assertEquals(1546062911, x |= (1546062911));
  assertEquals(1546195271, x += ((tmp = -3210667091, tmp)>>(tmp = 1323121165, tmp)));
  assertEquals(3092390542, x += x);
  assertEquals(-1199626354, x |= (406783756));
  assertEquals(-3650317194584908300, x *= (tmp = 3042878461.625484, tmp));
  assertEquals(-7.650495675092354e+27, x *= (2095844078));
  assertEquals(0, x >>= (tmp = 342617880.3384919, tmp));
  assertEquals(22, x ^= (((tmp = 381409558.9104688, tmp)>>((2823172888.974557)>>x))>>x));
  assertEquals(736383550, x += (736383528));
  assertEquals(0, x %= x);
  assertEquals(0, x += x);
  assertEquals(-1553157831, x -= (1553157831));
  assertEquals(1838556960, x <<= (3158944357.262641));
  assertEquals(5503285699.188747, x *= ((tmp = 2437440276, tmp)/(814308583.8128904)));
  assertEquals(5824889900.188747, x -= (((tmp = 1171445694, tmp)-(tmp = -1584666956, tmp))^(tmp = 1217545373, tmp)));
  assertEquals(747032, x >>>= (-89332085));
  assertEquals(747032, x |= (x^(x^(x>>>x))));
  assertEquals(747032, x >>>= ((-1558482440)*((tmp = -2413907480, tmp)+(3003996862.384156))));
  assertEquals(7.747761349084291e+23, x += ((tmp = 518064022.64624584, tmp)*((tmp = 2001951702, tmp)*x)));
  assertEquals(0, x <<= (2769324707.5640426));
  assertEquals(NaN, x %= (((((((-2458056470.7717686)&x)>>(tmp = -361831232.42602444, tmp))*(2611108609.6727047))>>>x)/(-1713747021.8431413))*(-1143281532)));
  assertEquals(NaN, x %= ((x^((-613836813)*(tmp = -3180432597.0601435, tmp)))%x));
  assertEquals(NaN, x /= ((-1607092857)^x));
  assertEquals(0, x &= (-1190719534));
  assertEquals(0, x >>>= x);
  assertEquals(0, x += (x>>(642177579.1580218)));
  assertEquals(-3129552333, x += (-3129552333));
  assertEquals(1165414963, x &= x);
  assertEquals(2222, x >>= (((tmp = 2606317568, tmp)|x)+(tmp = 1844107136, tmp)));
  assertEquals(NaN, x %= ((x^x)<<(x/(((tmp = -1362148700, tmp)&((tmp = 76371048, tmp)<<x))>>>((x^(-2605741153))>>(((tmp = -2131608159.7634726, tmp)|(((2827792229.8004875)|(((-848439251)+(-2576768890.123433))|((tmp = -2617711776, tmp)-((-199980264)&((tmp = -46967951.76266599, tmp)/(-733253537))))))*(tmp = 1820087608, tmp)))>>>(tmp = -3118359396.4298744, tmp)))))));
  assertEquals(NaN, x /= ((2144871731)*x));
  assertEquals(NaN, x *= x);
  assertEquals(NaN, x %= (tmp = 234811462.08692443, tmp));
  assertEquals(0, x >>>= ((1121416685)|(x^(((tmp = -2905413334, tmp)<<(tmp = -3091554324.030834, tmp))<<x))));
  assertEquals(-55938048, x |= ((tmp = -55938048, tmp)+(x*(tmp = -1518809027.2695136, tmp))));
  assertEquals(-3.3234995678333864e-10, x /= (x*(tmp = -3008876576, tmp)));
  assertEquals(0, x <<= (x/((((((-2168824234.2418427)>>(((tmp = 1976810951, tmp)%x)<<(x*(x>>(x%(3146266192))))))%(tmp = 1756971968.122397, tmp))>>>(-2859440157.8352804))/(-1001406.1919288635))>>>(-1358031926))));
  assertEquals(-0, x *= (tmp = -1756000533, tmp));
  assertEquals(-0, x %= (2522761446.869926));
  assertEquals(0, x >>>= (((1087690535)>>>(2741387979))^x));
  assertEquals(0, x -= x);
  assertEquals(0, x >>= (-819422694.2188396));
  assertEquals(0, x ^= x);
  assertEquals(NaN, x /= x);
  assertEquals(0, x &= (tmp = 86627723, tmp));
  assertEquals(0, x += x);
  assertEquals(0, x %= (tmp = -2317915475, tmp));
  assertEquals(Infinity, x += (((-3072799584)^(-2487458319))/(((tmp = -3050692353, tmp)&x)>>(-777977292.8500206))));
  assertEquals(Infinity, x += x);
  assertEquals(Infinity, x -= (tmp = 484428269, tmp));
  assertEquals(Infinity, x *= x);
  assertEquals(Infinity, x /= (2059586218.2278104));
  assertEquals(Infinity, x *= (tmp = 415918523.8350445, tmp));
  assertEquals(-1800869091, x |= (((-1800869091)>>>(x>>>(tmp = -2832575051, tmp)))>>>x));
  assertEquals(6196126991451132000, x *= ((-1467292383.8458765)+(-1973339154.7911158)));
  assertEquals(6196126992684649000, x += (1233517421));
  assertEquals(1, x /= x);
  assertEquals(-7153809722216516000, x -= (((-2984550787.146106)<<(tmp = 743743974, tmp))*((3155151275)/((-1771412568.8965073)%x))));
  assertEquals(-7153809721471491000, x -= (-745024056));
  assertEquals(5.117699353102001e+37, x *= x);
  assertEquals(0, x >>= x);
  assertEquals(-0, x *= ((-2651785447.666973)<<(-1124902998)));
  assertEquals(-0, x /= (2119202944));
  assertEquals(1042673805.5205957, x -= ((x<<x)-(tmp = 1042673805.5205957, tmp)));
  assertEquals(62, x >>>= (tmp = 2769597912.977452, tmp));
  assertEquals(34, x &= ((tmp = -61541150, tmp)%(x^(-943160469))));
  assertEquals(34, x ^= ((-2625482224.4605474)<<(-2277806338.3461556)));
  assertEquals(536870912, x <<= ((-2373927426.4757633)^x));
  assertEquals(536870912, x &= x);
  assertEquals(512, x >>>= ((-1626769708.310139)<<((tmp = 641796314, tmp)/(721629637.3215691))));
  assertEquals(0, x <<= (-113973033));
  assertEquals(NaN, x /= x);
  assertEquals(NaN, x += (-1602711788.2390788));
  assertEquals(NaN, x *= (x%x));
  assertEquals(0, x &= (x<<(x|(x>>((x>>>(x%((1182960050)^(((-220896609)-((((tmp = 1518275435.360103, tmp)/(tmp = -88234820, tmp))^x)/x))>>(3169930777.548236)))))-(tmp = -2912668817.662395, tmp))))));
  assertEquals(0, x *= ((2323969408.7524366)/(((tmp = -3089229853, tmp)>>>((((tmp = -1012580544.5631487, tmp)>>(1138049418.9023373))>>x)&x))*(tmp = 626912001, tmp))));
  assertEquals(0, x >>>= x);
  assertEquals(NaN, x /= (x%(-868024322)));
  assertEquals(NaN, x /= (tmp = -1749532322, tmp));
  assertEquals(1861918711, x |= (-2433048585.853014));
  assertEquals(1861918711, x >>= (((102451747)>>>((((241651917.47259736)/((((((((1759022236)^(tmp = -2592022722, tmp))+((-1748044969)>>>(704597925)))/(-1639604842))%((1349846853.7345295)<<(-729695861)))/(x>>((tmp = -2654474404.7365866, tmp)>>x)))>>>(((-480356478)|(x%((tmp = -1668269244.6979945, tmp)+(tmp = -2441424458.565183, tmp))))^((1634981212.7598324)>>>(tmp = 122455570.22000062, tmp))))<<x))*((tmp = -1058636137.5037816, tmp)+((2794083757.138838)&((x/(50081370))&x))))/x))/((tmp = -243106636, tmp)<<((x*((tmp = -648475219.5971704, tmp)>>((tmp = -1568913034, tmp)-((tmp = 911458615, tmp)|x))))>>>(tmp = 2714767933.920696, tmp)))));
  assertEquals(0, x ^= x);
  assertEquals(-2080484602, x |= (((1544771831.4758213)|x)^(-538113039)));
  assertEquals(696451072, x <<= (tmp = -1587032689, tmp));
  assertEquals(-162595645, x += (tmp = -859046717, tmp));
  assertEquals(516546456, x >>>= x);
  assertEquals(623083588, x += ((-1371850352)^(tmp = -1469933252, tmp)));
  assertEquals(92342412, x %= (tmp = -132685294, tmp));
  assertEquals(500272110, x |= ((tmp = 1616032506, tmp)%((tmp = 1589569590.4269853, tmp)|(-972791738.1829333))));
  assertEquals(3247086, x %= (((tmp = 1372216208, tmp)|(-638950076.3387425))&((-2619249161.849716)&(73957896))));
  assertEquals(0, x >>>= (tmp = -1482343462.6911879, tmp));
  assertEquals(1265125662, x ^= (tmp = -3029841634, tmp));
  assertEquals(4941897, x >>>= (-2039728632));
  assertEquals(206857, x &= (tmp = 226962365.45571184, tmp));
  assertEquals(1.0925018562586405e+24, x += ((tmp = 2687424146, tmp)*(((-1998020319)%x)*(-2080331363))));
  assertEquals(-1.755270751212437e+32, x *= (-160665242));
  assertEquals(0, x <<= (3152796521.6427975));
  assertEquals(0, x ^= ((((((tmp = -855001595, tmp)<<(2007525777))-(x-(x-x)))/(3036585090.9701214))&(1827983388))*((tmp = -915604789.0515733, tmp)&(((((tmp = -806628722.7820358, tmp)%x)/(tmp = -2773117447, tmp))|x)<<(((tmp = -2902300974.7300634, tmp)|x)/(-1608133440))))));
  assertEquals(0, x |= ((((((119024954)*(((x^(tmp = 2939514414, tmp))|x)^(x-(tmp = -1597415597.6795669, tmp))))+(((tmp = -182277816.14547157, tmp)<<(((-2983451324.3908825)^(tmp = 1572568307, tmp))+(-1165604960.8619013)))/(x>>((tmp = -2127699399, tmp)>>((x^(((((tmp = -1968667383, tmp)^(tmp = 3120052415.9964113, tmp))|(((x|(((x^((tmp = 2831505153, tmp)<<((-3150506831.547093)+((x%(tmp = 383761651, tmp))%(2856803457)))))+(((tmp = -2426953997, tmp)^(tmp = -2667954801.1010714, tmp))*(tmp = -2707801631, tmp)))&(tmp = 2082935238.794707, tmp)))^((tmp = 697573323.5349133, tmp)-x))%(tmp = 661936357, tmp)))/(-1717944600.261446))>>>((2423776015.0968056)^((-1410322010)|((x<<(tmp = 2935993226, tmp))/(tmp = -1533896392, tmp))))))*(tmp = -596675330, tmp))))))>>>(((2944268153)^(x&(144579050.93126357)))/(-2123810677.2619643)))>>>(1473040195.9009588))*x));
  assertEquals(0, x /= (2877666495));
  assertEquals(2174852514, x -= (tmp = -2174852514, tmp));
  assertEquals(543713128, x >>>= x);
  assertEquals(2978128878.939105, x += (tmp = 2434415750.939105, tmp));
  assertEquals(3529591145844655600, x *= (tmp = 1185170719.3753138, tmp));
  assertEquals(659, x >>>= ((((((x<<(((((-425423078)/(((tmp = 160617689.20550323, tmp)&(-1524740325.5003028))%(tmp = -1869426475, tmp)))<<(((x^(-487449247))>>>(tmp = -1962893666.7754712, tmp))%x))*x)>>((tmp = 623413085, tmp)&(x+(((((-2200726309.083274)-(x-x))+x)&(-1304849509))|((((tmp = -431896184, tmp)>>>(x>>(-1932126133)))<<((1078543321.2196498)*(-10761352)))>>(tmp = -2681391737.5003796, tmp)))))))/x)-(tmp = -1768629117, tmp))/(((((tmp = -2320718566.0664535, tmp)%x)+(-2831503351.995921))>>>(-2695416841.3578796))*(943979723)))<<x)|((652520546.7651662)>>(1045534827.6806792))));
  assertEquals(531, x &= (tmp = -293707149, tmp));
  assertEquals(0, x >>= (tmp = -678056747.5701449, tmp));
  assertEquals(1184651529.8021393, x += (tmp = 1184651529.8021393, tmp));
  assertEquals(1721719611, x |= (tmp = 1645413178, tmp));
  assertEquals(-406880257, x |= (tmp = 2268544460, tmp));
  assertEquals(-4194304, x <<= (tmp = -109701322.43455839, tmp));
  assertEquals(17592186044416, x *= x);
  assertEquals(0, x ^= (x&x));
  assertEquals(0, x <<= (tmp = 1715401127, tmp));
  assertEquals(-1793087394, x |= (tmp = -1793087394.730585, tmp));
  assertEquals(-2, x >>= x);
  assertEquals(263607360.10747814, x += (tmp = 263607362.10747814, tmp));
  assertEquals(1073214955, x |= (893759979.3631718));
  assertEquals(703953930, x -= ((2738450011)%(x^(tmp = 679402836, tmp))));
  assertEquals(1, x >>= (tmp = 2262515165.6670284, tmp));
  assertEquals(0, x >>= (((tmp = 747896494, tmp)^((tmp = -1005070319, tmp)+x))|x));
  assertEquals(0, x >>= ((953612771)>>>(tmp = 3066170923.3875694, tmp)));
  assertEquals(-314941454, x -= (x+(((314941454)%(((tmp = 2200222912.9440064, tmp)>>>(2534128736.805429))>>>(x|((747716234)%(((tmp = -252254528, tmp)%(-1553513480.1875453))&x)))))<<x)));
  assertEquals(-535686958, x &= (-522809126));
  assertEquals(0.5480312086215239, x /= (tmp = -977475278, tmp));
  assertEquals(-1199953459.6090598, x *= ((-2189571393)+((3186862741.37774)>>(tmp = -2193090564.5026345, tmp))));
  assertEquals(-1199953459.6090598, x %= ((tmp = 2986532440, tmp)*(2685122845)));
  assertEquals(-1199953459.6090598, x %= (1951182743.7399902));
  assertEquals(51262285383887820, x *= (-42720228));
  assertEquals(-424776752, x |= x);
  assertEquals(166221344210236600, x *= (tmp = -391314598.6158786, tmp));
  assertEquals(-1883425600, x >>= (((tmp = -1020679296, tmp)^((-1416867718)+(-1412351617)))<<(-2743753169)));
  assertEquals(0, x &= (x/(-2250026610)));
  assertEquals(-1111956501, x ^= (tmp = 3183010795, tmp));
  assertEquals(2012059503, x ^= (tmp = -900369276, tmp));
  assertEquals(15719214, x >>>= (tmp = -3196277049, tmp));
  assertEquals(15719214, x |= x);
  assertEquals(100779035, x -= ((-1245802025)^(-2964289852)));
  assertEquals(0, x >>= x);
  assertEquals(0, x &= (((x<<((2361941389.708063)%x))>>((328256762.09842086)>>>((((tmp = 3094192285, tmp)-(((x>>(tmp = -2920437464, tmp))<<(tmp = -2693021467, tmp))-(x>>>((2410065554)%(x%(tmp = 2487056196.689908, tmp))))))-(tmp = -866314146, tmp))^((1754098471)-((((((-2450740191)-(tmp = 1977885539.6785035, tmp))*((tmp = -1205431332, tmp)>>>x))>>(-870601854))>>(tmp = -301859264, tmp))|((tmp = -2308971516.8301244, tmp)/x))))))&((2307007357)-((tmp = -1518812934, tmp)+(2562270162)))));
  assertEquals(0, x <<= x);
  assertEquals(-1802124619, x |= (-1802124619));
  assertEquals(-1802124619, x %= ((1617132364.306333)+((1678465962.079633)|((516698570)%(((569813606)*(-1800804098.6270027))%((tmp = 1976706935, tmp)-((tmp = -1830228989.5488424, tmp)>>(((x^((tmp = 1015246068.3791624, tmp)>>x))^((-2171682812.246772)-(tmp = -398330350, tmp)))&x))))))));
  assertEquals(904564673.6237984, x -= (tmp = -2706689292.6237984, tmp));
  assertEquals(818237248768128900, x *= x);
  assertEquals(254842325.2585001, x %= (1550087667.9657679));
  assertEquals(-1163919360, x <<= x);
  assertEquals(-3.4644526843674166, x /= ((-446801454)+(x>>>(tmp = -2025151870, tmp))));
  assertEquals(0, x &= ((((((((-1739617728)&(x&(((tmp = -2946470036.552597, tmp)/x)*x)))^(-1130501404))>>>x)/((1870230831)>>>(840301398)))%x)/x)/(-2927537567)));
  assertEquals(0, x >>= x);
  assertEquals(0, x >>>= (x&(x&x)));
  assertEquals(0, x &= ((-579614044)-(-756012505.4048488)));
  assertEquals(-2970367642, x -= (tmp = 2970367642, tmp));
  assertEquals(-415129376, x ^= (tmp = 2847041926.060355, tmp));
  assertEquals(-1505681312, x &= (tmp = -1225184902.9215767, tmp));
  assertEquals(-3174471329.5807734, x += (-1668790017.5807734));
  assertEquals(-Infinity, x /= (x>>x));
  assertEquals(NaN, x -= x);
  assertEquals(0, x ^= (x^(((-1407936301.5682082)<<((x^(((tmp = 3213446217.307076, tmp)|x)|((tmp = 3219810777.3171635, tmp)/(tmp = 1561807400, tmp))))>>>((tmp = 2449910203.0949173, tmp)|((((1954662538.7453175)>>(tmp = -1711636239.9916713, tmp))>>>(tmp = 406219731.214718, tmp))<<(((-907908634.4609842)^((((((tmp = 2408712345, tmp)*(tmp = 1740346634.5154347, tmp))>>(tmp = 715783991, tmp))^(tmp = -655628853.2821262, tmp))%(tmp = 2819143280.434571, tmp))/(-1240412852)))*x)))))/x)));
  assertEquals(0, x >>>= x);
  assertEquals(0, x <<= x);
  assertEquals(0, x >>>= (((-3198075268.8543105)>>(((((x+((tmp = -133461401.50823164, tmp)-((x&(((((tmp = 2617977319, tmp)>>((tmp = -2704719576.8734636, tmp)|((tmp = -977362542.2423751, tmp)<<(x<<(tmp = 3054487697.1441813, tmp)))))>>>((-1635655471)%x))/(-2079513672))%(tmp = 1993563806, tmp)))<<(tmp = -1310524200.6106496, tmp))))%((((-2558804500.7722936)+(tmp = -1641265491, tmp))<<((tmp = -1309608349, tmp)>>>x))/((tmp = -2306644272, tmp)<<x)))*(-2009396162.3063657))+(267343314.3720045))-(-2212612983.661479)))|x));
  assertEquals(NaN, x %= x);
  assertEquals(NaN, x *= x);
  assertEquals(-824822309, x |= (-824822309));
  assertEquals(-807944741, x |= (((-598067403)*((x&(tmp = 2897778389, tmp))>>>(-1322468310.3699632)))|x));
  assertEquals(90004223.44097246, x /= (((tmp = -481122620, tmp)&x)%((tmp = 1109368524, tmp)/(((-3150568522.633032)<<(tmp = 2923396776, tmp))^(x-((x/x)&(x/(-287976185.1049104))))))));
  assertEquals(0.4521931751193329, x /= (tmp = 199039323, tmp));
  assertEquals(1.8110466604491368e-10, x /= (2496860986.492693));
  assertEquals(0, x |= x);
  assertEquals(-1225944576, x += ((tmp = -807700791.631221, tmp)<<((-700782615.4781106)-((((-2954619897)>>>x)<<((tmp = 997657844, tmp)>>>(1227994596)))/((-1234591654.8495834)*((tmp = -191189053.70693636, tmp)+(tmp = -3027659304, tmp)))))));
  assertEquals(-1225811383, x |= (-1866233271));
  assertEquals(3069155913, x >>>= (((x/(-99524153.40911508))%(x>>>((((tmp = 2985975640, tmp)/(tmp = 2781516546.2494454, tmp))&(((2234114508)|(((x/(tmp = -1224195047, tmp))<<x)^(x>>>((537884375.5698513)+x))))^((tmp = -2144817497.5089426, tmp)|(-498079183.8178189))))>>>((x+x)&(-3086080103.6460695)))))<<(((tmp = 2151157136, tmp)*x)/(((x/x)>>>(-1149734628.4364533))-((3025445835.654089)+(tmp = 530902725.91127443, tmp))))));
  assertEquals(-1733702568, x ^= (tmp = 776361489.423534, tmp));
  assertEquals(8981504, x &= ((tmp = 2902581847, tmp)*(x-(-2697760560))));
  assertEquals(1153166.8526612986, x -= ((x/(tmp = -1375025594.5027463, tmp))+((3043576689.1538706)%(x+x))));
  assertEquals(3389855, x |= (x+x));
  assertEquals(-488458393.17759943, x += (-491848248.17759943));
  assertEquals(40982867145206920, x *= ((3132857155)|(tmp = -218356553, tmp)));
  assertEquals(688, x >>= (((((tmp = 403321821, tmp)+((tmp = 2536984658, tmp)%((tmp = 2759309029.8753624, tmp)|(((tmp = 1994203554.7417293, tmp)^((704660500.434877)*(tmp = 1536292958.2691746, tmp)))+(-164139788)))))/((1205950994.1255205)+x))^((((tmp = 975272146.0133443, tmp)-(150107797))/(-1764309514))^((x>>>(x^(x^x)))+(203250124))))>>>(tmp = 1864959239.512323, tmp)));
  assertEquals(10, x >>= ((tmp = 1631996431.9620514, tmp)>>x));
  assertEquals(10, x %= (tmp = 2678904916, tmp));
  assertEquals(335544320, x <<= (tmp = -2759037415.6811256, tmp));
  assertEquals(-153389967, x |= ((tmp = -2411636565, tmp)+(tmp = -2305156154, tmp)));
  assertEquals(-1171, x >>= x);
  assertEquals(813080576, x &= (((tmp = -65428547, tmp)&(tmp = 3163266999, tmp))<<x));
  assertEquals(4346532303, x += ((tmp = -761515569.0707853, tmp)>>>(((tmp = 143240971.0661509, tmp)<<x)*(x^((tmp = -271697192.8471005, tmp)&x)))));
  assertEquals(-863299035, x ^= ((((2663001827.1492147)>>>((x/(((tmp = 482665912, tmp)-(x>>(tmp = 354425840.784659, tmp)))>>((-2012932893)>>>x)))/((tmp = -1354385830.6042836, tmp)>>>(-2149023857))))^((tmp = 585746520, tmp)+(tmp = 756104608, tmp)))^(517529841.184085)));
  assertEquals(-997654012, x &= (((tmp = -404836025.15326166, tmp)+((tmp = 3035650114.0402126, tmp)<<((-1308209196)>>(tmp = 693748480, tmp))))<<(((465774671.4458921)<<x)/(1971108057))));
  assertEquals(-320581507110848260, x *= ((x-(tmp = -2266777911.7123194, tmp))^(tmp = -2810021113.304348, tmp)));
  assertEquals(-320581508271196300, x += ((-1195215841.5355926)|(x-((2715907107.4276557)+(((-843426980)>>(x&(x%(tmp = -1139279208.34768, tmp))))^x)))));
  assertEquals(368031616, x &= x);
  assertEquals(368031616, x %= (tmp = 1211767328, tmp));
  assertEquals(-67505614939510744, x *= (tmp = -183423412.56766033, tmp));
  assertEquals(959424552, x >>= ((tmp = -171120122.5083747, tmp)/x));
  assertEquals(30949179.096774194, x /= (((x-((((x&(tmp = -180770090, tmp))<<(((tmp = -2061363045.419958, tmp)*((655711531)^((1205768703)-(tmp = 2468523718.8679857, tmp))))+(-2746704581)))+((-853685888)*(tmp = -2299124234, tmp)))|(tmp = 2429502966, tmp)))|(((-985794986.0232368)>>>(2890862426))%x))>>(tmp = 1005542138.8415397, tmp)));
  assertEquals(30949179, x |= x);
  assertEquals(30949179, x %= (810126097.6814196));
  assertEquals(120895, x >>= (tmp = 3065886056.1873975, tmp));
  assertEquals(1934320, x <<= (1478650660.7445493));
  assertEquals(0, x >>= (1069658046.2191329));
  assertEquals(NaN, x %= x);
  assertEquals(NaN, x %= (x*x));
  assertEquals(NaN, x *= ((((2148513916)+(tmp = -210070225.85489202, tmp))>>(975470028))+((-3060642402)>>x)));
  assertEquals(NaN, x *= (2888778384));
  assertEquals(NaN, x -= (294531300.16350067));
  assertEquals(-465620423, x ^= (tmp = -465620423.5891335, tmp));
  assertEquals(1613303808, x &= (-2530649850.1952305));
  assertEquals(2045458658, x |= (tmp = 432158946.5708574, tmp));
  assertEquals(0, x >>>= (2277328255.770018));
  assertEquals(0, x &= (-64904722.41319156));
  assertEquals(0, x >>= x);
  assertEquals(3109394857.361766, x += (3109394857.361766));
  assertEquals(1519021650, x ^= ((tmp = -2632472653, tmp)|(tmp = 2161964921.8225584, tmp)));
  assertEquals(370854, x >>>= ((1486892931.4564312)-((tmp = 3017755741.9547133, tmp)>>>x)));
  assertEquals(1333145110.39802, x -= ((-1051580495.39802)-(tmp = 281193761, tmp)));
  assertEquals(0, x ^= x);
  assertEquals(0, x |= x);
  assertEquals(0, x <<= x);
  assertEquals(0, x >>>= x);
  assertEquals(799202788.1455135, x -= (tmp = -799202788.1455135, tmp));
  assertEquals(1539080192, x <<= (x%(((((x-x)|(((((x%(959993901))+(tmp = -2647575570.092733, tmp))/(tmp = -2040600976.5104427, tmp))*(x*(tmp = 2785252760, tmp)))>>(-377867259)))/((x&(1549738240.013423))>>>(tmp = -1502185618, tmp)))*x)%(1159283801.0002391))));
  assertEquals(0, x >>= (-268660225));
  assertEquals(-0, x /= (-2795206270.635887));
  assertEquals(0, x >>>= (1869556260.2489955));
  assertEquals(64202212, x ^= ((((tmp = -942983515.5386059, tmp)*(((1057759788)-x)*(tmp = 2038041858, tmp)))>>x)+(tmp = 64202212, tmp)));
  assertEquals(2021126977, x -= ((tmp = -2009912898, tmp)^((2240062309)%x)));
  assertEquals(4332348265459724000, x *= (tmp = 2143530968, tmp));
  assertEquals(1472, x >>>= ((283380755)<<x));
  assertEquals(-1672370407872, x *= (tmp = -1136121201, tmp));
  assertEquals(338573318, x ^= (tmp = 2329579078.4832354, tmp));
  assertEquals(2377388772.1662374, x -= (tmp = -2038815454.1662374, tmp));
  assertEquals(-1.264761712403516, x /= ((((tmp = -2106209534, tmp)>>((((((tmp = 626190172, tmp)/x)>>>(-824270996.8545206))/((1258369810.9498723)-(tmp = -2947556209, tmp)))^((((366784589.24711144)|(1462064104.828938))-(1571045395.777879))<<(444685689.60103726)))>>(tmp = -2757110357.410516, tmp)))/(x>>>((tmp = 829226010, tmp)>>>(629512715))))|x));
  assertEquals(-2905481691.264762, x -= (2905481690));
  assertEquals(-1710543566.1481905, x -= (-1194938125.1165714));
  assertEquals(-3421087132.296381, x += x);
  assertEquals(-884178944, x <<= ((-1820881235)|x));
  assertEquals(-884178944, x &= (x%(tmp = -2298828530, tmp)));
  assertEquals(1516503040, x <<= ((tmp = -3039882653, tmp)+((tmp = 1956034508, tmp)<<(x>>(tmp = 280388051, tmp)))));
  assertEquals(3033006080, x += x);
  assertEquals(846431222.321887, x %= (x+(-1939718651.1609435)));
  assertEquals(-846431224, x ^= ((-1742116766.54132)/x));
  assertEquals(1157918728, x &= (tmp = 1966568030, tmp));
  assertEquals(1157918728, x >>>= ((((((tmp = -2392096728.184257, tmp)*(x&(-3051259597.301086)))>>>(((tmp = 1712991918.071982, tmp)*(tmp = -714525951, tmp))-((-1784801647)>>((-1270567991)%(((214272558)/(((-3110194570)|(tmp = 2558910020, tmp))&(-1266294955.717899)))*((2654922400.609189)>>>(tmp = 370485018, tmp)))))))*(((tmp = -2621203138.1838865, tmp)%(858913517))*((tmp = -1564229442.2596471, tmp)>>((tmp = 1898557618, tmp)|(-1282356275)))))*(tmp = -1253508468, tmp))+((-361964404.75944185)|x)));
  assertEquals(961668975, x += (-196249753));
  assertEquals(1, x >>= (tmp = 890453053, tmp));
  assertEquals(1, x >>= (((((tmp = 871309275, tmp)/(x>>>((tmp = 2033022083, tmp)&(tmp = -1393761939, tmp))))%((437488665.104565)^(tmp = 2808776860.4572067, tmp)))-((tmp = -359283111.49483967, tmp)<<((tmp = 2985855945, tmp)%(tmp = -596479825.9114966, tmp))))/(-1965528507)));
  assertEquals(0, x >>= ((tmp = -1753776989, tmp)%(tmp = 322622654, tmp)));
  assertEquals(84411424, x ^= (((x|(x|(tmp = -1617122265, tmp)))&(tmp = -313813263, tmp))&(1472888112.0258927)));
  assertEquals(67633184, x &= ((1556833131.0776267)<<(x<<(1501219716.5575724))));
  assertEquals(68002293, x |= (((tmp = 188984203.0350548, tmp)>>>(tmp = 1356052777, tmp))%(x*(tmp = -2944960865, tmp))));
  assertEquals(67108864, x &= (((1046644783.9042064)<<x)+((-2796345632)>>>(((-1913290350.3687286)<<(((((tmp = -2223692353, tmp)>>x)&(x<<(x>>((((tmp = -976850020, tmp)%(tmp = 1379692507, tmp))>>>(1120103052.2077985))>>(tmp = 5592070.612784743, tmp)))))<<(x+((tmp = -3154037212.9764376, tmp)%(((x-(-1961060483.6965141))+(((1920670676)-(2852444470.7530622))/(((1445954602)>>((1353665887)>>(tmp = 111411560.64111042, tmp)))<<x)))+x))))<<((-1773130852.6651905)^((1216129132)>>(1511187313.2680469)))))|((tmp = -1107142147, tmp)|(tmp = -768165441.4956136, tmp))))));
  assertEquals(0, x -= x);
  assertEquals(0, x %= (tmp = -1655707538.0778136, tmp));
  assertEquals(-184120712930843900, x += (x+((tmp = -3174410166, tmp)+((tmp = -301807453, tmp)*(tmp = 610060182.1666535, tmp)))));
  assertEquals(-54598560, x >>= (-1365351357));
  assertEquals(-6763.94449950446, x /= (((-1953016847)<<((673287269.7002038)%(-558739761)))>>>(tmp = 1607754129, tmp)));
  assertEquals(-1, x >>= x);
  assertEquals(1, x >>>= x);
  assertEquals(0, x >>>= x);
  assertEquals(0, x >>= ((-384747983)+((((tmp = -949058352.381772, tmp)>>>(-1920744986))-(-882729639))^((x^((tmp = 2351364046, tmp)<<(((tmp = -3110165747, tmp)^(-1266489735))-((tmp = -371614326, tmp)>>((tmp = -2064968414, tmp)&(-2075036504.617934))))))&(((-2616501739)&(tmp = 2591437335.4029164, tmp))>>x)))));
  assertEquals(0, x >>>= ((tmp = 2946468282, tmp)&((-2741453019)>>x)));
  assertEquals(0, x -= ((x%(-134700915))&(-1955768279)));
  assertEquals(NaN, x /= x);
  assertEquals(NaN, x /= (x^(((((((tmp = 3185669685.772061, tmp)>>(tmp = -1973500738, tmp))-(tmp = -87401348.93002152, tmp))>>(tmp = -2813508730, tmp))&(tmp = -778957225, tmp))<<(x-(x&((-2821756608)+(((((tmp = 2475456548, tmp)/(tmp = 997998362, tmp))<<((tmp = -83043634, tmp)|x))%(636120329))%(tmp = -1910213427.7556462, tmp))))))%x)));
  assertEquals(0, x &= x);
  assertEquals(0, x <<= x);
  assertEquals(0, x >>>= (x%x));
  assertEquals(0, x %= (745221113));
  assertEquals(0, x >>>= ((1467615554.7672596)|x));
  assertEquals(0, x /= (tmp = 735317995, tmp));
  assertEquals(-1513001460, x |= (2781965836));
  assertEquals(-1513001460, x |= (x%(1970577124.3780568)));
  assertEquals(-0, x %= x);
  assertEquals(1864972269, x ^= (-2429995027.840316));
  assertEquals(1226843341, x &= (tmp = -639621923.5135081, tmp));
  assertEquals(1226843339.3171186, x += ((1297620268.272113)/(-771070549)));
  assertEquals(76677708, x >>>= (1009134980));
  assertEquals(0, x ^= x);
  assertEquals(0, x ^= x);
  assertEquals(NaN, x /= x);
  assertEquals(716040787, x |= ((1851586229)-(1135545441.3502865)));
  assertEquals(1385693184, x <<= x);
  assertEquals(1321, x >>= (x^((tmp = -1576632297.0860603, tmp)>>>(405218605))));
  assertEquals(-1319012931, x |= (-1319014243));
  assertEquals(-1319012931, x >>= ((((1689898279.3580785)<<((((x^(x>>>((((tmp = 2635260332, tmp)*(tmp = 2053357650, tmp))*x)*(2856480122.339903))))>>x)&(-2382703000.077593))%(1183918594)))*(tmp = -1670081449, tmp))<<x));
  assertEquals(-528327581.7646315, x %= (tmp = -790685349.2353685, tmp));
  assertEquals(2073431790, x ^= (tmp = 2601800333, tmp));
  assertEquals(-6514722684180, x -= (((tmp = 824141806.0668694, tmp)>>>(((-1865885282.8723454)&(x&(x|((900188006.3757659)>>>(x&x)))))+(2227126244.0526423)))*x));
  assertEquals(1450593, x >>>= ((2157053647)>>(x+(-2934071355.418474))));
  assertEquals(576782336, x <<= ((1054640368.827202)&((tmp = -3182236876.434615, tmp)>>(tmp = 2129856634.0328193, tmp))));
  assertEquals(2950754326, x -= (tmp = -2373971990, tmp));
  assertEquals(738197504, x <<= (1188157369.5988827));
  assertEquals(0, x <<= (x+((tmp = -839533141, tmp)&((((((tmp = -1148768474.7306862, tmp)|(172650299))+(tmp = -2739838654, tmp))/(3132557129))%x)>>>(tmp = -1229961746.2466633, tmp)))));
  assertEquals(0, x %= (tmp = -2974207636, tmp));
  assertEquals(0, x %= ((2323482163)>>>x));
  assertEquals(0, x &= (((x/(x+(x>>((tmp = 55935149, tmp)%x))))|((3109182235)>>>(tmp = 1217127738.8831062, tmp)))+((((tmp = -385114910, tmp)*((((((tmp = -2535158574.634239, tmp)&(x+x))<<(-2821692922.43476))&(-776804130.9457026))>>((-1374832535)^(tmp = 2175402162.701251, tmp)))%(-1646995095)))-(x*(tmp = -921556123, tmp)))^(79224621))));
  assertEquals(128935435, x |= ((tmp = 2279459038, tmp)%(tmp = -537630900.5271742, tmp)));
  assertEquals(128935435, x /= ((((((x<<(2750024311))-((-1332480769.4784315)&(1418160003)))&(1551783357))<<(((((-2870460218.55027)|((-1958752193.7746758)&(2551525625)))>>>((((tmp = -1698256471, tmp)^(((((((((tmp = -830799466, tmp)+x)-(-111590590))+(tmp = -1105568112.3921182, tmp))/((tmp = -3058577907, tmp)|(((-1944923240.2965696)%(-2884545285))<<(tmp = -1993196044.1645615, tmp))))^(x>>(tmp = -2961488181.3795304, tmp)))&x)*x)|(((tmp = 97259132.88922262, tmp)<<((1601451019.343733)&x))*(x|x))))+((((x>>x)<<x)+(-868409202.2512136))/(((tmp = -2893170791, tmp)-((x|(-853641616))%(((tmp = 549313922, tmp)&(-768036601.6759064))%(tmp = -543862220.9338839, tmp))))-((tmp = 1639851636, tmp)+((2164412959)/(-273028039.941242))))))>>>((((-2382311775.753495)^(-2062191030.2406163))>>>(tmp = -1054563031, tmp))/(-862111938.7009578))))%x)+(-3103170117.625942)))%((tmp = -1144062234, tmp)>>x))>>>(tmp = 1216332814.00042, tmp)));
  assertEquals(41.631074722901715, x /= (x&(-2542806180.962227)));
  assertEquals(41.631074722901715, x %= (-14003386.556780577));
  assertEquals(8, x &= (x&((-2231622948)%(tmp = 488279963.9445952, tmp))));
  assertEquals(9.002961614252625e-9, x /= ((53802728.56204891)<<(((867697152.3709695)-(538719895.5707034))&(-631307825.4491808))));
  assertEquals(0, x >>= x);
  assertEquals(-0, x *= (tmp = -785674989, tmp));
  assertEquals(-0, x += x);
  assertEquals(0, x /= (-250703244));
  assertEquals(0, x <<= ((tmp = -661062581.5511999, tmp)|x));
  assertEquals(0, x &= (-1299482308));
  assertEquals(0, x &= ((-399690060)>>>(2448074202.385213)));
  assertEquals(0, x &= (2574341201));
  assertEquals(0, x <<= ((x|(((tmp = 2458873162.645012, tmp)+(tmp = -1999705422.8188977, tmp))<<((x^(tmp = -392530472, tmp))>>>x)))&(((tmp = 2463000826.7781224, tmp)|(tmp = 3020656037, tmp))-x)));
  assertEquals(1397603760, x += ((tmp = -1359413071, tmp)-(tmp = -2757016831, tmp)));
  assertEquals(513823851, x -= (883779909));
  assertEquals(-1765712747, x ^= (2288060670.6797976));
  assertEquals(3117741504918286000, x *= x);
  assertEquals(3117741506284045300, x += (1365759456));
  assertEquals(6035555595.597267, x /= (tmp = 516562470, tmp));
  assertEquals(104203275, x &= (tmp = 376835755.32434213, tmp));
  assertEquals(10858322520725624, x *= x);
  assertEquals(59458951, x >>>= (153765028));
  assertEquals(49370856, x += ((tmp = -1291276092, tmp)>>x));
  assertEquals(0, x %= x);
  assertEquals(0, x += x);
  assertEquals(-1494589645, x -= (1494589645));
  assertEquals(-0, x %= x);
  assertEquals(0, x <<= (x&((2730708043.467806)<<x)));
  assertEquals(0, x /= ((tmp = -1483912394.153527, tmp)>>>((tmp = 1800568769, tmp)^((((((tmp = 1351568510, tmp)>>(tmp = -1337992543.2562337, tmp))>>>(tmp = 2602239360.40513, tmp))*x)%x)+(-2095840128.0700707)))));
  assertEquals(-0, x /= ((2363946613)^(tmp = -2227868069, tmp)));
  assertEquals(0, x &= ((((2634933507)<<(2798775374.140882))>>>x)>>>(((tmp = 1135200853.6396222, tmp)-(tmp = -1529829490.7007523, tmp))-(((((((((x^((x|(2135742668.591568))-(924230444.8390535)))%(tmp = -2459525610.51898, tmp))+(x&((tmp = 1177231743.809653, tmp)/(tmp = 1743270357.2735395, tmp))))|(((tmp = -1894305017, tmp)^((tmp = 1791704240, tmp)&x))%(-1569751461)))>>>(tmp = -2078321944, tmp))|x)*(((x*(tmp = -163239354, tmp))<<((tmp = 2859087562.694203, tmp)&(-657988325.9410558)))^(2508013840)))-((-243572350)+(x%((-1095206140)+((tmp = 3213566608.942816, tmp)*((2256442613)%((tmp = 1723751298, tmp)^(x-((-1145710681.2693722)|x)))))))))+(1556870627)))));
  assertEquals(130883024.97423434, x -= (-130883024.97423434));
  assertEquals(0.046720352789736276, x /= (tmp = 2801413456, tmp));
  assertEquals(1806558189, x |= (tmp = 1806558189.157823, tmp));
  assertEquals(72.40475060062144, x /= (x%((1932591076.531628)>>(1982030182))));
  assertEquals(-1077558321.5975945, x += (tmp = -1077558394.002345, tmp));
  assertEquals(98187, x >>>= x);
  assertEquals(97792, x &= (tmp = -1032487404, tmp));
  assertEquals(709197609, x |= (x^(709179177)));
  assertEquals(11081212, x >>>= (tmp = 1412940006.169063, tmp));
  assertEquals(11081212, x &= x);
  assertEquals(-1920311203, x -= ((tmp = 1931392415, tmp)<<((x%(tmp = -2873576383, tmp))%x)));
  assertEquals(-1920311203, x |= (x&(-993884718.2172024)));
  assertEquals(-4, x >>= (1409411613.0051966));
  assertEquals(-7947632484, x *= ((-2856731734)^((-1181032235.9132767)-((tmp = 780101930, tmp)+((tmp = -1732707132.6253016, tmp)^x)))));
  assertEquals(-2016362769, x ^= (tmp = 2711125619.2455907, tmp));
  assertEquals(-61535, x >>= x);
  assertEquals(-124771649, x ^= (tmp = 124726558, tmp));
  assertEquals(-1, x >>= x);
  assertEquals(-0, x %= (x*x));
  assertEquals(0, x <<= x);
  assertEquals(0, x /= (2444628112));
  assertEquals(0, x <<= ((-38968517.72504854)<<x));
  assertEquals(-1504619917, x |= (tmp = 2790347379, tmp));
  assertEquals(-1504619917, x &= x);
  assertEquals(2790347379, x >>>= ((1825218368)<<(-1843582593.2843356)));
  assertEquals(7786038495492170000, x *= x);
  assertEquals(-11011696, x |= (((tmp = 2931644407.4936504, tmp)-(3077095016.001658))%(tmp = -1731851949, tmp)));
  assertEquals(-107866, x %= ((-697845074.1661191)>>(772708134)));
  assertEquals(356779149, x ^= (-356884949.503757));
  assertEquals(0, x %= x);
  assertEquals(0, x *= ((tmp = 1542291783, tmp)^x));
  assertEquals(0, x += ((tmp = 1105314644.002441, tmp)&x));
  assertEquals(-1005882993, x ^= (-1005882993.0899806));
  assertEquals(-1301065066, x += (tmp = -295182073, tmp));
  assertEquals(-1454702592, x <<= ((-2440858737.390277)&(-1363565201.7888322)));
  assertEquals(-201539012492525570, x *= ((((tmp = -1416268089, tmp)|x)-(tmp = 1669129769, tmp))&(x<<((x/(-2614041678.7423654))%x))));
  assertEquals(-2.1995276811535986e+25, x *= (x/(-1846667987.154371)));
  assertEquals(0, x |= ((x*(((x>>>((tmp = 1044173034, tmp)>>>((x<<((tmp = -2906412863, tmp)%((tmp = -437401503, tmp)<<(((((x|(2167319070))<<((tmp = 2766179640.1840167, tmp)&(-2372076054)))*(tmp = -241617431.06416297, tmp))*((((((tmp = 2570465382.5574293, tmp)>>>(x/((-2851324509.354545)%x)))>>(((x+((tmp = -614687945, tmp)^x))^((((tmp = 1653437743, tmp)>>x)/(tmp = 3072995069, tmp))>>x))*(((((-290508242)>>((tmp = 2969511554, tmp)<<(tmp = 158176292.95642304, tmp)))<<(32376015))+(tmp = 2391895870.4562025, tmp))*x)))&((((x/(tmp = 365292078.53605413, tmp))>>x)/(1167322811.0008812))|(((tmp = 2487970377.365221, tmp)^x)<<((tmp = 2342607988.711308, tmp)/(((2276081555.340126)-(((tmp = -2571071930, tmp)>>(tmp = -248468735.76550984, tmp))>>>(tmp = -2862254985.608489, tmp)))^(-1312017395))))))<<x)&(2762717852.949236)))+((((-2492896493)&x)<<(-2756272781.4642315))/x)))))*(2405395452))))>>((-1433975206)/((tmp = -2064757738.6740267, tmp)<<((((tmp = -1563531255, tmp)-(-589277532.2110934))<<x)^(2249328237.0923448)))))-x))-(-225624231)));
  assertEquals(0, x *= (tmp = 1657982666.2188392, tmp));
  assertEquals(86443387, x |= (tmp = 86443387.25165462, tmp));
  assertEquals(86443387, x %= (-1341731981.702294));
  assertEquals(172886774, x <<= ((-1799840391)&(1011948481.310498)));
  assertEquals(-1115684864, x <<= x);
  assertEquals(-2098253702059525600, x *= (1880686715.1865616));
  assertEquals(-2098253700213206300, x -= (tmp = -1846319435.0583687, tmp));
  assertEquals(570692096, x &= (((tmp = -1572055366.64332, tmp)%(tmp = 1720120910, tmp))%((x-(912386952.5959761))*(tmp = -1146251719.4027123, tmp))));
  assertEquals(603979776, x <<= ((-329752233.8144052)&(tmp = -368636559, tmp)));
  assertEquals(603979776, x <<= x);
  assertEquals(364791569817010200, x *= x);
  assertEquals(0, x &= ((2074587775.983799)/(tmp = 438856632.76449287, tmp)));
  assertEquals(0, x &= (((1509671758)*(tmp = -935801537.7325008, tmp))>>>(((tmp = -1752877566, tmp)<<x)%(tmp = -517163766, tmp))));
  assertEquals(-2031730599, x ^= ((2264285273)&(tmp = -1762662949.014101, tmp)));
  assertEquals(-843578945, x %= (-1188151654));
  assertEquals(-2147483648, x <<= x);
  assertEquals(-2147483648, x >>= (tmp = -3165079200.229641, tmp));
  assertEquals(-44086313.1323726, x %= ((x%(-254466243.48728585))-((x>>(-457411829.1063688))-((-2606923436.9333453)/x))));
  assertEquals(-44086313, x |= x);
  assertEquals(1037812, x >>>= ((tmp = 342497258.9786743, tmp)+(1652928385.8150895)));
  assertEquals(-2371695599678100, x *= (tmp = -2285284425, tmp));
  assertEquals(-2371697387004653, x += (tmp = -1787326553.0542095, tmp));
  assertEquals(0, x ^= x);
  assertEquals(0, x >>= ((x^(tmp = 544039787, tmp))>>>x));
  assertEquals(0, x &= ((x%(((((((tmp = -424572417.1088555, tmp)|(-2381863189))/(tmp = -2007482475.1809125, tmp))&(((((tmp = 311016073, tmp)>>(tmp = -1548839845, tmp))+((-2557740399.7947464)<<(2399113209)))&x)>>>x))%(-297180308.7721617))-(tmp = 860906293, tmp))^x))%(-2740622304)));
  assertEquals(4971841192462909000, x += ((tmp = -2723203837.572612, tmp)+((((-2909100706)+(-951999374))|(-3116735764))*(3087123539.422669))));
  assertEquals(-460, x >>= (1081807537.557404));
  assertEquals(2354165127.3906384, x += (tmp = 2354165587.3906384, tmp));
  assertEquals(357.8680960002211, x /= ((((x<<(((x&x)+(1113841407))|((x/(tmp = 384533564, tmp))>>>(-605853882))))%x)&((tmp = 2050375842, tmp)>>>x))>>(((2745147573)^x)<<(x-(900043292)))));
  assertEquals(0, x *= (x>>>(-295974954.5058532)));
  assertEquals(0, x *= ((-2448592125.815531)*(tmp = -94957474.8986013, tmp)));
  assertEquals(0, x &= ((x>>x)^(tmp = -1335129180, tmp)));
  assertEquals(395092065, x |= ((3081659156)^(tmp = -1608334475, tmp)));
  assertEquals(395092065, x &= x);
  assertEquals(-413337639, x += (x^(tmp = -664996071.3641524, tmp)));
  assertEquals(-1604423637896759800, x *= (x>>>(tmp = 1242912352.955432, tmp)));
  assertEquals(0, x &= ((((((tmp = 651293313, tmp)|(((2541604468.635497)>>>(tmp = 758815817.7145422, tmp))>>>((-1948795647)/x)))&x)/((tmp = -3161497100, tmp)+(782910972.3648237)))>>>x)%(834206255.5560443)));
  assertEquals(0, x >>>= (tmp = 125945571, tmp));
  assertEquals(NaN, x -= (x%x));
  assertEquals(NaN, x %= (tmp = 282259853, tmp));
  assertEquals(NaN, x += (tmp = -2081332383, tmp));
  assertEquals(0, x >>>= (((x>>(-2298589097.7522116))|((((x>>>(x-(tmp = 755218194, tmp)))|x)%x)-(tmp = 2206031927, tmp)))>>>((((x&(x-x))^(tmp = 2836686653, tmp))*((x<<(tmp = -1624140906.4099245, tmp))>>>((2942895486)|((x>>>x)>>>(-1586571476)))))|((781668993)+(-1857786909)))));
  assertEquals(0, x &= (tmp = -708084218.9248881, tmp));
  assertEquals(0, x %= (1645913394.5625715));
  assertEquals(0, x <<= ((x^((tmp = 1185413900, tmp)*((-2441179733.997965)*(tmp = 2554099020.066989, tmp))))%((1704286567.29923)/x)));
  assertEquals(0, x += x);
  assertEquals(0, x *= x);
  assertEquals(0, x |= (x>>>(139138112.141927)));
  assertEquals(0, x >>>= (tmp = 2142326564, tmp));
  assertEquals(0, x |= x);
  assertEquals(-0, x /= ((((x+(2817799428))|x)%((1050079768)-(x>>>((1452893834.8981247)|((((tmp = -1737187310.889149, tmp)/(tmp = -362842139, tmp))%(1234225406))%(((x|x)*((-1055695643.739629)-((x-x)*(945954197.676585))))-(tmp = 786185315.346615, tmp)))))))<<(-173891691)));
  assertEquals(0, x &= (-2842855092.319309));
  assertEquals(0, x &= ((-3188403836.570895)/x));
  assertEquals(0, x *= (x+x));
  assertEquals(NaN, x /= (x>>>(((tmp = 391037497.68871593, tmp)/((192754032)*(1382659402.5745282)))/((((-2187364928)>>>x)>>(tmp = 2563448665.7594023, tmp))^(tmp = 1500866009.7632217, tmp)))));
  assertEquals(NaN, x /= ((tmp = -935036555.2500343, tmp)-(x/(((x&(x^(tmp = -3001352832.5034075, tmp)))^x)/((1122547613)>>x)))));
  assertEquals(0, x >>= (tmp = -2951766379.0809536, tmp));
  assertEquals(-632945188, x ^= (-632945188.7188203));
  assertEquals(-632945188, x %= ((((((tmp = -3181527314.82724, tmp)&(2280175415))>>(x^(x|x)))^(tmp = -524233678.52970886, tmp))*x)|((tmp = 1782882786, tmp)>>>(tmp = -592607219, tmp))));
  assertEquals(404189184, x <<= ((tmp = -2761472127, tmp)^(36616299.88780403)));
  assertEquals(872651572, x ^= (tmp = 739568436.6252247, tmp));
  assertEquals(13, x >>>= ((tmp = -1033843418.865577, tmp)%(x%(1247263629.0445533))));
  assertEquals(0, x >>>= x);
  assertEquals(0, x >>= (3189175317));
  assertEquals(0, x &= (((2391973519.6142406)^((-2950058736.191456)|(x*x)))>>(tmp = 343822384.294345, tmp)));
  assertEquals(0, x >>>= (tmp = -2306246544, tmp));
  assertEquals(-1572339598, x ^= ((tmp = 2991380083.337327, tmp)&(tmp = -1361507970, tmp)));
  assertEquals(649, x >>>= ((1961407923.4950056)>>(x-(-872821523.7513013))));
  assertEquals(649, x ^= (((x&(tmp = -702931788, tmp))^(((x>>x)|(((tmp = 2710759269, tmp)/(x>>(x*((((((tmp = -2428445134.9555864, tmp)+(-1859938743))%(x<<x))*((236868604)+((tmp = -3066688385, tmp)/(787503572.8839133))))/(tmp = 3215629315, tmp))>>(-1315823020)))))%(1461368627.1293125)))>>>(tmp = -2921804417.5735087, tmp)))/(x>>>(((tmp = 2175260691.824617, tmp)/((-582958935.7628009)-((((((x>>x)|(2590503723.4810824))^(tmp = -1994324549, tmp))-(-684683327))/(tmp = -3133419531, tmp))|(tmp = -328974092.05095506, tmp))))>>(-447624639.4518213)))));
  assertEquals(649, x %= ((((1854382717)|(((x+(tmp = 2568081234, tmp))-x)+((tmp = 1043086140, tmp)<<((tmp = 2979118595.0496006, tmp)+((x&(2669577199.852803))/(-2567808445.101112))))))<<((((tmp = -1471092047, tmp)&((-3099138855.21041)-((tmp = -798574377.526715, tmp)&((2255586141)<<(-1069867774)))))>>>(((x*(tmp = -2810255707.781517, tmp))/x)*(2706435744.054121)))^(394262253)))^((844325548.0612085)/(tmp = 1434691648, tmp))));
  assertEquals(823215943.1924392, x += (tmp = 823215294.1924392, tmp));
  assertEquals(536872706, x &= ((-334612686)%((1303605874)|x)));
  assertEquals(-30666374.413486242, x += ((tmp = -567539080.4134862, tmp)%(tmp = -1655555936.3195171, tmp)));
  assertEquals(-56438727096752984, x *= (tmp = 1840410814, tmp));
  assertEquals(-33200107.984488487, x %= (((tmp = 3007206509, tmp)-(3079337725.6659536))%(1819565202.5011497)));
  assertEquals(-1214493182, x ^= (-3060193769));
  assertEquals(-1214493179.1335113, x -= ((-3218099496.595745)/(1122662554)));
  assertEquals(-1214493179, x >>= ((-375364195)<<(((tmp = 619439637.8754326, tmp)>>(-1830023279.9486575))&(tmp = -1106180387.2448823, tmp))));
  assertEquals(-303623295, x >>= (-2109241374.3349872));
  assertEquals(-0, x %= x);
  assertEquals(0, x |= x);
  assertEquals(1917126206, x -= (-1917126206));
  assertEquals(2659779928, x -= (tmp = -742653722, tmp));
  assertEquals(-1635187368, x >>= ((tmp = -674385169, tmp)*((9848362.783326745)|(x*(55220544.00989556)))));
  assertEquals(-1981113695, x ^= ((tmp = 392404985, tmp)>>(((x<<((2006207061)<<(tmp = 2558988218, tmp)))*((((tmp = 1789304307.1153054, tmp)/(2538061546))<<(tmp = 556026116, tmp))&((tmp = 1076457999.6424632, tmp)*(tmp = -1822378633.2489474, tmp))))%(((((-1117046924)&((-69013651)%(x&(((-2320327696)/(x&x))-(tmp = 2458222544, tmp)))))>>((-3092360983.0037227)/(-3171415636)))*(((tmp = 2520431213, tmp)<<(1066492762.6149663))+((tmp = 1272200889, tmp)^((1687693123.2295754)+x))))-(-1096823395)))));
  assertEquals(-990556848, x >>= x);
  assertEquals(981202869119695100, x *= x);
  assertEquals(981202869119695100, x -= (x/x));
  assertEquals(0, x ^= (x>>x));
  assertEquals(NaN, x %= x);
  assertEquals(0, x ^= x);
  assertEquals(0, x *= ((((2980512718)>>>x)<<((x^(-1111233869))>>((2531466092.6036797)>>>(((tmp = -1791229364, tmp)*(-2210950307.206208))%((tmp = -806645443, tmp)<<((((((((tmp = 112334634.26187229, tmp)%(x|((((2154021796.1166573)+x)&((-1047293079.9686966)^(tmp = -1894127139, tmp)))+(tmp = 1910946653.2314827, tmp))))^(293142672.5016146))-x)<<(-1593533039.8718698))+x)>>(x<<(((46359706.50393462)&(tmp = 272146661, tmp))|(tmp = 2117690168, tmp))))%(tmp = -1784737092.4924843, tmp)))))))-(1465796246)));
  assertEquals(0, x &= x);
  assertEquals(NaN, x %= x);
  assertEquals(0, x &= (x+(-1612418456)));
  assertEquals(0, x &= ((tmp = -843964311, tmp)/x));
  assertEquals(NaN, x /= x);
  assertEquals(NaN, x *= x);
  assertEquals(NaN, x += (x>>>(54020240)));
  assertEquals(489206868, x |= (489206868));
  assertEquals(489206868, x &= x);
  assertEquals(489206848, x &= ((tmp = -1699133906.2361684, tmp)>>(tmp = 2658633814, tmp)));
  assertEquals(489206848, x |= x);
  assertEquals(1910559006, x -= (tmp = -1421352158, tmp));
  assertEquals(1, x >>= x);
  assertEquals(0, x -= x);
  assertEquals(0, x %= (x^(tmp = 2745376003.2927403, tmp)));
  assertEquals(0, x %= (((tmp = 3199743302.1063356, tmp)^((-1905944176)&(x>>>(187247029.5209098))))<<((x*((-1394648387)*(1252234289)))-(3140049815))));
  assertEquals(0, x <<= (-2567872355));
  assertEquals(0, x %= (tmp = 1057707555.8604916, tmp));
  assertEquals(0, x %= ((tmp = -1877857405.0228279, tmp)>>>(((tmp = 423831184, tmp)*((tmp = -2106757468.324615, tmp)%(tmp = -1197717524.6540637, tmp)))>>(tmp = -93746263.46774769, tmp))));
  assertEquals(0, x |= x);
  assertEquals(-0, x *= ((tmp = 1317609776.6323466, tmp)*(tmp = -26959885.89325118, tmp)));
  assertEquals(0, x >>= (-1288116122.0091262));
  assertEquals(0, x &= ((370818172.92511404)%((tmp = -528319853.54781747, tmp)*(x/((tmp = -2839758076, tmp)^(x+(((-1258213460.041857)<<(tmp = 302017800.72064054, tmp))|((((tmp = -624254210, tmp)^((-338165065.97507)|((623392964)-x)))>>>x)%(tmp = 2767629843.0643625, tmp)))))))));
  assertEquals(0, x >>>= x);
  assertEquals(0, x >>>= x);
  assertEquals(0, x |= ((-2001549164.1988192)*x));
  assertEquals(0, x -= x);
  assertEquals(0, x *= (((((165836842.14390492)*(tmp = -3220002961, tmp))|(-2840620221.747431))%((x/(tmp = 3153915610, tmp))>>>(tmp = 2018941558, tmp)))>>>x));
  assertEquals(-0, x *= (-231994402.93764925));
  assertEquals(0, x <<= x);
  assertEquals(0, x %= (tmp = 2702385056.1149964, tmp));
  assertEquals(0, x <<= (tmp = 378459323, tmp));
  assertEquals(0, x >>>= ((x&(x&(((-1014963013)<<(x&((tmp = -3110294840, tmp)|(x+(x<<(1129643420))))))+(1093795819.1853619))))+((((tmp = -2295103369.697398, tmp)&(((370501313.43019223)>>>(2465439579))/x))-x)>>x)));
  assertEquals(0, x /= ((tmp = 1779625847, tmp)+(tmp = -662459654.6908865, tmp)));
  assertEquals(0, x -= x);
  assertEquals(0, x %= ((tmp = 2723291421, tmp)|(277246502.4027958)));
  assertEquals(0, x ^= (((-2936270162)>>>((((tmp = -2019015609.1648235, tmp)|(47218153))*(-823685284))+x))&(x<<(x*(x|(((tmp = -941955398, tmp)^(tmp = -2365238993.5300865, tmp))-(778674685)))))));
  assertEquals(0, x >>>= x);
  assertEquals(NaN, x %= x);
  assertEquals(0, x &= (-175235975.8858137));
  assertEquals(-2684493800.1062117, x += (tmp = -2684493800.1062117, tmp));
  assertEquals(-1290806265.6063132, x -= (-1393687534.4998984));
  assertEquals(-1290806265, x >>= (((x>>(tmp = -1710112056.4935386, tmp))*(586227650.2860553))<<(tmp = -2918251533.6052856, tmp)));
  assertEquals(23470008, x >>>= x);
  assertEquals(1668734969, x |= ((-295560682.9663689)^(x|((((tmp = -1183847364, tmp)&(3135327694))+(1679127747.1406744))-((-1895825528)%((tmp = -3180115006, tmp)+((tmp = 2373812187, tmp)|x)))))));
  assertEquals(1744306169, x |= (1188503928.5009093));
  assertEquals(1744306169, x %= (tmp = -2723982401.4997177, tmp));
  assertEquals(3488612338, x += x);
  assertEquals(3488612337, x += (((x/(-325849204))>>x)|(-1820624550.9149108)));
  assertEquals(-1511119305, x ^= (tmp = 1778506182.2952862, tmp));
  assertEquals(-12211415, x %= (x^(tmp = -54943035, tmp)));
  assertEquals(-12211415, x %= ((-1267051884)%(-643566443.0122576)));
  assertEquals(-30.84976063258681, x /= (((1052047194)>>>x)&(1495698235.5117269)));
  assertEquals(-61.69952126517362, x += x);
  assertEquals(-244, x <<= (x^(x+(tmp = -2822258210.076373, tmp))));
  assertEquals(-6652, x &= ((tmp = 2593685093, tmp)>>((((2047688852.4609032)<<((x*(-611076291))*x))^(-2665364024.817528))>>>(165267874))));
  assertEquals(0, x -= x);
  assertEquals(0, x /= (2454186758));
  assertEquals(0, x &= (tmp = -2226895206, tmp));
  assertEquals(0, x += x);
  assertEquals(-21390701, x += ((-1369004846.0816503)>>(tmp = -2661552634.039692, tmp)));
  assertEquals(-0.012568536912921919, x /= (1701924507.856429));
  assertEquals(7.09517966608176e-11, x /= (tmp = -177141911.8955555, tmp));
  assertEquals(0, x >>= (tmp = 231535697, tmp));
  assertEquals(1383687797, x ^= (tmp = -2911279499.568808, tmp));
  assertEquals(1383687797, x %= (tmp = -2258636646.5294995, tmp));
  assertEquals(1319, x >>= ((tmp = -2549411892.8426056, tmp)/(((((1532476676)^(153720871.82640445))+x)/(((2988190456.3206205)&(tmp = -2920873674, tmp))-(((((tmp = -1044518167.0581458, tmp)>>x)-((((tmp = -194701879.13505793, tmp)&(498352051))&((tmp = -2167339635.6529818, tmp)^(((x>>(tmp = 700159851, tmp))*(tmp = 2874921158, tmp))/x)))-((2856128689)|((-1876321441)>>>(2110732915)))))^((((tmp = -193379494.18825436, tmp)/(-3055182489.533142))<<x)+((tmp = -2286109605, tmp)>>(tmp = 698475484.3987849, tmp))))^(3182231653.500364))))|(((tmp = -194670835, tmp)>>>((786780139)%(((2114171416.2305853)^(1703145352.8143656))/x)))>>>((tmp = -3029462067, tmp)>>((67647572.02624655)&(x*(-2394283060))))))));
  assertEquals(13903855, x |= ((tmp = -2515306586, tmp)>>>x));
  assertEquals(54311, x >>>= ((-2413722658)-((tmp = -2159787584, tmp)^(tmp = 949937622.9744623, tmp))));
  assertEquals(108622, x += x);
  assertEquals(1250717187, x ^= ((tmp = 842692148, tmp)+(((2649331689.694273)<<x)-(tmp = -2992181273, tmp))));
  assertEquals(4536777, x %= (tmp = 73304730, tmp));
  assertEquals(0, x -= x);
  assertEquals(-580081499, x ^= ((tmp = -580081499.0170684, tmp)^(x%(tmp = -1542730817.88261, tmp))));
  assertEquals(-1382738784, x <<= x);
  assertEquals(-1382738784, x <<= x);
  assertEquals(2912228512, x >>>= (x*(x>>>x)));
  assertEquals(-1076374105, x |= (2589443367));
  assertEquals(-0.2818750938197037, x /= (((tmp = -1559525732.9603848, tmp)|(-477068917.5483327))>>>((-688616257)*((((tmp = -1192490153.1226473, tmp)*(-502280624.0265591))<<(-442688727.4881985))%(x+(((((tmp = -2948836853.831935, tmp)-(tmp = -2850398330.910424, tmp))>>>(x>>>(-1947835558)))^x)+(x*x)))))));
  assertEquals(2032826546, x |= (tmp = 2032826546.819327, tmp));
  assertEquals(3408404827.14316, x += (tmp = 1375578281.1431599, tmp));
  assertEquals(258183922.14315987, x %= (tmp = 350024545, tmp));
  assertEquals(479694848, x <<= (tmp = -481187157, tmp));
  assertEquals(-2147483648, x <<= (((tmp = -2956588045.472398, tmp)>>>(((tmp = -1838455399.1775856, tmp)&(((((tmp = -637547, tmp)/x)&(x^((-44876328.1767962)+(((-2059598286)-(1071496688))%(tmp = -1492254402, tmp)))))-(x%x))*(x|x)))>>(1226250760)))<<x));
  assertEquals(-2288163338.9020815, x -= (140679690.9020816));
  assertEquals(4954833118513997000, x *= (-2165419327.4906025));
  assertEquals(1578331238, x ^= (-2410854298.2270393));
  assertEquals(-810627292, x += (-2388958530));
  assertEquals(-810627292, x ^= ((1495296640.4087524)/(tmp = 1561790291, tmp)));
  assertEquals(657116606535253200, x *= x);
  assertEquals(0.675840332689047, x %= (((-1816548473)^(((tmp = -151918689.19451094, tmp)|(1819911186.535233))/((((((1514297447)+(tmp = 856485190.9684253, tmp))&(((1809369464.4363992)<<(493538496))*x))+((x*(x>>(x&(tmp = 222293461, tmp))))>>>(((784519621)|x)^((-580766922)>>(tmp = -947264116, tmp)))))>>>((((2794210354.22964)>>>(((2896952532.0183973)*((x+(tmp = -1813175940, tmp))<<(tmp = -1302618293, tmp)))&x))>>(x-(((x|((1456466890.1952953)*x))^(-169979758.19158387))-(x-x))))>>x))&(tmp = 2671604078.3026733, tmp))))/(-1701675745)));
  assertEquals(0.675840332689047, x %= ((tmp = 2421871143, tmp)^x));
  assertEquals(NaN, x %= ((((tmp = 1175526323.433271, tmp)+(tmp = 2813009575.952405, tmp))%((tmp = -3112133516.3303423, tmp)&x))&((((((-424329392)^(tmp = 1430146361, tmp))+x)-(1533557337.268306))%((tmp = -3117619446, tmp)-(-3127129232)))>>>x)));
  assertEquals(NaN, x += x);
  assertEquals(0, x >>>= ((1710641057.7325037)%(104961723.56541145)));
  assertEquals(0, x <<= (tmp = -970072906, tmp));
  assertEquals(0, x *= (87768668));
  assertEquals(-1464968122, x ^= (tmp = -1464968122, tmp));
  assertEquals(-1467983895, x ^= ((tmp = -1204896021, tmp)>>>(((91792661)&(x>>>(((-2364345606)>>>x)*x)))+x)));
  assertEquals(2.991581508270506, x /= (-490704963.5591147));
  assertEquals(0, x >>>= x);
  assertEquals(0, x >>= ((tmp = 639854873, tmp)%(tmp = 743486160.3597239, tmp)));
  assertEquals(0, x <<= (tmp = 1045577245.3403939, tmp));
  assertEquals(0, x >>= ((tmp = -1932462290, tmp)|(tmp = 1629217987, tmp)));
  assertEquals(517617438, x ^= ((tmp = 2737789043, tmp)%(tmp = -2220171604.135681, tmp)));
  assertEquals(126371, x >>>= ((tmp = 205210223.69909227, tmp)-(tmp = 598118404, tmp)));
  assertEquals(918548455, x |= ((918228734.8363427)+(x+x)));
  assertEquals(918548455, x |= ((tmp = 599828198, tmp)>>((tmp = -851081330, tmp)|(tmp = -1152596996.8443217, tmp))));
  assertEquals(918548443.7739062, x -= ((tmp = 1497642976.2260938, tmp)%(x>>(tmp = -548469702.5849569, tmp))));
  assertEquals(0.7739062309265137, x %= (x&x));
  assertEquals(2317939163.8239403, x *= (tmp = 2995116296, tmp));
  assertEquals(1014415360, x <<= (-279972114));
  assertEquals(0, x &= ((296810932)/(x*(tmp = -2750499950, tmp))));
  assertEquals(0, x *= (x%((126285451.05086231)>>>(x*(tmp = -2789790532, tmp)))));
  assertEquals(0, x >>>= ((975695102.5771483)%(x-((-1011726540)-((tmp = 2223194882, tmp)/x)))));
  assertEquals(-1747794584, x |= (-1747794584.3839395));
  assertEquals(-543544679, x %= (tmp = -1204249905, tmp));
  assertEquals(-543544679, x %= (-881024001));
  assertEquals(1, x /= x);
  assertEquals(-1879376393, x |= ((tmp = 161643764, tmp)|(tmp = 2281346499.9084272, tmp)));
  assertEquals(1.321124264431369, x /= (-1422558379.7061746));
  assertEquals(1, x >>>= (x&(tmp = -963118950.4710281, tmp)));
  assertEquals(3, x ^= ((x+x)/x));
  assertEquals(1, x /= x);
  assertEquals(1, x &= (2090796073));
  assertEquals(-1284301873, x ^= (((-11041168.146357536)+(tmp = -1273260707.8134556, tmp))+x));
  assertEquals(292559045, x &= (x&((-2401110739)^((tmp = 630802904, tmp)^(((1012634447.0346229)+x)%((tmp = -1240091095, tmp)%(x/(-1483936527))))))));
  assertEquals(0, x %= x);
  assertEquals(0, x /= (tmp = 613145428.3653506, tmp));
  assertEquals(0, x /= ((x-(tmp = 3116638456, tmp))*(-973300716)));
  assertEquals(0, x %= (tmp = -1794741286.0464535, tmp));
  assertEquals(0, x &= x);
  assertEquals(0, x >>= (-551370105.0746605));
  assertEquals(-1471996874, x ^= ((2822970422.2331414)-x));
  assertEquals(-277914313, x |= (tmp = -818980601.2544096, tmp));
  assertEquals(-34, x >>= x);
  assertEquals(305422768, x -= (-305422802));
  assertEquals(-2406146240, x += (tmp = -2711569008, tmp));
  assertEquals(1073745408, x &= (tmp = -3046625618, tmp));
  assertEquals(1073745408, x <<= ((-1234108306.7646303)<<((-233519302)|x)));
  assertEquals(1073745408, x %= (tmp = 1898831268, tmp));
  assertEquals(1073745408, x <<= (((tmp = 3089406038, tmp)/x)&(-2960027680)));
  assertEquals(65536, x >>>= (2858188366));
  assertEquals(128, x >>>= ((-2640257239.857275)%((tmp = -3185405235.3177376, tmp)*x)));
  assertEquals(128, x >>>= x);
  assertEquals(128, x -= (x&(x-(tmp = -247588018, tmp))));
  assertEquals(81616906825.07776, x *= (tmp = 637632084.57092, tmp));
  assertEquals(78860097686.07776, x -= (((1507215684)^((709254783)+(((x<<x)*((-2890828152.667641)%(2537817529.2041526)))^x)))+(3114024487)));
  assertEquals(-2920545695.721283, x += (((tmp = -2555437435, tmp)>>>x)-((2920546109.72129)+x)));
  assertEquals(-2879412281.721283, x += ((-1662428756)>>>(tmp = -1928491386.6926208, tmp)));
  assertEquals(67403845, x &= (tmp = 2921644117, tmp));
  assertEquals(16850961, x >>>= (((-1039328365)>>>(tmp = -768615112, tmp))<<((1037261855)*(tmp = -2906902831.4797926, tmp))));
  assertEquals(0, x ^= x);
  assertEquals(0, x *= ((-2729056530)/((-1776175111)%(1493002300.4604707))));
  assertEquals(0, x *= (tmp = 370696035.22912216, tmp));
  assertEquals(0, x ^= x);
  assertEquals(0, x |= ((((((tmp = -1541196993, tmp)^x)/(854730380.1799632))/(2879117705.492209))+((((-2892068577)^(-2460614446.1044483))>>>((743413943)<<(-1285280084.4220598)))/(tmp = -1719994579.5141463, tmp)))%(((((tmp = 2522797851.088227, tmp)<<(tmp = 2257160597.1538725, tmp))/(-680406007))&((x>>>(tmp = -260350730, tmp))^(tmp = 1920522110.852598, tmp)))>>(-697620442))));
  assertEquals(0, x &= x);
  assertEquals(-591399642.958673, x += (x-(tmp = 591399642.958673, tmp)));
  assertEquals(27, x >>>= (tmp = -726721317.2109983, tmp));
  assertEquals(-2043736843, x -= (2043736870));
  assertEquals(-3991674, x >>= (tmp = 1098126089, tmp));
  assertEquals(-997919, x >>= ((x%(((x*(((-1497329257.1781685)%(2334511329.2690516))/(-3072526140.6635056)))+(-1843998852))-(tmp = 240300314.34070587, tmp)))+(714080860.6032693)));
  assertEquals(-0, x %= x);
  assertEquals(NaN, x /= x);
  assertEquals(0, x >>= (tmp = 538348328.5363884, tmp));
  assertEquals(0, x *= (800317515));
  assertEquals(0, x -= x);
  assertEquals(0, x >>= (984205514));
  assertEquals(857282491, x += (tmp = 857282491, tmp));
  assertEquals(587792897, x &= (tmp = 2951307845.164059, tmp));
  assertEquals(595301269, x |= (tmp = 24285588.90314555, tmp));
  assertEquals(1190602538, x += x);
  assertEquals(0, x -= x);
  assertEquals(-442423060, x |= ((x^((x-(tmp = 2342497475.637024, tmp))%(-1900074414.7678084)))|((tmp = 1932380130, tmp)%(x%(2291727569.817062)))));
  assertEquals(-442423060, x %= (((tmp = 703479475.545413, tmp)>>(x-x))<<(2435723056.753845)));
  assertEquals(1, x /= x);
  assertEquals(0, x >>= x);
  assertEquals(-1265317851, x |= (tmp = -1265317851, tmp));
  assertEquals(-2, x >>= (-2015895906.8256726));
  assertEquals(-0, x %= x);
  assertEquals(-0, x %= (((1219237746)+(284683029))*(((tmp = 2288119628, tmp)|(-404658161.2563329))*(-265228691.74142504))));
  assertEquals(1039509109, x -= (-1039509109));
  assertEquals(2079018218, x += x);
  assertEquals(-1979.9362673719077, x /= ((3219723500)>>x));
  assertEquals(-62, x >>= ((x/(326466691))*(tmp = -607654070, tmp)));
  assertEquals(-45, x |= (tmp = -2954888429.549882, tmp));
  assertEquals(-1180929712, x &= (3114037588.570232));
  assertEquals(815550480, x &= (-2302684143.3378315));
  assertEquals(815550480, x %= (-2177479570));
  assertEquals(815550480, x %= (tmp = 2895822167, tmp));
  assertEquals(815550480, x %= (-1247621230.5438688));
  assertEquals(283929811, x -= ((tmp = 251831053.17096448, tmp)|((tmp = 1140463506.004994, tmp)+(tmp = -743224673.546309, tmp))));
  assertEquals(1825767424, x <<= (((tmp = 1732353599, tmp)^(tmp = 658726044, tmp))>>>((-2827889370.932477)%(tmp = 1950139204.3291233, tmp))));
  assertEquals(1828450414, x |= (tmp = 1618538606, tmp));
  assertEquals(0, x <<= (-2411670689.045702));
  assertEquals(0, x <<= (-27744888.428537607));
  assertEquals(-0, x /= (tmp = -1597552450, tmp));
  assertEquals(0, x >>>= (((2165722776.7220936)>>>(tmp = 1233069931, tmp))>>>(-1120420811)));
  assertEquals(-0, x *= ((tmp = -1505252656, tmp)>>((((3035637099.6156535)&((467761577.7669761)>>(-361034537)))^(tmp = -2347994840.6541123, tmp))*(tmp = -2191739821, tmp))));
  assertEquals(0, x &= (795727404.0738752));
  assertEquals(-0, x *= (tmp = -3125944685.3991394, tmp));
  assertEquals(-0, x *= (x&x));
  assertEquals(0, x >>= ((tmp = -2045709233, tmp)^x));
  assertEquals(NaN, x /= (x>>(x/(3102894071))));
  assertEquals(NaN, x += ((tmp = 2149079756.8941655, tmp)-(tmp = 810121645.305179, tmp)));
  assertEquals(0, x >>>= (-859842989));
  assertEquals(0, x >>>= (tmp = 2530531143.9369526, tmp));
  assertEquals(0, x >>= (((-932981419.6254237)|(tmp = 1591591715, tmp))>>>(x+((3149795006)>>>(tmp = 613352154, tmp)))));
  assertEquals(-4294967295, x -= ((((-2289331668)%(-282648480.0078714))>>(-1373720705.5142756))>>>((tmp = 15511563.517014384, tmp)/(360279080))));
  assertEquals(1, x &= x);
  assertEquals(0, x >>= (x^(-2791872557.5190563)));
  assertEquals(0, x &= ((tmp = 336466956.7847167, tmp)>>((1235728252.053619)|(x<<((1828176636.13488)%x)))));
  assertEquals(-0, x *= (-364042830.8894656));
  assertEquals(0, x >>>= x);
  assertEquals(-1675298680, x |= ((2323049541.321387)+(296619075)));
  assertEquals(-0, x %= x);
  assertEquals(-1583048579.4420977, x += (-1583048579.4420977));
  assertEquals(0, x -= x);
  assertEquals(-2, x ^= ((603171992.0545617)/(((-271888695.718297)%(tmp = -400159585, tmp))^((((tmp = 1536123971, tmp)-(tmp = -2310418666.6243773, tmp))|((tmp = 2242779597.1219435, tmp)<<(tmp = 1758127684.4745512, tmp)))/x))));
  assertEquals(-2, x &= (x&x));
  assertEquals(0, x &= ((tmp = -1098806007.4049063, tmp)/(((2862384059.3229523)/((((tmp = -92960842, tmp)-(x>>(tmp = 1244068344.2269042, tmp)))&x)*(tmp = -1919148313, tmp)))<<(-2486665929))));
  assertEquals(0, x &= x);
  assertEquals(-1441272634.582818, x -= (1441272634.582818));
  assertEquals(-3, x >>= (tmp = 3186393693.7727594, tmp));
  assertEquals(-1206855850, x ^= (((tmp = 607979495.303539, tmp)-(tmp = -2480131951, tmp))^(x*((tmp = 1324153477, tmp)/((1248126288)+(x|(1917331780.0741704)))))));
  assertEquals(-1206855853, x ^= (x>>>(653288765.1749961)));
  assertEquals(-1206857725, x &= (3149461539.6019173));
  assertEquals(3088109571, x >>>= (x*(x<<(tmp = 1543540084, tmp))));
  assertEquals(536903680, x &= (tmp = 644851760, tmp));
  assertEquals(536903674.312194, x += (((-3183290076)-((tmp = 40738191.12097299, tmp)-x))/((x>>>(3151371851.9408646))^(tmp = 472698205.22445416, tmp))));
  assertEquals(2127424750.0506563, x -= (tmp = -1590521075.7384624, tmp));
  assertEquals(2127424750.0506563, x %= (tmp = 3027273433.361373, tmp));
  assertEquals(0, x >>= (x>>(1445204441.702043)));
  assertEquals(NaN, x %= (x<<x));
  assertEquals(0, x ^= ((tmp = -2903841152.136344, tmp)-(x%(2938662860))));
  assertEquals(0, x <<= (x<<x));
  assertEquals(0, x >>>= (tmp = -979481631.33442, tmp));
  assertEquals(0, x >>= x);
  assertEquals(0, x &= (((x%((((((tmp = 1657446354.6820035, tmp)>>(-1916527001.2992697))/x)>>(tmp = 1450467955, tmp))&(277676820))+(x/(-945587805))))/((tmp = -690095354, tmp)^x))+(tmp = -2651195021, tmp)));
  assertEquals(0, x <<= (752343428.2934296));
  assertEquals(0, x /= (tmp = 3022310299, tmp));
  assertEquals(0, x >>= (x%((388245402)>>>x)));
  assertEquals(NaN, x %= x);
  assertEquals(NaN, x %= ((tmp = 1205123529.8649468, tmp)>>>(-2848300932)));
  assertEquals(0, x >>= ((x>>>x)<<(tmp = 487841938, tmp)));
  assertEquals(0, x *= (((273436000.9463471)|(tmp = 141134074.27978027, tmp))^(tmp = 1220326800.7885802, tmp)));
  assertEquals(1525600768, x |= (((x^(-2674777396))-(tmp = 1966360716.3434916, tmp))<<(794782595.9340223)));
  assertEquals(761927595, x %= (tmp = -763673173, tmp));
  assertEquals(1.1353588586934338, x /= ((x&((-1897159300.4789193)*(-348338328.0939896)))&(978680905.6470605)));
  assertEquals(8.631173314966319e-10, x /= (1315416592));
  assertEquals(0, x >>= ((tmp = -2581239435, tmp)-((-628818404.1122074)<<x)));
  assertEquals(0, x -= x);
  assertEquals(0, x *= (2925158236));
  assertEquals(0, x /= (x+(tmp = 1405531594.0181243, tmp)));
  assertEquals(0, x *= (2712022631.230831));
  assertEquals(0, x >>= (tmp = 80518779.81608999, tmp));
  assertEquals(1953477932.8046472, x += (tmp = 1953477932.8046472, tmp));
  assertEquals(1953477932, x >>= (tmp = 3025539936, tmp));
  assertEquals(1953477932, x -= ((-2675119685.8812313)>>(x/(-1808264410.9754841))));
  assertEquals(1292620430, x += ((-660857502)%((((tmp = -698782819, tmp)%(tmp = 2847304199, tmp))<<(-2423443217.1315413))+x)));
  assertEquals(78895, x >>>= x);
  assertEquals(2, x >>= x);
  assertEquals(2, x <<= (tmp = 1313641888.8301702, tmp));
  assertEquals(1857416935.2532766, x += (tmp = 1857416933.2532766, tmp));
  assertEquals(-1677721600, x <<= (tmp = -2482476902, tmp));
  assertEquals(309226853.62854385, x -= (tmp = -1986948453.6285439, tmp));
  assertEquals(33965156, x &= (2409088742));
  assertEquals(Infinity, x /= (x-(x<<((x/(tmp = -3106546671.536726, tmp))/((tmp = 2695710176, tmp)-((((-2102442864)&(857636911.7079853))/x)%(-65640292)))))));
  assertEquals(1270005091, x |= (tmp = 1270005091.0081215, tmp));
  assertEquals(1270005091, x %= (tmp = -1833876598.2761571, tmp));
  assertEquals(158750636, x >>>= x);
  assertEquals(-1000809106.0879555, x -= (tmp = 1159559742.0879555, tmp));
  assertEquals(72400936, x &= ((2448271389.3097963)%(tmp = 1517733861, tmp)));
  assertEquals(282816, x >>= x);
  assertEquals(282816, x %= (tmp = 3192677386, tmp));
  assertEquals(0.00021521351827207216, x /= (1314118194.2040696));
  assertEquals(Infinity, x /= (((tmp = 2822091386.1977024, tmp)&x)%(tmp = -3155658210, tmp)));
  assertEquals(NaN, x %= (-359319199));
  assertEquals(0, x >>>= (((tmp = -2651558483, tmp)-(x<<(tmp = 2537675226.941645, tmp)))<<(tmp = 667468049.0240343, tmp)));
  assertEquals(-0, x *= (tmp = -2827980482.12998, tmp));
  assertEquals(-0, x %= (((tmp = -689972329.3533998, tmp)>>>x)|(tmp = -7488144, tmp)));
  assertEquals(0, x >>>= x);
  assertEquals(0, x |= x);
  assertEquals(-2410373675.2262926, x -= (2410373675.2262926));
  assertEquals(1840423, x >>= ((-1081642113)^x));
  assertEquals(-4829451429403412, x *= (-2624098606.35485));
  assertEquals(-94552231, x %= (tmp = -97015883, tmp));
  assertEquals(-94433287, x ^= (((tmp = -2297735280, tmp)&(((tmp = 2261074987.7072973, tmp)%((((2565078998)^(-2573247878))|x)|(((tmp = -2120919004.7239416, tmp)>>(tmp = -579224101, tmp))>>>(1905808441))))*(x|(3149383322))))>>(542664972)));
  assertEquals(0, x ^= (x<<(tmp = -3112569312, tmp)));
  assertEquals(0, x <<= (-2141934818.7052917));
  assertEquals(0, x >>= (tmp = -2539525922, tmp));
  assertEquals(-434467613, x ^= (tmp = -434467613, tmp));
  assertEquals(-274792709, x |= (1233452601.462551));
  assertEquals(-274726917, x |= (-2130333750));
  assertEquals(-272629761, x |= (-1516071602.5622227));
  assertEquals(-272629761, x |= ((tmp = 3012131694, tmp)&((tmp = -2595342375.8674774, tmp)-((tmp = -2710765792, tmp)>>>((x-(tmp = 2397845540, tmp))+(2496667307))))));
  assertEquals(-4194305, x |= (1343705633.165825));
  assertEquals(4190207, x >>>= ((tmp = 276587830, tmp)*((tmp = -1517753936, tmp)>>x)));
  assertEquals(0, x >>= (x|((2247486919)-((-1664642412.4710495)*((((tmp = -358185292.17083216, tmp)-(tmp = -1472193444, tmp))*(tmp = 2699733752, tmp))&((x|(x<<(1137610148.1318119)))>>(((375089690.8764564)*x)&(tmp = 859788933.9560187, tmp))))))));
  assertEquals(0, x %= (3080673960));
  assertEquals(0, x >>>= (1328846190.1963305));
  assertEquals(1249447579, x |= (-3045519717.580775));
  assertEquals(-0.8743931060971377, x /= (-1428931187));
  assertEquals(1, x |= ((tmp = -1756877535.7557893, tmp)/((-142900015.93200803)<<(1414557031.347334))));
  assertEquals(759627265, x ^= (759627264.0514802));
  assertEquals(741823, x >>= (1106391210));
  assertEquals(610451, x &= ((x>>>((919849416)+((tmp = -427708986, tmp)^((x%x)|(tmp = -2853100288.932063, tmp)))))*x));
  assertEquals(372650423401, x *= x);
  assertEquals(410404493, x >>>= ((((-1425086765)>>>x)>>((2813118707.914771)>>(-424850240)))^x));
  assertEquals(120511585729013, x *= ((tmp = -1889454669, tmp)>>>x));
  assertEquals(120513295294304.22, x -= (tmp = -1709565291.2115698, tmp));
  assertEquals(6164, x >>>= ((2244715719.397763)^(tmp = -741235818.6903033, tmp)));
  assertEquals(937572790.468221, x -= (tmp = -937566626.468221, tmp));
  assertEquals(937572790, x |= ((2129102867.156146)*(x%x)));
  assertEquals(32, x &= ((2700124055.3712993)>>>((1977241506)>>>(-2915605511))));
  assertEquals(32, x %= (tmp = -2513825862, tmp));
  assertEquals(0, x <<= (-1379604802));
  assertEquals(0, x >>>= (tmp = -1033248759, tmp));
  assertEquals(-1151517050, x ^= (3143450246));
  assertEquals(-180577, x |= ((738373819.4081701)^(-357134176)));
  assertEquals(-0, x %= x);
  assertEquals(-2086887759, x |= (tmp = 2208079537, tmp));
  assertEquals(-2, x >>= (1460216478.7305799));
  assertEquals(-2, x %= ((-1979700249.0593133)^(-3156454032.4790583)));
  assertEquals(-256, x <<= ((1810316926)>>>(tmp = 414362256, tmp)));
  assertEquals(-1, x >>= (((((((-1616428585.595561)*((tmp = 2574896242.9045777, tmp)|(86659152.37838173)))>>(((tmp = 2476869361, tmp)&((x+((tmp = -2445847462.1974697, tmp)>>(tmp = -1960643509.5255682, tmp)))+(x|(((((2231574372.778028)|(tmp = 1824767560, tmp))>>>((1108035230.2692142)|(tmp = 2354035815, tmp)))/((tmp = -2602922032, tmp)>>(-925080304.7681987)))-x))))-(x>>x)))>>>((tmp = 751425805.8402164, tmp)|(tmp = 1165240270.3437088, tmp)))-x)*(2870745939))-(x>>>((tmp = 2986532631.405425, tmp)>>>(((tmp = 2547448699, tmp)+(((((x<<(((((-2756908638.4197435)>>>(3134770084))-(-1147872642.3756688))%(x*(tmp = -282198341.6600039, tmp)))+(-770969864.2055655)))+((-2725270341)^x))/(-3093925722))>>(x&x))>>((tmp = -2705768192, tmp)>>>(((tmp = 577253091.6042917, tmp)/(((x&(((((x+x)>>>(-1000588972))/(x&(717414336)))^(tmp = 428782104.21504414, tmp))>>>(1084724288.953223)))%(tmp = -2130932217.4562194, tmp))&x))-(-286367389)))))+((x>>(tmp = 2001277117, tmp))>>((tmp = 1028512592, tmp)^((tmp = 2055148650, tmp)+((tmp = 1490798399, tmp)/(tmp = -2077566434.2678986, tmp))))))))));
  assertEquals(-1, x |= (tmp = 1542129482, tmp));
  assertEquals(-671816743, x &= (tmp = -671816743.9111726, tmp));
  assertEquals(-1840333080, x -= (1168516337));
  assertEquals(-1755382023, x |= ((((tmp = 2625163636.0142937, tmp)>>>((tmp = 1534304735, tmp)^x))-(tmp = -1959666777.9995313, tmp))%x));
  assertEquals(-1750421896, x += (x>>>(tmp = -1364828055.1003118, tmp)));
  assertEquals(-72864007, x %= (tmp = 239651127, tmp));
  assertEquals(-72863956, x -= (((tmp = -1103261657.626319, tmp)*((tmp = 2789506613, tmp)+((tmp = 2294239314, tmp)>>>(2588428607.5454817))))>>x));
  assertEquals(-170337477, x -= (tmp = 97473521, tmp));
  assertEquals(-170337477, x |= (((tmp = 246292300.58998203, tmp)/(((tmp = -2664407492, tmp)|((-2416228818)^(tmp = 909802077, tmp)))%(tmp = 532643021.68109465, tmp)))/(tmp = 1015597843.8295637, tmp)));
  assertEquals(1, x >>>= (((tmp = -2247554641.7422867, tmp)/(1186555294))%(tmp = -785511772.3124621, tmp)));
  assertEquals(1188939891.668705, x -= (tmp = -1188939890.668705, tmp));
  assertEquals(1188939891, x &= x);
  assertEquals(1188413555, x &= (((tmp = -372965330.5709038, tmp)%(((tmp = 3108909487, tmp)|(x^(-1056955571.9951684)))^(-1549217484.009048)))/(x>>>(1403428437.9368362))));
  assertEquals(-0.7343692094664643, x /= (-1618278026.4758227));
  assertEquals(0, x -= x);
  assertEquals(0, x &= (-2701762139.7500515));
  assertEquals(0, x >>>= (((-1692761485.2299166)^x)+(tmp = -1221349575.938864, tmp)));
  assertEquals(0, x <<= ((2148160230)<<x));
  assertEquals(0, x <<= (((x<<(-740907931.38363))&(tmp = -930960051.6095045, tmp))>>(x/((tmp = -1921545150.1239789, tmp)/(-3015379806)))));
  assertEquals(0, x <<= x);
  assertEquals(NaN, x /= (x|x));
  assertEquals(0, x >>= (tmp = -2265988773, tmp));
  assertEquals(-0, x *= (((x<<(-928153614))<<(-989694208))^(2544757713.481016)));
  assertEquals(0, x >>= ((tmp = 578009959.5299993, tmp)>>x));
  assertEquals(0, x /= ((((tmp = 412689800.0431709, tmp)&(1630886276))*(tmp = 2028783080.7296097, tmp))/x));
  assertEquals(0, x |= ((((x*(-2197198786))>>((2719887264.761987)<<(tmp = 2253246512, tmp)))-(tmp = -150703768.07045603, tmp))/(((-3160098146)%(((((1486098047.843547)>>(((tmp = -593773744.1144242, tmp)&(x<<(2651087978)))|((-680492758.930413)>>(tmp = 88363052.13662052, tmp))))<<x)<<(tmp = 2232672341, tmp))/((x<<x)&(((((348589117.64135563)<<(-1010050456.3097556))^(x/(tmp = -2282328795, tmp)))-(tmp = 1653716293, tmp))-((3157124731)/((tmp = 3007369535.341745, tmp)%(tmp = -2246556917, tmp)))))))+x)));
  assertEquals(0, x >>= ((1935211663.5568764)>>(x-(tmp = 2116580032, tmp))));
  assertEquals(-1725272693, x ^= (tmp = -1725272693, tmp));
  assertEquals(313683, x >>>= (-1782632531.2877684));
  assertEquals(0.009772287443565642, x /= (tmp = 32099240, tmp));
  assertEquals(-647945916.9902277, x += (-647945917));
  assertEquals(3647021380, x >>>= ((((((((2470411371.688199)<<x)>>x)-(x>>>((tmp = 1750747780, tmp)/x)))-x)<<(tmp = -2666186351.695101, tmp))^(((tmp = 2749205312.6666174, tmp)%x)&(2069802830.360536)))<<(tmp = 6051917.9244532585, tmp)));
  assertEquals(-647939220, x |= ((x>>>((tmp = -2980404582.794245, tmp)>>>(-996846982)))^x));
  assertEquals(-572178450, x |= ((-800571300.3277931)+(tmp = 2084365671, tmp)));
  assertEquals(1172311208, x &= (x&((tmp = -1207487657.8953774, tmp)^x)));
  assertEquals(12176516458994, x += ((((tmp = -1534997221, tmp)%(412142731))*((tmp = 2958726303, tmp)>>(1489169839)))+(((-574726407.2051775)>>>(((1772885017)<<(947804536.9958035))>>(-2406844737)))>>x)));
  assertEquals(-1480065024, x <<= x);
  assertEquals(-1736999042.227129, x += (tmp = -256934018.22712898, tmp));
  assertEquals(-1338699394, x ^= ((((((x%(((tmp = -2551168455.222048, tmp)|(3213507293.930222))/((-1559278033)>>((tmp = 3107774495.3698573, tmp)-(2456375180.8660913)))))*((x*(tmp = 1088820004.8562922, tmp))+((tmp = 1850986704.9836102, tmp)%(tmp = -1226590364, tmp))))*(1786192008))&(((2193303940.310299)%(tmp = 1041726867.0602217, tmp))|((2210722848)/((-1293401295.6714435)&((tmp = 3052430315, tmp)|x)))))>>>(tmp = -2028014470.1524236, tmp))+(((1695818039.0383925)<<((1669068145)*(-2746592133.899276)))<<(tmp = 519092169, tmp))));
  assertEquals(-334674849, x >>= (1170377794));
  assertEquals(-10214, x >>= ((tmp = 1074704264.3712895, tmp)>>>((tmp = -1200860192, tmp)^((tmp = 539325023.4101218, tmp)*((tmp = -588989295, tmp)|x)))));
  assertEquals(1384169472, x &= (1384171140));
  assertEquals(1384169472, x >>>= ((tmp = -2161405973.830981, tmp)*(tmp = 2054628644, tmp)));
  assertEquals(1610140972, x |= (527961388));
  assertEquals(1073273198, x += ((tmp = -259650225.71344328, tmp)&(tmp = -344359694, tmp)));
  assertEquals(65507, x >>= ((x<<((tmp = 2925070713.5245204, tmp)%(x+((tmp = -1229447799, tmp)/(((x/(x|(((-2337139694)|((((((2996268529.7965417)&x)%(((tmp = -1088587413, tmp)>>(-1384104418.90339))>>((tmp = -1643984822.3946526, tmp)+x)))%(((1118125268.4540217)-((((-1975051668.6652594)-(-704573232))+((tmp = 1674952373, tmp)/(tmp = 1321895696.0062659, tmp)))*(tmp = 1820002533.2021284, tmp)))>>>(tmp = -583960746.9993203, tmp)))|((tmp = -2577675508.550925, tmp)&x))/(tmp = 1459790066, tmp)))/(((((1051712301.7804044)&(tmp = -2726396354, tmp))^(tmp = 263937254.18934345, tmp))+(((x^x)*(((tmp = -2289491571, tmp)+x)%(-2239181148)))&x))>>(tmp = -1743418186.3030887, tmp)))))/(tmp = 1475718622, tmp))<<x)))))|(x&((((tmp = -2934707420, tmp)<<x)/x)^(1022527598.7386684)))));
  assertEquals(2047, x >>= (x-(tmp = 2300626270, tmp)));
  assertEquals(8384512, x <<= (tmp = -1917680820, tmp));
  assertEquals(0, x <<= (2393691134));
  assertEquals(0, x >>= x);
  assertEquals(649995936.5853252, x -= (tmp = -649995936.5853252, tmp));
  assertEquals(649995936, x &= x);
  assertEquals(-0.33672017582945424, x /= (tmp = -1930374188, tmp));
  assertEquals(-0.33672017582945424, x += (x&((1208055031)^(-2761287670.968586))));
  assertEquals(0, x |= x);
  assertEquals(0, x <<= ((-2038368978)/x));
  assertEquals(0, x >>= (x&((tmp = 2481378057.738218, tmp)&(x+(1172701643)))));
  assertEquals(0, x <<= ((x*(((((((tmp = 70690601.3046323, tmp)&(((((((((((x+(x+(x^(3118107461))))<<(264682213.41888392))&(tmp = -709415381.8623683, tmp))%(((((-1840054964)>>>(tmp = -405893120.89603686, tmp))|((-625507229)^(3128979265)))>>(x>>((tmp = -2480442390, tmp)*((x>>(tmp = -421414980.88330936, tmp))>>>((tmp = 1850868592, tmp)&(-2948543832.879225))))))|((2986545185)&((tmp = -1947550706, tmp)%(((tmp = 2590238422.1414256, tmp)/(((tmp = -361038812, tmp)>>x)|(((tmp = 1798444068, tmp)|((x&((tmp = -3104542069, tmp)-x))*((tmp = -1158658918, tmp)+((tmp = 2777031040.5552707, tmp)<<(-2816019335.9008327)))))<<x)))/(((2287795988.231702)/x)/(((-2588712925)>>>(2521189250))*((tmp = -2533527920, tmp)+(tmp = 1762281307.2162101, tmp)))))))))/x)/(tmp = 1047121955.5357032, tmp))|(((-121292251)<<(x^(x-(tmp = 1420006180, tmp))))%((-2278606219)>>>(((tmp = -1412487726, tmp)&(((((tmp = 253596554.16016424, tmp)/(tmp = 2083376247.0079951, tmp))^(x^((1549116789.8449988)>>>((((-1844170084)^(tmp = 1886066422, tmp))&x)<<(34918329)))))^(tmp = -440805555.3369155, tmp))-x))%(-1936512969)))))+(2911511178.4035435))|(1012059391))|(x>>>(tmp = -2551794626.158037, tmp)))+((2926596072.210515)/(tmp = -280299595.0450909, tmp))))&((tmp = 1501086971, tmp)^(tmp = 2114076983, tmp)))-((-1679390574.1466925)-(941349044)))-((x>>x)>>((-2600539474.2033434)+(tmp = 2567056503.9079475, tmp))))*(tmp = 1285896052, tmp))%(((tmp = 1191465410.7595167, tmp)>>((tmp = -2857472754, tmp)%x))>>>(((tmp = 1960819627.6552541, tmp)&(-2651207221.127376))*((((-687312743)+((x>>x)<<x))|((((((1549588195)*((tmp = 2733091019, tmp)^((527322540)<<(x>>x))))%(tmp = -2063962943, tmp))*x)*(734060600))&(-3049417708)))+(((((1084267726)+((x|x)^((tmp = -1917070472.4858549, tmp)%((690016078.9375831)*x))))%((((((tmp = -2091172769, tmp)%(2532365378))>>>(-871354260))/(tmp = 254167019.07825458, tmp))&(1330216175.9871218))>>(tmp = 1931099207, tmp)))^(-1116448185.2618852))>>((961660080.8135855)/x)))))))>>>(-1486048007.7053368)));
  assertEquals(0, x >>= x);
  assertEquals(0, x %= (tmp = -1202200444.6506357, tmp));
  assertEquals(-0, x *= (-527500796.4145117));
  assertEquals(0, x >>= (tmp = -2082822707, tmp));
  assertEquals(0, x *= ((-1882398459.290778)>>>x));
  assertEquals(0, x &= (x/(tmp = -1569332286.392817, tmp)));
  assertEquals(-390169607, x |= (-390169607.11600184));
  assertEquals(-780339214, x += x);
  assertEquals(-780339214, x %= (2765959073));
  assertEquals(-5954, x >>= (tmp = -1900007055, tmp));
  assertEquals(743563420, x &= ((((-1520146483.5367205)|(-2075330284.3762321))-(tmp = -2263151872, tmp))%(-1264641939.957402)));
  assertEquals(1487126840, x += (x>>>(((x+((tmp = -1263274491, tmp)>>>x))&(470419048.0490037))%(tmp = -2642587112, tmp))));
  assertEquals(Infinity, x /= (x^x));
  assertEquals(0, x ^= ((tmp = -1436368543, tmp)+(x/(tmp = -1125415374.3297129, tmp))));
  assertEquals(0, x += x);
  assertEquals(0, x <<= x);
  assertEquals(0, x &= (tmp = 3101147204.2905564, tmp));
  assertEquals(0, x &= (tmp = 2914487586.606511, tmp));
  assertEquals(0, x += x);
  assertEquals(0, x -= (((-1738542908.6138556)&(((x+x)-(tmp = -2801153969, tmp))%(tmp = -1206684064.1477358, tmp)))>>((-2575546469.271897)|(tmp = -2573119106, tmp))));
  assertEquals(-1468808707, x ^= (tmp = -1468808707, tmp));
  assertEquals(1357349882, x <<= (tmp = -2808501087.7003627, tmp));
  assertEquals(-572025862, x |= ((((tmp = -2415486246.573399, tmp)/((tmp = -707895732.4593301, tmp)&x))%((-1960091005.0425267)*(972618070.9166157)))-(1649962343)));
  assertEquals(327213586796843100, x *= (x%(1337884626)));
  assertEquals(42991616, x &= (-2905576654.1280055));
  assertEquals(-26049289585042860, x *= (-605915571.6557121));
  assertEquals(597809748, x >>= ((362850791.077795)/(tmp = 1222777657.4401796, tmp)));
  assertEquals(597809748, x |= x);
  assertEquals(770065246, x -= ((-711227660)|(tmp = -508554506, tmp)));
  assertEquals(593000483097040500, x *= x);
  assertEquals(0, x %= x);
  assertEquals(0, x <<= (317862995.456813));
  assertEquals(0, x >>= ((tmp = 2518385735, tmp)+((-2973864605.267604)/(-930953312.718833))));
  assertEquals(1227822411, x ^= (x^(1227822411.8553264)));
  assertEquals(1090520320, x &= (x+((((-2100097959)>>(x/(tmp = -2002285068, tmp)))/(-364207954.9242482))-((tmp = 2771293106.7927113, tmp)-(tmp = -847237774, tmp)))));
  assertEquals(1090520320, x >>= (((((2439492849)<<((-2932672756.2578926)*((743648426.7224461)+((2942284935)<<((x/(((tmp = 886289462.6565771, tmp)+(-459458622.7475352))>>(tmp = -785521448.4979162, tmp)))|(tmp = -11630282.877367258, tmp))))))-(tmp = -647511106.9602091, tmp))^x)&x));
  assertEquals(115944291.48829031, x %= (243644007.12792742));
  assertEquals(1, x /= x);
  assertEquals(0, x >>>= ((tmp = -819782567, tmp)%(tmp = 2774793208.1994505, tmp)));
  assertEquals(0, x >>= (tmp = 721096000.2409859, tmp));
  assertEquals(0, x &= ((x%x)%x));
  assertEquals(-0, x *= ((-1670466344)<<x));
  assertEquals(0, x >>= (-677240844.904707));
  assertEquals(NaN, x %= (((((-1575993236.6126876)/(-2846264078.9581823))^((((-2220459664)-(((-1809496020)>>>(tmp = -3015964803.4566207, tmp))&x))/(tmp = -3081895596.0486784, tmp))>>>(x&x)))%(x^(-1338943139)))^(x-((((2074140963.2841332)^(tmp = 1878485274, tmp))%(((x/(-2568856967.6491556))^x)<<((x+x)^((((2139002721)|(x<<(-1356174045.840464)))>>x)-(tmp = 2305062176, tmp)))))>>>(((((x<<(tmp = -1663280319.078543, tmp))-((1498355849.4158854)-((-1321681257)>>>(tmp = -1321415088.6152222, tmp))))^(-2266278142.1584673))+(858538943))&((((x-((x|(((tmp = -1576599651, tmp)+((tmp = 1595319586, tmp)&(-2736785205.9203863)))>>((x+((-1856237826)+x))<<(tmp = -1590561854.3540869, tmp))))^(((-41283672.55606127)&(tmp = 2971132248, tmp))+x)))/(-849371349.1667476))%(x*((-1705070934.6892798)>>>x)))<<((2418200640)*x)))))));
  assertEquals(0, x >>>= (tmp = 664214199.5283061, tmp));
  assertEquals(0, x <<= ((-2827299151)<<(1815817649)));
  assertEquals(1405772596, x |= (tmp = 1405772596, tmp));
  assertEquals(-1483422104, x <<= (-2791499935.6822596));
  assertEquals(-45271, x >>= (1740128943.4254808));
  assertEquals(-45271, x <<= ((2072269957)-((tmp = -2553664811.4472017, tmp)*(tmp = -2502730352, tmp))));
  assertEquals(1192951471.6745887, x -= (-1192996742.6745887));
  assertEquals(-353370112, x <<= (tmp = -1410280844, tmp));
  assertEquals(0, x ^= (x%((2754092728)*(-1017564599.1094015))));
  assertEquals(-2662096003.2397957, x -= (tmp = 2662096003.2397957, tmp));
  assertEquals(-2587094028.50764, x -= (tmp = -75001974.7321558, tmp));
  assertEquals(6693055512339889000, x *= x);
  assertEquals(897526784, x %= (x-((tmp = 897526813, tmp)%(-1525574090))));
  assertEquals(7011928, x >>= ((-440899641.344357)%x));
  assertEquals(8382047686388683, x += (x*(1195398423.8538609)));
  assertEquals(16764095372777366, x += x);
  assertEquals(16764096859576696, x -= (tmp = -1486799329.7207344, tmp));
  assertEquals(16764099774187724, x += (2914611029));
  assertEquals(16764102926624664, x -= (-3152436939.724612));
  assertEquals(-538220648, x |= x);
  assertEquals(269110324, x /= (((-2114698894.6014318)/(tmp = 767687453, tmp))>>(623601568.1558858)));
  assertEquals(256, x >>= x);
  assertEquals(-293446891, x += (x+(-293447403)));
  assertEquals(119, x >>>= ((1759400753)>>(2481263470.4489403)));
  assertEquals(14, x >>= (762849027.89693));
  assertEquals(16, x += (x&(x>>(1104537666.1510491))));
  assertEquals(-12499808227.980995, x *= (tmp = -781238014.2488122, tmp));
  assertEquals(1, x /= x);
  assertEquals(1, x &= x);
  assertEquals(0, x >>>= ((tmp = 1513381008, tmp)|(tmp = 1593208075.7259543, tmp)));
  assertEquals(0, x &= (-788154636.2843091));
  assertEquals(-0, x /= (tmp = -2124830879, tmp));
  assertEquals(0, x &= (934237436));
  assertEquals(0, x |= x);
  assertEquals(-79370942.97651315, x += (-79370942.97651315));
  assertEquals(-79370942.97651315, x %= ((tmp = -2683255523, tmp)<<(tmp = 2323123280.287587, tmp)));
  assertEquals(-79370942, x |= x);
  assertEquals(0.05861647801688159, x /= (-1354072177.061561));
  assertEquals(0, x <<= (((((((tmp = 1989257036, tmp)&(tmp = 1565496213.6578887, tmp))&x)&(tmp = -2798643735.905287, tmp))&(2354854813.43784))%(tmp = 1118124748, tmp))<<((tmp = 2453617740, tmp)*(((tmp = 1762604500.492329, tmp)<<(-2865619363))%(((2474193854.640994)|((tmp = 1425847419.6256948, tmp)|(((-1271669386)%((x|((tmp = -2059795445.3607287, tmp)+x))*(x*x)))>>>(tmp = -2997360849.0750895, tmp))))/(tmp = 2326894252, tmp))))));
  assertEquals(0, x >>>= ((-671325215)/((-727408755.8793397)>>(tmp = 315457854, tmp))));
  assertEquals(0, x >>= (x&x));
  assertEquals(0, x <<= ((x/x)>>>(((((x&x)-((x*(((tmp = -2689062497.0087833, tmp)^x)/((-1465906334.9701924)<<(tmp = -349000262, tmp))))*x))%(1630399442.5429945))*x)+((tmp = 605234630, tmp)%(tmp = 2325750892.5065155, tmp)))));
  assertEquals(0, x |= (x%((x>>(((((tmp = 1622100459, tmp)<<x)&((((((tmp = 2411490075, tmp)<<x)|x)>>((x<<x)-(-2133780459)))/x)&(x+x)))%(x/((((tmp = 580125125.5035453, tmp)>>>(-470336002.1246581))|((tmp = 871348531, tmp)*x))>>(2866448831.23781))))-((2352334552)-(-562797641.6467373))))-(x^(tmp = -681731388, tmp)))));
  assertEquals(0, x <<= (tmp = -1358347010.3729038, tmp));
  assertEquals(-260967814, x |= ((tmp = -260967814.45976686, tmp)%(tmp = 1126020255.1772437, tmp)));
  assertEquals(NaN, x %= ((((tmp = 3176388281, tmp)<<(tmp = 611228283.2600244, tmp))>>>((tmp = 3068009824, tmp)+(tmp = 2482705111, tmp)))>>>((tmp = -750778285.2580311, tmp)>>>x)));
  assertEquals(0, x <<= (x>>>x));
  assertEquals(0, x /= (1238919162));
  assertEquals(0, x >>= (x^x));
  assertEquals(0, x &= (-2137844801));
  assertEquals(0, x >>>= (x^(x*(-1774217252))));
  assertEquals(0, x >>= x);
  assertEquals(0, x |= x);
  assertEquals(0, x &= (x<<(tmp = 2791377560, tmp)));
  assertEquals(-1330674638.8117397, x += (tmp = -1330674638.8117397, tmp));
  assertEquals(353, x >>>= (-212202857.4320326));
  assertEquals(353, x ^= ((((x+(tmp = 1448262278, tmp))-(-3141272537))>>(tmp = 1116596587.7832575, tmp))>>>((x-(((tmp = 303953098, tmp)>>>((tmp = 691514425, tmp)/((176223098)*(((2876180016)%(-1805235275.892374))|x))))<<(((tmp = 528736141.838547, tmp)^(2556817082))*(2898381286.2846575))))|((-1445518239)&(tmp = 389789481.9604758, tmp)))));
  assertEquals(0, x >>>= (-227376461.14343977));
  assertEquals(0, x <<= (tmp = -2575967504, tmp));
  assertEquals(0, x <<= (x^((-2668391896)>>((x+(tmp = 598697235.9205595, tmp))+((((-2105306785)|((-1174912319.794015)>>>(x-((148979923)%((((tmp = -2459140558.4436393, tmp)|(1265905916.494016))^(tmp = 1213922357.2230597, tmp))|(1028030636))))))%x)+(((tmp = 1393280827.0135512, tmp)^((tmp = 1210906638, tmp)+(-1572777641.1396031)))<<x))))));
  assertEquals(0, x *= (tmp = 2134187165, tmp));
  assertEquals(-1084549964, x -= (tmp = 1084549964, tmp));
  assertEquals(-2045706240, x &= ((tmp = -1250758905.7889671, tmp)*(x+(((x<<(x/(tmp = -738983664.845448, tmp)))>>>x)&(tmp = 2197525295, tmp)))));
  assertEquals(-2045706240, x ^= (((522049712.14743733)>>(tmp = -2695628092, tmp))>>>(tmp = -2603972068, tmp)));
  assertEquals(2249261056, x >>>= x);
  assertEquals(-33291, x |= ((((1891467762)<<(184547486.213719))-((458875403.50689447)^(((x&(x*x))|x)%(-3127945140))))|(-100765232)));
  assertEquals(-33291, x %= (1460486884.1367688));
  assertEquals(-1, x >>= (tmp = -2667341441, tmp));
  assertEquals(-3.6289151568259606e-10, x /= (tmp = 2755644474.4072013, tmp));
  assertEquals(-3.6289151568259606e-10, x %= (tmp = 1186700893.0751028, tmp));
  assertEquals(0, x <<= (tmp = -1199872107.9612694, tmp));
  assertEquals(371216449, x ^= ((tmp = 371324611.1357789, tmp)&(x-(x|((tmp = -518410357, tmp)>>((tmp = 687379733, tmp)/x))))));
  assertEquals(0.3561383159088311, x /= (((((x%(((((-2293101242)%((((495316779)/x)-((-3198854939.8857965)>>>((tmp = -288916023, tmp)-(x^(tmp = -2504080119.431858, tmp)))))^(-1201674989)))-((2965433901)*(405932927)))/((1974547923)|(tmp = 534069372, tmp)))-(x-((x+(-1258297330))%x))))<<(((-2648166176.4947824)^(-3043930615))&(1550481610)))<<(tmp = -3118264986.743822, tmp))<<x)|x));
  assertEquals(-46272499.15029934, x -= (tmp = 46272499.50643766, tmp));
  assertEquals(-6, x >>= ((tmp = -731454087.0621192, tmp)>>>x));
  assertEquals(-2.7207928474520667e-9, x /= (((x<<(x|((tmp = -1650731700.9540024, tmp)/(tmp = -677823292, tmp))))^((((((1972576122.928667)>>x)%(2952412902.115453))<<((-2888879343)+(tmp = -425663504, tmp)))>>>(((((tmp = 1089969932, tmp)>>>(x|((-2088509661)/(1131470551))))>>>x)+x)|(tmp = 955695979.7982506, tmp)))|(((((tmp = 826954002.6188571, tmp)^(2016485728))|((x/(((x<<(tmp = 2493217141, tmp))/(-2259979800.997408))-(tmp = -427592173.41389966, tmp)))%(((-471172918)/x)>>>((383234436.16425097)&(tmp = 1664411146.5308032, tmp)))))*(tmp = 1863669754.7545495, tmp))*(x>>(2062197604)))))>>>((x-(2624545856))*(tmp = 1025803102, tmp))));
  assertEquals(0, x >>= ((tmp = 1068702028, tmp)*(296106770)));
  assertEquals(0, x ^= (x/x));
  assertEquals(85359536, x ^= (((x|(((tmp = 740629227, tmp)<<(-1107397366))%((tmp = 2315368172, tmp)>>(((-2269513683)|(-2698795048))+(-396757976)))))*(929482738.803125))^(((-1415213955.4198723)-(tmp = -2885808324, tmp))>>>((tmp = -472842353.85736656, tmp)&(tmp = 1684231312.4497018, tmp)))));
  assertEquals(2075131904, x <<= x);
  assertEquals(123, x >>>= (x>>>(tmp = 754093009, tmp)));
  assertEquals(0, x >>= ((-2690948145)/((1988638799)+x)));
  assertEquals(0, x >>>= (tmp = -798849903.2467625, tmp));
  assertEquals(NaN, x %= x);
  assertEquals(NaN, x *= (2431863540.4609756));
  assertEquals(484934656, x |= ((-2322193663)*(tmp = -2754666771, tmp)));
  assertEquals(-82505091404694530, x *= (tmp = -170136513, tmp));
  assertEquals(-82505090515370620, x += ((-148762237)&(tmp = 889417717, tmp)));
  assertEquals(-908221124, x %= (tmp = -2346393300, tmp));
  assertEquals(-1242515799, x ^= (2083328917));
  assertEquals(-1126056310271520600, x *= ((((tmp = -3065605442, tmp)<<(-3012703413))|x)^(-2081329316.4781387)));
  assertEquals(-1126056309941068000, x += ((((tmp = 1886925157, tmp)&((tmp = -163003119.31722307, tmp)/((tmp = 2094816076, tmp)>>((tmp = -706947027, tmp)^x))))^((1819889650.5261197)<<(-1641091933)))>>x));
  assertEquals(-1864360191, x |= (((x/x)|x)|x));
  assertEquals(-1864360191, x &= x);
  assertEquals(-3728720382, x += x);
  assertEquals(1042663165, x ^= (535165183.4230335));
  assertEquals(2644530017.8833704, x += (1601866852.8833704));
  assertEquals(-574949401, x |= ((tmp = 943193254.5210983, tmp)^((x%(tmp = -2645213497, tmp))*(-1904818769))));
  assertEquals(1763223578, x ^= ((x^(tmp = -2244359016, tmp))^(tmp = 320955522, tmp)));
  assertEquals(-1.9640961474334235, x /= (tmp = -897727731.0502782, tmp));
  assertEquals(1, x >>>= (x-(-3183031393.8967886)));
  assertEquals(1, x &= (tmp = 1732572051.4196641, tmp));
  assertEquals(1, x >>= (-1642797568));
  assertEquals(-2339115203.3140306, x += (-2339115204.3140306));
  assertEquals(1955852093, x ^= (((((-1469402389)/(-2648643333.1454573))>>>x)<<(x/x))>>x));
  assertEquals(-965322519, x ^= (3001399252));
  assertEquals(-2139727840, x &= (tmp = 2298411812.964484, tmp));
  assertEquals(2103328, x &= (tmp = -2488723009, tmp));
  assertEquals(1799011007, x |= (tmp = -2498057537.226923, tmp));
  assertEquals(1799011007, x |= ((-308193085)>>>x));
  assertEquals(1799011007, x |= x);
  assertEquals(818879107, x ^= (1542823996.423564));
  assertEquals(-2601416919234843600, x *= ((-2357923057.076759)-x));
  assertEquals(-2601416920481796600, x -= (x|(tmp = -3048039765, tmp)));
  assertEquals(-33690112, x <<= x);
  assertEquals(1039491072, x &= (tmp = 1039491474.3389125, tmp));
  assertEquals(126891, x >>= (-3079837011.6151257));
  assertEquals(-163191923097543, x *= (((tmp = -2847221258.4048786, tmp)*(x-(tmp = 1527622853.5925639, tmp)))^x));
  assertEquals(753616551, x ^= (-946895202));
  assertEquals(-347691264, x <<= (tmp = -433184408.33790135, tmp));
  assertEquals(0, x <<= (x|(tmp = -1911731462.6835637, tmp)));
  assertEquals(-0, x *= (tmp = -2616154415.1662617, tmp));
  assertEquals(0, x >>= x);
  assertEquals(0, x -= x);
  assertEquals(0, x *= (2272504250.501526));
  assertEquals(0, x ^= x);
  assertEquals(NaN, x %= x);
  assertEquals(0, x >>>= (2475346113));
  assertEquals(NaN, x /= (((x+(-2646140897))&(((tmp = 1039073714.142481, tmp)-x)*x))|(x*(((-1277822905.773948)>>(tmp = 2035512354.2400663, tmp))*(77938193.80013895)))));
  assertEquals(0, x ^= (x<<(tmp = 2491934268, tmp)));
  assertEquals(0, x &= (tmp = 569878335.4607931, tmp));
  assertEquals(-88575883, x ^= ((453890820.8012209)-((1569189876)%((-1280613677.7083852)^(-1902514249.29567)))));
  assertEquals(-88575883, x %= (tmp = 257947563.19206762, tmp));
  assertEquals(-88575881.7863678, x -= ((tmp = 1257547359.029678, tmp)/(x^(tmp = 948265672.821815, tmp))));
  assertEquals(-169, x >>= (tmp = -2530523309.6703596, tmp));
  assertEquals(-1, x >>= x);
  assertEquals(-1, x |= x);
  assertEquals(131071, x >>>= (-673590289));
}
f();
