// Copyright 2008 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

var x;

// Converts a number to string respecting -0.
function stringify(n) {
  if ((1 / n) === -Infinity) return "-0";
  return String(n);
}

function f(expected, y) {
  function testEval(string, x, y) {
    var mulFunction = Function("x, y", "return " + string);
    return mulFunction(x, y);
  }
  function mulTest(expected, x, y) {
    assertEquals(expected, x * y);
    assertEquals(expected, testEval(stringify(x) + " * y", x, y));
    assertEquals(expected, testEval("x * " + stringify(y), x, y));
    assertEquals(expected, testEval(stringify(x) + " * " + stringify(y), x, y));
  }
  mulTest(expected, x, y);
  mulTest(-expected, -x, y);
  mulTest(-expected, x, -y);
  mulTest(expected, -x, -y);
  if (x === y) return;  // Symmetric cases not necessary.
  mulTest(expected, y, x);
  mulTest(-expected, -y, x);
  mulTest(-expected, y, -x);
  mulTest(expected, -y, -x);
}

x = 134217728;
f(0, 0);
f(134217728, 1);
f(268435456, 2);
f(402653184, 3);
f(536870912, 4);
f(671088640, 5);
f(939524096, 7);
f(1073741824, 8);
f(1207959552, 9);
f(2013265920, 15);
f(2147483648, 16);
f(2281701376, 17);
f(4160749568, 31);
f(4294967296, 32);
f(4429185024, 33);
f(8455716864, 63);
f(8589934592, 64);
f(8724152320, 65);
f(17045651456, 127);
f(17179869184, 128);
f(17314086912, 129);
f(34225520640, 255);
f(34359738368, 256);
f(34493956096, 257);
f(68585259008, 511);
f(68719476736, 512);
f(68853694464, 513);
f(137304735744, 1023);
f(137438953472, 1024);
f(137573171200, 1025);
f(274743689216, 2047);
f(274877906944, 2048);
f(275012124672, 2049);
f(549621596160, 4095);
f(549755813888, 4096);
f(549890031616, 4097);
f(1099377410048, 8191);
f(1099511627776, 8192);
f(1099645845504, 8193);
f(2198889037824, 16383);
f(2199023255552, 16384);
f(2199157473280, 16385);
f(4397912293376, 32767);
f(4398046511104, 32768);
f(4398180728832, 32769);
f(8795958804480, 65535);
f(8796093022208, 65536);
f(8796227239936, 65537);
f(17592051826688, 131071);
f(17592186044416, 131072);
f(17592320262144, 131073);
f(35184237871104, 262143);
f(35184372088832, 262144);
f(35184506306560, 262145);
f(70368609959936, 524287);
f(70368744177664, 524288);
f(70368878395392, 524289);
f(140737354137600, 1048575);
f(140737488355328, 1048576);
f(140737622573056, 1048577);
f(281474842492928, 2097151);
f(281474976710656, 2097152);
f(281475110928384, 2097153);
f(562949819203584, 4194303);
f(562949953421312, 4194304);
f(562950087639040, 4194305);
f(1125899772624896, 8388607);
f(1125899906842624, 8388608);
f(1125900041060352, 8388609);
x = 134217729;
f(0, 0);
f(134217729, 1);
f(268435458, 2);
f(402653187, 3);
f(536870916, 4);
f(671088645, 5);
f(939524103, 7);
f(1073741832, 8);
f(1207959561, 9);
f(2013265935, 15);
f(2147483664, 16);
f(2281701393, 17);
f(4160749599, 31);
f(4294967328, 32);
f(4429185057, 33);
f(8455716927, 63);
f(8589934656, 64);
f(8724152385, 65);
f(17045651583, 127);
f(17179869312, 128);
f(17314087041, 129);
f(34225520895, 255);
f(34359738624, 256);
f(34493956353, 257);
f(68585259519, 511);
f(68719477248, 512);
f(68853694977, 513);
f(137304736767, 1023);
f(137438954496, 1024);
f(137573172225, 1025);
f(274743691263, 2047);
f(274877908992, 2048);
f(275012126721, 2049);
f(549621600255, 4095);
f(549755817984, 4096);
f(549890035713, 4097);
f(1099377418239, 8191);
f(1099511635968, 8192);
f(1099645853697, 8193);
f(2198889054207, 16383);
f(2199023271936, 16384);
f(2199157489665, 16385);
f(4397912326143, 32767);
f(4398046543872, 32768);
f(4398180761601, 32769);
f(8795958870015, 65535);
f(8796093087744, 65536);
f(8796227305473, 65537);
f(17592051957759, 131071);
f(17592186175488, 131072);
f(17592320393217, 131073);
f(35184238133247, 262143);
f(35184372350976, 262144);
f(35184506568705, 262145);
f(70368610484223, 524287);
f(70368744701952, 524288);
f(70368878919681, 524289);
f(140737355186175, 1048575);
f(140737489403904, 1048576);
f(140737623621633, 1048577);
f(281474844590079, 2097151);
f(281474978807808, 2097152);
f(281475113025537, 2097153);
f(562949823397887, 4194303);
f(562949957615616, 4194304);
f(562950091833345, 4194305);
f(1125899781013503, 8388607);
f(1125899915231232, 8388608);
f(1125900049448961, 8388609);
x = 268435455;
f(0, 0);
f(268435455, 1);
f(536870910, 2);
f(805306365, 3);
f(1073741820, 4);
f(1342177275, 5);
f(1879048185, 7);
f(2147483640, 8);
f(2415919095, 9);
f(4026531825, 15);
f(4294967280, 16);
f(4563402735, 17);
f(8321499105, 31);
f(8589934560, 32);
f(8858370015, 33);
f(16911433665, 63);
f(17179869120, 64);
f(17448304575, 65);
f(34091302785, 127);
f(34359738240, 128);
f(34628173695, 129);
f(68451041025, 255);
f(68719476480, 256);
f(68987911935, 257);
f(137170517505, 511);
f(137438952960, 512);
f(137707388415, 513);
f(274609470465, 1023);
f(274877905920, 1024);
f(275146341375, 1025);
f(549487376385, 2047);
f(549755811840, 2048);
f(550024247295, 2049);
f(1099243188225, 4095);
f(1099511623680, 4096);
f(1099780059135, 4097);
f(2198754811905, 8191);
f(2199023247360, 8192);
f(2199291682815, 8193);
f(4397778059265, 16383);
f(4398046494720, 16384);
f(4398314930175, 16385);
f(8795824553985, 32767);
f(8796092989440, 32768);
f(8796361424895, 32769);
f(17591917543425, 65535);
f(17592185978880, 65536);
f(17592454414335, 65537);
f(35184103522305, 131071);
f(35184371957760, 131072);
f(35184640393215, 131073);
f(70368475480065, 262143);
f(70368743915520, 262144);
f(70369012350975, 262145);
f(140737219395585, 524287);
f(140737487831040, 524288);
f(140737756266495, 524289);
f(281474707226625, 1048575);
f(281474975662080, 1048576);
f(281475244097535, 1048577);
f(562949682888705, 2097151);
f(562949951324160, 2097152);
f(562950219759615, 2097153);
f(1125899634212865, 4194303);
f(1125899902648320, 4194304);
f(1125900171083775, 4194305);
x = 268435456;
f(0, 0);
f(268435456, 1);
f(536870912, 2);
f(805306368, 3);
f(1073741824, 4);
f(1342177280, 5);
f(1879048192, 7);
f(2147483648, 8);
f(2415919104, 9);
f(4026531840, 15);
f(4294967296, 16);
f(4563402752, 17);
f(8321499136, 31);
f(8589934592, 32);
f(8858370048, 33);
f(16911433728, 63);
f(17179869184, 64);
f(17448304640, 65);
f(34091302912, 127);
f(34359738368, 128);
f(34628173824, 129);
f(68451041280, 255);
f(68719476736, 256);
f(68987912192, 257);
f(137170518016, 511);
f(137438953472, 512);
f(137707388928, 513);
f(274609471488, 1023);
f(274877906944, 1024);
f(275146342400, 1025);
f(549487378432, 2047);
f(549755813888, 2048);
f(550024249344, 2049);
f(1099243192320, 4095);
f(1099511627776, 4096);
f(1099780063232, 4097);
f(2198754820096, 8191);
f(2199023255552, 8192);
f(2199291691008, 8193);
f(4397778075648, 16383);
f(4398046511104, 16384);
f(4398314946560, 16385);
f(8795824586752, 32767);
f(8796093022208, 32768);
f(8796361457664, 32769);
f(17591917608960, 65535);
f(17592186044416, 65536);
f(17592454479872, 65537);
f(35184103653376, 131071);
f(35184372088832, 131072);
f(35184640524288, 131073);
f(70368475742208, 262143);
f(70368744177664, 262144);
f(70369012613120, 262145);
f(140737219919872, 524287);
f(140737488355328, 524288);
f(140737756790784, 524289);
f(281474708275200, 1048575);
f(281474976710656, 1048576);
f(281475245146112, 1048577);
f(562949684985856, 2097151);
f(562949953421312, 2097152);
f(562950221856768, 2097153);
f(1125899638407168, 4194303);
f(1125899906842624, 4194304);
f(1125900175278080, 4194305);
x = 268435457;
f(0, 0);
f(268435457, 1);
f(536870914, 2);
f(805306371, 3);
f(1073741828, 4);
f(1342177285, 5);
f(1879048199, 7);
f(2147483656, 8);
f(2415919113, 9);
f(4026531855, 15);
f(4294967312, 16);
f(4563402769, 17);
f(8321499167, 31);
f(8589934624, 32);
f(8858370081, 33);
f(16911433791, 63);
f(17179869248, 64);
f(17448304705, 65);
f(34091303039, 127);
f(34359738496, 128);
f(34628173953, 129);
f(68451041535, 255);
f(68719476992, 256);
f(68987912449, 257);
f(137170518527, 511);
f(137438953984, 512);
f(137707389441, 513);
f(274609472511, 1023);
f(274877907968, 1024);
f(275146343425, 1025);
f(549487380479, 2047);
f(549755815936, 2048);
f(550024251393, 2049);
f(1099243196415, 4095);
f(1099511631872, 4096);
f(1099780067329, 4097);
f(2198754828287, 8191);
f(2199023263744, 8192);
f(2199291699201, 8193);
f(4397778092031, 16383);
f(4398046527488, 16384);
f(4398314962945, 16385);
f(8795824619519, 32767);
f(8796093054976, 32768);
f(8796361490433, 32769);
f(17591917674495, 65535);
f(17592186109952, 65536);
f(17592454545409, 65537);
f(35184103784447, 131071);
f(35184372219904, 131072);
f(35184640655361, 131073);
f(70368476004351, 262143);
f(70368744439808, 262144);
f(70369012875265, 262145);
f(140737220444159, 524287);
f(140737488879616, 524288);
f(140737757315073, 524289);
f(281474709323775, 1048575);
f(281474977759232, 1048576);
f(281475246194689, 1048577);
f(562949687083007, 2097151);
f(562949955518464, 2097152);
f(562950223953921, 2097153);
f(1125899642601471, 4194303);
f(1125899911036928, 4194304);
f(1125900179472385, 4194305);
x = 536870911;
f(0, 0);
f(536870911, 1);
f(1073741822, 2);
f(1610612733, 3);
f(2147483644, 4);
f(2684354555, 5);
f(3758096377, 7);
f(4294967288, 8);
f(4831838199, 9);
f(8053063665, 15);
f(8589934576, 16);
f(9126805487, 17);
f(16642998241, 31);
f(17179869152, 32);
f(17716740063, 33);
f(33822867393, 63);
f(34359738304, 64);
f(34896609215, 65);
f(68182605697, 127);
f(68719476608, 128);
f(69256347519, 129);
f(136902082305, 255);
f(137438953216, 256);
f(137975824127, 257);
f(274341035521, 511);
f(274877906432, 512);
f(275414777343, 513);
f(549218941953, 1023);
f(549755812864, 1024);
f(550292683775, 1025);
f(1098974754817, 2047);
f(1099511625728, 2048);
f(1100048496639, 2049);
f(2198486380545, 4095);
f(2199023251456, 4096);
f(2199560122367, 4097);
f(4397509632001, 8191);
f(4398046502912, 8192);
f(4398583373823, 8193);
f(8795556134913, 16383);
f(8796093005824, 16384);
f(8796629876735, 16385);
f(17591649140737, 32767);
f(17592186011648, 32768);
f(17592722882559, 32769);
f(35183835152385, 65535);
f(35184372023296, 65536);
f(35184908894207, 65537);
f(70368207175681, 131071);
f(70368744046592, 131072);
f(70369280917503, 131073);
f(140736951222273, 262143);
f(140737488093184, 262144);
f(140738024964095, 262145);
f(281474439315457, 524287);
f(281474976186368, 524288);
f(281475513057279, 524289);
f(562949415501825, 1048575);
f(562949952372736, 1048576);
f(562950489243647, 1048577);
f(1125899367874561, 2097151);
f(1125899904745472, 2097152);
f(1125900441616383, 2097153);
x = 536870912;
f(0, 0);
f(536870912, 1);
f(1073741824, 2);
f(1610612736, 3);
f(2147483648, 4);
f(2684354560, 5);
f(3758096384, 7);
f(4294967296, 8);
f(4831838208, 9);
f(8053063680, 15);
f(8589934592, 16);
f(9126805504, 17);
f(16642998272, 31);
f(17179869184, 32);
f(17716740096, 33);
f(33822867456, 63);
f(34359738368, 64);
f(34896609280, 65);
f(68182605824, 127);
f(68719476736, 128);
f(69256347648, 129);
f(136902082560, 255);
f(137438953472, 256);
f(137975824384, 257);
f(274341036032, 511);
f(274877906944, 512);
f(275414777856, 513);
f(549218942976, 1023);
f(549755813888, 1024);
f(550292684800, 1025);
f(1098974756864, 2047);
f(1099511627776, 2048);
f(1100048498688, 2049);
f(2198486384640, 4095);
f(2199023255552, 4096);
f(2199560126464, 4097);
f(4397509640192, 8191);
f(4398046511104, 8192);
f(4398583382016, 8193);
f(8795556151296, 16383);
f(8796093022208, 16384);
f(8796629893120, 16385);
f(17591649173504, 32767);
f(17592186044416, 32768);
f(17592722915328, 32769);
f(35183835217920, 65535);
f(35184372088832, 65536);
f(35184908959744, 65537);
f(70368207306752, 131071);
f(70368744177664, 131072);
f(70369281048576, 131073);
f(140736951484416, 262143);
f(140737488355328, 262144);
f(140738025226240, 262145);
f(281474439839744, 524287);
f(281474976710656, 524288);
f(281475513581568, 524289);
f(562949416550400, 1048575);
f(562949953421312, 1048576);
f(562950490292224, 1048577);
f(1125899369971712, 2097151);
f(1125899906842624, 2097152);
f(1125900443713536, 2097153);
