// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-typed-array-gen.h'

namespace typed_array {
const kBuiltinNameOf: constexpr string = '%TypedArray%.of';

// %TypedArray%.of ( ...items )
// https://tc39.github.io/ecma262/#sec-%typedarray%.of
transitioning javascript builtin
TypedArrayOf(js-implicit context: NativeContext, receiver: JSAny)(...arguments):
    JSTypedArray {
  try {
    // 1. Let len be the actual number of arguments passed to this function.
    const len: uintptr = Unsigned(arguments.length);

    // 2. Let items be the List of arguments passed to this function.

    // 3. Let C be the this value.
    // 4. If IsConstructor(C) is false, throw a TypeError exception.
    const constructor = Cast<Constructor>(receiver) otherwise NotConstructor;

    // 5. Let newObj be ? TypedArrayCreate(C, len).
    const newObj = TypedArrayCreateByLength(
        constructor, Convert<Number>(len), kBuiltinNameOf);

    const accessor: TypedArrayAccessor =
        GetTypedArrayAccessor(newObj.elements_kind);

    // 6. Let k be 0.
    // 7. Repeat, while k < len
    for (let k: uintptr = 0; k < len; k++) {
      // 7a. Let kValue be items[k].
      const kValue: JSAny = arguments[Signed(k)];

      // 7b. Let Pk be ! ToString(k).
      // 7c. Perform ? Set(newObj, Pk, kValue, true).
      // Buffer may be detached during executing ToNumber/ToBigInt.
      accessor.StoreJSAny(context, newObj, k, kValue) otherwise IfDetached;

      // 7d. Increase k by 1. (done by the loop).
    }

    // 8. Return newObj.
    return newObj;
  } label NotConstructor deferred {
    ThrowTypeError(MessageTemplate::kNotConstructor, receiver);
  } label IfDetached deferred {
    ThrowTypeError(MessageTemplate::kDetachedOperation, kBuiltinNameOf);
  }
}
}
