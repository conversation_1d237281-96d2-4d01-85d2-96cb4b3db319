# Copyright 2018 the V8 project authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# gn_isolate_map.pyl - A mapping of Ninja build target names to GN labels and
# test type classifications for the tests that are run on the bots.
#
# This file is based on testing/buildbot/gn_isolate_map.pyl for Chromium, but
# is covering V8 stand-alone tests instead.
# See https://cs.chromium.org/chromium/src/testing/buildbot/gn_isolate_map.pyl
# for more detailed documentation.

{
  "All": {
    "label": "//:All",
    "type": "script",
  },
  "benchmarks": {
    "label": "//test/benchmarks:v8_benchmarks",
    "type": "script",
  },
  "bot_default": {
    "label": "//test:v8_bot_default",
    "type": "script",
  },
  "check-static-initializers": {
    "label": "//tools:v8_check_static_initializers",
    "type": "script",
  },
  "d8_default": {
    "label": "//test:v8_d8_default",
    "type": "script",
  },
  "generate-bytecode-expectations": {
    "label": "//test/unittests:generate-bytecode-expectations",
    "type": "script",
  },
  "mjsunit": {
    "label": "//test/mjsunit:v8_mjsunit",
    "type": "script",
  },
  "mozilla": {
    "label": "//test/mozilla:v8_mozilla",
    "type": "script",
  },
  "optimize_for_size": {
    "label": "//test:v8_optimize_for_size",
    "type": "script",
  },
  "perf": {
    "label": "//test:v8_perf",
    "type": "script",
  },
  "perf_integration": {
    "label": "//test:v8_perf",
    "type": "script",
  },
  "jsfunfuzz": {
    "label": "//tools/jsfunfuzz:v8_jsfunfuzz",
    "type": "script",
  },
  "run-gcmole": {
    "label": "//tools/gcmole:v8_gcmole_files",
    "type": "script",
  },
  "run-num-fuzzer": {
    "label": "//test:v8_run_num_fuzzer",
    "type": "script",
  },
  "test262": {
    "label": "//test/test262:v8_test262",
    "type": "script",
  },
  "unittests": {
    "label": "//test/unittests:unittests",
    "type": "script",
  },
  "fuchsia-unittests": {
    "label": "//test/unittests:v8_unittests_fuchsia",
    "type": "script",
  },
  "webkit": {
    "label": "//test/webkit:v8_webkit",
    "type": "script",
  },
}
