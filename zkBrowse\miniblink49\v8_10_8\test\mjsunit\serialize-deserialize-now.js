// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
//
// Flags: --allow-natives-syntax

%SerializeDeserializeNow();

const xs = [0, 1, 2];
var o = { a: 0, b: 1, c: 2 };

%SerializeDeserializeNow();

const p = new Promise((resolve, reject) => { resolve("Promise"); });
p.then((msg) => console.log(msg));

%SerializeDeserializeNow();
