// Copyright 2014 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Flags: --allow-natives-syntax

function g1(i) {
  var x = i * 1;
  return (x >>> 0) % 1000000000000;
}

function g2(i) {
  var x = i * 1;
  return ((x >>> 0) % 1000000000000) | 0;
}

function test1() {
  assertEquals(**********, g1(-**********));
  assertEquals(**********, g1(-**********));
  assertEquals(**********, g1(-**********));

  assertEquals(**********, g1(-**********));
  assertEquals(**********, g1(-**********));
  assertEquals(**********, g1(-**********));
  assertEquals(**********, g1(-**********));
  assertEquals(**********, g1(-**********));

  assertEquals(**********, g1(**********));
  assertEquals(**********, g1(**********));
  assertEquals(**********, g1(**********));

  assertEquals(**********, g1(**********));
  assertEquals(**********, g1(**********));
  assertEquals(**********, g1(**********));

  assertEquals(3, g1(**********));
  assertEquals(2, g1(**********));
  assertEquals(1, g1(**********));
  assertEquals(0, g1(**********));
  assertEquals(4294967295, g1(4294967295));
  assertEquals(4294967294, g1(4294967294));
  assertEquals(4294967293, g1(4294967293));
  assertEquals(4294967292, g1(4294967292));
}

%NeverOptimizeFunction(test1);
test1();

function test2() {
  assertEquals(-**********, g2(-**********));
  assertEquals(-**********, g2(-**********));
  assertEquals(-**********, g2(-**********));

  assertEquals(-**********, g2(-**********));
  assertEquals(-**********, g2(-**********));
  assertEquals(-**********, g2(-**********));
  assertEquals(-**********, g2(-**********));
  assertEquals(**********, g2(-**********));

  assertEquals(-1294967296, g2(**********));
  assertEquals(-1294967295, g2(**********));
  assertEquals(-1294967294, g2(**********));

  assertEquals(-294967296, g2(**********));
  assertEquals(-294567295, g2(**********));
  assertEquals(-294567294, g2(**********));

  assertEquals(3, g2(**********));
  assertEquals(2, g2(**********));
  assertEquals(1, g2(**********));
  assertEquals(0, g2(**********));
  assertEquals(-1, g2(4294967295));
  assertEquals(-2, g2(4294967294));
  assertEquals(-3, g2(4294967293));
  assertEquals(-4, g2(4294967292));
}

%NeverOptimizeFunction(test2);
test2();
