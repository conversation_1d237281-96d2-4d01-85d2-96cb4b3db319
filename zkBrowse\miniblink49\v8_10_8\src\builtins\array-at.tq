// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace array {
macro ConvertRelativeIndex(index: Number, length: Number):
    Number labels OutOfBoundsLow, OutOfBoundsHigh {
  const relativeIndex = index >= 0 ? index : length + index;
  if (relativeIndex < 0) goto OutOfBoundsLow;
  if (relativeIndex >= length) goto OutOfBoundsHigh;
  return relativeIndex;
}

// https://tc39.es/proposal-item-method/#sec-array.prototype.at
transitioning javascript builtin ArrayPrototypeAt(
    js-implicit context: NativeContext, receiver: JSAny)(index: JSAny): JSAny {
  // 1. Let O be ? ToObject(this value).
  const o = ToObject_Inline(context, receiver);

  // 2. Let len be ? LengthOfArrayLike(O).
  const len = GetLengthProperty(o);

  try {
    // 3. Let relativeIndex be ? ToInteger(index).
    const relativeIndex = ToInteger_Inline(index);

    // 4. If relativeIndex ≥ 0, then
    //   a. Let k be relativeIndex.
    // 5. Else,
    //   a. Let k be len + relativeIndex.
    const k = ConvertRelativeIndex(relativeIndex, len) otherwise OutOfBounds,
          OutOfBounds;

    // 7. Return ? Get(O, ! ToString(k)).
    return GetProperty(o, k);
  } label OutOfBounds {
    // 6. If k < 0 or k ≥ len, then return undefined.
    return Undefined;
  }
}
}
