; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

EXTRN	__imp__VirtualQuery@12:PROC
EXTRN	__imp__lstrcpyW@8:PROC
EXTRN	__imp__lstrcpynA@12:PROC
EXTRN	__imp__SHCreateDirectoryExA@12:PROC
EXTRN	__imp__RtlCaptureStackBackTrace@16:PROC
EXTRN	__imp__StrStrIA@8:PROC
EXTRN	__imp__lstrcpynW@12:PROC
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
_BSS	ENDS
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
$SG4294347542 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347543 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294347540 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347541 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294347538 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347539 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294347536 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347537 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347546 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294347544 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294347545 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294347526 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294347527 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294347524 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294347525 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294347522 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294347523 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347520 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347521 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347534 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347535 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294347532 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294347533 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294347530 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294347531 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294347528 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294347529 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294347510 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347511 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347508 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347509 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347506 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347507 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347504 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347505 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347518 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347519 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347516 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347517 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347514 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347515 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347512 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347513 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294347494 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347495 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294347492 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294347493 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347490 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347491 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347488 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347489 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347502 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347503 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347500 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347501 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347498 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347499 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347496 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294347497 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294347478 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347479 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294347476 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294347477 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347474 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347475 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347472 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294347473 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294347486 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347487 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294347484 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294347485 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347482 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294347483 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294347480 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347481 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347462 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347463 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347460 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294347461 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347458 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294347459 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294347456 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294347457 DB 00H, 00H
	ORG $+2
$SG4294347470 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347471 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294347468 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347469 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347466 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347467 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347464 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347465 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347446 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294347447 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294347444 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294347445 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294347442 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294347443 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294347440 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294347441 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294347454 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294347455 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294347452 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294347453 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294347450 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294347451 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294347448 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294347449 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294347430 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294347431 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294347428 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294347429 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294347426 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294347427 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294347424 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294347425 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294347438 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294347439 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294347436 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294347437 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294347434 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294347435 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294347432 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294347433 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294347414 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294347415 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294347412 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294347413 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294347410 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294347411 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294347408 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294347409 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294347422 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294347423 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294347420 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294347421 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294347418 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294347419 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294347416 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347417 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294347398 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347399 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294347396 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347397 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347394 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347395 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347392 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347393 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347406 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294347407 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347404 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294347405 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294347402 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294347403 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294347400 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347401 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294347382 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294347383 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347380 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294347381 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294347378 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294347379 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294347376 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294347377 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294347390 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347391 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294347388 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294347389 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294347386 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347387 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294347384 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347385 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347366 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294347367 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294347364 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347365 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347362 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294347363 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294347360 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294347361 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294347374 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294347375 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294347372 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294347373 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294347370 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347371 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294347368 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347369 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294347350 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294347351 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294347348 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294347349 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294347346 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294347347 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347344 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294347345 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294347358 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294347359 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294347356 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294347357 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347354 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294347355 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347352 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294347353 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294347334 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347335 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294347332 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294347333 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347330 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294347331 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294347328 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347329 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347342 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294347343 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294347340 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347341 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294347338 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294347339 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347336 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294347337 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294347318 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294347319 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294347316 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294347317 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294347314 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294347315 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294347312 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294347313 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294347326 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294347327 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294347324 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347325 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294347322 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294347323 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294347320 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347321 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294347302 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294347303 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294347300 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294347301 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294347298 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294347299 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294347296 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347297 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347310 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294347311 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294347308 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294347309 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294347306 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294347307 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294347304 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294347305 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294347286 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294347287 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294347284 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294347285 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294347282 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294347283 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294347280 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294347281 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294347294 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294347295 DB 00H, 00H
	ORG $+2
$SG4294347292 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347293 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294347290 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294347291 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294347288 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294347289 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294347270 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294347271 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294347268 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347269 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294347266 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347267 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347264 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347265 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294347278 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294347279 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294347276 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294347277 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294347274 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347275 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294347272 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347273 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347254 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294347255 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294347252 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347253 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294347250 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294347251 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347248 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294347249 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347262 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294347263 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294347260 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294347261 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347258 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294347259 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347256 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294347257 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294347238 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294347239 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294347236 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294347237 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294347234 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294347235 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347232 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294347233 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294347246 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294347247 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294347244 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294347245 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294347242 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294347243 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294347240 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294347241 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294347222 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294347223 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294347220 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294347221 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294347218 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294347219 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294347216 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294347217 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294347230 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294347231 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294347228 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294347229 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294347226 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294347227 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294347224 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294347225 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294347206 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294347207 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347204 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294347205 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347202 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294347203 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294347200 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294347201 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294347214 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294347215 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347212 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294347213 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347210 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294347211 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347208 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294347209 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294347190 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347191 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347188 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294347189 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294347186 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294347187 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347184 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294347185 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294347198 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347199 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294347196 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347197 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294347194 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347195 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347192 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294347193 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294347174 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347175 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294347172 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347173 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294347170 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347171 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294347168 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347169 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347182 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294347183 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294347180 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294347181 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347178 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294347179 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347176 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347177 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294347158 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294347159 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294347156 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294347157 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347154 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347155 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347152 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347153 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294347166 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347167 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294347164 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294347165 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294347162 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347163 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294347160 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294347161 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294347142 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294347143 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294347140 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294347141 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347138 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347139 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347136 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294347137 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347150 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294347151 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347148 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294347149 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294347146 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294347147 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294347144 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294347145 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347126 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294347127 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294347124 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347125 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347122 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294347123 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294347120 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347121 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294347134 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294347135 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347132 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347133 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294347130 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347131 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347128 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294347129 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294347110 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294347111 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347108 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347109 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294347106 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347107 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347104 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347105 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347118 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347119 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294347116 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347117 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294347114 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347115 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347112 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347113 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347094 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347095 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347092 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294347093 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294347090 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294347091 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294347088 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294347089 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347102 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294347103 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294347100 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294347101 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294347098 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294347099 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294347096 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294347097 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347078 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294347079 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294347076 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294347077 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294347074 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347075 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347072 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294347073 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294347086 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294347087 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294347084 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294347085 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294347082 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294347083 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294347080 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294347081 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294347070 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294347071 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294347068 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294347069 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294347030 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294347031 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294347028 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347029 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347026 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294347027 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294347024 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347025 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294347034 DB 'M', 00H, 00H, 00H
$SG4294347035 DB 'S', 00H, 00H, 00H
$SG4294347032 DB 'B', 00H, 00H, 00H
$SG4294347033 DB 'D', 00H, 00H, 00H
$SG4294347022 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347023 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294347020 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294347021 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294347018 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294347019 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294347016 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294347017 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294346983 DB 00H, 00H
	ORG $+2
$SG4294346984 DB 00H, 00H
	ORG $+2
$SG4294346985 DB ':', 00H, 00H, 00H
$SG4294346892 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294346178 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294346179 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294345730 DB '\Macromed\Flash\', 00H
	ORG $+3
$SG4294345731 DB '\', 00H
	ORG $+2
$SG4294345728 DB 'Temp\', 00H
	ORG $+2
$SG4294345729 DB '\npswf', 00H
PUBLIC	??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z ; std::operator+<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	?HookApi@JsApi@@YAHPAPAXPAXH@Z			; JsApi::HookApi
PUBLIC	?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; JsApi::HookFlashCachePath
PUBLIC	?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
PUBLIC	?m_wsAppDataDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsAppDataDir
PUBLIC	?old_SHGetFolderPathA@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPAD@ZA ; `anonymous namespace'::old_SHGetFolderPathA
PUBLIC	?old_SHGetFolderPathW@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPA_W@ZA ; `anonymous namespace'::old_SHGetFolderPathW
PUBLIC	?old_GetTempPathA@?A0xa937c340@@3P6GKKPAD@ZA	; `anonymous namespace'::old_GetTempPathA
PUBLIC	?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
PUBLIC	?old_GetTempPathW@?A0xa937c340@@3P6GKKPA_W@ZA	; `anonymous namespace'::old_GetTempPathW
PUBLIC	?m_wsTempDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsTempDir
?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A DB 018H DUP (?) ; `anonymous namespace'::m_sAppDataDir
?m_wsAppDataDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A DB 018H DUP (?) ; `anonymous namespace'::m_wsAppDataDir
_BSS	ENDS
;	COMDAT ?$TSS0@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
_BSS	SEGMENT
?$TSS0@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA DD 01H DUP (?) ; TSS0<`template-parameter-2',JsApi::okFlashCachePath,std::D::ar_traits,JsApi::allocator<char> >
?old_SHGetFolderPathA@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPAD@ZA DD 01H DUP (?) ; `anonymous namespace'::old_SHGetFolderPathA
?old_SHGetFolderPathW@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPA_W@ZA DD 01H DUP (?) ; `anonymous namespace'::old_SHGetFolderPathW
?old_GetTempPathA@?A0xa937c340@@3P6GKKPAD@ZA DD 01H DUP (?) ; `anonymous namespace'::old_GetTempPathA
?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A DB 018H DUP (?) ; `anonymous namespace'::m_sTempDir
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
?old_GetTempPathW@?A0xa937c340@@3P6GKKPA_W@ZA DD 01H DUP (?) ; `anonymous namespace'::old_GetTempPathW
?m_wsTempDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A DB 018H DUP (?) ; `anonymous namespace'::m_wsTempDir
_BSS	ENDS
;	COMDAT ?bHooked@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4_NA
_BSS	SEGMENT
?bHooked@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4_NA DB 01H DUP (?) ; `JsApi::HookFlashCachePath'::`2'::bHooked
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z$1
	DD	00H
	DD	FLAT:__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z$0
__ehfuncinfo$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z$1
	DD	00H
	DD	FLAT:__unwindfunclet$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z$0
__ehfuncinfo$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 019930522H
	DD	06H
	DD	FLAT:__unwindtable$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$1
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$2
	DD	02H
	DD	FLAT:__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$3
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$4
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$5
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
?m_sAppDataDir$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Em_sAppDataDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::m_sAppDataDir$initializer$
?m_wsAppDataDir$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Em_wsAppDataDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::m_wsAppDataDir$initializer$
?m_sTempDir$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Em_sTempDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::m_sTempDir$initializer$
?m_wsTempDir$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Em_wsTempDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::m_wsTempDir$initializer$
?old_SHGetFolderPathW$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Eold_SHGetFolderPathW@?A0xa937c340@@YAXXZ ; `anonymous namespace'::old_SHGetFolderPathW$initializer$
?old_SHGetFolderPathA$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Eold_SHGetFolderPathA@?A0xa937c340@@YAXXZ ; `anonymous namespace'::old_SHGetFolderPathA$initializer$
?old_GetTempPathA$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Eold_GetTempPathA@?A0xa937c340@@YAXXZ ; `anonymous namespace'::old_GetTempPathA$initializer$
?old_GetTempPathW$initializer$@?A0xa937c340@@3P6AXXZA DD FLAT:??__Eold_GetTempPathW@?A0xa937c340@@YAXXZ ; `anonymous namespace'::old_GetTempPathW$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
tv161 = -164						; size = 4
tv164 = -160						; size = 4
tv159 = -156						; size = 4
tv163 = -152						; size = 4
tv157 = -148						; size = 4
tv91 = -144						; size = 4
tv154 = -140						; size = 4
tv162 = -136						; size = 4
$T2 = -132						; size = 24
$T3 = -108						; size = 24
$T4 = -84						; size = 24
$T5 = -60						; size = 24
$T6 = -36						; size = 24
__$EHRec$ = -12						; size = 12
_sNewPath$ = 8						; size = 4
?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z PROC ; JsApi::HookFlashCachePath

; 133  : 	bool HookFlashCachePath(const std::string& sNewPath) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	81 ec 98 00 00
	00		 sub	 esp, 152		; 00000098H
  0001e	56		 push	 esi
  0001f	57		 push	 edi
  00020	8d bd 5c ff ff
	ff		 lea	 edi, DWORD PTR [ebp-164]
  00026	b9 26 00 00 00	 mov	 ecx, 38			; 00000026H
  0002b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00030	f3 ab		 rep stosd

; 134  : 		static bool bHooked = HookFlashDir(true);

  00032	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:__tls_array
  00038	8b 08		 mov	 ecx, DWORD PTR [eax]
  0003a	8b 15 00 00 00
	00		 mov	 edx, DWORD PTR ?$TSS0@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
  00040	3b 91 00 00 00
	00		 cmp	 edx, DWORD PTR __Init_thread_epoch[ecx]
  00046	7e 40		 jle	 SHORT $LN2@HookFlashC
  00048	68 00 00 00 00	 push	 OFFSET ?$TSS0@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
  0004d	e8 00 00 00 00	 call	 __Init_thread_header
  00052	83 c4 04	 add	 esp, 4
  00055	83 3d 00 00 00
	00 ff		 cmp	 DWORD PTR ?$TSS0@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA, -1
  0005c	75 2a		 jne	 SHORT $LN2@HookFlashC
  0005e	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00065	6a 01		 push	 1
  00067	e8 00 00 00 00	 call	 ?HookFlashDir@?A0xa937c340@@YA_N_N@Z ; `anonymous namespace'::HookFlashDir
  0006c	83 c4 04	 add	 esp, 4
  0006f	a2 00 00 00 00	 mov	 BYTE PTR ?bHooked@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4_NA, al
  00074	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0007b	68 00 00 00 00	 push	 OFFSET ?$TSS0@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
  00080	e8 00 00 00 00	 call	 __Init_thread_footer
  00085	83 c4 04	 add	 esp, 4
$LN2@HookFlashC:

; 135  : 		m_sAppDataDir = AddBackslash(sNewPath.c_str()); SHCreateDirectoryExA(nullptr, m_sAppDataDir.c_str(), nullptr);

  00088	8b 4d 08	 mov	 ecx, DWORD PTR _sNewPath$[ebp]
  0008b	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00090	50		 push	 eax
  00091	8d 45 dc	 lea	 eax, DWORD PTR $T6[ebp]
  00094	50		 push	 eax
  00095	e8 00 00 00 00	 call	 ?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z ; `anonymous namespace'::AddBackslash
  0009a	83 c4 08	 add	 esp, 8
  0009d	89 85 78 ff ff
	ff		 mov	 DWORD PTR tv162[ebp], eax
  000a3	8b 8d 78 ff ff
	ff		 mov	 ecx, DWORD PTR tv162[ebp]
  000a9	89 8d 74 ff ff
	ff		 mov	 DWORD PTR tv154[ebp], ecx
  000af	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
  000b6	8b 95 74 ff ff
	ff		 mov	 edx, DWORD PTR tv154[ebp]
  000bc	52		 push	 edx
  000bd	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
  000c2	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
  000c7	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000ce	8d 4d dc	 lea	 ecx, DWORD PTR $T6[ebp]
  000d1	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  000d6	8b f4		 mov	 esi, esp
  000d8	6a 00		 push	 0
  000da	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
  000df	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  000e4	50		 push	 eax
  000e5	6a 00		 push	 0
  000e7	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__SHCreateDirectoryExA@12
  000ed	3b f4		 cmp	 esi, esp
  000ef	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 136  : 		m_wsAppDataDir = JsApi::AnsiToUnicode(m_sAppDataDir.c_str());

  000f4	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
  000f9	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  000fe	50		 push	 eax
  000ff	8d 4d c4	 lea	 ecx, DWORD PTR $T5[ebp]
  00102	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00107	c7 45 fc 02 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 2
  0010e	8d 45 c4	 lea	 eax, DWORD PTR $T5[ebp]
  00111	50		 push	 eax
  00112	8d 4d ac	 lea	 ecx, DWORD PTR $T4[ebp]
  00115	51		 push	 ecx
  00116	e8 00 00 00 00	 call	 ?AnsiToUnicode@JsApi@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@@Z ; JsApi::AnsiToUnicode
  0011b	83 c4 08	 add	 esp, 8
  0011e	89 85 70 ff ff
	ff		 mov	 DWORD PTR tv91[ebp], eax
  00124	8b 95 70 ff ff
	ff		 mov	 edx, DWORD PTR tv91[ebp]
  0012a	89 95 6c ff ff
	ff		 mov	 DWORD PTR tv157[ebp], edx
  00130	c6 45 fc 03	 mov	 BYTE PTR __$EHRec$[ebp+8], 3
  00134	8b 85 6c ff ff
	ff		 mov	 eax, DWORD PTR tv157[ebp]
  0013a	50		 push	 eax
  0013b	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsAppDataDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsAppDataDir
  00140	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV01@$$QAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00145	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  00149	8d 4d ac	 lea	 ecx, DWORD PTR $T4[ebp]
  0014c	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00151	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00158	8d 4d c4	 lea	 ecx, DWORD PTR $T5[ebp]
  0015b	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

; 137  : 		m_sTempDir = m_sAppDataDir + "Temp\\"; SHCreateDirectoryExA(nullptr, m_sTempDir.c_str(), nullptr);

  00160	68 00 00 00 00	 push	 OFFSET $SG4294345728
  00165	68 00 00 00 00	 push	 OFFSET ?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
  0016a	8d 4d 94	 lea	 ecx, DWORD PTR $T3[ebp]
  0016d	51		 push	 ecx
  0016e	e8 00 00 00 00	 call	 ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z ; std::operator+<char,std::char_traits<char>,std::allocator<char> >
  00173	83 c4 0c	 add	 esp, 12			; 0000000cH
  00176	89 85 68 ff ff
	ff		 mov	 DWORD PTR tv163[ebp], eax
  0017c	8b 95 68 ff ff
	ff		 mov	 edx, DWORD PTR tv163[ebp]
  00182	89 95 64 ff ff
	ff		 mov	 DWORD PTR tv159[ebp], edx
  00188	c7 45 fc 04 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 4
  0018f	8b 85 64 ff ff
	ff		 mov	 eax, DWORD PTR tv159[ebp]
  00195	50		 push	 eax
  00196	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
  0019b	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
  001a0	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  001a7	8d 4d 94	 lea	 ecx, DWORD PTR $T3[ebp]
  001aa	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  001af	8b f4		 mov	 esi, esp
  001b1	6a 00		 push	 0
  001b3	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
  001b8	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  001bd	50		 push	 eax
  001be	6a 00		 push	 0
  001c0	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__SHCreateDirectoryExA@12
  001c6	3b f4		 cmp	 esi, esp
  001c8	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 138  : 		m_wsTempDir = JsApi::AnsiToUnicode(m_sTempDir);

  001cd	68 00 00 00 00	 push	 OFFSET ?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
  001d2	8d 8d 7c ff ff
	ff		 lea	 ecx, DWORD PTR $T2[ebp]
  001d8	51		 push	 ecx
  001d9	e8 00 00 00 00	 call	 ?AnsiToUnicode@JsApi@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@@Z ; JsApi::AnsiToUnicode
  001de	83 c4 08	 add	 esp, 8
  001e1	89 85 60 ff ff
	ff		 mov	 DWORD PTR tv164[ebp], eax
  001e7	8b 95 60 ff ff
	ff		 mov	 edx, DWORD PTR tv164[ebp]
  001ed	89 95 5c ff ff
	ff		 mov	 DWORD PTR tv161[ebp], edx
  001f3	c7 45 fc 05 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 5
  001fa	8b 85 5c ff ff
	ff		 mov	 eax, DWORD PTR tv161[ebp]
  00200	50		 push	 eax
  00201	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsTempDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsTempDir
  00206	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV01@$$QAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  0020b	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00212	8d 8d 7c ff ff
	ff		 lea	 ecx, DWORD PTR $T2[ebp]
  00218	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 139  : 
; 140  : 		return true;

  0021d	b0 01		 mov	 al, 1

; 141  : 	}

  0021f	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00222	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00229	5f		 pop	 edi
  0022a	5e		 pop	 esi
  0022b	81 c4 a4 00 00
	00		 add	 esp, 164		; 000000a4H
  00231	3b ec		 cmp	 ebp, esp
  00233	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00238	8b e5		 mov	 esp, ebp
  0023a	5d		 pop	 ebp
  0023b	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0:
  00000	68 00 00 00 00	 push	 OFFSET ?$TSS0@?1??HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
  00005	e8 00 00 00 00	 call	 __Init_thread_abort
  0000a	59		 pop	 ecx
  0000b	c3		 ret	 0
__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$1:
  0000c	8d 4d dc	 lea	 ecx, DWORD PTR $T6[ebp]
  0000f	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$2:
  00014	8d 4d c4	 lea	 ecx, DWORD PTR $T5[ebp]
  00017	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$3:
  0001c	8d 4d ac	 lea	 ecx, DWORD PTR $T4[ebp]
  0001f	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$4:
  00024	8d 4d 94	 lea	 ecx, DWORD PTR $T3[ebp]
  00027	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$5:
  0002c	8d 8d 7c ff ff
	ff		 lea	 ecx, DWORD PTR $T2[ebp]
  00032	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__ehhandler$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z:
  00037	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
  0003c	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ENDP ; JsApi::HookFlashCachePath
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\include\detours.h
_TEXT	SEGMENT
_ppOld$ = 8						; size = 4
_pNew$ = 12						; size = 4
_bHook$ = 16						; size = 4
?HookApi@@YAHPAPAXPAXH@Z PROC				; HookApi

; 502  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 503  : 	DetourTransactionBegin();

  00004	e8 00 00 00 00	 call	 _DetourTransactionBegin@0

; 504  : 	DetourUpdateThread(GetCurrentThread());

  00009	8b f4		 mov	 esi, esp
  0000b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetCurrentThread@0
  00011	3b f4		 cmp	 esi, esp
  00013	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00018	50		 push	 eax
  00019	e8 00 00 00 00	 call	 _DetourUpdateThread@4

; 505  : 	if(bHook){

  0001e	83 7d 10 00	 cmp	 DWORD PTR _bHook$[ebp], 0
  00022	74 0f		 je	 SHORT $LN2@HookApi

; 506  : 		DetourAttach(ppOld, pNew);

  00024	8b 45 0c	 mov	 eax, DWORD PTR _pNew$[ebp]
  00027	50		 push	 eax
  00028	8b 4d 08	 mov	 ecx, DWORD PTR _ppOld$[ebp]
  0002b	51		 push	 ecx
  0002c	e8 00 00 00 00	 call	 _DetourAttach@8

; 507  : 	}else{

  00031	eb 0d		 jmp	 SHORT $LN3@HookApi
$LN2@HookApi:

; 508  : 		DetourDetach(ppOld, pNew);

  00033	8b 55 0c	 mov	 edx, DWORD PTR _pNew$[ebp]
  00036	52		 push	 edx
  00037	8b 45 08	 mov	 eax, DWORD PTR _ppOld$[ebp]
  0003a	50		 push	 eax
  0003b	e8 00 00 00 00	 call	 _DetourDetach@8
$LN3@HookApi:

; 509  : 	}
; 510  : 	DetourTransactionCommit();

  00040	e8 00 00 00 00	 call	 _DetourTransactionCommit@0

; 511  : 	return TRUE;

  00045	b8 01 00 00 00	 mov	 eax, 1

; 512  : }

  0004a	5e		 pop	 esi
  0004b	3b ec		 cmp	 ebp, esp
  0004d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00052	5d		 pop	 ebp
  00053	c3		 ret	 0
?HookApi@@YAHPAPAXPAXH@Z ENDP				; HookApi
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Em_sAppDataDir@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Em_sAppDataDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic initializer for 'm_sAppDataDir'', COMDAT

; 11   : 	std::string m_sAppDataDir; std::wstring m_wsAppDataDir;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
  00008	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0000d	68 00 00 00 00	 push	 OFFSET ??__Fm_sAppDataDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::`dynamic atexit destructor for 'm_sAppDataDir''
  00012	e8 00 00 00 00	 call	 _atexit
  00017	83 c4 04	 add	 esp, 4
  0001a	3b ec		 cmp	 ebp, esp
  0001c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00021	5d		 pop	 ebp
  00022	c3		 ret	 0
??__Em_sAppDataDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic initializer for 'm_sAppDataDir''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__Fm_sAppDataDir@?A0xa937c340@@YAXXZ
text$yd	SEGMENT
??__Fm_sAppDataDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic atexit destructor for 'm_sAppDataDir'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
  00008	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__Fm_sAppDataDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic atexit destructor for 'm_sAppDataDir''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Em_wsAppDataDir@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Em_wsAppDataDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic initializer for 'm_wsAppDataDir'', COMDAT

; 11   : 	std::string m_sAppDataDir; std::wstring m_wsAppDataDir;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsAppDataDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsAppDataDir
  00008	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0000d	68 00 00 00 00	 push	 OFFSET ??__Fm_wsAppDataDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::`dynamic atexit destructor for 'm_wsAppDataDir''
  00012	e8 00 00 00 00	 call	 _atexit
  00017	83 c4 04	 add	 esp, 4
  0001a	3b ec		 cmp	 ebp, esp
  0001c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00021	5d		 pop	 ebp
  00022	c3		 ret	 0
??__Em_wsAppDataDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic initializer for 'm_wsAppDataDir''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__Fm_wsAppDataDir@?A0xa937c340@@YAXXZ
text$yd	SEGMENT
??__Fm_wsAppDataDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic atexit destructor for 'm_wsAppDataDir'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsAppDataDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsAppDataDir
  00008	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__Fm_wsAppDataDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic atexit destructor for 'm_wsAppDataDir''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Em_sTempDir@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Em_sTempDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic initializer for 'm_sTempDir'', COMDAT

; 12   : 	std::string m_sTempDir; std::wstring m_wsTempDir;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
  00008	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0000d	68 00 00 00 00	 push	 OFFSET ??__Fm_sTempDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::`dynamic atexit destructor for 'm_sTempDir''
  00012	e8 00 00 00 00	 call	 _atexit
  00017	83 c4 04	 add	 esp, 4
  0001a	3b ec		 cmp	 ebp, esp
  0001c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00021	5d		 pop	 ebp
  00022	c3		 ret	 0
??__Em_sTempDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic initializer for 'm_sTempDir''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__Fm_sTempDir@?A0xa937c340@@YAXXZ
text$yd	SEGMENT
??__Fm_sTempDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic atexit destructor for 'm_sTempDir'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
  00008	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__Fm_sTempDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic atexit destructor for 'm_sTempDir''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Em_wsTempDir@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Em_wsTempDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic initializer for 'm_wsTempDir'', COMDAT

; 12   : 	std::string m_sTempDir; std::wstring m_wsTempDir;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsTempDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsTempDir
  00008	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0000d	68 00 00 00 00	 push	 OFFSET ??__Fm_wsTempDir@?A0xa937c340@@YAXXZ ; `anonymous namespace'::`dynamic atexit destructor for 'm_wsTempDir''
  00012	e8 00 00 00 00	 call	 _atexit
  00017	83 c4 04	 add	 esp, 4
  0001a	3b ec		 cmp	 ebp, esp
  0001c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00021	5d		 pop	 ebp
  00022	c3		 ret	 0
??__Em_wsTempDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic initializer for 'm_wsTempDir''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__Fm_wsTempDir@?A0xa937c340@@YAXXZ
text$yd	SEGMENT
??__Fm_wsTempDir@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic atexit destructor for 'm_wsTempDir'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsTempDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsTempDir
  00008	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__Fm_wsTempDir@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic atexit destructor for 'm_wsTempDir''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
$T2 = -52						; size = 4
_size$ = -48						; size = 4
_spath$ = -40						; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_path$ = 12						; size = 4
?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z PROC ; `anonymous namespace'::AddBackslash

; 14   : 	std::string AddBackslash(const char* path) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 28	 sub	 esp, 40			; 00000028H
  0001b	57		 push	 edi
  0001c	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  0001f	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 cc 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 15   : 		std::string spath; if (path) spath = path;

  00032	8d 4d d8	 lea	 ecx, DWORD PTR _spath$[ebp]
  00035	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0003a	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
  00041	83 7d 0c 00	 cmp	 DWORD PTR _path$[ebp], 0
  00045	74 0c		 je	 SHORT $LN2@AddBacksla
  00047	8b 45 0c	 mov	 eax, DWORD PTR _path$[ebp]
  0004a	50		 push	 eax
  0004b	8d 4d d8	 lea	 ecx, DWORD PTR _spath$[ebp]
  0004e	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
$LN2@AddBacksla:

; 16   : 		size_t size = spath.size();

  00053	8d 4d d8	 lea	 ecx, DWORD PTR _spath$[ebp]
  00056	e8 00 00 00 00	 call	 ?size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::size
  0005b	89 45 d0	 mov	 DWORD PTR _size$[ebp], eax

; 17   : 		if (!size || spath[size - 1] != '\\') {

  0005e	83 7d d0 00	 cmp	 DWORD PTR _size$[ebp], 0
  00062	74 17		 je	 SHORT $LN4@AddBacksla
  00064	8b 4d d0	 mov	 ecx, DWORD PTR _size$[ebp]
  00067	83 e9 01	 sub	 ecx, 1
  0006a	51		 push	 ecx
  0006b	8d 4d d8	 lea	 ecx, DWORD PTR _spath$[ebp]
  0006e	e8 00 00 00 00	 call	 ??A?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAADI@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator[]
  00073	0f be 10	 movsx	 edx, BYTE PTR [eax]
  00076	83 fa 5c	 cmp	 edx, 92			; 0000005cH
  00079	74 0d		 je	 SHORT $LN3@AddBacksla
$LN4@AddBacksla:

; 18   : 			spath += "\\";

  0007b	68 00 00 00 00	 push	 OFFSET $SG4294345731
  00080	8d 4d d8	 lea	 ecx, DWORD PTR _spath$[ebp]
  00083	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=
$LN3@AddBacksla:

; 19   : 		}
; 20   : 		return spath;

  00088	8d 45 d8	 lea	 eax, DWORD PTR _spath$[ebp]
  0008b	50		 push	 eax
  0008c	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0008f	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00094	8b 4d cc	 mov	 ecx, DWORD PTR $T2[ebp]
  00097	83 c9 01	 or	 ecx, 1
  0009a	89 4d cc	 mov	 DWORD PTR $T2[ebp], ecx
  0009d	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000a1	8d 4d d8	 lea	 ecx, DWORD PTR _spath$[ebp]
  000a4	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  000a9	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 21   : 	}

  000ac	52		 push	 edx
  000ad	8b cd		 mov	 ecx, ebp
  000af	50		 push	 eax
  000b0	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN12@AddBacksla
  000b6	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000bb	58		 pop	 eax
  000bc	5a		 pop	 edx
  000bd	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000c0	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000c7	5f		 pop	 edi
  000c8	83 c4 34	 add	 esp, 52			; 00000034H
  000cb	3b ec		 cmp	 ebp, esp
  000cd	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000d2	8b e5		 mov	 esp, ebp
  000d4	5d		 pop	 ebp
  000d5	c3		 ret	 0
  000d6	66 90		 npad	 2
$LN12@AddBacksla:
  000d8	01 00 00 00	 DD	 1
  000dc	00 00 00 00	 DD	 $LN11@AddBacksla
$LN11@AddBacksla:
  000e0	d8 ff ff ff	 DD	 -40			; ffffffd8H
  000e4	18 00 00 00	 DD	 24			; 00000018H
  000e8	00 00 00 00	 DD	 $LN9@AddBacksla
$LN9@AddBacksla:
  000ec	73		 DB	 115			; 00000073H
  000ed	70		 DB	 112			; 00000070H
  000ee	61		 DB	 97			; 00000061H
  000ef	74		 DB	 116			; 00000074H
  000f0	68		 DB	 104			; 00000068H
  000f1	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _spath$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z$1:
  00008	8b 45 cc	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN8@AddBacksla
  00014	83 65 cc fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN8@AddBacksla:
  00020	c3		 ret	 0
__ehhandler$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?AddBackslash@?A0xa937c340@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PBD@Z ENDP ; `anonymous namespace'::AddBackslash
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
_sName$1 = -572						; size = 260
_i$2 = -308						; size = 2
_mbi$ = -300						; size = 28
_wFrames$ = -268					; size = 2
_pFrames$ = -260					; size = 248
_nMaxFrames$ = -8					; size = 2
_bret$ = -4						; size = 4
?IsCallFromFlash@?A0xa937c340@@YAHXZ PROC		; `anonymous namespace'::IsCallFromFlash

; 24   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	81 ec 40 02 00
	00		 sub	 esp, 576		; 00000240H
  00009	56		 push	 esi
  0000a	57		 push	 edi
  0000b	8d bd c0 fd ff
	ff		 lea	 edi, DWORD PTR [ebp-576]
  00011	b9 90 00 00 00	 mov	 ecx, 144		; 00000090H
  00016	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0001b	f3 ab		 rep stosd

; 25   : 		BOOL bret = FALSE;

  0001d	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR _bret$[ebp], 0

; 26   : 		const WORD nMaxFrames = 62;

  00024	b8 3e 00 00 00	 mov	 eax, 62			; 0000003eH
  00029	66 89 45 f8	 mov	 WORD PTR _nMaxFrames$[ebp], ax

; 27   : 		PVOID pFrames[nMaxFrames] = { NULL };

  0002d	68 f8 00 00 00	 push	 248			; 000000f8H
  00032	6a 00		 push	 0
  00034	8d 8d fc fe ff
	ff		 lea	 ecx, DWORD PTR _pFrames$[ebp]
  0003a	51		 push	 ecx
  0003b	e8 00 00 00 00	 call	 _memset
  00040	83 c4 0c	 add	 esp, 12			; 0000000cH

; 28   : 		WORD wFrames = RtlCaptureStackBackTrace(0, nMaxFrames, pFrames, NULL);

  00043	8b f4		 mov	 esi, esp
  00045	6a 00		 push	 0
  00047	8d 95 fc fe ff
	ff		 lea	 edx, DWORD PTR _pFrames$[ebp]
  0004d	52		 push	 edx
  0004e	6a 3e		 push	 62			; 0000003eH
  00050	6a 00		 push	 0
  00052	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__RtlCaptureStackBackTrace@16
  00058	3b f4		 cmp	 esi, esp
  0005a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005f	66 89 85 f4 fe
	ff ff		 mov	 WORD PTR _wFrames$[ebp], ax

; 29   : 		MEMORY_BASIC_INFORMATION mbi = { 0 };

  00066	33 c0		 xor	 eax, eax
  00068	89 85 d4 fe ff
	ff		 mov	 DWORD PTR _mbi$[ebp], eax
  0006e	89 85 d8 fe ff
	ff		 mov	 DWORD PTR _mbi$[ebp+4], eax
  00074	89 85 dc fe ff
	ff		 mov	 DWORD PTR _mbi$[ebp+8], eax
  0007a	89 85 e0 fe ff
	ff		 mov	 DWORD PTR _mbi$[ebp+12], eax
  00080	89 85 e4 fe ff
	ff		 mov	 DWORD PTR _mbi$[ebp+16], eax
  00086	89 85 e8 fe ff
	ff		 mov	 DWORD PTR _mbi$[ebp+20], eax
  0008c	89 85 ec fe ff
	ff		 mov	 DWORD PTR _mbi$[ebp+24], eax

; 30   : 		for (WORD i = 0; i<wFrames; i++)

  00092	33 c9		 xor	 ecx, ecx
  00094	66 89 8d cc fe
	ff ff		 mov	 WORD PTR _i$2[ebp], cx
  0009b	eb 12		 jmp	 SHORT $LN4@IsCallFrom
$LN2@IsCallFrom:
  0009d	66 8b 95 cc fe
	ff ff		 mov	 dx, WORD PTR _i$2[ebp]
  000a4	66 83 c2 01	 add	 dx, 1
  000a8	66 89 95 cc fe
	ff ff		 mov	 WORD PTR _i$2[ebp], dx
$LN4@IsCallFrom:
  000af	0f b7 85 cc fe
	ff ff		 movzx	 eax, WORD PTR _i$2[ebp]
  000b6	0f b7 8d f4 fe
	ff ff		 movzx	 ecx, WORD PTR _wFrames$[ebp]
  000bd	3b c1		 cmp	 eax, ecx
  000bf	0f 8d c3 00 00
	00		 jge	 $LN3@IsCallFrom

; 31   : 		{
; 32   : 			if (!pFrames[i]) break;

  000c5	0f b7 95 cc fe
	ff ff		 movzx	 edx, WORD PTR _i$2[ebp]
  000cc	83 bc 95 fc fe
	ff ff 00	 cmp	 DWORD PTR _pFrames$[ebp+edx*4], 0
  000d4	75 05		 jne	 SHORT $LN5@IsCallFrom
  000d6	e9 ad 00 00 00	 jmp	 $LN3@IsCallFrom
$LN5@IsCallFrom:

; 33   : 			if (VirtualQuery(pFrames[i], &mbi, sizeof(mbi)))

  000db	8b f4		 mov	 esi, esp
  000dd	6a 1c		 push	 28			; 0000001cH
  000df	8d 85 d4 fe ff
	ff		 lea	 eax, DWORD PTR _mbi$[ebp]
  000e5	50		 push	 eax
  000e6	0f b7 8d cc fe
	ff ff		 movzx	 ecx, WORD PTR _i$2[ebp]
  000ed	8b 94 8d fc fe
	ff ff		 mov	 edx, DWORD PTR _pFrames$[ebp+ecx*4]
  000f4	52		 push	 edx
  000f5	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__VirtualQuery@12
  000fb	3b f4		 cmp	 esi, esp
  000fd	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00102	85 c0		 test	 eax, eax
  00104	74 7d		 je	 SHORT $LN6@IsCallFrom

; 34   : 			{
; 35   : 				char sName[MAX_PATH] = { 0 };

  00106	68 04 01 00 00	 push	 260			; 00000104H
  0010b	6a 00		 push	 0
  0010d	8d 85 c4 fd ff
	ff		 lea	 eax, DWORD PTR _sName$1[ebp]
  00113	50		 push	 eax
  00114	e8 00 00 00 00	 call	 _memset
  00119	83 c4 0c	 add	 esp, 12			; 0000000cH

; 36   : 				GetModuleFileNameA((HMODULE)mbi.AllocationBase, sName, MAX_PATH);

  0011c	8b f4		 mov	 esi, esp
  0011e	68 04 01 00 00	 push	 260			; 00000104H
  00123	8d 8d c4 fd ff
	ff		 lea	 ecx, DWORD PTR _sName$1[ebp]
  00129	51		 push	 ecx
  0012a	8b 95 d8 fe ff
	ff		 mov	 edx, DWORD PTR _mbi$[ebp+4]
  00130	52		 push	 edx
  00131	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetModuleFileNameA@12
  00137	3b f4		 cmp	 esi, esp
  00139	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 37   : 				if (StrStrIA(sName, "\\Macromed\\Flash\\")
; 38   : 					||StrStrIA(sName, "\\npswf")

  0013e	8b f4		 mov	 esi, esp
  00140	68 00 00 00 00	 push	 OFFSET $SG4294345730
  00145	8d 85 c4 fd ff
	ff		 lea	 eax, DWORD PTR _sName$1[ebp]
  0014b	50		 push	 eax
  0014c	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__StrStrIA@8
  00152	3b f4		 cmp	 esi, esp
  00154	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00159	85 c0		 test	 eax, eax
  0015b	75 1f		 jne	 SHORT $LN8@IsCallFrom
  0015d	8b f4		 mov	 esi, esp
  0015f	68 00 00 00 00	 push	 OFFSET $SG4294345729
  00164	8d 8d c4 fd ff
	ff		 lea	 ecx, DWORD PTR _sName$1[ebp]
  0016a	51		 push	 ecx
  0016b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__StrStrIA@8
  00171	3b f4		 cmp	 esi, esp
  00173	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00178	85 c0		 test	 eax, eax
  0017a	74 07		 je	 SHORT $LN6@IsCallFrom
$LN8@IsCallFrom:

; 39   : 					) {
; 40   : 					//OutputDebugStringA(sName);
; 41   : 					return TRUE;

  0017c	b8 01 00 00 00	 mov	 eax, 1
  00181	eb 08		 jmp	 SHORT $LN1@IsCallFrom
$LN6@IsCallFrom:

; 42   : 				}
; 43   : 			}
; 44   : 		}

  00183	e9 15 ff ff ff	 jmp	 $LN2@IsCallFrom
$LN3@IsCallFrom:

; 45   : 		return bret;

  00188	8b 45 fc	 mov	 eax, DWORD PTR _bret$[ebp]
$LN1@IsCallFrom:

; 46   : 	}

  0018b	52		 push	 edx
  0018c	8b cd		 mov	 ecx, ebp
  0018e	50		 push	 eax
  0018f	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN14@IsCallFrom
  00195	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0019a	58		 pop	 eax
  0019b	5a		 pop	 edx
  0019c	5f		 pop	 edi
  0019d	5e		 pop	 esi
  0019e	81 c4 40 02 00
	00		 add	 esp, 576		; 00000240H
  001a4	3b ec		 cmp	 ebp, esp
  001a6	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001ab	8b e5		 mov	 esp, ebp
  001ad	5d		 pop	 ebp
  001ae	c3		 ret	 0
  001af	90		 npad	 1
$LN14@IsCallFrom:
  001b0	03 00 00 00	 DD	 3
  001b4	00 00 00 00	 DD	 $LN13@IsCallFrom
$LN13@IsCallFrom:
  001b8	fc fe ff ff	 DD	 -260			; fffffefcH
  001bc	f8 00 00 00	 DD	 248			; 000000f8H
  001c0	00 00 00 00	 DD	 $LN10@IsCallFrom
  001c4	d4 fe ff ff	 DD	 -300			; fffffed4H
  001c8	1c 00 00 00	 DD	 28			; 0000001cH
  001cc	00 00 00 00	 DD	 $LN11@IsCallFrom
  001d0	c4 fd ff ff	 DD	 -572			; fffffdc4H
  001d4	04 01 00 00	 DD	 260			; 00000104H
  001d8	00 00 00 00	 DD	 $LN12@IsCallFrom
$LN12@IsCallFrom:
  001dc	73		 DB	 115			; 00000073H
  001dd	4e		 DB	 78			; 0000004eH
  001de	61		 DB	 97			; 00000061H
  001df	6d		 DB	 109			; 0000006dH
  001e0	65		 DB	 101			; 00000065H
  001e1	00		 DB	 0
$LN11@IsCallFrom:
  001e2	6d		 DB	 109			; 0000006dH
  001e3	62		 DB	 98			; 00000062H
  001e4	69		 DB	 105			; 00000069H
  001e5	00		 DB	 0
$LN10@IsCallFrom:
  001e6	70		 DB	 112			; 00000070H
  001e7	46		 DB	 70			; 00000046H
  001e8	72		 DB	 114			; 00000072H
  001e9	61		 DB	 97			; 00000061H
  001ea	6d		 DB	 109			; 0000006dH
  001eb	65		 DB	 101			; 00000065H
  001ec	73		 DB	 115			; 00000073H
  001ed	00		 DB	 0
?IsCallFromFlash@?A0xa937c340@@YAHXZ ENDP		; `anonymous namespace'::IsCallFromFlash
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Eold_SHGetFolderPathW@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Eold_SHGetFolderPathW@?A0xa937c340@@YAXXZ PROC	; `anonymous namespace'::`dynamic initializer for 'old_SHGetFolderPathW'', COMDAT

; 49   : 	HRESULT(WINAPI *old_SHGetFolderPathW)(HWND hwndOwner, int nFolder, HANDLE hToken, DWORD dwFlags, LPWSTR pszPath) = SHGetFolderPathW;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	a1 00 00 00 00	 mov	 eax, DWORD PTR __imp__SHGetFolderPathW@20
  00008	a3 00 00 00 00	 mov	 DWORD PTR ?old_SHGetFolderPathW@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPA_W@ZA, eax ; `anonymous namespace'::old_SHGetFolderPathW
  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
??__Eold_SHGetFolderPathW@?A0xa937c340@@YAXXZ ENDP	; `anonymous namespace'::`dynamic initializer for 'old_SHGetFolderPathW''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
_hwndOwner$ = 8						; size = 4
_nFolder$ = 12						; size = 4
_hToken$ = 16						; size = 4
_dwFlags$ = 20						; size = 4
_pszPath$ = 24						; size = 4
?my_SHGetFolderPathW@?A0xa937c340@@YGJPAUHWND__@@HPAXKPA_W@Z PROC ; `anonymous namespace'::my_SHGetFolderPathW

; 51   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 52   : 		if ((nFolder & CSIDL_APPDATA)||(nFolder & CSIDL_LOCAL_APPDATA))

  00004	8b 45 0c	 mov	 eax, DWORD PTR _nFolder$[ebp]
  00007	83 e0 1a	 and	 eax, 26			; 0000001aH
  0000a	75 08		 jne	 SHORT $LN3@my_SHGetFo
  0000c	8b 4d 0c	 mov	 ecx, DWORD PTR _nFolder$[ebp]
  0000f	83 e1 1c	 and	 ecx, 28			; 0000001cH
  00012	74 31		 je	 SHORT $LN2@my_SHGetFo
$LN3@my_SHGetFo:

; 53   : 		{
; 54   : 			if(pszPath && IsCallFromFlash())

  00014	83 7d 18 00	 cmp	 DWORD PTR _pszPath$[ebp], 0
  00018	74 2b		 je	 SHORT $LN2@my_SHGetFo
  0001a	e8 00 00 00 00	 call	 ?IsCallFromFlash@?A0xa937c340@@YAHXZ ; `anonymous namespace'::IsCallFromFlash
  0001f	85 c0		 test	 eax, eax
  00021	74 22		 je	 SHORT $LN2@my_SHGetFo

; 55   : 			{
; 56   : 				lstrcpyW(pszPath, m_wsAppDataDir.c_str());

  00023	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsAppDataDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsAppDataDir
  00028	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEPB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  0002d	8b f4		 mov	 esi, esp
  0002f	50		 push	 eax
  00030	8b 55 18	 mov	 edx, DWORD PTR _pszPath$[ebp]
  00033	52		 push	 edx
  00034	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcpyW@8
  0003a	3b f4		 cmp	 esi, esp
  0003c	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 57   : 				return S_OK;

  00041	33 c0		 xor	 eax, eax
  00043	eb 23		 jmp	 SHORT $LN1@my_SHGetFo
$LN2@my_SHGetFo:

; 58   : 			}
; 59   : 		}
; 60   : 
; 61   : 		return old_SHGetFolderPathW(hwndOwner, nFolder, hToken, dwFlags, pszPath);

  00045	8b f4		 mov	 esi, esp
  00047	8b 45 18	 mov	 eax, DWORD PTR _pszPath$[ebp]
  0004a	50		 push	 eax
  0004b	8b 4d 14	 mov	 ecx, DWORD PTR _dwFlags$[ebp]
  0004e	51		 push	 ecx
  0004f	8b 55 10	 mov	 edx, DWORD PTR _hToken$[ebp]
  00052	52		 push	 edx
  00053	8b 45 0c	 mov	 eax, DWORD PTR _nFolder$[ebp]
  00056	50		 push	 eax
  00057	8b 4d 08	 mov	 ecx, DWORD PTR _hwndOwner$[ebp]
  0005a	51		 push	 ecx
  0005b	ff 15 00 00 00
	00		 call	 DWORD PTR ?old_SHGetFolderPathW@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPA_W@ZA ; `anonymous namespace'::old_SHGetFolderPathW
  00061	3b f4		 cmp	 esi, esp
  00063	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@my_SHGetFo:

; 62   : 	}

  00068	5e		 pop	 esi
  00069	3b ec		 cmp	 ebp, esp
  0006b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00070	5d		 pop	 ebp
  00071	c2 14 00	 ret	 20			; 00000014H
?my_SHGetFolderPathW@?A0xa937c340@@YGJPAUHWND__@@HPAXKPA_W@Z ENDP ; `anonymous namespace'::my_SHGetFolderPathW
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Eold_SHGetFolderPathA@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Eold_SHGetFolderPathA@?A0xa937c340@@YAXXZ PROC	; `anonymous namespace'::`dynamic initializer for 'old_SHGetFolderPathA'', COMDAT

; 64   : 	HRESULT(WINAPI *old_SHGetFolderPathA)(HWND hwndOwner, int nFolder, HANDLE hToken, DWORD dwFlags, LPSTR pszPath) = SHGetFolderPathA;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	a1 00 00 00 00	 mov	 eax, DWORD PTR __imp__SHGetFolderPathA@20
  00008	a3 00 00 00 00	 mov	 DWORD PTR ?old_SHGetFolderPathA@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPAD@ZA, eax ; `anonymous namespace'::old_SHGetFolderPathA
  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
??__Eold_SHGetFolderPathA@?A0xa937c340@@YAXXZ ENDP	; `anonymous namespace'::`dynamic initializer for 'old_SHGetFolderPathA''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
_hwndOwner$ = 8						; size = 4
_nFolder$ = 12						; size = 4
_hToken$ = 16						; size = 4
_dwFlags$ = 20						; size = 4
_pszPath$ = 24						; size = 4
?my_SHGetFolderPathA@?A0xa937c340@@YGJPAUHWND__@@HPAXKPAD@Z PROC ; `anonymous namespace'::my_SHGetFolderPathA

; 66   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 67   : 
; 68   : 		if ((nFolder & CSIDL_APPDATA) || (nFolder & CSIDL_LOCAL_APPDATA))

  00004	8b 45 0c	 mov	 eax, DWORD PTR _nFolder$[ebp]
  00007	83 e0 1a	 and	 eax, 26			; 0000001aH
  0000a	75 08		 jne	 SHORT $LN3@my_SHGetFo
  0000c	8b 4d 0c	 mov	 ecx, DWORD PTR _nFolder$[ebp]
  0000f	83 e1 1c	 and	 ecx, 28			; 0000001cH
  00012	74 31		 je	 SHORT $LN2@my_SHGetFo
$LN3@my_SHGetFo:

; 69   : 		{
; 70   : 			if (pszPath && IsCallFromFlash())

  00014	83 7d 18 00	 cmp	 DWORD PTR _pszPath$[ebp], 0
  00018	74 2b		 je	 SHORT $LN2@my_SHGetFo
  0001a	e8 00 00 00 00	 call	 ?IsCallFromFlash@?A0xa937c340@@YAHXZ ; `anonymous namespace'::IsCallFromFlash
  0001f	85 c0		 test	 eax, eax
  00021	74 22		 je	 SHORT $LN2@my_SHGetFo

; 71   : 			{
; 72   : 				lstrcpyA(pszPath, m_sAppDataDir.c_str());

  00023	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sAppDataDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sAppDataDir
  00028	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  0002d	8b f4		 mov	 esi, esp
  0002f	50		 push	 eax
  00030	8b 55 18	 mov	 edx, DWORD PTR _pszPath$[ebp]
  00033	52		 push	 edx
  00034	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcpyA@8
  0003a	3b f4		 cmp	 esi, esp
  0003c	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 73   : 				return S_OK;				

  00041	33 c0		 xor	 eax, eax
  00043	eb 23		 jmp	 SHORT $LN1@my_SHGetFo
$LN2@my_SHGetFo:

; 74   : 			}
; 75   : 		}
; 76   : 
; 77   : 		return old_SHGetFolderPathA(hwndOwner, nFolder, hToken, dwFlags, pszPath);

  00045	8b f4		 mov	 esi, esp
  00047	8b 45 18	 mov	 eax, DWORD PTR _pszPath$[ebp]
  0004a	50		 push	 eax
  0004b	8b 4d 14	 mov	 ecx, DWORD PTR _dwFlags$[ebp]
  0004e	51		 push	 ecx
  0004f	8b 55 10	 mov	 edx, DWORD PTR _hToken$[ebp]
  00052	52		 push	 edx
  00053	8b 45 0c	 mov	 eax, DWORD PTR _nFolder$[ebp]
  00056	50		 push	 eax
  00057	8b 4d 08	 mov	 ecx, DWORD PTR _hwndOwner$[ebp]
  0005a	51		 push	 ecx
  0005b	ff 15 00 00 00
	00		 call	 DWORD PTR ?old_SHGetFolderPathA@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPAD@ZA ; `anonymous namespace'::old_SHGetFolderPathA
  00061	3b f4		 cmp	 esi, esp
  00063	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@my_SHGetFo:

; 78   : 	}

  00068	5e		 pop	 esi
  00069	3b ec		 cmp	 ebp, esp
  0006b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00070	5d		 pop	 ebp
  00071	c2 14 00	 ret	 20			; 00000014H
?my_SHGetFolderPathA@?A0xa937c340@@YGJPAUHWND__@@HPAXKPAD@Z ENDP ; `anonymous namespace'::my_SHGetFolderPathA
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Eold_GetTempPathA@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Eold_GetTempPathA@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic initializer for 'old_GetTempPathA'', COMDAT

; 80   : 	DWORD(WINAPI *old_GetTempPathA)(DWORD nBufferLength, LPSTR lpBuffer) = GetTempPathA;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	a1 00 00 00 00	 mov	 eax, DWORD PTR __imp__GetTempPathA@8
  00008	a3 00 00 00 00	 mov	 DWORD PTR ?old_GetTempPathA@?A0xa937c340@@3P6GKKPAD@ZA, eax ; `anonymous namespace'::old_GetTempPathA
  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
??__Eold_GetTempPathA@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic initializer for 'old_GetTempPathA''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
_nSize$1 = -8						; size = 4
_dwRet$ = -4						; size = 4
_nBufferLength$ = 8					; size = 4
_lpBuffer$ = 12						; size = 4
?my_GetTempPathA@?A0xa937c340@@YGKKPAD@Z PROC		; `anonymous namespace'::my_GetTempPathA

; 81   : 	DWORD (WINAPI my_GetTempPathA)(DWORD nBufferLength, LPSTR lpBuffer) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 82   : 		DWORD dwRet = 0;

  00015	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR _dwRet$[ebp], 0

; 83   : 		if (IsCallFromFlash()) {

  0001c	e8 00 00 00 00	 call	 ?IsCallFromFlash@?A0xa937c340@@YAHXZ ; `anonymous namespace'::IsCallFromFlash
  00021	85 c0		 test	 eax, eax
  00023	74 44		 je	 SHORT $LN2@my_GetTemp

; 84   : 			DWORD nSize = m_sTempDir.size();

  00025	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
  0002a	e8 00 00 00 00	 call	 ?size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::size
  0002f	89 45 f8	 mov	 DWORD PTR _nSize$1[ebp], eax

; 85   : 			if (lpBuffer) {

  00032	83 7d 0c 00	 cmp	 DWORD PTR _lpBuffer$[ebp], 0
  00036	74 29		 je	 SHORT $LN3@my_GetTemp

; 86   : 				lstrcpynA(lpBuffer, m_sTempDir.c_str(), nBufferLength);

  00038	8b f4		 mov	 esi, esp
  0003a	8b 45 08	 mov	 eax, DWORD PTR _nBufferLength$[ebp]
  0003d	50		 push	 eax
  0003e	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_sTempDir@?A0xa937c340@@3V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@A ; `anonymous namespace'::m_sTempDir
  00043	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00048	50		 push	 eax
  00049	8b 4d 0c	 mov	 ecx, DWORD PTR _lpBuffer$[ebp]
  0004c	51		 push	 ecx
  0004d	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcpynA@12
  00053	3b f4		 cmp	 esi, esp
  00055	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 87   : 				return nSize;

  0005a	8b 45 f8	 mov	 eax, DWORD PTR _nSize$1[ebp]
  0005d	eb 21		 jmp	 SHORT $LN1@my_GetTemp

; 88   : 			}else{

  0005f	eb 08		 jmp	 SHORT $LN2@my_GetTemp
$LN3@my_GetTemp:

; 89   : 				return nSize + 1;

  00061	8b 45 f8	 mov	 eax, DWORD PTR _nSize$1[ebp]
  00064	83 c0 01	 add	 eax, 1
  00067	eb 17		 jmp	 SHORT $LN1@my_GetTemp
$LN2@my_GetTemp:

; 90   : 			}
; 91   : 		}
; 92   : 		return old_GetTempPathA(nBufferLength, lpBuffer);

  00069	8b f4		 mov	 esi, esp
  0006b	8b 55 0c	 mov	 edx, DWORD PTR _lpBuffer$[ebp]
  0006e	52		 push	 edx
  0006f	8b 45 08	 mov	 eax, DWORD PTR _nBufferLength$[ebp]
  00072	50		 push	 eax
  00073	ff 15 00 00 00
	00		 call	 DWORD PTR ?old_GetTempPathA@?A0xa937c340@@3P6GKKPAD@ZA ; `anonymous namespace'::old_GetTempPathA
  00079	3b f4		 cmp	 esi, esp
  0007b	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@my_GetTemp:

; 93   : 	}

  00080	5e		 pop	 esi
  00081	83 c4 08	 add	 esp, 8
  00084	3b ec		 cmp	 ebp, esp
  00086	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0008b	8b e5		 mov	 esp, ebp
  0008d	5d		 pop	 ebp
  0008e	c2 08 00	 ret	 8
?my_GetTempPathA@?A0xa937c340@@YGKKPAD@Z ENDP		; `anonymous namespace'::my_GetTempPathA
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
;	COMDAT ??__Eold_GetTempPathW@?A0xa937c340@@YAXXZ
text$di	SEGMENT
??__Eold_GetTempPathW@?A0xa937c340@@YAXXZ PROC		; `anonymous namespace'::`dynamic initializer for 'old_GetTempPathW'', COMDAT

; 95   : 	DWORD(WINAPI *old_GetTempPathW)(DWORD nBufferLength, LPWSTR lpBuffer) = GetTempPathW;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	a1 00 00 00 00	 mov	 eax, DWORD PTR __imp__GetTempPathW@8
  00008	a3 00 00 00 00	 mov	 DWORD PTR ?old_GetTempPathW@?A0xa937c340@@3P6GKKPA_W@ZA, eax ; `anonymous namespace'::old_GetTempPathW
  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
??__Eold_GetTempPathW@?A0xa937c340@@YAXXZ ENDP		; `anonymous namespace'::`dynamic initializer for 'old_GetTempPathW''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
_nSize$ = -4						; size = 4
_nBufferLength$ = 8					; size = 4
_lpBuffer$ = 12						; size = 4
?my_GetTempPathW@?A0xa937c340@@YGKKPA_W@Z PROC		; `anonymous namespace'::my_GetTempPathW

; 96   : 	DWORD(WINAPI my_GetTempPathW)(DWORD nBufferLength, LPWSTR lpBuffer) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 97   : 		DWORD nSize = m_wsTempDir.size();

  0000c	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsTempDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsTempDir
  00011	e8 00 00 00 00	 call	 ?size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::size
  00016	89 45 fc	 mov	 DWORD PTR _nSize$[ebp], eax

; 98   : 		if (lpBuffer) {

  00019	83 7d 0c 00	 cmp	 DWORD PTR _lpBuffer$[ebp], 0
  0001d	74 29		 je	 SHORT $LN2@my_GetTemp

; 99   : 			lstrcpynW(lpBuffer, m_wsTempDir.c_str(), nBufferLength);

  0001f	8b f4		 mov	 esi, esp
  00021	8b 45 08	 mov	 eax, DWORD PTR _nBufferLength$[ebp]
  00024	50		 push	 eax
  00025	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_wsTempDir@?A0xa937c340@@3V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@A ; `anonymous namespace'::m_wsTempDir
  0002a	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEPB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  0002f	50		 push	 eax
  00030	8b 4d 0c	 mov	 ecx, DWORD PTR _lpBuffer$[ebp]
  00033	51		 push	 ecx
  00034	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcpynW@12
  0003a	3b f4		 cmp	 esi, esp
  0003c	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 100  : 			return nSize;

  00041	8b 45 fc	 mov	 eax, DWORD PTR _nSize$[ebp]
  00044	eb 21		 jmp	 SHORT $LN1@my_GetTemp

; 101  : 		}
; 102  : 		else {

  00046	eb 08		 jmp	 SHORT $LN3@my_GetTemp
$LN2@my_GetTemp:

; 103  : 			return nSize + 1;

  00048	8b 45 fc	 mov	 eax, DWORD PTR _nSize$[ebp]
  0004b	83 c0 01	 add	 eax, 1
  0004e	eb 17		 jmp	 SHORT $LN1@my_GetTemp
$LN3@my_GetTemp:

; 104  : 		}
; 105  : 		return old_GetTempPathW(nBufferLength, lpBuffer);

  00050	8b f4		 mov	 esi, esp
  00052	8b 55 0c	 mov	 edx, DWORD PTR _lpBuffer$[ebp]
  00055	52		 push	 edx
  00056	8b 45 08	 mov	 eax, DWORD PTR _nBufferLength$[ebp]
  00059	50		 push	 eax
  0005a	ff 15 00 00 00
	00		 call	 DWORD PTR ?old_GetTempPathW@?A0xa937c340@@3P6GKKPA_W@ZA ; `anonymous namespace'::old_GetTempPathW
  00060	3b f4		 cmp	 esi, esp
  00062	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@my_GetTemp:

; 106  : 	}

  00067	5e		 pop	 esi
  00068	83 c4 04	 add	 esp, 4
  0006b	3b ec		 cmp	 ebp, esp
  0006d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00072	8b e5		 mov	 esp, ebp
  00074	5d		 pop	 ebp
  00075	c2 08 00	 ret	 8
?my_GetTempPathW@?A0xa937c340@@YGKKPA_W@Z ENDP		; `anonymous namespace'::my_GetTempPathW
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
_bHook$ = 8						; size = 1
?HookFlashDir@?A0xa937c340@@YA_N_N@Z PROC		; `anonymous namespace'::HookFlashDir

; 109  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 110  : 		HookApi(&(PVOID&)old_SHGetFolderPathW, my_SHGetFolderPathW, bHook);	HookApi(&(PVOID&)old_SHGetFolderPathA, my_SHGetFolderPathA, bHook);

  00003	0f b6 45 08	 movzx	 eax, BYTE PTR _bHook$[ebp]
  00007	50		 push	 eax
  00008	68 00 00 00 00	 push	 OFFSET ?my_SHGetFolderPathW@?A0xa937c340@@YGJPAUHWND__@@HPAXKPA_W@Z ; `anonymous namespace'::my_SHGetFolderPathW
  0000d	68 00 00 00 00	 push	 OFFSET ?old_SHGetFolderPathW@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPA_W@ZA ; `anonymous namespace'::old_SHGetFolderPathW
  00012	e8 00 00 00 00	 call	 ?HookApi@@YAHPAPAXPAXH@Z ; HookApi
  00017	83 c4 0c	 add	 esp, 12			; 0000000cH
  0001a	0f b6 4d 08	 movzx	 ecx, BYTE PTR _bHook$[ebp]
  0001e	51		 push	 ecx
  0001f	68 00 00 00 00	 push	 OFFSET ?my_SHGetFolderPathA@?A0xa937c340@@YGJPAUHWND__@@HPAXKPAD@Z ; `anonymous namespace'::my_SHGetFolderPathA
  00024	68 00 00 00 00	 push	 OFFSET ?old_SHGetFolderPathA@?A0xa937c340@@3P6GJPAUHWND__@@HPAXKPAD@ZA ; `anonymous namespace'::old_SHGetFolderPathA
  00029	e8 00 00 00 00	 call	 ?HookApi@@YAHPAPAXPAXH@Z ; HookApi
  0002e	83 c4 0c	 add	 esp, 12			; 0000000cH

; 111  : 		HookApi(&(PVOID&)old_GetTempPathW, my_GetTempPathW, bHook);	HookApi(&(PVOID&)old_GetTempPathA, my_GetTempPathA, bHook);

  00031	0f b6 55 08	 movzx	 edx, BYTE PTR _bHook$[ebp]
  00035	52		 push	 edx
  00036	68 00 00 00 00	 push	 OFFSET ?my_GetTempPathW@?A0xa937c340@@YGKKPA_W@Z ; `anonymous namespace'::my_GetTempPathW
  0003b	68 00 00 00 00	 push	 OFFSET ?old_GetTempPathW@?A0xa937c340@@3P6GKKPA_W@ZA ; `anonymous namespace'::old_GetTempPathW
  00040	e8 00 00 00 00	 call	 ?HookApi@@YAHPAPAXPAXH@Z ; HookApi
  00045	83 c4 0c	 add	 esp, 12			; 0000000cH
  00048	0f b6 45 08	 movzx	 eax, BYTE PTR _bHook$[ebp]
  0004c	50		 push	 eax
  0004d	68 00 00 00 00	 push	 OFFSET ?my_GetTempPathA@?A0xa937c340@@YGKKPAD@Z ; `anonymous namespace'::my_GetTempPathA
  00052	68 00 00 00 00	 push	 OFFSET ?old_GetTempPathA@?A0xa937c340@@3P6GKKPAD@ZA ; `anonymous namespace'::old_GetTempPathA
  00057	e8 00 00 00 00	 call	 ?HookApi@@YAHPAPAXPAXH@Z ; HookApi
  0005c	83 c4 0c	 add	 esp, 12			; 0000000cH

; 112  : 		return true;

  0005f	b0 01		 mov	 al, 1

; 113  : 	}

  00061	3b ec		 cmp	 ebp, esp
  00063	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00068	5d		 pop	 ebp
  00069	c3		 ret	 0
?HookFlashDir@?A0xa937c340@@YA_N_N@Z ENDP		; `anonymous namespace'::HookFlashDir
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_path_hook.cpp
_TEXT	SEGMENT
_ppOld$ = 8						; size = 4
_pNew$ = 12						; size = 4
_bHook$ = 16						; size = 4
?HookApi@JsApi@@YAHPAPAXPAXH@Z PROC			; JsApi::HookApi

; 120  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 121  : 		DetourTransactionBegin();

  00004	e8 00 00 00 00	 call	 _DetourTransactionBegin@0

; 122  : 		DetourUpdateThread(GetCurrentThread());

  00009	8b f4		 mov	 esi, esp
  0000b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetCurrentThread@0
  00011	3b f4		 cmp	 esi, esp
  00013	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00018	50		 push	 eax
  00019	e8 00 00 00 00	 call	 _DetourUpdateThread@4

; 123  : 		if (bHook) {

  0001e	83 7d 10 00	 cmp	 DWORD PTR _bHook$[ebp], 0
  00022	74 0f		 je	 SHORT $LN2@HookApi

; 124  : 			DetourAttach(ppOld, pNew);

  00024	8b 45 0c	 mov	 eax, DWORD PTR _pNew$[ebp]
  00027	50		 push	 eax
  00028	8b 4d 08	 mov	 ecx, DWORD PTR _ppOld$[ebp]
  0002b	51		 push	 ecx
  0002c	e8 00 00 00 00	 call	 _DetourAttach@8

; 125  : 		}
; 126  : 		else {

  00031	eb 0d		 jmp	 SHORT $LN3@HookApi
$LN2@HookApi:

; 127  : 			DetourDetach(ppOld, pNew);

  00033	8b 55 0c	 mov	 edx, DWORD PTR _pNew$[ebp]
  00036	52		 push	 edx
  00037	8b 45 08	 mov	 eax, DWORD PTR _ppOld$[ebp]
  0003a	50		 push	 eax
  0003b	e8 00 00 00 00	 call	 _DetourDetach@8
$LN3@HookApi:

; 128  : 		}
; 129  : 		DetourTransactionCommit();

  00040	e8 00 00 00 00	 call	 _DetourTransactionCommit@0

; 130  : 		return TRUE;

  00045	b8 01 00 00 00	 mov	 eax, 1

; 131  : 	}

  0004a	5e		 pop	 esi
  0004b	3b ec		 cmp	 ebp, esp
  0004d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00052	5d		 pop	 ebp
  00053	c3		 ret	 0
?HookApi@JsApi@@YAHPAPAXPAXH@Z ENDP			; JsApi::HookApi
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstring
;	COMDAT ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z
_TEXT	SEGMENT
$T2 = -48						; size = 4
__Ans$ = -40						; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
__Left$ = 12						; size = 4
__Right$ = 16						; size = 4
??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z PROC ; std::operator+<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 4093 : 	{	// return string + NTCTS

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 24	 sub	 esp, 36			; 00000024H
  0001b	56		 push	 esi
  0001c	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00021	89 45 d0	 mov	 DWORD PTR [ebp-48], eax
  00024	89 45 d4	 mov	 DWORD PTR [ebp-44], eax
  00027	89 45 d8	 mov	 DWORD PTR [ebp-40], eax
  0002a	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0002d	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00030	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00033	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00036	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00039	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0003c	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 4094 : 	using _String_type = basic_string<_Elem, _Traits, _Alloc>;
; 4095 : 	using _Size_type = typename _String_type::size_type;
; 4096 : 	_String_type _Ans;

  00043	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00046	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0004b	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 4097 : 	_Ans.reserve(_Convert_size<_Size_type>(_Left.size() + _Traits::length(_Right)));

  00052	8b 4d 0c	 mov	 ecx, DWORD PTR __Left$[ebp]
  00055	e8 00 00 00 00	 call	 ?size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::size
  0005a	8b f0		 mov	 esi, eax
  0005c	8b 45 10	 mov	 eax, DWORD PTR __Right$[ebp]
  0005f	50		 push	 eax
  00060	e8 00 00 00 00	 call	 ?length@?$char_traits@D@std@@SAIQBD@Z ; std::char_traits<char>::length
  00065	83 c4 04	 add	 esp, 4
  00068	03 f0		 add	 esi, eax
  0006a	56		 push	 esi
  0006b	e8 00 00 00 00	 call	 ??$_Convert_size@I@std@@YAII@Z ; std::_Convert_size<unsigned int>
  00070	83 c4 04	 add	 esp, 4
  00073	50		 push	 eax
  00074	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00077	e8 00 00 00 00	 call	 ?reserve@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXI@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::reserve

; 4098 : 	_Ans += _Left;

  0007c	8b 4d 0c	 mov	 ecx, DWORD PTR __Left$[ebp]
  0007f	51		 push	 ecx
  00080	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00083	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=

; 4099 : 	_Ans += _Right;

  00088	8b 55 10	 mov	 edx, DWORD PTR __Right$[ebp]
  0008b	52		 push	 edx
  0008c	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  0008f	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=

; 4100 : 	return (_Ans);

  00094	8d 45 d8	 lea	 eax, DWORD PTR __Ans$[ebp]
  00097	50		 push	 eax
  00098	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0009b	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  000a0	8b 4d d0	 mov	 ecx, DWORD PTR $T2[ebp]
  000a3	83 c9 01	 or	 ecx, 1
  000a6	89 4d d0	 mov	 DWORD PTR $T2[ebp], ecx
  000a9	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000ad	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  000b0	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  000b5	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 4101 : 	}

  000b8	52		 push	 edx
  000b9	8b cd		 mov	 ecx, ebp
  000bb	50		 push	 eax
  000bc	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN9@operator
  000c2	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000c7	58		 pop	 eax
  000c8	5a		 pop	 edx
  000c9	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000cc	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000d3	5e		 pop	 esi
  000d4	83 c4 30	 add	 esp, 48			; 00000030H
  000d7	3b ec		 cmp	 ebp, esp
  000d9	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000de	8b e5		 mov	 esp, ebp
  000e0	5d		 pop	 ebp
  000e1	c3		 ret	 0
  000e2	66 90		 npad	 2
$LN9@operator:
  000e4	01 00 00 00	 DD	 1
  000e8	00 00 00 00	 DD	 $LN8@operator
$LN8@operator:
  000ec	d8 ff ff ff	 DD	 -40			; ffffffd8H
  000f0	18 00 00 00	 DD	 24			; 00000018H
  000f4	00 00 00 00	 DD	 $LN6@operator
$LN6@operator:
  000f8	5f		 DB	 95			; 0000005fH
  000f9	41		 DB	 65			; 00000041H
  000fa	6e		 DB	 110			; 0000006eH
  000fb	73		 DB	 115			; 00000073H
  000fc	00		 DB	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z$1:
  00008	8b 45 d0	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN5@operator
  00014	83 65 d0 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN5@operator:
  00020	c3		 ret	 0
__ehhandler$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@QBD@Z ENDP ; std::operator+<char,std::char_traits<char>,std::allocator<char> >
END
