[["v8_Default_embedded_blob_code_data_", 93957345371712, 93957345373344], ["v8::internal::Runtime_CompileForOnStackReplacement(int, unsigned long*, v8::internal::Isolate*)", 93957340005712, 93957340007781], ["__write", 139871564238624, 139871564238781], ["fwrite", 139871563740416, 139871563740870], ["v8::internal::Runtime_AllocateInYoungGeneration(int, unsigned long*, v8::internal::Isolate*)", 93957340107136, 93957340107454], ["v8::internal::compiler::TopLevelLiveRange::AddUseInterval(v8::internal::compiler::LifetimePosition, v8::internal::compiler::LifetimePosition, v8::internal::Zone*, bool)", 93957343090272, 93957343090533], ["v8::internal::RootScavengeVisitor::VisitRootPointer(v8::internal::Root, char const*, v8::internal::FullObjectSlot)", 93957337529568, 93957337529610]]