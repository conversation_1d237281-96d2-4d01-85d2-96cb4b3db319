// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-typed-array-gen.h'

namespace typed_array {
const kBuiltinNameEntries: constexpr string = '%TypedArray%.prototype.entries';

// %TypedArray%.entries ()
// https://tc39.github.io/ecma262/#sec-%typedarray%.entries
transitioning javascript builtin
TypedArrayPrototypeEntries(js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): JSArrayIterator {
  try {
    const array: JSTypedArray = Cast<JSTypedArray>(receiver)
        otherwise NotTypedArray;

    EnsureAttached(array) otherwise IsDetached;
    return CreateArrayIterator(array, IterationKind::kEntries);
  } label NotTypedArray deferred {
    ThrowTypeError(MessageTemplate::kNotTypedArray, kBuiltinNameEntries);
  } label IsDetached deferred {
    ThrowTypeError(MessageTemplate::kDetachedOperation, kBuiltinNameEntries);
  }
}
}
