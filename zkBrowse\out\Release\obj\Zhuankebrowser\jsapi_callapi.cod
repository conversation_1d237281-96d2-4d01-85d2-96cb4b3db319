; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_callapi.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

EXTRN	_wcstombs:PROC
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
_BSS	ENDS
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
$SG4294395735 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395734 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395733 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395732 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294395731 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395730 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294395729 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294395728 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294395743 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294395742 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294395741 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294395740 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294395739 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395738 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294395737 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395736 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294395719 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294395718 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395717 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395716 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395715 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395714 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395713 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395712 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395727 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294395726 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294395725 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294395724 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294395723 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294395722 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294395721 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294395720 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395703 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395702 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395701 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395700 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395699 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395698 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395697 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395696 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395711 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395710 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294395709 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395708 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395707 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395706 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395705 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395704 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395687 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395686 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395685 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395684 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294395683 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395682 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395681 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294395680 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294395695 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395694 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294395693 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294395692 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294395691 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395690 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395689 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294395688 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395671 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395670 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294395669 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294395668 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294395667 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395666 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395665 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395664 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395679 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294395678 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395677 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395676 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294395675 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395674 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395673 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294395672 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395655 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294395654 DB 00H, 00H
	ORG $+2
$SG4294395653 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294395652 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294395651 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294395650 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294395649 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294395648 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294395663 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395662 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395661 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395660 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395659 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395658 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395657 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294395656 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294395639 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294395638 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294395637 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294395636 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294395635 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294395634 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294395633 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294395632 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294395647 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294395646 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294395645 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294395644 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294395643 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294395642 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294395641 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294395640 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294395623 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294395622 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294395621 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294395620 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294395619 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294395618 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294395617 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294395616 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294395631 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294395630 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294395629 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294395628 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294395627 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294395626 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294395625 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294395624 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294395607 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294395606 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294395605 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294395604 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395603 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294395602 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294395601 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294395600 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294395615 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294395614 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294395613 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395612 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294395611 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294395610 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294395609 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294395608 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294395591 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395590 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395589 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395588 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395587 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395586 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294395585 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294395584 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294395599 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294395598 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294395597 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395596 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294395595 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395594 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395593 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395592 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294395575 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294395574 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294395573 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294395572 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294395571 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294395570 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294395569 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294395568 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294395583 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395582 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395581 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395580 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395579 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294395578 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294395577 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294395576 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294395559 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294395558 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294395557 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294395556 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294395555 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294395554 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395553 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294395552 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395567 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395566 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294395565 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395564 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294395563 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294395562 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395561 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395560 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294395543 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294395542 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294395541 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294395540 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294395539 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294395538 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294395537 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395536 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395551 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294395550 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294395549 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294395548 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294395547 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294395546 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294395545 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294395544 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395527 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294395526 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395525 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395524 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294395523 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294395522 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294395521 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395520 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294395535 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294395534 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294395533 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294395532 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294395531 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395530 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395529 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294395528 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294395511 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294395510 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294395509 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294395508 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294395507 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294395506 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294395505 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294395504 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294395519 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294395518 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294395517 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395516 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294395515 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294395514 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294395513 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294395512 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294395495 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294395494 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395493 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395492 DB 00H, 00H
	ORG $+2
$SG4294395491 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294395490 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294395489 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395488 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294395503 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294395502 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294395501 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294395500 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294395499 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294395498 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294395497 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294395496 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294395479 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294395478 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294395477 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294395476 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294395475 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294395474 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294395473 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294395472 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294395487 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294395486 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294395485 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294395484 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294395483 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294395482 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294395481 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294395480 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294395463 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395462 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294395461 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395460 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294395459 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294395458 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395457 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294395456 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395471 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395470 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395469 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395468 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294395467 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294395466 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294395465 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395464 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395447 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294395446 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395445 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294395444 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294395443 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294395442 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294395441 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294395440 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294395455 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294395454 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294395453 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294395452 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294395451 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294395450 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294395449 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395448 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395431 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294395430 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294395429 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294395428 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294395427 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294395426 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294395425 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294395424 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294395439 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294395438 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294395437 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294395436 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294395435 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294395434 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294395433 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294395432 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395415 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294395414 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294395413 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294395412 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395411 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294395410 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395409 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294395408 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395423 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294395422 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294395421 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294395420 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294395419 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294395418 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294395417 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294395416 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294395399 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294395398 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294395397 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294395396 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294395395 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395394 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294395393 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395392 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395407 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294395406 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294395405 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294395404 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395403 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294395402 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395401 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294395400 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294395383 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294395382 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294395381 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294395380 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294395379 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294395378 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395377 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294395376 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395391 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395390 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294395389 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294395388 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395387 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395386 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294395385 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294395384 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395367 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395366 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395365 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395364 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294395363 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395362 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294395361 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294395360 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294395375 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294395374 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294395373 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395372 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294395371 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395370 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294395369 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395368 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294395351 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395350 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294395349 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395348 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395347 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294395346 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294395345 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294395344 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294395359 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395358 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294395357 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294395356 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294395355 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294395354 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395353 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294395352 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395335 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395334 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395333 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294395332 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395331 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294395330 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294395329 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395328 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395343 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294395342 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395341 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294395340 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294395339 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294395338 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395337 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294395336 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395319 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294395318 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294395317 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395316 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294395315 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395314 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294395313 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395312 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395327 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395326 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294395325 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294395324 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294395323 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294395322 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395321 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395320 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294395303 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395302 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395301 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395300 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294395299 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294395298 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294395297 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294395296 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294395311 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395310 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395309 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395308 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395307 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294395306 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294395305 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395304 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395287 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294395286 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395285 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294395284 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294395283 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294395282 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294395281 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294395280 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294395295 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294395294 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395293 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294395292 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395291 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395290 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294395289 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294395288 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294395271 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395270 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294395269 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294395268 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294395267 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294395266 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294395265 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294395279 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294395278 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294395277 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294395276 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294395275 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294395274 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294395273 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294395272 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395232 DB 'S', 00H, 00H, 00H
$SG4294395223 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294395222 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294395221 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395220 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294395219 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395218 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395217 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294395216 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294395231 DB 'M', 00H, 00H, 00H
$SG4294395230 DB 'D', 00H, 00H, 00H
$SG4294395229 DB 'B', 00H, 00H, 00H
$SG4294395228 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294395227 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294395226 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395225 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395224 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294395215 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294395214 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294395213 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294395182 DB ':', 00H, 00H, 00H
$SG4294395181 DB 00H, 00H
	ORG $+2
$SG4294395180 DB 00H, 00H
	ORG $+2
$SG4294395089 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294394375 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294394376 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294393930 DB 'chs', 00H
PUBLIC	??$_Copy_memmove@PAIPAI@std@@YAPAIPAI00@Z	; std::_Copy_memmove<unsigned int *,unsigned int *>
PUBLIC	??$_Seek_wrapped@I@std@@YAXAAPAIQAI@Z		; std::_Seek_wrapped<unsigned int>
PUBLIC	??$_Uninitialized_move_al_unchecked@IIV?$allocator@I@std@@@std@@YAPAIQAI00AAV?$allocator@I@0@U_Really_trivial_ptr_iterator_tag@0@U?$integral_constant@_N$00@0@@Z ; std::_Uninitialized_move_al_unchecked<unsigned int,unsigned int,std::allocator<unsigned int> >
PUBLIC	??$_Ptr_move_cat@II@std@@YA?AU_Really_trivial_ptr_iterator_tag@0@ABQAI0@Z ; std::_Ptr_move_cat<unsigned int,unsigned int>
PUBLIC	??$_Get_unwrapped_n@IH$0A@@std@@YAPAIQAIH@Z	; std::_Get_unwrapped_n<unsigned int,int,0>
PUBLIC	??$_Idl_distance1@PAIPAI@std@@YAHABQAI0Urandom_access_iterator_tag@0@@Z ; std::_Idl_distance1<unsigned int *,unsigned int *>
PUBLIC	??$_Get_unwrapped@I@std@@YAPAIQAI@Z		; std::_Get_unwrapped<unsigned int>
PUBLIC	??$_Idl_distance@PAIPAI@std@@YAHABQAI0@Z	; std::_Idl_distance<unsigned int *,unsigned int *>
PUBLIC	??$_Uninitialized_move@PAIPAIV?$allocator@I@std@@@std@@YAPAIQAI0PAIAAV?$allocator@I@0@@Z ; std::_Uninitialized_move<unsigned int *,unsigned int *,std::allocator<unsigned int> >
PUBLIC	??$_Destroy_range1@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@U?$integral_constant@_N$00@0@@Z ; std::_Destroy_range1<std::allocator<unsigned int> >
PUBLIC	?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$allocator@I@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first
PUBLIC	?_Umove_if_noexcept1@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00U?$integral_constant@_N$00@2@@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept1
PUBLIC	?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
PUBLIC	?max_size@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAIABV?$allocator@I@2@@Z ; std::_Default_allocator_traits<std::allocator<unsigned int> >::max_size
PUBLIC	??0?$allocator@I@std@@QAE@XZ			; std::allocator<unsigned int>::allocator<unsigned int>
PUBLIC	?allocate@?$allocator@I@std@@QAEPAII@Z		; std::allocator<unsigned int>::allocate
PUBLIC	?max_size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::max_size
PUBLIC	?_Umove@?$vector@IV?$allocator@I@std@@@std@@AAEPAIPAI00@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove
PUBLIC	?_Umove_if_noexcept@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept
PUBLIC	?_Calculate_growth@?$vector@IV?$allocator@I@std@@@std@@ABEII@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Calculate_growth
PUBLIC	?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AAEXQAIII@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Change_array
PUBLIC	?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::_Xlength
PUBLIC	??0?$_Vector_val@U?$_Simple_types@I@std@@@std@@QAE@XZ ; std::_Vector_val<std::_Simple_types<unsigned int> >::_Vector_val<std::_Simple_types<unsigned int> >
PUBLIC	??$_Destroy_range@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@@Z ; std::_Destroy_range<std::allocator<unsigned int> >
PUBLIC	??$?0$$V@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAE@U_Zero_then_variadic_args_t@1@@Z ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1><>
PUBLIC	??$_Emplace_reallocate@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEPAIQAIABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_reallocate<unsigned int const &>
PUBLIC	??$construct@IABI@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAXAAV?$allocator@I@1@QAIABI@Z ; std::_Default_allocator_traits<std::allocator<unsigned int> >::construct<unsigned int,unsigned int const &>
PUBLIC	??$_Unfancy@I@std@@YAPAIPAI@Z			; std::_Unfancy<unsigned int>
PUBLIC	??$forward@ABI@std@@YAABIABI@Z			; std::forward<unsigned int const &>
PUBLIC	??$_Unfancy_maybe_null@I@std@@YAPAIPAI@Z	; std::_Unfancy_maybe_null<unsigned int>
PUBLIC	?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second
PUBLIC	?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second
PUBLIC	?deallocate@?$allocator@I@std@@QAEXQAII@Z	; std::allocator<unsigned int>::deallocate
PUBLIC	?capacity@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::capacity
PUBLIC	?_Destroy@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI0@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Destroy
PUBLIC	?_Orphan_all@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEXXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Orphan_all
PUBLIC	?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
PUBLIC	?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
PUBLIC	?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
PUBLIC	?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
PUBLIC	?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$allocator@I@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first
PUBLIC	?_Has_unused_capacity@?$vector@IV?$allocator@I@std@@@std@@ABE_NXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::_Has_unused_capacity
PUBLIC	?_Tidy@?$vector@IV?$allocator@I@std@@@std@@AAEXXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::_Tidy
PUBLIC	?_Orphan_range@?$vector@IV?$allocator@I@std@@@std@@ABEXPAI0@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Orphan_range
PUBLIC	??0?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAE@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >
PUBLIC	?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
PUBLIC	?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
PUBLIC	?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
PUBLIC	?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
PUBLIC	?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
PUBLIC	??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ	; std::vector<unsigned int,std::allocator<unsigned int> >::vector<unsigned int,std::allocator<unsigned int> >
PUBLIC	??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ	; std::vector<unsigned int,std::allocator<unsigned int> >::~vector<unsigned int,std::allocator<unsigned int> >
PUBLIC	??$_Emplace_back_with_unused_capacity@ABI@?$vector@IV?$allocator@I@std@@@std@@AAEXABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_back_with_unused_capacity<unsigned int const &>
PUBLIC	??$emplace_back@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::emplace_back<unsigned int const &>
PUBLIC	?push_back@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::push_back
PUBLIC	?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::data
PUBLIC	?size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::size
PUBLIC	?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z ; CallApi::CallFunction
PUBLIC	?CallFunction@CallApi@@YAIP6GHXZPAII_N@Z	; CallApi::CallFunction
PUBLIC	?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z ; CallApi::ws2s
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__ehfuncinfo$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z DD 019930522H
	DD	07H
	DD	FLAT:__unwindtable$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$0
	DD	00H
	DD	FLAT:__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$1
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$1
	DD	02H
	DD	FLAT:__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$2
	DD	03H
	DD	FLAT:__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$3
	DD	02H
	DD	FLAT:__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$3
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$4
__ehfuncinfo$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z$2
	DD	00H
	DD	FLAT:__unwindfunclet$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z$1
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_callapi.cpp
_TEXT	SEGMENT
$T2 = -104						; size = 4
$T3 = -100						; size = 4
$T4 = -96						; size = 4
_result$ = -88						; size = 24
_nConverted$ = -60					; size = 4
__Dest$ = -56						; size = 4
__Dsize$ = -52						; size = 4
__Source$ = -48						; size = 4
_curLocale$ = -40					; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_ws$ = 12						; size = 4
?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z PROC ; CallApi::ws2s

; 13   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 5c	 sub	 esp, 92			; 0000005cH
  0001b	57		 push	 edi
  0001c	8d 7d 98	 lea	 edi, DWORD PTR [ebp-104]
  0001f	b9 17 00 00 00	 mov	 ecx, 23			; 00000017H
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 98 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 14   : 		std::string curLocale = setlocale(LC_ALL, NULL);        // curLocale = "C";

  00032	6a 00		 push	 0
  00034	6a 00		 push	 0
  00036	e8 00 00 00 00	 call	 _setlocale
  0003b	83 c4 08	 add	 esp, 8
  0003e	50		 push	 eax
  0003f	8d 4d d8	 lea	 ecx, DWORD PTR _curLocale$[ebp]
  00042	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00047	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 15   : 		setlocale(LC_ALL, "chs");

  0004e	68 00 00 00 00	 push	 OFFSET $SG4294393930
  00053	6a 00		 push	 0
  00055	e8 00 00 00 00	 call	 _setlocale
  0005a	83 c4 08	 add	 esp, 8

; 16   : 		const wchar_t* _Source = ws.c_str();

  0005d	8b 4d 0c	 mov	 ecx, DWORD PTR _ws$[ebp]
  00060	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEPB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  00065	89 45 d0	 mov	 DWORD PTR __Source$[ebp], eax

; 17   : 		size_t _Dsize = 2 * ws.size() + 1;

  00068	8b 4d 0c	 mov	 ecx, DWORD PTR _ws$[ebp]
  0006b	e8 00 00 00 00	 call	 ?size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::size
  00070	8d 44 00 01	 lea	 eax, DWORD PTR [eax+eax+1]
  00074	89 45 cc	 mov	 DWORD PTR __Dsize$[ebp], eax

; 18   : 		char *_Dest = new char[_Dsize];

  00077	8b 4d cc	 mov	 ecx, DWORD PTR __Dsize$[ebp]
  0007a	51		 push	 ecx
  0007b	e8 00 00 00 00	 call	 ??_U@YAPAXI@Z		; operator new[]
  00080	83 c4 04	 add	 esp, 4
  00083	89 45 a0	 mov	 DWORD PTR $T4[ebp], eax
  00086	8b 55 a0	 mov	 edx, DWORD PTR $T4[ebp]
  00089	89 55 c8	 mov	 DWORD PTR __Dest$[ebp], edx

; 19   : 		memset(_Dest,0,_Dsize);

  0008c	8b 45 cc	 mov	 eax, DWORD PTR __Dsize$[ebp]
  0008f	50		 push	 eax
  00090	6a 00		 push	 0
  00092	8b 4d c8	 mov	 ecx, DWORD PTR __Dest$[ebp]
  00095	51		 push	 ecx
  00096	e8 00 00 00 00	 call	 _memset
  0009b	83 c4 0c	 add	 esp, 12			; 0000000cH

; 20   : 		size_t nConverted=0; wcstombs( _Dest, _Source,_Dsize);

  0009e	c7 45 c4 00 00
	00 00		 mov	 DWORD PTR _nConverted$[ebp], 0
  000a5	8b 55 cc	 mov	 edx, DWORD PTR __Dsize$[ebp]
  000a8	52		 push	 edx
  000a9	8b 45 d0	 mov	 eax, DWORD PTR __Source$[ebp]
  000ac	50		 push	 eax
  000ad	8b 4d c8	 mov	 ecx, DWORD PTR __Dest$[ebp]
  000b0	51		 push	 ecx
  000b1	e8 00 00 00 00	 call	 _wcstombs
  000b6	83 c4 0c	 add	 esp, 12			; 0000000cH

; 21   : 		std::string result = _Dest;

  000b9	8b 55 c8	 mov	 edx, DWORD PTR __Dest$[ebp]
  000bc	52		 push	 edx
  000bd	8d 4d a8	 lea	 ecx, DWORD PTR _result$[ebp]
  000c0	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  000c5	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2

; 22   : 		delete []_Dest;

  000c9	8b 45 c8	 mov	 eax, DWORD PTR __Dest$[ebp]
  000cc	89 45 9c	 mov	 DWORD PTR $T3[ebp], eax
  000cf	8b 4d 9c	 mov	 ecx, DWORD PTR $T3[ebp]
  000d2	51		 push	 ecx
  000d3	e8 00 00 00 00	 call	 ??_V@YAXPAX@Z		; operator delete[]
  000d8	83 c4 04	 add	 esp, 4

; 23   : 		setlocale(LC_ALL, curLocale.c_str());

  000db	8d 4d d8	 lea	 ecx, DWORD PTR _curLocale$[ebp]
  000de	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  000e3	50		 push	 eax
  000e4	6a 00		 push	 0
  000e6	e8 00 00 00 00	 call	 _setlocale
  000eb	83 c4 08	 add	 esp, 8

; 24   : 		return result;

  000ee	8d 55 a8	 lea	 edx, DWORD PTR _result$[ebp]
  000f1	52		 push	 edx
  000f2	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  000f5	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  000fa	8b 45 98	 mov	 eax, DWORD PTR $T2[ebp]
  000fd	83 c8 01	 or	 eax, 1
  00100	89 45 98	 mov	 DWORD PTR $T2[ebp], eax
  00103	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00107	8d 4d a8	 lea	 ecx, DWORD PTR _result$[ebp]
  0010a	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  0010f	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00113	8d 4d d8	 lea	 ecx, DWORD PTR _curLocale$[ebp]
  00116	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  0011b	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 25   : 	}

  0011e	52		 push	 edx
  0011f	8b cd		 mov	 ecx, ebp
  00121	50		 push	 eax
  00122	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@ws2s
  00128	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0012d	58		 pop	 eax
  0012e	5a		 pop	 edx
  0012f	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00132	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00139	5f		 pop	 edi
  0013a	83 c4 68	 add	 esp, 104		; 00000068H
  0013d	3b ec		 cmp	 ebp, esp
  0013f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00144	8b e5		 mov	 esp, ebp
  00146	5d		 pop	 ebp
  00147	c3		 ret	 0
$LN11@ws2s:
  00148	02 00 00 00	 DD	 2
  0014c	00 00 00 00	 DD	 $LN10@ws2s
$LN10@ws2s:
  00150	d8 ff ff ff	 DD	 -40			; ffffffd8H
  00154	18 00 00 00	 DD	 24			; 00000018H
  00158	00 00 00 00	 DD	 $LN7@ws2s
  0015c	a8 ff ff ff	 DD	 -88			; ffffffa8H
  00160	18 00 00 00	 DD	 24			; 00000018H
  00164	00 00 00 00	 DD	 $LN8@ws2s
$LN8@ws2s:
  00168	72		 DB	 114			; 00000072H
  00169	65		 DB	 101			; 00000065H
  0016a	73		 DB	 115			; 00000073H
  0016b	75		 DB	 117			; 00000075H
  0016c	6c		 DB	 108			; 0000006cH
  0016d	74		 DB	 116			; 00000074H
  0016e	00		 DB	 0
$LN7@ws2s:
  0016f	63		 DB	 99			; 00000063H
  00170	75		 DB	 117			; 00000075H
  00171	72		 DB	 114			; 00000072H
  00172	4c		 DB	 76			; 0000004cH
  00173	6f		 DB	 111			; 0000006fH
  00174	63		 DB	 99			; 00000063H
  00175	61		 DB	 97			; 00000061H
  00176	6c		 DB	 108			; 0000006cH
  00177	65		 DB	 101			; 00000065H
  00178	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _curLocale$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z$1:
  00008	8d 4d a8	 lea	 ecx, DWORD PTR _result$[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z$2:
  00010	8b 45 98	 mov	 eax, DWORD PTR $T2[ebp]
  00013	83 e0 01	 and	 eax, 1
  00016	0f 84 0c 00 00
	00		 je	 $LN6@ws2s
  0001c	83 65 98 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00020	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00023	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN6@ws2s:
  00028	c3		 ret	 0
__ehhandler$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z:
  00029	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z
  0002e	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z ENDP ; CallApi::ws2s
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_callapi.cpp
_TEXT	SEGMENT
_j$1 = -24						; size = 4
_i$2 = -20						; size = 4
_j$3 = -16						; size = 4
_i$ = -12						; size = 4
_iArg$ = -8						; size = 4
_lRet$ = -4						; size = 4
_fnAddr$ = 8						; size = 4
_vArgs$ = 12						; size = 4
_cArgs$ = 16						; size = 4
_bcdecl$ = 20						; size = 1
?CallFunction@CallApi@@YAIP6GHXZPAII_N@Z PROC		; CallApi::CallFunction

; 253  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 18	 sub	 esp, 24			; 00000018H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  0000e	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00011	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  00014	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00017	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  0001a	89 45 fc	 mov	 DWORD PTR [ebp-4], eax

; 254  : 	using namespace std;
; 255  : 
; 256  : 	uintptr_t lRet=0;		

  0001d	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR _lRet$[ebp], 0

; 257  : 	uintptr_t iArg=0;

  00024	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR _iArg$[ebp], 0

; 258  : 	size_t i=0;

  0002b	c7 45 f4 00 00
	00 00		 mov	 DWORD PTR _i$[ebp], 0

; 259  : 	if (!fnAddr) return 0;

  00032	83 7d 08 00	 cmp	 DWORD PTR _fnAddr$[ebp], 0
  00036	75 04		 jne	 SHORT $LN8@CallFuncti
  00038	33 c0		 xor	 eax, eax
  0003a	eb 67		 jmp	 SHORT $LN1@CallFuncti
$LN8@CallFuncti:

; 260  : 
; 261  : 	for(size_t j=0;j<cArgs;j++)

  0003c	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR _j$3[ebp], 0
  00043	eb 09		 jmp	 SHORT $LN4@CallFuncti
$LN2@CallFuncti:
  00045	8b 45 f0	 mov	 eax, DWORD PTR _j$3[ebp]
  00048	83 c0 01	 add	 eax, 1
  0004b	89 45 f0	 mov	 DWORD PTR _j$3[ebp], eax
$LN4@CallFuncti:
  0004e	8b 4d f0	 mov	 ecx, DWORD PTR _j$3[ebp]
  00051	3b 4d 10	 cmp	 ecx, DWORD PTR _cArgs$[ebp]
  00054	73 1d		 jae	 SHORT $LN3@CallFuncti

; 262  : 	{
; 263  : 		size_t i=cArgs-j-1;	

  00056	8b 55 10	 mov	 edx, DWORD PTR _cArgs$[ebp]
  00059	2b 55 f0	 sub	 edx, DWORD PTR _j$3[ebp]
  0005c	83 ea 01	 sub	 edx, 1
  0005f	89 55 ec	 mov	 DWORD PTR _i$2[ebp], edx

; 264  : 		iArg=vArgs[i];

  00062	8b 45 ec	 mov	 eax, DWORD PTR _i$2[ebp]
  00065	8b 4d 0c	 mov	 ecx, DWORD PTR _vArgs$[ebp]
  00068	8b 14 81	 mov	 edx, DWORD PTR [ecx+eax*4]
  0006b	89 55 f8	 mov	 DWORD PTR _iArg$[ebp], edx

; 265  : 		__asm{
; 266  : 			push iArg		

  0006e	ff 75 f8	 push	 DWORD PTR _iArg$[ebp]

; 267  : 		}
; 268  : 	}

  00071	eb d2		 jmp	 SHORT $LN2@CallFuncti
$LN3@CallFuncti:

; 269  : 
; 270  : 	__asm{
; 271  : 		call fnAddr

  00073	ff 55 08	 call	 DWORD PTR _fnAddr$[ebp]

; 272  : 			mov lRet,eax

  00076	89 45 fc	 mov	 DWORD PTR _lRet$[ebp], eax

; 273  : 	}
; 274  : 	if(bcdecl)

  00079	0f b6 45 14	 movzx	 eax, BYTE PTR _bcdecl$[ebp]
  0007d	85 c0		 test	 eax, eax
  0007f	74 1f		 je	 SHORT $LN9@CallFuncti

; 275  : 	{
; 276  : 		for(size_t j=0;j<cArgs;j++) __asm add esp,4

  00081	c7 45 e8 00 00
	00 00		 mov	 DWORD PTR _j$1[ebp], 0
  00088	eb 09		 jmp	 SHORT $LN7@CallFuncti
$LN5@CallFuncti:
  0008a	8b 4d e8	 mov	 ecx, DWORD PTR _j$1[ebp]
  0008d	83 c1 01	 add	 ecx, 1
  00090	89 4d e8	 mov	 DWORD PTR _j$1[ebp], ecx
$LN7@CallFuncti:
  00093	8b 55 e8	 mov	 edx, DWORD PTR _j$1[ebp]
  00096	3b 55 10	 cmp	 edx, DWORD PTR _cArgs$[ebp]
  00099	73 05		 jae	 SHORT $LN9@CallFuncti
  0009b	83 c4 04	 add	 esp, 4
  0009e	eb ea		 jmp	 SHORT $LN5@CallFuncti
$LN9@CallFuncti:

; 277  : 	}
; 278  : 	return lRet;

  000a0	8b 45 fc	 mov	 eax, DWORD PTR _lRet$[ebp]
$LN1@CallFuncti:

; 279  : }

  000a3	83 c4 18	 add	 esp, 24			; 00000018H
  000a6	3b ec		 cmp	 ebp, esp
  000a8	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000ad	8b e5		 mov	 esp, ebp
  000af	5d		 pop	 ebp
  000b0	c3		 ret	 0
?CallFunction@CallApi@@YAIP6GHXZPAII_N@Z ENDP		; CallApi::CallFunction
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_callapi.cpp
_TEXT	SEGMENT
tv177 = -200						; size = 4
$T2 = -196						; size = 4
$T3 = -192						; size = 4
$T4 = -188						; size = 4
$T5 = -184						; size = 24
$T6 = -160						; size = 24
_i$7 = -136						; size = 4
_j$8 = -132						; size = 4
_vArgs$ = -124						; size = 12
_i$ = -108						; size = 4
_iArg$ = -100						; size = 4
_lRet$ = -92						; size = 4
_sFuncName$9 = -84					; size = 24
_sLibName$10 = -52					; size = 24
_hMod$ = -24						; size = 4
_fnAddr$ = -20						; size = 4
_cArgs$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_pDispParams$ = 8					; size = 4
_bcdecl$ = 12						; size = 1
?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z PROC	; CallApi::CallFunction

; 283  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	81 ec bc 00 00
	00		 sub	 esp, 188		; 000000bcH
  0001e	56		 push	 esi
  0001f	57		 push	 edi
  00020	8d bd 38 ff ff
	ff		 lea	 edi, DWORD PTR [ebp-200]
  00026	b9 2f 00 00 00	 mov	 ecx, 47			; 0000002fH
  0002b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00030	f3 ab		 rep stosd

; 284  : 	using namespace std;
; 285  : 
; 286  : 	size_t cArgs=pDispParams->cArgs;

  00032	8b 45 08	 mov	 eax, DWORD PTR _pDispParams$[ebp]
  00035	8b 48 08	 mov	 ecx, DWORD PTR [eax+8]
  00038	89 4d f0	 mov	 DWORD PTR _cArgs$[ebp], ecx

; 287  : 	if(cArgs<1) return 0;

  0003b	83 7d f0 01	 cmp	 DWORD PTR _cArgs$[ebp], 1
  0003f	73 07		 jae	 SHORT $LN7@CallFuncti
  00041	33 c0		 xor	 eax, eax
  00043	e9 6c 03 00 00	 jmp	 $LN1@CallFuncti
$LN7@CallFuncti:

; 288  : 
; 289  : 	FARPROC fnAddr=NULL;

  00048	c7 45 ec 00 00
	00 00		 mov	 DWORD PTR _fnAddr$[ebp], 0

; 290  : 	HMODULE hMod=NULL;

  0004f	c7 45 e8 00 00
	00 00		 mov	 DWORD PTR _hMod$[ebp], 0

; 291  : 
; 292  : 	if(pDispParams->rgvarg[cArgs-1].vt==VT_BSTR)

  00056	8b 55 f0	 mov	 edx, DWORD PTR _cArgs$[ebp]
  00059	83 ea 01	 sub	 edx, 1
  0005c	c1 e2 04	 shl	 edx, 4
  0005f	8b 45 08	 mov	 eax, DWORD PTR _pDispParams$[ebp]
  00062	8b 08		 mov	 ecx, DWORD PTR [eax]
  00064	0f b7 14 11	 movzx	 edx, WORD PTR [ecx+edx]
  00068	83 fa 08	 cmp	 edx, 8
  0006b	0f 85 77 01 00
	00		 jne	 $LN8@CallFuncti

; 293  : 	{
; 294  : 		if(cArgs<2) return 0;		

  00071	83 7d f0 02	 cmp	 DWORD PTR _cArgs$[ebp], 2
  00075	73 07		 jae	 SHORT $LN10@CallFuncti
  00077	33 c0		 xor	 eax, eax
  00079	e9 36 03 00 00	 jmp	 $LN1@CallFuncti
$LN10@CallFuncti:

; 295  : 		string sLibName=ws2s(pDispParams->rgvarg[cArgs-1].bstrVal);

  0007e	8b 45 f0	 mov	 eax, DWORD PTR _cArgs$[ebp]
  00081	83 e8 01	 sub	 eax, 1
  00084	c1 e0 04	 shl	 eax, 4
  00087	8b 4d 08	 mov	 ecx, DWORD PTR _pDispParams$[ebp]
  0008a	8b 11		 mov	 edx, DWORD PTR [ecx]
  0008c	8b 44 02 08	 mov	 eax, DWORD PTR [edx+eax+8]
  00090	50		 push	 eax
  00091	8d 8d 60 ff ff
	ff		 lea	 ecx, DWORD PTR $T6[ebp]
  00097	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@QB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0009c	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  000a3	8d 8d 60 ff ff
	ff		 lea	 ecx, DWORD PTR $T6[ebp]
  000a9	51		 push	 ecx
  000aa	8d 55 cc	 lea	 edx, DWORD PTR _sLibName$10[ebp]
  000ad	52		 push	 edx
  000ae	e8 00 00 00 00	 call	 ?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z ; CallApi::ws2s
  000b3	83 c4 08	 add	 esp, 8
  000b6	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  000ba	8d 8d 60 ff ff
	ff		 lea	 ecx, DWORD PTR $T6[ebp]
  000c0	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 296  : 		string sFuncName=ws2s(pDispParams->rgvarg[cArgs-2].bstrVal);

  000c5	8b 45 f0	 mov	 eax, DWORD PTR _cArgs$[ebp]
  000c8	83 e8 02	 sub	 eax, 2
  000cb	c1 e0 04	 shl	 eax, 4
  000ce	8b 4d 08	 mov	 ecx, DWORD PTR _pDispParams$[ebp]
  000d1	8b 11		 mov	 edx, DWORD PTR [ecx]
  000d3	8b 44 02 08	 mov	 eax, DWORD PTR [edx+eax+8]
  000d7	50		 push	 eax
  000d8	8d 8d 48 ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  000de	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@QB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000e3	c6 45 fc 03	 mov	 BYTE PTR __$EHRec$[ebp+8], 3
  000e7	8d 8d 48 ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  000ed	51		 push	 ecx
  000ee	8d 55 ac	 lea	 edx, DWORD PTR _sFuncName$9[ebp]
  000f1	52		 push	 edx
  000f2	e8 00 00 00 00	 call	 ?ws2s@CallApi@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@3@@Z ; CallApi::ws2s
  000f7	83 c4 08	 add	 esp, 8
  000fa	c6 45 fc 05	 mov	 BYTE PTR __$EHRec$[ebp+8], 5
  000fe	8d 8d 48 ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  00104	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 297  : 		cArgs-=2;

  00109	8b 45 f0	 mov	 eax, DWORD PTR _cArgs$[ebp]
  0010c	83 e8 02	 sub	 eax, 2
  0010f	89 45 f0	 mov	 DWORD PTR _cArgs$[ebp], eax

; 298  : 
; 299  : 		hMod=LoadLibraryA(sLibName.c_str());

  00112	8d 4d cc	 lea	 ecx, DWORD PTR _sLibName$10[ebp]
  00115	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  0011a	8b f4		 mov	 esi, esp
  0011c	50		 push	 eax
  0011d	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__LoadLibraryA@4
  00123	3b f4		 cmp	 esi, esp
  00125	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0012a	89 45 e8	 mov	 DWORD PTR _hMod$[ebp], eax

; 300  : 		if(!hMod) return 0;

  0012d	83 7d e8 00	 cmp	 DWORD PTR _hMod$[ebp], 0
  00131	75 30		 jne	 SHORT $LN11@CallFuncti
  00133	c7 85 44 ff ff
	ff 00 00 00 00	 mov	 DWORD PTR $T4[ebp], 0
  0013d	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  00141	8d 4d ac	 lea	 ecx, DWORD PTR _sFuncName$9[ebp]
  00144	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00149	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00150	8d 4d cc	 lea	 ecx, DWORD PTR _sLibName$10[ebp]
  00153	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00158	8b 85 44 ff ff
	ff		 mov	 eax, DWORD PTR $T4[ebp]
  0015e	e9 51 02 00 00	 jmp	 $LN1@CallFuncti
$LN11@CallFuncti:

; 301  : 
; 302  : 		fnAddr=GetProcAddress(hMod,sFuncName.c_str());

  00163	8d 4d ac	 lea	 ecx, DWORD PTR _sFuncName$9[ebp]
  00166	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  0016b	8b f4		 mov	 esi, esp
  0016d	50		 push	 eax
  0016e	8b 4d e8	 mov	 ecx, DWORD PTR _hMod$[ebp]
  00171	51		 push	 ecx
  00172	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetProcAddress@8
  00178	3b f4		 cmp	 esi, esp
  0017a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0017f	89 45 ec	 mov	 DWORD PTR _fnAddr$[ebp], eax

; 303  : 		if(!fnAddr)	{

  00182	83 7d ec 00	 cmp	 DWORD PTR _fnAddr$[ebp], 0
  00186	75 43		 jne	 SHORT $LN12@CallFuncti

; 304  : 			FreeLibrary(hMod);

  00188	8b f4		 mov	 esi, esp
  0018a	8b 55 e8	 mov	 edx, DWORD PTR _hMod$[ebp]
  0018d	52		 push	 edx
  0018e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__FreeLibrary@4
  00194	3b f4		 cmp	 esi, esp
  00196	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 305  : 			return 0;

  0019b	c7 85 40 ff ff
	ff 00 00 00 00	 mov	 DWORD PTR $T3[ebp], 0
  001a5	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  001a9	8d 4d ac	 lea	 ecx, DWORD PTR _sFuncName$9[ebp]
  001ac	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  001b1	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  001b8	8d 4d cc	 lea	 ecx, DWORD PTR _sLibName$10[ebp]
  001bb	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  001c0	8b 85 40 ff ff
	ff		 mov	 eax, DWORD PTR $T3[ebp]
  001c6	e9 e9 01 00 00	 jmp	 $LN1@CallFuncti
$LN12@CallFuncti:

; 306  : 		}
; 307  : 	}else{

  001cb	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  001cf	8d 4d ac	 lea	 ecx, DWORD PTR _sFuncName$9[ebp]
  001d2	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  001d7	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  001de	8d 4d cc	 lea	 ecx, DWORD PTR _sLibName$10[ebp]
  001e1	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  001e6	eb 60		 jmp	 SHORT $LN15@CallFuncti
$LN8@CallFuncti:

; 308  : 		if(pDispParams->rgvarg[cArgs-1].vt!=VT_R8){

  001e8	8b 45 f0	 mov	 eax, DWORD PTR _cArgs$[ebp]
  001eb	83 e8 01	 sub	 eax, 1
  001ee	c1 e0 04	 shl	 eax, 4
  001f1	8b 4d 08	 mov	 ecx, DWORD PTR _pDispParams$[ebp]
  001f4	8b 11		 mov	 edx, DWORD PTR [ecx]
  001f6	0f b7 04 02	 movzx	 eax, WORD PTR [edx+eax]
  001fa	83 f8 05	 cmp	 eax, 5
  001fd	74 17		 je	 SHORT $LN13@CallFuncti

; 309  : 			fnAddr=(FARPROC)pDispParams->rgvarg[cArgs-1].lVal;	

  001ff	8b 4d f0	 mov	 ecx, DWORD PTR _cArgs$[ebp]
  00202	83 e9 01	 sub	 ecx, 1
  00205	c1 e1 04	 shl	 ecx, 4
  00208	8b 55 08	 mov	 edx, DWORD PTR _pDispParams$[ebp]
  0020b	8b 02		 mov	 eax, DWORD PTR [edx]
  0020d	8b 4c 08 08	 mov	 ecx, DWORD PTR [eax+ecx+8]
  00211	89 4d ec	 mov	 DWORD PTR _fnAddr$[ebp], ecx

; 310  : 		}else{

  00214	eb 1c		 jmp	 SHORT $LN14@CallFuncti
$LN13@CallFuncti:

; 311  : 			fnAddr=(FARPROC)(uintptr_t)pDispParams->rgvarg[cArgs-1].dblVal;

  00216	8b 55 f0	 mov	 edx, DWORD PTR _cArgs$[ebp]
  00219	83 ea 01	 sub	 edx, 1
  0021c	c1 e2 04	 shl	 edx, 4
  0021f	8b 45 08	 mov	 eax, DWORD PTR _pDispParams$[ebp]
  00222	8b 08		 mov	 ecx, DWORD PTR [eax]
  00224	f2 0f 10 44 11
	08		 movsd	 xmm0, QWORD PTR [ecx+edx+8]
  0022a	e8 00 00 00 00	 call	 __dtoui3
  0022f	89 45 ec	 mov	 DWORD PTR _fnAddr$[ebp], eax
$LN14@CallFuncti:

; 312  : 		}
; 313  : 		cArgs-=1;

  00232	8b 55 f0	 mov	 edx, DWORD PTR _cArgs$[ebp]
  00235	83 ea 01	 sub	 edx, 1
  00238	89 55 f0	 mov	 DWORD PTR _cArgs$[ebp], edx

; 314  : 		if(!fnAddr) return 0;

  0023b	83 7d ec 00	 cmp	 DWORD PTR _fnAddr$[ebp], 0
  0023f	75 07		 jne	 SHORT $LN15@CallFuncti
  00241	33 c0		 xor	 eax, eax
  00243	e9 6c 01 00 00	 jmp	 $LN1@CallFuncti
$LN15@CallFuncti:

; 315  : 	}
; 316  : 
; 317  : 	uintptr_t lRet=0;		

  00248	c7 45 a4 00 00
	00 00		 mov	 DWORD PTR _lRet$[ebp], 0

; 318  : 	uintptr_t iArg;
; 319  : 	size_t i=0;

  0024f	c7 45 94 00 00
	00 00		 mov	 DWORD PTR _i$[ebp], 0

; 320  : 	vector<uintptr_t> vArgs;

  00256	8d 4d 84	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  00259	e8 00 00 00 00	 call	 ??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ ; std::vector<unsigned int,std::allocator<unsigned int> >::vector<unsigned int,std::allocator<unsigned int> >
  0025e	c7 45 fc 06 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 6

; 321  : 
; 322  : 	for(size_t j=0;j<cArgs;j++)

  00265	c7 85 7c ff ff
	ff 00 00 00 00	 mov	 DWORD PTR _j$8[ebp], 0
  0026f	eb 0f		 jmp	 SHORT $LN4@CallFuncti
$LN2@CallFuncti:
  00271	8b 85 7c ff ff
	ff		 mov	 eax, DWORD PTR _j$8[ebp]
  00277	83 c0 01	 add	 eax, 1
  0027a	89 85 7c ff ff
	ff		 mov	 DWORD PTR _j$8[ebp], eax
$LN4@CallFuncti:
  00280	8b 8d 7c ff ff
	ff		 mov	 ecx, DWORD PTR _j$8[ebp]
  00286	3b 4d f0	 cmp	 ecx, DWORD PTR _cArgs$[ebp]
  00289	0f 83 c8 00 00
	00		 jae	 $LN3@CallFuncti

; 323  : 	{
; 324  : 		size_t i=cArgs-j-1;

  0028f	8b 55 f0	 mov	 edx, DWORD PTR _cArgs$[ebp]
  00292	2b 95 7c ff ff
	ff		 sub	 edx, DWORD PTR _j$8[ebp]
  00298	83 ea 01	 sub	 edx, 1
  0029b	89 95 78 ff ff
	ff		 mov	 DWORD PTR _i$7[ebp], edx

; 325  : 		//_variant_t vtmp=pDispParams->rgvarg[i];
; 326  : 		switch(pDispParams->rgvarg[i].vt)

  002a1	8b 85 78 ff ff
	ff		 mov	 eax, DWORD PTR _i$7[ebp]
  002a7	c1 e0 04	 shl	 eax, 4
  002aa	8b 4d 08	 mov	 ecx, DWORD PTR _pDispParams$[ebp]
  002ad	8b 11		 mov	 edx, DWORD PTR [ecx]
  002af	0f b7 04 02	 movzx	 eax, WORD PTR [edx+eax]
  002b3	89 85 38 ff ff
	ff		 mov	 DWORD PTR tv177[ebp], eax
  002b9	8b 8d 38 ff ff
	ff		 mov	 ecx, DWORD PTR tv177[ebp]
  002bf	83 e9 03	 sub	 ecx, 3
  002c2	89 8d 38 ff ff
	ff		 mov	 DWORD PTR tv177[ebp], ecx
  002c8	83 bd 38 ff ff
	ff 10		 cmp	 DWORD PTR tv177[ebp], 16 ; 00000010H
  002cf	77 60		 ja	 SHORT $LN19@CallFuncti
  002d1	8b 95 38 ff ff
	ff		 mov	 edx, DWORD PTR tv177[ebp]
  002d7	0f b6 82 00 00
	00 00		 movzx	 eax, BYTE PTR $LN27@CallFuncti[edx]
  002de	ff 24 85 00 00
	00 00		 jmp	 DWORD PTR $LN35@CallFuncti[eax*4]
$LN16@CallFuncti:

; 327  : 		{
; 328  : 		case VT_R8:
; 329  : 			iArg=(uintptr_t)pDispParams->rgvarg[i].dblVal;

  002e5	8b 8d 78 ff ff
	ff		 mov	 ecx, DWORD PTR _i$7[ebp]
  002eb	c1 e1 04	 shl	 ecx, 4
  002ee	8b 55 08	 mov	 edx, DWORD PTR _pDispParams$[ebp]
  002f1	8b 02		 mov	 eax, DWORD PTR [edx]
  002f3	f2 0f 10 44 08
	08		 movsd	 xmm0, QWORD PTR [eax+ecx+8]
  002f9	e8 00 00 00 00	 call	 __dtoui3
  002fe	89 45 9c	 mov	 DWORD PTR _iArg$[ebp], eax

; 330  : 			break;

  00301	eb 43		 jmp	 SHORT $LN5@CallFuncti
$LN17@CallFuncti:

; 331  : 		case VT_UI4:
; 332  : 		case VT_I4:
; 333  : 			iArg=(uintptr_t)pDispParams->rgvarg[i].lVal;

  00303	8b 8d 78 ff ff
	ff		 mov	 ecx, DWORD PTR _i$7[ebp]
  00309	c1 e1 04	 shl	 ecx, 4
  0030c	8b 55 08	 mov	 edx, DWORD PTR _pDispParams$[ebp]
  0030f	8b 02		 mov	 eax, DWORD PTR [edx]
  00311	8b 4c 08 08	 mov	 ecx, DWORD PTR [eax+ecx+8]
  00315	89 4d 9c	 mov	 DWORD PTR _iArg$[ebp], ecx

; 334  : 			break;

  00318	eb 2c		 jmp	 SHORT $LN5@CallFuncti
$LN18@CallFuncti:

; 335  : 		case VT_BSTR:
; 336  : 			iArg=(uintptr_t)pDispParams->rgvarg[i].bstrVal;

  0031a	8b 95 78 ff ff
	ff		 mov	 edx, DWORD PTR _i$7[ebp]
  00320	c1 e2 04	 shl	 edx, 4
  00323	8b 45 08	 mov	 eax, DWORD PTR _pDispParams$[ebp]
  00326	8b 08		 mov	 ecx, DWORD PTR [eax]
  00328	8b 54 11 08	 mov	 edx, DWORD PTR [ecx+edx+8]
  0032c	89 55 9c	 mov	 DWORD PTR _iArg$[ebp], edx

; 337  : 			break;

  0032f	eb 15		 jmp	 SHORT $LN5@CallFuncti
$LN19@CallFuncti:

; 338  : 		default:
; 339  : 			iArg=(uintptr_t)pDispParams->rgvarg[i].llVal;

  00331	8b 85 78 ff ff
	ff		 mov	 eax, DWORD PTR _i$7[ebp]
  00337	c1 e0 04	 shl	 eax, 4
  0033a	8b 4d 08	 mov	 ecx, DWORD PTR _pDispParams$[ebp]
  0033d	8b 11		 mov	 edx, DWORD PTR [ecx]
  0033f	8b 44 02 08	 mov	 eax, DWORD PTR [edx+eax+8]
  00343	89 45 9c	 mov	 DWORD PTR _iArg$[ebp], eax
$LN5@CallFuncti:

; 340  : 			break;
; 341  : 		}
; 342  : 		vArgs.push_back(iArg);

  00346	8d 4d 9c	 lea	 ecx, DWORD PTR _iArg$[ebp]
  00349	51		 push	 ecx
  0034a	8d 4d 84	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  0034d	e8 00 00 00 00	 call	 ?push_back@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::push_back

; 343  : 	}

  00352	e9 1a ff ff ff	 jmp	 $LN2@CallFuncti
$LN3@CallFuncti:

; 344  : 
; 345  : 	lRet=CallFunction(fnAddr, vArgs.data(), vArgs.size(),bcdecl);

  00357	0f b6 55 0c	 movzx	 edx, BYTE PTR _bcdecl$[ebp]
  0035b	52		 push	 edx
  0035c	8d 4d 84	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  0035f	e8 00 00 00 00	 call	 ?size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::size
  00364	50		 push	 eax
  00365	8d 4d 84	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  00368	e8 00 00 00 00	 call	 ?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::data
  0036d	50		 push	 eax
  0036e	8b 45 ec	 mov	 eax, DWORD PTR _fnAddr$[ebp]
  00371	50		 push	 eax
  00372	e8 00 00 00 00	 call	 ?CallFunction@CallApi@@YAIP6GHXZPAII_N@Z ; CallApi::CallFunction
  00377	83 c4 10	 add	 esp, 16			; 00000010H
  0037a	89 45 a4	 mov	 DWORD PTR _lRet$[ebp], eax

; 346  : 
; 347  : 	if(hMod) FreeLibrary(hMod);

  0037d	83 7d e8 00	 cmp	 DWORD PTR _hMod$[ebp], 0
  00381	74 13		 je	 SHORT $LN20@CallFuncti
  00383	8b f4		 mov	 esi, esp
  00385	8b 4d e8	 mov	 ecx, DWORD PTR _hMod$[ebp]
  00388	51		 push	 ecx
  00389	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__FreeLibrary@4
  0038f	3b f4		 cmp	 esi, esp
  00391	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN20@CallFuncti:

; 348  : 	return lRet;

  00396	8b 55 a4	 mov	 edx, DWORD PTR _lRet$[ebp]
  00399	89 95 3c ff ff
	ff		 mov	 DWORD PTR $T2[ebp], edx
  0039f	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  003a6	8d 4d 84	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  003a9	e8 00 00 00 00	 call	 ??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ ; std::vector<unsigned int,std::allocator<unsigned int> >::~vector<unsigned int,std::allocator<unsigned int> >
  003ae	8b 85 3c ff ff
	ff		 mov	 eax, DWORD PTR $T2[ebp]
$LN1@CallFuncti:

; 349  : }

  003b4	52		 push	 edx
  003b5	8b cd		 mov	 ecx, ebp
  003b7	50		 push	 eax
  003b8	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN34@CallFuncti
  003be	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  003c3	58		 pop	 eax
  003c4	5a		 pop	 edx
  003c5	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  003c8	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  003cf	5f		 pop	 edi
  003d0	5e		 pop	 esi
  003d1	81 c4 c8 00 00
	00		 add	 esp, 200		; 000000c8H
  003d7	3b ec		 cmp	 ebp, esp
  003d9	e8 00 00 00 00	 call	 __RTC_CheckEsp
  003de	8b e5		 mov	 esp, ebp
  003e0	5d		 pop	 ebp
  003e1	c3		 ret	 0
  003e2	66 90		 npad	 2
$LN34@CallFuncti:
  003e4	04 00 00 00	 DD	 4
  003e8	00 00 00 00	 DD	 $LN33@CallFuncti
$LN33@CallFuncti:
  003ec	cc ff ff ff	 DD	 -52			; ffffffccH
  003f0	18 00 00 00	 DD	 24			; 00000018H
  003f4	00 00 00 00	 DD	 $LN28@CallFuncti
  003f8	ac ff ff ff	 DD	 -84			; ffffffacH
  003fc	18 00 00 00	 DD	 24			; 00000018H
  00400	00 00 00 00	 DD	 $LN29@CallFuncti
  00404	9c ff ff ff	 DD	 -100			; ffffff9cH
  00408	04 00 00 00	 DD	 4
  0040c	00 00 00 00	 DD	 $LN30@CallFuncti
  00410	84 ff ff ff	 DD	 -124			; ffffff84H
  00414	0c 00 00 00	 DD	 12			; 0000000cH
  00418	00 00 00 00	 DD	 $LN31@CallFuncti
$LN31@CallFuncti:
  0041c	76		 DB	 118			; 00000076H
  0041d	41		 DB	 65			; 00000041H
  0041e	72		 DB	 114			; 00000072H
  0041f	67		 DB	 103			; 00000067H
  00420	73		 DB	 115			; 00000073H
  00421	00		 DB	 0
$LN30@CallFuncti:
  00422	69		 DB	 105			; 00000069H
  00423	41		 DB	 65			; 00000041H
  00424	72		 DB	 114			; 00000072H
  00425	67		 DB	 103			; 00000067H
  00426	00		 DB	 0
$LN29@CallFuncti:
  00427	73		 DB	 115			; 00000073H
  00428	46		 DB	 70			; 00000046H
  00429	75		 DB	 117			; 00000075H
  0042a	6e		 DB	 110			; 0000006eH
  0042b	63		 DB	 99			; 00000063H
  0042c	4e		 DB	 78			; 0000004eH
  0042d	61		 DB	 97			; 00000061H
  0042e	6d		 DB	 109			; 0000006dH
  0042f	65		 DB	 101			; 00000065H
  00430	00		 DB	 0
$LN28@CallFuncti:
  00431	73		 DB	 115			; 00000073H
  00432	4c		 DB	 76			; 0000004cH
  00433	69		 DB	 105			; 00000069H
  00434	62		 DB	 98			; 00000062H
  00435	4e		 DB	 78			; 0000004eH
  00436	61		 DB	 97			; 00000061H
  00437	6d		 DB	 109			; 0000006dH
  00438	65		 DB	 101			; 00000065H
  00439	00		 DB	 0
  0043a	66 90		 npad	 2
$LN35@CallFuncti:
  0043c	00 00 00 00	 DD	 $LN17@CallFuncti
  00440	00 00 00 00	 DD	 $LN16@CallFuncti
  00444	00 00 00 00	 DD	 $LN18@CallFuncti
  00448	00 00 00 00	 DD	 $LN19@CallFuncti
$LN27@CallFuncti:
  0044c	00		 DB	 0
  0044d	03		 DB	 3
  0044e	01		 DB	 1
  0044f	03		 DB	 3
  00450	03		 DB	 3
  00451	02		 DB	 2
  00452	03		 DB	 3
  00453	03		 DB	 3
  00454	03		 DB	 3
  00455	03		 DB	 3
  00456	03		 DB	 3
  00457	03		 DB	 3
  00458	03		 DB	 3
  00459	03		 DB	 3
  0045a	03		 DB	 3
  0045b	03		 DB	 3
  0045c	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$0:
  00000	8d 8d 60 ff ff
	ff		 lea	 ecx, DWORD PTR $T6[ebp]
  00006	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$1:
  0000b	8d 4d cc	 lea	 ecx, DWORD PTR _sLibName$10[ebp]
  0000e	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$2:
  00013	8d 8d 48 ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  00019	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$3:
  0001e	8d 4d ac	 lea	 ecx, DWORD PTR _sFuncName$9[ebp]
  00021	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z$4:
  00026	8d 4d 84	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  00029	e9 00 00 00 00	 jmp	 ??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ ; std::vector<unsigned int,std::allocator<unsigned int> >::~vector<unsigned int,std::allocator<unsigned int> >
__ehhandler$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z:
  0002e	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z
  00033	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?CallFunction@CallApi@@YAIPAUtagDISPPARAMS@@_N@Z ENDP	; CallApi::CallFunction
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ PROC	; std::vector<unsigned int,std::allocator<unsigned int> >::size, COMDAT
; _this$ = ecx

; 1703 : 		{	// return length of sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1704 : 		return (static_cast<size_type>(this->_Mylast() - this->_Myfirst()));

  0000f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00012	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00017	8b f0		 mov	 esi, eax
  00019	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001c	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00021	8b 0e		 mov	 ecx, DWORD PTR [esi]
  00023	2b 08		 sub	 ecx, DWORD PTR [eax]
  00025	c1 f9 02	 sar	 ecx, 2
  00028	8b c1		 mov	 eax, ecx

; 1705 : 		}

  0002a	5e		 pop	 esi
  0002b	83 c4 04	 add	 esp, 4
  0002e	3b ec		 cmp	 ebp, esp
  00030	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00035	8b e5		 mov	 esp, ebp
  00037	5d		 pop	 ebp
  00038	c3		 ret	 0
?size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ENDP	; std::vector<unsigned int,std::allocator<unsigned int> >::size
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ PROC	; std::vector<unsigned int,std::allocator<unsigned int> >::data, COMDAT
; _this$ = ecx

; 1608 : 		{	// return address of first element

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1609 : 		return (_Unfancy_maybe_null(this->_Myfirst()));

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  0002b	8b 00		 mov	 eax, DWORD PTR [eax]
  0002d	50		 push	 eax
  0002e	e8 00 00 00 00	 call	 ??$_Unfancy_maybe_null@I@std@@YAPAIPAI@Z ; std::_Unfancy_maybe_null<unsigned int>
  00033	83 c4 04	 add	 esp, 4

; 1610 : 		}

  00036	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00039	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00040	83 c4 10	 add	 esp, 16			; 00000010H
  00043	3b ec		 cmp	 ebp, esp
  00045	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004a	8b e5		 mov	 esp, ebp
  0004c	5d		 pop	 ebp
  0004d	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?data@?$vector@IV?$allocator@I@std@@@std@@QAEPAIXZ ENDP	; std::vector<unsigned int,std::allocator<unsigned int> >::data
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?push_back@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Val$ = 8						; size = 4
?push_back@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::push_back, COMDAT
; _this$ = ecx

; 931  : 		{	// insert element at end, provide strong guarantee

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 932  : 		emplace_back(_Val);

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	e8 00 00 00 00	 call	 ??$emplace_back@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::emplace_back<unsigned int const &>

; 933  : 		}

  0001a	83 c4 04	 add	 esp, 4
  0001d	3b ec		 cmp	 ebp, esp
  0001f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00024	8b e5		 mov	 esp, ebp
  00026	5d		 pop	 ebp
  00027	c2 04 00	 ret	 4
?push_back@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::push_back
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??$emplace_back@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z
_TEXT	SEGMENT
__Result$ = -8						; size = 4
_this$ = -4						; size = 4
_<_Val_0>$ = 8						; size = 4
??$emplace_back@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::emplace_back<unsigned int const &>, COMDAT
; _this$ = ecx

; 916  : 		{	// insert by perfectly forwarding into element at end, provide strong guarantee

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 917  : 		if (_Has_unused_capacity())

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?_Has_unused_capacity@?$vector@IV?$allocator@I@std@@@std@@ABE_NXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::_Has_unused_capacity
  0001f	0f b6 c0	 movzx	 eax, al
  00022	85 c0		 test	 eax, eax
  00024	74 17		 je	 SHORT $LN2@emplace_ba

; 918  : 			{
; 919  : 			return (_Emplace_back_with_unused_capacity(_STD forward<_Valty>(_Val)...));

  00026	8b 4d 08	 mov	 ecx, DWORD PTR _<_Val_0>$[ebp]
  00029	51		 push	 ecx
  0002a	e8 00 00 00 00	 call	 ??$forward@ABI@std@@YAABIABI@Z ; std::forward<unsigned int const &>
  0002f	83 c4 04	 add	 esp, 4
  00032	50		 push	 eax
  00033	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00036	e8 00 00 00 00	 call	 ??$_Emplace_back_with_unused_capacity@ABI@?$vector@IV?$allocator@I@std@@@std@@AAEXABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_back_with_unused_capacity<unsigned int const &>
  0003b	eb 23		 jmp	 SHORT $LN1@emplace_ba
$LN2@emplace_ba:

; 920  : 			}
; 921  : 
; 922  : 		_Ty& _Result = *_Emplace_reallocate(this->_Mylast(), _STD forward<_Valty>(_Val)...);

  0003d	8b 55 08	 mov	 edx, DWORD PTR _<_Val_0>$[ebp]
  00040	52		 push	 edx
  00041	e8 00 00 00 00	 call	 ??$forward@ABI@std@@YAABIABI@Z ; std::forward<unsigned int const &>
  00046	83 c4 04	 add	 esp, 4
  00049	50		 push	 eax
  0004a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0004d	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00052	8b 00		 mov	 eax, DWORD PTR [eax]
  00054	50		 push	 eax
  00055	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00058	e8 00 00 00 00	 call	 ??$_Emplace_reallocate@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEPAIQAIABI@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_reallocate<unsigned int const &>
  0005d	89 45 f8	 mov	 DWORD PTR __Result$[ebp], eax
$LN1@emplace_ba:

; 923  : #if _HAS_CXX17
; 924  : 		return (_Result);
; 925  : #else /* ^^^ _HAS_CXX17 ^^^ // vvv !_HAS_CXX17 vvv */
; 926  : 		(void)_Result;
; 927  : #endif /* _HAS_CXX17 */
; 928  : 		}

  00060	83 c4 08	 add	 esp, 8
  00063	3b ec		 cmp	 ebp, esp
  00065	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0006a	8b e5		 mov	 esp, ebp
  0006c	5d		 pop	 ebp
  0006d	c2 04 00	 ret	 4
??$emplace_back@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEXABI@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::emplace_back<unsigned int const &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??$_Emplace_back_with_unused_capacity@ABI@?$vector@IV?$allocator@I@std@@@std@@AAEXABI@Z
_TEXT	SEGMENT
tv142 = -12						; size = 4
__Result$ = -8						; size = 4
_this$ = -4						; size = 4
_<_Val_0>$ = 8						; size = 4
??$_Emplace_back_with_unused_capacity@ABI@?$vector@IV?$allocator@I@std@@@std@@AAEXABI@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_back_with_unused_capacity<unsigned int const &>, COMDAT
; _this$ = ecx

; 900  : 		{	// insert by perfectly forwarding into element at end, provide strong guarantee

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 901  : 			// pre: _Has_unused_capacity()
; 902  : 		_Alty_traits::construct(this->_Getal(), _Unfancy(this->_Mylast()), _STD forward<_Valty>(_Val)...);

  0001e	8b 45 08	 mov	 eax, DWORD PTR _<_Val_0>$[ebp]
  00021	50		 push	 eax
  00022	e8 00 00 00 00	 call	 ??$forward@ABI@std@@YAABIABI@Z ; std::forward<unsigned int const &>
  00027	83 c4 04	 add	 esp, 4
  0002a	50		 push	 eax
  0002b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002e	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00033	8b 08		 mov	 ecx, DWORD PTR [eax]
  00035	51		 push	 ecx
  00036	e8 00 00 00 00	 call	 ??$_Unfancy@I@std@@YAPAIPAI@Z ; std::_Unfancy<unsigned int>
  0003b	83 c4 04	 add	 esp, 4
  0003e	50		 push	 eax
  0003f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00042	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  00047	50		 push	 eax
  00048	e8 00 00 00 00	 call	 ??$construct@IABI@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAXAAV?$allocator@I@1@QAIABI@Z ; std::_Default_allocator_traits<std::allocator<unsigned int> >::construct<unsigned int,unsigned int const &>
  0004d	83 c4 0c	 add	 esp, 12			; 0000000cH

; 903  : 		_Orphan_range(this->_Mylast(), this->_Mylast());

  00050	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00053	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00058	8b 10		 mov	 edx, DWORD PTR [eax]
  0005a	52		 push	 edx
  0005b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0005e	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00063	8b 00		 mov	 eax, DWORD PTR [eax]
  00065	50		 push	 eax
  00066	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00069	e8 00 00 00 00	 call	 ?_Orphan_range@?$vector@IV?$allocator@I@std@@@std@@ABEXPAI0@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Orphan_range

; 904  : 		_Ty& _Result = *this->_Mylast();

  0006e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00071	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00076	8b 08		 mov	 ecx, DWORD PTR [eax]
  00078	89 4d f8	 mov	 DWORD PTR __Result$[ebp], ecx

; 905  : 		++this->_Mylast();

  0007b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0007e	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00083	89 45 f4	 mov	 DWORD PTR tv142[ebp], eax
  00086	8b 55 f4	 mov	 edx, DWORD PTR tv142[ebp]
  00089	8b 02		 mov	 eax, DWORD PTR [edx]
  0008b	83 c0 04	 add	 eax, 4
  0008e	8b 4d f4	 mov	 ecx, DWORD PTR tv142[ebp]
  00091	89 01		 mov	 DWORD PTR [ecx], eax

; 906  : #if _HAS_CXX17
; 907  : 		return (_Result);
; 908  : #else /* ^^^ _HAS_CXX17 ^^^ // vvv !_HAS_CXX17 vvv */
; 909  : 		(void)_Result;
; 910  : #endif /* _HAS_CXX17 */
; 911  : 		}

  00093	83 c4 0c	 add	 esp, 12			; 0000000cH
  00096	3b ec		 cmp	 ebp, esp
  00098	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0009d	8b e5		 mov	 esp, ebp
  0009f	5d		 pop	 ebp
  000a0	c2 04 00	 ret	 4
??$_Emplace_back_with_unused_capacity@ABI@?$vector@IV?$allocator@I@std@@@std@@AAEXABI@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_back_with_unused_capacity<unsigned int const &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ PROC	; std::vector<unsigned int,std::allocator<unsigned int> >::~vector<unsigned int,std::allocator<unsigned int> >, COMDAT
; _this$ = ecx

; 893  : 		{	// destroy the object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 894  : 		_Tidy();

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?_Tidy@?$vector@IV?$allocator@I@std@@@std@@AAEXXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::_Tidy

; 895  : 		}

  0002b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0002e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00035	83 c4 10	 add	 esp, 16			; 00000010H
  00038	3b ec		 cmp	 ebp, esp
  0003a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003f	8b e5		 mov	 esp, ebp
  00041	5d		 pop	 ebp
  00042	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$vector@IV?$allocator@I@std@@@std@@QAE@XZ ENDP	; std::vector<unsigned int,std::allocator<unsigned int> >::~vector<unsigned int,std::allocator<unsigned int> >
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ PROC	; std::vector<unsigned int,std::allocator<unsigned int> >::vector<unsigned int,std::allocator<unsigned int> >, COMDAT
; _this$ = ecx

; 651  : 		{	// construct empty vector

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 650  : 		: _Mybase()

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAE@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >

; 652  : 		}

  0002b	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0002e	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00031	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00038	83 c4 10	 add	 esp, 16			; 00000010H
  0003b	3b ec		 cmp	 ebp, esp
  0003d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00042	8b e5		 mov	 esp, ebp
  00044	5d		 pop	 ebp
  00045	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$vector@IV?$allocator@I@std@@@std@@QAE@XZ ENDP	; std::vector<unsigned int,std::allocator<unsigned int> >::vector<unsigned int,std::allocator<unsigned int> >
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast, COMDAT
; _this$ = ecx

; 590  : 		{	// return const reference to _Mylast

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 591  : 		return (_Get_data()._Mylast);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
  00016	83 c0 04	 add	 eax, 4

; 592  : 		}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast, COMDAT
; _this$ = ecx

; 585  : 		{	// return reference to _Mylast

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 586  : 		return (_Get_data()._Mylast);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
  00016	83 c0 04	 add	 eax, 4

; 587  : 		}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst, COMDAT
; _this$ = ecx

; 580  : 		{	// return const reference to _Myfirst

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 581  : 		return (_Get_data()._Myfirst);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data

; 582  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst, COMDAT
; _this$ = ecx

; 575  : 		{	// return reference to _Myfirst

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 576  : 		return (_Get_data()._Myfirst);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data

; 577  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal, COMDAT
; _this$ = ecx

; 544  : 		{	// return reference to allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 545  : 		return (_Mypair._Get_first());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$allocator@I@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first

; 546  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??0?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAE@XZ
_TEXT	SEGMENT
$T1 = -5						; size = 1
_this$ = -4						; size = 4
??0?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAE@XZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >, COMDAT
; _this$ = ecx

; 422  : 		{	// default construct allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 421  : 		: _Mypair(_Zero_then_variadic_args_t())

  00017	0f b6 45 fb	 movzx	 eax, BYTE PTR $T1[ebp]
  0001b	50		 push	 eax
  0001c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001f	e8 00 00 00 00	 call	 ??$?0$$V@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAE@U_Zero_then_variadic_args_t@1@@Z ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1><>

; 423  : 		}

  00024	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00027	83 c4 08	 add	 esp, 8
  0002a	3b ec		 cmp	 ebp, esp
  0002c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00031	8b e5		 mov	 esp, ebp
  00033	5d		 pop	 ebp
  00034	c3		 ret	 0
??0?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAE@XZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Orphan_range@?$vector@IV?$allocator@I@std@@@std@@ABEXPAI0@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 4
___formal$ = 12						; size = 4
?_Orphan_range@?$vector@IV?$allocator@I@std@@@std@@ABEXPAI0@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Orphan_range, COMDAT
; _this$ = ecx

; 1959 : 		{	// orphan iterators within specified (inclusive) range

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1960 : 		}

  0000e	8b e5		 mov	 esp, ebp
  00010	5d		 pop	 ebp
  00011	c2 08 00	 ret	 8
?_Orphan_range@?$vector@IV?$allocator@I@std@@@std@@ABEXPAI0@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Orphan_range
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Tidy@?$vector@IV?$allocator@I@std@@@std@@AAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Tidy@?$vector@IV?$allocator@I@std@@@std@@AAEXXZ PROC	; std::vector<unsigned int,std::allocator<unsigned int> >::_Tidy, COMDAT
; _this$ = ecx

; 1910 : 		{	// free all storage

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1911 : 		this->_Orphan_all();

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Orphan_all@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEXXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Orphan_all

; 1912 : 
; 1913 : 		if (this->_Myfirst() != pointer())

  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  0001e	83 38 00	 cmp	 DWORD PTR [eax], 0
  00021	74 6b		 je	 SHORT $LN1@Tidy

; 1914 : 			{	// destroy and deallocate old array
; 1915 : 			_Destroy(this->_Myfirst(), this->_Mylast());

  00023	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  0002b	8b 00		 mov	 eax, DWORD PTR [eax]
  0002d	50		 push	 eax
  0002e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00031	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00036	8b 08		 mov	 ecx, DWORD PTR [eax]
  00038	51		 push	 ecx
  00039	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0003c	e8 00 00 00 00	 call	 ?_Destroy@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI0@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Destroy

; 1916 : 			this->_Getal().deallocate(this->_Myfirst(), capacity());

  00041	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00044	e8 00 00 00 00	 call	 ?capacity@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::capacity
  00049	50		 push	 eax
  0004a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0004d	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00052	8b 10		 mov	 edx, DWORD PTR [eax]
  00054	52		 push	 edx
  00055	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00058	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  0005d	8b c8		 mov	 ecx, eax
  0005f	e8 00 00 00 00	 call	 ?deallocate@?$allocator@I@std@@QAEXQAII@Z ; std::allocator<unsigned int>::deallocate

; 1917 : 
; 1918 : 			this->_Myfirst() = pointer();

  00064	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00067	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  0006c	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 1919 : 			this->_Mylast() = pointer();

  00072	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00075	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  0007a	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 1920 : 			this->_Myend() = pointer();

  00080	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00083	e8 00 00 00 00	 call	 ?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
  00088	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0
$LN1@Tidy:

; 1921 : 			}
; 1922 : 		}

  0008e	83 c4 04	 add	 esp, 4
  00091	3b ec		 cmp	 ebp, esp
  00093	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00098	8b e5		 mov	 esp, ebp
  0009a	5d		 pop	 ebp
  0009b	c3		 ret	 0
?_Tidy@?$vector@IV?$allocator@I@std@@@std@@AAEXXZ ENDP	; std::vector<unsigned int,std::allocator<unsigned int> >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Has_unused_capacity@?$vector@IV?$allocator@I@std@@@std@@ABE_NXZ
_TEXT	SEGMENT
tv77 = -8						; size = 4
_this$ = -4						; size = 4
?_Has_unused_capacity@?$vector@IV?$allocator@I@std@@@std@@ABE_NXZ PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Has_unused_capacity, COMDAT
; _this$ = ecx

; 1725 : 		{	// micro-optimization for capacity() != size()

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1726 : 		return (this->_Myend() != this->_Mylast());

  00018	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001b	e8 00 00 00 00	 call	 ?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
  00020	8b f0		 mov	 esi, eax
  00022	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00025	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  0002a	8b 0e		 mov	 ecx, DWORD PTR [esi]
  0002c	3b 08		 cmp	 ecx, DWORD PTR [eax]
  0002e	74 09		 je	 SHORT $LN3@Has_unused
  00030	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv77[ebp], 1
  00037	eb 07		 jmp	 SHORT $LN4@Has_unused
$LN3@Has_unused:
  00039	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv77[ebp], 0
$LN4@Has_unused:
  00040	8a 45 f8	 mov	 al, BYTE PTR tv77[ebp]

; 1727 : 		}

  00043	5e		 pop	 esi
  00044	83 c4 08	 add	 esp, 8
  00047	3b ec		 cmp	 ebp, esp
  00049	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004e	8b e5		 mov	 esp, ebp
  00050	5d		 pop	 ebp
  00051	c3		 ret	 0
?_Has_unused_capacity@?$vector@IV?$allocator@I@std@@@std@@ABE_NXZ ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Has_unused_capacity
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$allocator@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$allocator@I@2@XZ PROC ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first, COMDAT
; _this$ = ecx

; 291  : 		{	// return reference to first

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 292  : 		return (*this);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 293  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$allocator@I@2@XZ ENDP ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend, COMDAT
; _this$ = ecx

; 600  : 		{	// return const reference to _Myend

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 601  : 		return (_Get_data()._Myend);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
  00016	83 c0 08	 add	 eax, 8

; 602  : 		}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend, COMDAT
; _this$ = ecx

; 595  : 		{	// return reference to _Myend

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 596  : 		return (_Get_data()._Myend);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
  00016	83 c0 08	 add	 eax, 8

; 597  : 		}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data, COMDAT
; _this$ = ecx

; 559  : 		{	// return const reference to _Vector_val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 560  : 		return (_Mypair._Get_second());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second

; 561  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data, COMDAT
; _this$ = ecx

; 554  : 		{	// return reference to _Vector_val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 555  : 		return (_Mypair._Get_second());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second

; 556  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Orphan_all@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Orphan_all@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEXXZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Orphan_all, COMDAT
; _this$ = ecx

; 534  : 		{	// orphan all iterators

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 535  : 		_Get_data()._Orphan_all();

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Get_data
  00016	8b c8		 mov	 ecx, eax
  00018	e8 00 00 00 00	 call	 ?_Orphan_all@_Container_base0@std@@QAEXXZ ; std::_Container_base0::_Orphan_all

; 536  : 		}

  0001d	83 c4 04	 add	 esp, 4
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	8b e5		 mov	 esp, ebp
  00029	5d		 pop	 ebp
  0002a	c3		 ret	 0
?_Orphan_all@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEXXZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Orphan_all
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Destroy@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI0@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
?_Destroy@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI0@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Destroy, COMDAT
; _this$ = ecx

; 1848 : 		{	// destroy [_First, _Last) using allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1849 : 		_Destroy_range(_First, _Last, this->_Getal());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  00016	50		 push	 eax
  00017	8b 45 0c	 mov	 eax, DWORD PTR __Last$[ebp]
  0001a	50		 push	 eax
  0001b	8b 4d 08	 mov	 ecx, DWORD PTR __First$[ebp]
  0001e	51		 push	 ecx
  0001f	e8 00 00 00 00	 call	 ??$_Destroy_range@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@@Z ; std::_Destroy_range<std::allocator<unsigned int> >
  00024	83 c4 0c	 add	 esp, 12			; 0000000cH

; 1850 : 		}

  00027	83 c4 04	 add	 esp, 4
  0002a	3b ec		 cmp	 ebp, esp
  0002c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00031	8b e5		 mov	 esp, ebp
  00033	5d		 pop	 ebp
  00034	c2 08 00	 ret	 8
?_Destroy@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI0@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Destroy
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?capacity@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?capacity@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::capacity, COMDAT
; _this$ = ecx

; 1714 : 		{	// return current length of allocated storage

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1715 : 		return (static_cast<size_type>(this->_Myend() - this->_Myfirst()));

  0000f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00012	e8 00 00 00 00	 call	 ?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
  00017	8b f0		 mov	 esi, eax
  00019	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001c	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABQAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00021	8b 0e		 mov	 ecx, DWORD PTR [esi]
  00023	2b 08		 sub	 ecx, DWORD PTR [eax]
  00025	c1 f9 02	 sar	 ecx, 2
  00028	8b c1		 mov	 eax, ecx

; 1716 : 		}

  0002a	5e		 pop	 esi
  0002b	83 c4 04	 add	 esp, 4
  0002e	3b ec		 cmp	 ebp, esp
  00030	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00035	8b e5		 mov	 esp, ebp
  00037	5d		 pop	 ebp
  00038	c3		 ret	 0
?capacity@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::capacity
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?deallocate@?$allocator@I@std@@QAEXQAII@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Ptr$ = 8						; size = 4
__Count$ = 12						; size = 4
?deallocate@?$allocator@I@std@@QAEXQAII@Z PROC		; std::allocator<unsigned int>::deallocate, COMDAT
; _this$ = ecx

; 990  : 		{	// deallocate object at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 991  : 		// no overflow check on the following multiply; we assume _Allocate did that check
; 992  : 		_Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Count$[ebp]
  00011	c1 e0 02	 shl	 eax, 2
  00014	50		 push	 eax
  00015	8b 4d 08	 mov	 ecx, DWORD PTR __Ptr$[ebp]
  00018	51		 push	 ecx
  00019	e8 00 00 00 00	 call	 ??$_Deallocate@$07$0A@@std@@YAXPAXI@Z ; std::_Deallocate<8,0>
  0001e	83 c4 08	 add	 esp, 8

; 993  : 		}

  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 08 00	 ret	 8
?deallocate@?$allocator@I@std@@QAEXQAII@Z ENDP		; std::allocator<unsigned int>::deallocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second, COMDAT
; _this$ = ecx

; 306  : 		{	// return const reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 307  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 308  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second, COMDAT
; _this$ = ecx

; 301  : 		{	// return reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 302  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 303  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAEAAV?$_Vector_val@U?$_Simple_types@I@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??$_Unfancy_maybe_null@I@std@@YAPAIPAI@Z
_TEXT	SEGMENT
__Ptr$ = 8						; size = 4
??$_Unfancy_maybe_null@I@std@@YAPAIPAI@Z PROC		; std::_Unfancy_maybe_null<unsigned int>, COMDAT

; 617  : 	{	// do nothing for plain pointers

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 618  : 	return (_Ptr);

  00003	8b 45 08	 mov	 eax, DWORD PTR __Ptr$[ebp]

; 619  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$_Unfancy_maybe_null@I@std@@YAPAIPAI@Z ENDP		; std::_Unfancy_maybe_null<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$forward@ABI@std@@YAABIABI@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$forward@ABI@std@@YAABIABI@Z PROC			; std::forward<unsigned int const &>, COMDAT

; 1573 : 	{	// forward an lvalue as either an lvalue or an rvalue

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1574 : 	return (static_cast<_Ty&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1575 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$forward@ABI@std@@YAABIABI@Z ENDP			; std::forward<unsigned int const &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstddef
;	COMDAT ??$_Unfancy@I@std@@YAPAIPAI@Z
_TEXT	SEGMENT
__Ptr$ = 8						; size = 4
??$_Unfancy@I@std@@YAPAIPAI@Z PROC			; std::_Unfancy<unsigned int>, COMDAT

; 345  : 	{	// do nothing for plain pointers

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 346  : 	return (_Ptr);

  00003	8b 45 08	 mov	 eax, DWORD PTR __Ptr$[ebp]

; 347  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$_Unfancy@I@std@@YAPAIPAI@Z ENDP			; std::_Unfancy<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$construct@IABI@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAXAAV?$allocator@I@1@QAIABI@Z
_TEXT	SEGMENT
$T1 = -4						; size = 4
___formal$ = 8						; size = 4
__Ptr$ = 12						; size = 4
_<_Args_0>$ = 16					; size = 4
??$construct@IABI@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAXAAV?$allocator@I@1@QAIABI@Z PROC ; std::_Default_allocator_traits<std::allocator<unsigned int> >::construct<unsigned int,unsigned int const &>, COMDAT

; 879  : 		{	// construct _Objty(_Types...) at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 880  : 		::new (const_cast<void *>(static_cast<const volatile void *>(_Ptr)))

  0000b	8b 45 0c	 mov	 eax, DWORD PTR __Ptr$[ebp]
  0000e	50		 push	 eax
  0000f	6a 04		 push	 4
  00011	e8 00 00 00 00	 call	 ??2@YAPAXIPAX@Z		; operator new
  00016	83 c4 08	 add	 esp, 8
  00019	89 45 fc	 mov	 DWORD PTR $T1[ebp], eax
  0001c	8b 4d 10	 mov	 ecx, DWORD PTR _<_Args_0>$[ebp]
  0001f	51		 push	 ecx
  00020	e8 00 00 00 00	 call	 ??$forward@ABI@std@@YAABIABI@Z ; std::forward<unsigned int const &>
  00025	83 c4 04	 add	 esp, 4
  00028	8b 55 fc	 mov	 edx, DWORD PTR $T1[ebp]
  0002b	8b 00		 mov	 eax, DWORD PTR [eax]
  0002d	89 02		 mov	 DWORD PTR [edx], eax

; 881  : 			_Objty(_STD forward<_Types>(_Args)...);
; 882  : 		}

  0002f	83 c4 04	 add	 esp, 4
  00032	3b ec		 cmp	 ebp, esp
  00034	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00039	8b e5		 mov	 esp, ebp
  0003b	5d		 pop	 ebp
  0003c	c3		 ret	 0
??$construct@IABI@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAXAAV?$allocator@I@1@QAIABI@Z ENDP ; std::_Default_allocator_traits<std::allocator<unsigned int> >::construct<unsigned int,unsigned int const &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??$_Emplace_reallocate@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEPAIQAIABI@Z
_TEXT	SEGMENT
__Constructed_first$ = -36				; size = 4
__Constructed_last$ = -32				; size = 4
__Newvec$ = -28						; size = 4
__Newcapacity$ = -24					; size = 4
__Newsize$ = -20					; size = 4
__Oldsize$ = -16					; size = 4
__Al$ = -12						; size = 4
__Whereoff$ = -8					; size = 4
_this$ = -4						; size = 4
__Whereptr$ = 8						; size = 4
_<_Val_0>$ = 12						; size = 4
??$_Emplace_reallocate@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEPAIQAIABI@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_reallocate<unsigned int const &>, COMDAT
; _this$ = ecx

; 942  : 		{	// reallocate and insert by perfectly forwarding _Val at _Whereptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 24	 sub	 esp, 36			; 00000024H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0000e	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00011	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00014	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00017	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0001a	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0001d	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00020	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00023	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  00026	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 943  : 			// pre: !_Has_unused_capacity()
; 944  : 		const size_type _Whereoff = static_cast<size_type>(_Whereptr - this->_Myfirst());

  00029	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002c	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00031	8b 4d 08	 mov	 ecx, DWORD PTR __Whereptr$[ebp]
  00034	2b 08		 sub	 ecx, DWORD PTR [eax]
  00036	c1 f9 02	 sar	 ecx, 2
  00039	89 4d f8	 mov	 DWORD PTR __Whereoff$[ebp], ecx

; 945  : 		_Alty& _Al = this->_Getal();

  0003c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0003f	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  00044	89 45 f4	 mov	 DWORD PTR __Al$[ebp], eax

; 946  : 		const size_type _Oldsize = size();

  00047	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0004a	e8 00 00 00 00	 call	 ?size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::size
  0004f	89 45 f0	 mov	 DWORD PTR __Oldsize$[ebp], eax

; 947  : 
; 948  : 		if (_Oldsize == max_size())

  00052	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00055	e8 00 00 00 00	 call	 ?max_size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::max_size
  0005a	39 45 f0	 cmp	 DWORD PTR __Oldsize$[ebp], eax
  0005d	75 05		 jne	 SHORT $LN2@Emplace_re

; 949  : 			{
; 950  : 			_Xlength();

  0005f	e8 00 00 00 00	 call	 ?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::_Xlength
$LN2@Emplace_re:

; 951  : 			}
; 952  : 
; 953  : 		const size_type _Newsize = _Oldsize + 1;

  00064	8b 55 f0	 mov	 edx, DWORD PTR __Oldsize$[ebp]
  00067	83 c2 01	 add	 edx, 1
  0006a	89 55 ec	 mov	 DWORD PTR __Newsize$[ebp], edx

; 954  : 		const size_type _Newcapacity = _Calculate_growth(_Newsize);

  0006d	8b 45 ec	 mov	 eax, DWORD PTR __Newsize$[ebp]
  00070	50		 push	 eax
  00071	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00074	e8 00 00 00 00	 call	 ?_Calculate_growth@?$vector@IV?$allocator@I@std@@@std@@ABEII@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Calculate_growth
  00079	89 45 e8	 mov	 DWORD PTR __Newcapacity$[ebp], eax

; 955  : 
; 956  : 		const pointer _Newvec = _Al.allocate(_Newcapacity);

  0007c	8b 4d e8	 mov	 ecx, DWORD PTR __Newcapacity$[ebp]
  0007f	51		 push	 ecx
  00080	8b 4d f4	 mov	 ecx, DWORD PTR __Al$[ebp]
  00083	e8 00 00 00 00	 call	 ?allocate@?$allocator@I@std@@QAEPAII@Z ; std::allocator<unsigned int>::allocate
  00088	89 45 e4	 mov	 DWORD PTR __Newvec$[ebp], eax

; 957  : 		const pointer _Constructed_last = _Newvec + _Whereoff + 1;

  0008b	8b 55 f8	 mov	 edx, DWORD PTR __Whereoff$[ebp]
  0008e	8b 45 e4	 mov	 eax, DWORD PTR __Newvec$[ebp]
  00091	8d 4c 90 04	 lea	 ecx, DWORD PTR [eax+edx*4+4]
  00095	89 4d e0	 mov	 DWORD PTR __Constructed_last$[ebp], ecx

; 958  : 		pointer _Constructed_first = _Constructed_last;

  00098	8b 55 e0	 mov	 edx, DWORD PTR __Constructed_last$[ebp]
  0009b	89 55 dc	 mov	 DWORD PTR __Constructed_first$[ebp], edx

; 959  : 
; 960  : 		_TRY_BEGIN

  0009e	b8 01 00 00 00	 mov	 eax, 1
  000a3	85 c0		 test	 eax, eax
  000a5	0f 84 b0 00 00
	00		 je	 $LN3@Emplace_re

; 961  : 		_Alty_traits::construct(_Al, _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  000ab	8b 4d 0c	 mov	 ecx, DWORD PTR _<_Val_0>$[ebp]
  000ae	51		 push	 ecx
  000af	e8 00 00 00 00	 call	 ??$forward@ABI@std@@YAABIABI@Z ; std::forward<unsigned int const &>
  000b4	83 c4 04	 add	 esp, 4
  000b7	50		 push	 eax
  000b8	8b 55 f8	 mov	 edx, DWORD PTR __Whereoff$[ebp]
  000bb	8b 45 e4	 mov	 eax, DWORD PTR __Newvec$[ebp]
  000be	8d 0c 90	 lea	 ecx, DWORD PTR [eax+edx*4]
  000c1	51		 push	 ecx
  000c2	e8 00 00 00 00	 call	 ??$_Unfancy@I@std@@YAPAIPAI@Z ; std::_Unfancy<unsigned int>
  000c7	83 c4 04	 add	 esp, 4
  000ca	50		 push	 eax
  000cb	8b 55 f4	 mov	 edx, DWORD PTR __Al$[ebp]
  000ce	52		 push	 edx
  000cf	e8 00 00 00 00	 call	 ??$construct@IABI@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAXAAV?$allocator@I@1@QAIABI@Z ; std::_Default_allocator_traits<std::allocator<unsigned int> >::construct<unsigned int,unsigned int const &>
  000d4	83 c4 0c	 add	 esp, 12			; 0000000cH

; 962  : 		_Constructed_first = _Newvec + _Whereoff;

  000d7	8b 45 f8	 mov	 eax, DWORD PTR __Whereoff$[ebp]
  000da	8b 4d e4	 mov	 ecx, DWORD PTR __Newvec$[ebp]
  000dd	8d 14 81	 lea	 edx, DWORD PTR [ecx+eax*4]
  000e0	89 55 dc	 mov	 DWORD PTR __Constructed_first$[ebp], edx

; 963  : 
; 964  : 		if (_Whereptr == this->_Mylast())

  000e3	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000e6	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  000eb	8b 4d 08	 mov	 ecx, DWORD PTR __Whereptr$[ebp]
  000ee	3b 08		 cmp	 ecx, DWORD PTR [eax]
  000f0	75 24		 jne	 SHORT $LN5@Emplace_re

; 965  : 			{	// at back, provide strong guarantee
; 966  : 			_Umove_if_noexcept(this->_Myfirst(), this->_Mylast(), _Newvec);

  000f2	8b 55 e4	 mov	 edx, DWORD PTR __Newvec$[ebp]
  000f5	52		 push	 edx
  000f6	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000f9	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  000fe	8b 00		 mov	 eax, DWORD PTR [eax]
  00100	50		 push	 eax
  00101	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00104	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00109	8b 08		 mov	 ecx, DWORD PTR [eax]
  0010b	51		 push	 ecx
  0010c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0010f	e8 00 00 00 00	 call	 ?_Umove_if_noexcept@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept

; 967  : 			}
; 968  : 		else

  00114	eb 43		 jmp	 SHORT $LN6@Emplace_re
$LN5@Emplace_re:

; 969  : 			{	// provide basic guarantee
; 970  : 			_Umove(this->_Myfirst(), _Whereptr, _Newvec);

  00116	8b 55 e4	 mov	 edx, DWORD PTR __Newvec$[ebp]
  00119	52		 push	 edx
  0011a	8b 45 08	 mov	 eax, DWORD PTR __Whereptr$[ebp]
  0011d	50		 push	 eax
  0011e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00121	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00126	8b 08		 mov	 ecx, DWORD PTR [eax]
  00128	51		 push	 ecx
  00129	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0012c	e8 00 00 00 00	 call	 ?_Umove@?$vector@IV?$allocator@I@std@@@std@@AAEPAIPAI00@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove

; 971  : 			_Constructed_first = _Newvec;

  00131	8b 55 e4	 mov	 edx, DWORD PTR __Newvec$[ebp]
  00134	89 55 dc	 mov	 DWORD PTR __Constructed_first$[ebp], edx

; 972  : 			_Umove(_Whereptr, this->_Mylast(), _Newvec + _Whereoff + 1);

  00137	8b 45 f8	 mov	 eax, DWORD PTR __Whereoff$[ebp]
  0013a	8b 4d e4	 mov	 ecx, DWORD PTR __Newvec$[ebp]
  0013d	8d 54 81 04	 lea	 edx, DWORD PTR [ecx+eax*4+4]
  00141	52		 push	 edx
  00142	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00145	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  0014a	8b 00		 mov	 eax, DWORD PTR [eax]
  0014c	50		 push	 eax
  0014d	8b 4d 08	 mov	 ecx, DWORD PTR __Whereptr$[ebp]
  00150	51		 push	 ecx
  00151	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00154	e8 00 00 00 00	 call	 ?_Umove@?$vector@IV?$allocator@I@std@@@std@@AAEPAIPAI00@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove
$LN6@Emplace_re:

; 973  : 			}

  00159	eb 24		 jmp	 SHORT $LN4@Emplace_re
$LN3@Emplace_re:

; 974  : 		_CATCH_ALL

  0015b	33 d2		 xor	 edx, edx
  0015d	74 20		 je	 SHORT $LN4@Emplace_re

; 975  : 		_Destroy(_Constructed_first, _Constructed_last);

  0015f	8b 45 e0	 mov	 eax, DWORD PTR __Constructed_last$[ebp]
  00162	50		 push	 eax
  00163	8b 4d dc	 mov	 ecx, DWORD PTR __Constructed_first$[ebp]
  00166	51		 push	 ecx
  00167	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0016a	e8 00 00 00 00	 call	 ?_Destroy@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI0@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Destroy

; 976  : 		_Al.deallocate(_Newvec, _Newcapacity);

  0016f	8b 55 e8	 mov	 edx, DWORD PTR __Newcapacity$[ebp]
  00172	52		 push	 edx
  00173	8b 45 e4	 mov	 eax, DWORD PTR __Newvec$[ebp]
  00176	50		 push	 eax
  00177	8b 4d f4	 mov	 ecx, DWORD PTR __Al$[ebp]
  0017a	e8 00 00 00 00	 call	 ?deallocate@?$allocator@I@std@@QAEXQAII@Z ; std::allocator<unsigned int>::deallocate
$LN4@Emplace_re:

; 977  : 		_RERAISE;
; 978  : 		_CATCH_END
; 979  : 
; 980  : 		_Change_array(_Newvec, _Newsize, _Newcapacity);

  0017f	8b 4d e8	 mov	 ecx, DWORD PTR __Newcapacity$[ebp]
  00182	51		 push	 ecx
  00183	8b 55 ec	 mov	 edx, DWORD PTR __Newsize$[ebp]
  00186	52		 push	 edx
  00187	8b 45 e4	 mov	 eax, DWORD PTR __Newvec$[ebp]
  0018a	50		 push	 eax
  0018b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0018e	e8 00 00 00 00	 call	 ?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AAEXQAIII@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Change_array

; 981  : 		return (this->_Myfirst() + _Whereoff);

  00193	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00196	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  0019b	8b 08		 mov	 ecx, DWORD PTR [eax]
  0019d	8b 55 f8	 mov	 edx, DWORD PTR __Whereoff$[ebp]
  001a0	8d 04 91	 lea	 eax, DWORD PTR [ecx+edx*4]
$LN8@Emplace_re:

; 982  : 		}

  001a3	83 c4 24	 add	 esp, 36			; 00000024H
  001a6	3b ec		 cmp	 ebp, esp
  001a8	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001ad	8b e5		 mov	 esp, ebp
  001af	5d		 pop	 ebp
  001b0	c2 08 00	 ret	 8
??$_Emplace_reallocate@ABI@?$vector@IV?$allocator@I@std@@@std@@QAEPAIQAIABI@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Emplace_reallocate<unsigned int const &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$?0$$V@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAE@U_Zero_then_variadic_args_t@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 1
??$?0$$V@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAE@U_Zero_then_variadic_args_t@1@@Z PROC ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1><>, COMDAT
; _this$ = ecx

; 278  : 		{	// construct from forwarded values

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 277  : 		: _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...)

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??0?$allocator@I@std@@QAE@XZ ; std::allocator<unsigned int>::allocator<unsigned int>
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ??0?$_Vector_val@U?$_Simple_types@I@std@@@std@@QAE@XZ ; std::_Vector_val<std::_Simple_types<unsigned int> >::_Vector_val<std::_Simple_types<unsigned int> >

; 279  : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 04 00	 ret	 4
??$?0$$V@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QAE@U_Zero_then_variadic_args_t@1@@Z ENDP ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1><>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$_Destroy_range@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@@Z
_TEXT	SEGMENT
$T1 = -1						; size = 1
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
__Al$ = 16						; size = 4
??$_Destroy_range@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@@Z PROC ; std::_Destroy_range<std::allocator<unsigned int> >, COMDAT

; 1154 : 	{	// destroy [_First, _Last), choose optimization

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 1155 : 		// note that this is an optimization for debug mode codegen;
; 1156 : 		// in release mode the BE removes all of this
; 1157 : 	using _Val = typename _Alloc::value_type;
; 1158 : 	_Destroy_range1(_First, _Last, _Al, bool_constant<conjunction_v<

  0000b	33 c0		 xor	 eax, eax
  0000d	88 45 ff	 mov	 BYTE PTR $T1[ebp], al
  00010	0f b6 4d ff	 movzx	 ecx, BYTE PTR $T1[ebp]
  00014	51		 push	 ecx
  00015	8b 55 10	 mov	 edx, DWORD PTR __Al$[ebp]
  00018	52		 push	 edx
  00019	8b 45 0c	 mov	 eax, DWORD PTR __Last$[ebp]
  0001c	50		 push	 eax
  0001d	8b 4d 08	 mov	 ecx, DWORD PTR __First$[ebp]
  00020	51		 push	 ecx
  00021	e8 00 00 00 00	 call	 ??$_Destroy_range1@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@U?$integral_constant@_N$00@0@@Z ; std::_Destroy_range1<std::allocator<unsigned int> >
  00026	83 c4 10	 add	 esp, 16			; 00000010H

; 1159 : 		is_trivially_destructible<_Val>,
; 1160 : 		_Uses_default_destroy<_Alloc, _Val *>>>{});
; 1161 : 	}

  00029	83 c4 04	 add	 esp, 4
  0002c	3b ec		 cmp	 ebp, esp
  0002e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00033	8b e5		 mov	 esp, ebp
  00035	5d		 pop	 ebp
  00036	c3		 ret	 0
??$_Destroy_range@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@@Z ENDP ; std::_Destroy_range<std::allocator<unsigned int> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??0?$_Vector_val@U?$_Simple_types@I@std@@@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0?$_Vector_val@U?$_Simple_types@I@std@@@std@@QAE@XZ PROC ; std::_Vector_val<std::_Simple_types<unsigned int> >::_Vector_val<std::_Simple_types<unsigned int> >, COMDAT
; _this$ = ecx

; 392  : 		{	// initialize values

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 389  : 		: _Myfirst(),

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 390  : 		_Mylast(),

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	c7 41 04 00 00
	00 00		 mov	 DWORD PTR [ecx+4], 0

; 391  : 		_Myend()

  00021	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00024	c7 42 08 00 00
	00 00		 mov	 DWORD PTR [edx+8], 0

; 393  : 		}

  0002b	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002e	8b e5		 mov	 esp, ebp
  00030	5d		 pop	 ebp
  00031	c3		 ret	 0
??0?$_Vector_val@U?$_Simple_types@I@std@@@std@@QAE@XZ ENDP ; std::_Vector_val<std::_Simple_types<unsigned int> >::_Vector_val<std::_Simple_types<unsigned int> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ
_TEXT	SEGMENT
?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Xlength, COMDAT

; 1925 : 		{	// report a length_error

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1926 : 		_Xlength_error("vector<T> too long");

  00003	68 00 00 00 00	 push	 OFFSET ??_C@_0BD@OLBABOEK@vector?$DMT?$DO?5too?5long@
  00008	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPBD@Z ; std::_Xlength_error
$LN2@Xlength:

; 1927 : 		}

  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
?_Xlength@?$vector@IV?$allocator@I@std@@@std@@CAXXZ ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Xlength
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AAEXQAIII@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Newvec$ = 8						; size = 4
__Newsize$ = 12						; size = 4
__Newcapacity$ = 16					; size = 4
?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AAEXQAIII@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Change_array, COMDAT
; _this$ = ecx

; 1895 : 		{	// orphan all iterators, discard old array, acquire new array

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1896 : 		this->_Orphan_all();

  0000f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00012	e8 00 00 00 00	 call	 ?_Orphan_all@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEXXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Orphan_all

; 1897 : 
; 1898 : 		if (this->_Myfirst() != pointer())

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  0001f	83 38 00	 cmp	 DWORD PTR [eax], 0
  00022	74 41		 je	 SHORT $LN2@Change_arr

; 1899 : 			{	// destroy and deallocate old array
; 1900 : 			_Destroy(this->_Myfirst(), this->_Mylast());

  00024	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00027	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  0002c	8b 00		 mov	 eax, DWORD PTR [eax]
  0002e	50		 push	 eax
  0002f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00032	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00037	8b 08		 mov	 ecx, DWORD PTR [eax]
  00039	51		 push	 ecx
  0003a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0003d	e8 00 00 00 00	 call	 ?_Destroy@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI0@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Destroy

; 1901 : 			this->_Getal().deallocate(this->_Myfirst(), capacity());

  00042	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00045	e8 00 00 00 00	 call	 ?capacity@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::capacity
  0004a	50		 push	 eax
  0004b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0004e	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  00053	8b 10		 mov	 edx, DWORD PTR [eax]
  00055	52		 push	 edx
  00056	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00059	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  0005e	8b c8		 mov	 ecx, eax
  00060	e8 00 00 00 00	 call	 ?deallocate@?$allocator@I@std@@QAEXQAII@Z ; std::allocator<unsigned int>::deallocate
$LN2@Change_arr:

; 1902 : 			}
; 1903 : 
; 1904 : 		this->_Myfirst() = _Newvec;

  00065	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00068	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myfirst
  0006d	8b 4d 08	 mov	 ecx, DWORD PTR __Newvec$[ebp]
  00070	89 08		 mov	 DWORD PTR [eax], ecx

; 1905 : 		this->_Mylast() = _Newvec + _Newsize;

  00072	8b 55 0c	 mov	 edx, DWORD PTR __Newsize$[ebp]
  00075	8b 45 08	 mov	 eax, DWORD PTR __Newvec$[ebp]
  00078	8d 34 90	 lea	 esi, DWORD PTR [eax+edx*4]
  0007b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0007e	e8 00 00 00 00	 call	 ?_Mylast@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Mylast
  00083	89 30		 mov	 DWORD PTR [eax], esi

; 1906 : 		this->_Myend() = _Newvec + _Newcapacity;

  00085	8b 4d 10	 mov	 ecx, DWORD PTR __Newcapacity$[ebp]
  00088	8b 55 08	 mov	 edx, DWORD PTR __Newvec$[ebp]
  0008b	8d 34 8a	 lea	 esi, DWORD PTR [edx+ecx*4]
  0008e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00091	e8 00 00 00 00	 call	 ?_Myend@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAPAIXZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Myend
  00096	89 30		 mov	 DWORD PTR [eax], esi

; 1907 : 		}

  00098	5e		 pop	 esi
  00099	83 c4 04	 add	 esp, 4
  0009c	3b ec		 cmp	 ebp, esp
  0009e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000a3	8b e5		 mov	 esp, ebp
  000a5	5d		 pop	 ebp
  000a6	c2 0c 00	 ret	 12			; 0000000cH
?_Change_array@?$vector@IV?$allocator@I@std@@@std@@AAEXQAIII@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Change_array
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Calculate_growth@?$vector@IV?$allocator@I@std@@@std@@ABEII@Z
_TEXT	SEGMENT
__Geometric$ = -12					; size = 4
__Oldcapacity$ = -8					; size = 4
_this$ = -4						; size = 4
__Newsize$ = 8						; size = 4
?_Calculate_growth@?$vector@IV?$allocator@I@std@@@std@@ABEII@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Calculate_growth, COMDAT
; _this$ = ecx

; 1853 : 		{	// given _Oldcapacity and _Newsize, calculate geometric growth

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1854 : 		const size_type _Oldcapacity = capacity();

  0001e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00021	e8 00 00 00 00	 call	 ?capacity@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::capacity
  00026	89 45 f8	 mov	 DWORD PTR __Oldcapacity$[ebp], eax

; 1855 : 
; 1856 : 		if (_Oldcapacity > max_size() - _Oldcapacity / 2)

  00029	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ; std::vector<unsigned int,std::allocator<unsigned int> >::max_size
  00031	8b 4d f8	 mov	 ecx, DWORD PTR __Oldcapacity$[ebp]
  00034	d1 e9		 shr	 ecx, 1
  00036	2b c1		 sub	 eax, ecx
  00038	39 45 f8	 cmp	 DWORD PTR __Oldcapacity$[ebp], eax
  0003b	76 05		 jbe	 SHORT $LN2@Calculate_

; 1857 : 			{
; 1858 : 			return (_Newsize);	// geometric growth would overflow

  0003d	8b 45 08	 mov	 eax, DWORD PTR __Newsize$[ebp]
  00040	eb 1b		 jmp	 SHORT $LN1@Calculate_
$LN2@Calculate_:

; 1859 : 			}
; 1860 : 
; 1861 : 		const size_type _Geometric = _Oldcapacity + _Oldcapacity / 2;

  00042	8b 55 f8	 mov	 edx, DWORD PTR __Oldcapacity$[ebp]
  00045	d1 ea		 shr	 edx, 1
  00047	03 55 f8	 add	 edx, DWORD PTR __Oldcapacity$[ebp]
  0004a	89 55 f4	 mov	 DWORD PTR __Geometric$[ebp], edx

; 1862 : 
; 1863 : 		if (_Geometric < _Newsize)

  0004d	8b 45 f4	 mov	 eax, DWORD PTR __Geometric$[ebp]
  00050	3b 45 08	 cmp	 eax, DWORD PTR __Newsize$[ebp]
  00053	73 05		 jae	 SHORT $LN3@Calculate_

; 1864 : 			{
; 1865 : 			return (_Newsize);	// geometric growth would be insufficient

  00055	8b 45 08	 mov	 eax, DWORD PTR __Newsize$[ebp]
  00058	eb 03		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 1866 : 			}
; 1867 : 
; 1868 : 		return (_Geometric);	// geometric growth is sufficient

  0005a	8b 45 f4	 mov	 eax, DWORD PTR __Geometric$[ebp]
$LN1@Calculate_:

; 1869 : 		}

  0005d	83 c4 0c	 add	 esp, 12			; 0000000cH
  00060	3b ec		 cmp	 ebp, esp
  00062	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00067	8b e5		 mov	 esp, ebp
  00069	5d		 pop	 ebp
  0006a	c2 04 00	 ret	 4
?_Calculate_growth@?$vector@IV?$allocator@I@std@@@std@@ABEII@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Umove_if_noexcept@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00@Z
_TEXT	SEGMENT
$T1 = -5						; size = 1
_this$ = -4						; size = 4
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
__Dest$ = 16						; size = 4
?_Umove_if_noexcept@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept, COMDAT
; _this$ = ecx

; 1842 : 		{	// move_if_noexcept [_First, _Last) to raw _Dest, using allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1843 : 		_Umove_if_noexcept1(_First, _Last, _Dest,

  00017	33 c0		 xor	 eax, eax
  00019	88 45 fb	 mov	 BYTE PTR $T1[ebp], al
  0001c	0f b6 4d fb	 movzx	 ecx, BYTE PTR $T1[ebp]
  00020	51		 push	 ecx
  00021	8b 55 10	 mov	 edx, DWORD PTR __Dest$[ebp]
  00024	52		 push	 edx
  00025	8b 45 0c	 mov	 eax, DWORD PTR __Last$[ebp]
  00028	50		 push	 eax
  00029	8b 4d 08	 mov	 ecx, DWORD PTR __First$[ebp]
  0002c	51		 push	 ecx
  0002d	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00030	e8 00 00 00 00	 call	 ?_Umove_if_noexcept1@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00U?$integral_constant@_N$00@2@@Z ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept1

; 1844 : 			bool_constant<disjunction_v<is_nothrow_move_constructible<_Ty>, negation<is_copy_constructible<_Ty>>>>{});
; 1845 : 		}

  00035	83 c4 08	 add	 esp, 8
  00038	3b ec		 cmp	 ebp, esp
  0003a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003f	8b e5		 mov	 esp, ebp
  00041	5d		 pop	 ebp
  00042	c2 0c 00	 ret	 12			; 0000000cH
?_Umove_if_noexcept@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Umove@?$vector@IV?$allocator@I@std@@@std@@AAEPAIPAI00@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
__Dest$ = 16						; size = 4
?_Umove@?$vector@IV?$allocator@I@std@@@std@@AAEPAIPAI00@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove, COMDAT
; _this$ = ecx

; 1827 : 		{	// move [_First, _Last) to raw _Dest, using allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1828 : 		return (_Uninitialized_move(_First, _Last, _Dest, this->_Getal()));

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  00016	50		 push	 eax
  00017	8b 45 10	 mov	 eax, DWORD PTR __Dest$[ebp]
  0001a	50		 push	 eax
  0001b	8b 4d 0c	 mov	 ecx, DWORD PTR __Last$[ebp]
  0001e	51		 push	 ecx
  0001f	8b 55 08	 mov	 edx, DWORD PTR __First$[ebp]
  00022	52		 push	 edx
  00023	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PAIPAIV?$allocator@I@std@@@std@@YAPAIQAI0PAIAAV?$allocator@I@0@@Z ; std::_Uninitialized_move<unsigned int *,unsigned int *,std::allocator<unsigned int> >
  00028	83 c4 10	 add	 esp, 16			; 00000010H

; 1829 : 		}

  0002b	83 c4 04	 add	 esp, 4
  0002e	3b ec		 cmp	 ebp, esp
  00030	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00035	8b e5		 mov	 esp, ebp
  00037	5d		 pop	 ebp
  00038	c2 0c 00	 ret	 12			; 0000000cH
?_Umove@?$vector@IV?$allocator@I@std@@@std@@AAEPAIPAI00@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?max_size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ
_TEXT	SEGMENT
$T1 = -12						; size = 4
$T2 = -8						; size = 4
_this$ = -4						; size = 4
?max_size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::max_size, COMDAT
; _this$ = ecx

; 1708 : 		{	// return maximum possible length of sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1709 : 		return (_Min_value(static_cast<size_type>((numeric_limits<difference_type>::max)()),

  0001e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00021	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  00026	50		 push	 eax
  00027	e8 00 00 00 00	 call	 ?max_size@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAIABV?$allocator@I@2@@Z ; std::_Default_allocator_traits<std::allocator<unsigned int> >::max_size
  0002c	83 c4 04	 add	 esp, 4
  0002f	89 45 f8	 mov	 DWORD PTR $T2[ebp], eax
  00032	e8 00 00 00 00	 call	 ?max@?$numeric_limits@H@std@@SAHXZ ; std::numeric_limits<int>::max
  00037	89 45 f4	 mov	 DWORD PTR $T1[ebp], eax
  0003a	8d 45 f8	 lea	 eax, DWORD PTR $T2[ebp]
  0003d	50		 push	 eax
  0003e	8d 4d f4	 lea	 ecx, DWORD PTR $T1[ebp]
  00041	51		 push	 ecx
  00042	e8 00 00 00 00	 call	 ??$_Min_value@I@std@@YAABIABI0@Z ; std::_Min_value<unsigned int>
  00047	83 c4 08	 add	 esp, 8
  0004a	8b 00		 mov	 eax, DWORD PTR [eax]

; 1710 : 			_Alty_traits::max_size(this->_Getal())));
; 1711 : 		}

  0004c	83 c4 0c	 add	 esp, 12			; 0000000cH
  0004f	3b ec		 cmp	 ebp, esp
  00051	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00056	8b e5		 mov	 esp, ebp
  00058	5d		 pop	 ebp
  00059	c3		 ret	 0
?max_size@?$vector@IV?$allocator@I@std@@@std@@QBEIXZ ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?allocate@?$allocator@I@std@@QAEPAII@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Count$ = 8						; size = 4
?allocate@?$allocator@I@std@@QAEPAII@Z PROC		; std::allocator<unsigned int>::allocate, COMDAT
; _this$ = ecx

; 996  : 		{	// allocate array of _Count elements

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 997  : 		return (static_cast<_Ty *>(_Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count))));

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Count$[ebp]
  00011	50		 push	 eax
  00012	e8 00 00 00 00	 call	 ??$_Get_size_of_n@$03@std@@YAII@Z ; std::_Get_size_of_n<4>
  00017	83 c4 04	 add	 esp, 4
  0001a	50		 push	 eax
  0001b	e8 00 00 00 00	 call	 ??$_Allocate@$07U_Default_allocate_traits@std@@$0A@@std@@YAPAXI@Z ; std::_Allocate<8,std::_Default_allocate_traits,0>
  00020	83 c4 04	 add	 esp, 4

; 998  : 		}

  00023	83 c4 04	 add	 esp, 4
  00026	3b ec		 cmp	 ebp, esp
  00028	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002d	8b e5		 mov	 esp, ebp
  0002f	5d		 pop	 ebp
  00030	c2 04 00	 ret	 4
?allocate@?$allocator@I@std@@QAEPAII@Z ENDP		; std::allocator<unsigned int>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??0?$allocator@I@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0?$allocator@I@std@@QAE@XZ PROC			; std::allocator<unsigned int>::allocator<unsigned int>, COMDAT
; _this$ = ecx

; 979  : 	constexpr allocator() noexcept

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 980  : 		{	// construct default allocator (do nothing)
; 981  : 		}

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
??0?$allocator@I@std@@QAE@XZ ENDP			; std::allocator<unsigned int>::allocator<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?max_size@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAIABV?$allocator@I@2@@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
?max_size@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAIABV?$allocator@I@2@@Z PROC ; std::_Default_allocator_traits<std::allocator<unsigned int> >::max_size, COMDAT

; 891  : 		{	// get maximum size

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 892  : 		return (static_cast<size_t>(-1) / sizeof(value_type));

  00003	b8 ff ff ff 3f	 mov	 eax, 1073741823		; 3fffffffH

; 893  : 		}

  00008	5d		 pop	 ebp
  00009	c3		 ret	 0
?max_size@?$_Default_allocator_traits@V?$allocator@I@std@@@std@@SAIABV?$allocator@I@2@@Z ENDP ; std::_Default_allocator_traits<std::allocator<unsigned int> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$allocator@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$allocator@I@2@XZ PROC ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal, COMDAT
; _this$ = ecx

; 549  : 		{	// return const reference to allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 550  : 		return (_Mypair._Get_first());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$allocator@I@2@XZ ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first

; 551  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QBEABV?$allocator@I@2@XZ ENDP ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ?_Umove_if_noexcept1@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00U?$integral_constant@_N$00@2@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
__Dest$ = 16						; size = 4
___formal$ = 20						; size = 1
?_Umove_if_noexcept1@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00U?$integral_constant@_N$00@2@@Z PROC ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept1, COMDAT
; _this$ = ecx

; 1832 : 		{	// move [_First, _Last) to raw _Dest, using allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1833 : 		_Uninitialized_move(_First, _Last, _Dest, this->_Getal());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Getal@?$_Vector_alloc@U?$_Vec_base_types@IV?$allocator@I@std@@@std@@@std@@QAEAAV?$allocator@I@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<unsigned int,std::allocator<unsigned int> > >::_Getal
  00016	50		 push	 eax
  00017	8b 45 10	 mov	 eax, DWORD PTR __Dest$[ebp]
  0001a	50		 push	 eax
  0001b	8b 4d 0c	 mov	 ecx, DWORD PTR __Last$[ebp]
  0001e	51		 push	 ecx
  0001f	8b 55 08	 mov	 edx, DWORD PTR __First$[ebp]
  00022	52		 push	 edx
  00023	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PAIPAIV?$allocator@I@std@@@std@@YAPAIQAI0PAIAAV?$allocator@I@0@@Z ; std::_Uninitialized_move<unsigned int *,unsigned int *,std::allocator<unsigned int> >
  00028	83 c4 10	 add	 esp, 16			; 00000010H

; 1834 : 		}

  0002b	83 c4 04	 add	 esp, 4
  0002e	3b ec		 cmp	 ebp, esp
  00030	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00035	8b e5		 mov	 esp, ebp
  00037	5d		 pop	 ebp
  00038	c2 10 00	 ret	 16			; 00000010H
?_Umove_if_noexcept1@?$vector@IV?$allocator@I@std@@@std@@AAEXPAI00U?$integral_constant@_N$00@2@@Z ENDP ; std::vector<unsigned int,std::allocator<unsigned int> >::_Umove_if_noexcept1
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$allocator@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$allocator@I@2@XZ PROC ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first, COMDAT
; _this$ = ecx

; 296  : 		{	// return const reference to first

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 297  : 		return (*this);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 298  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_first@?$_Compressed_pair@V?$allocator@I@std@@V?$_Vector_val@U?$_Simple_types@I@std@@@2@$00@std@@QBEABV?$allocator@I@2@XZ ENDP ; std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1>::_Get_first
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$_Destroy_range1@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@U?$integral_constant@_N$00@0@@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
___formal$ = 12						; size = 4
___formal$ = 16						; size = 4
___formal$ = 20						; size = 1
??$_Destroy_range1@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@U?$integral_constant@_N$00@0@@Z PROC ; std::_Destroy_range1<std::allocator<unsigned int> >, COMDAT

; 1145 : 	{	// destroy [_First, _Last), trivially destructible and default destroy

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1146 : 		// nothing to do
; 1147 : 	}

  00003	5d		 pop	 ebp
  00004	c3		 ret	 0
??$_Destroy_range1@V?$allocator@I@std@@@std@@YAXPAI0AAV?$allocator@I@0@U?$integral_constant@_N$00@0@@Z ENDP ; std::_Destroy_range1<std::allocator<unsigned int> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory
;	COMDAT ??$_Uninitialized_move@PAIPAIV?$allocator@I@std@@@std@@YAPAIQAI0PAIAAV?$allocator@I@0@@Z
_TEXT	SEGMENT
$T1 = -38						; size = 1
$T2 = -37						; size = 1
__UDest$ = -32						; size = 4
__ULast$ = -20						; size = 4
__UFirst$ = -8						; size = 4
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
__Dest$ = 16						; size = 4
__Al$ = 20						; size = 4
??$_Uninitialized_move@PAIPAIV?$allocator@I@std@@@std@@YAPAIQAI0PAIAAV?$allocator@I@0@@Z PROC ; std::_Uninitialized_move<unsigned int *,unsigned int *,std::allocator<unsigned int> >, COMDAT

; 251  : 	{	// move [_First, _Last) to raw _Dest, using _Al

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 28	 sub	 esp, 40			; 00000028H
  00006	57		 push	 edi
  00007	8d 7d d8	 lea	 edi, DWORD PTR [ebp-40]
  0000a	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  0000f	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00014	f3 ab		 rep stosd

; 252  : 		// note: only called internally from elsewhere in the STL
; 253  : 	const auto _UFirst = _Get_unwrapped(_First);

  00016	8b 45 08	 mov	 eax, DWORD PTR __First$[ebp]
  00019	50		 push	 eax
  0001a	e8 00 00 00 00	 call	 ??$_Get_unwrapped@I@std@@YAPAIQAI@Z ; std::_Get_unwrapped<unsigned int>
  0001f	83 c4 04	 add	 esp, 4
  00022	89 45 f8	 mov	 DWORD PTR __UFirst$[ebp], eax

; 254  : 	const auto _ULast = _Get_unwrapped(_Last);

  00025	8b 4d 0c	 mov	 ecx, DWORD PTR __Last$[ebp]
  00028	51		 push	 ecx
  00029	e8 00 00 00 00	 call	 ??$_Get_unwrapped@I@std@@YAPAIQAI@Z ; std::_Get_unwrapped<unsigned int>
  0002e	83 c4 04	 add	 esp, 4
  00031	89 45 ec	 mov	 DWORD PTR __ULast$[ebp], eax

; 255  : 	const auto _UDest = _Get_unwrapped_n(_Dest, _Idl_distance<_InIt>(_UFirst, _ULast));

  00034	8d 55 ec	 lea	 edx, DWORD PTR __ULast$[ebp]
  00037	52		 push	 edx
  00038	8d 45 f8	 lea	 eax, DWORD PTR __UFirst$[ebp]
  0003b	50		 push	 eax
  0003c	e8 00 00 00 00	 call	 ??$_Idl_distance@PAIPAI@std@@YAHABQAI0@Z ; std::_Idl_distance<unsigned int *,unsigned int *>
  00041	83 c4 08	 add	 esp, 8
  00044	50		 push	 eax
  00045	8b 4d 10	 mov	 ecx, DWORD PTR __Dest$[ebp]
  00048	51		 push	 ecx
  00049	e8 00 00 00 00	 call	 ??$_Get_unwrapped_n@IH$0A@@std@@YAPAIQAIH@Z ; std::_Get_unwrapped_n<unsigned int,int,0>
  0004e	83 c4 08	 add	 esp, 8
  00051	89 45 e0	 mov	 DWORD PTR __UDest$[ebp], eax

; 256  : 	_Seek_wrapped(_Dest,

  00054	0f b6 55 db	 movzx	 edx, BYTE PTR $T2[ebp]
  00058	52		 push	 edx
  00059	8d 45 e0	 lea	 eax, DWORD PTR __UDest$[ebp]
  0005c	50		 push	 eax
  0005d	8d 4d f8	 lea	 ecx, DWORD PTR __UFirst$[ebp]
  00060	51		 push	 ecx
  00061	8d 55 da	 lea	 edx, DWORD PTR $T1[ebp]
  00064	52		 push	 edx
  00065	e8 00 00 00 00	 call	 ??$_Ptr_move_cat@II@std@@YA?AU_Really_trivial_ptr_iterator_tag@0@ABQAI0@Z ; std::_Ptr_move_cat<unsigned int,unsigned int>
  0006a	83 c4 0c	 add	 esp, 12			; 0000000cH
  0006d	8a 00		 mov	 al, BYTE PTR [eax]
  0006f	0f b6 c8	 movzx	 ecx, al
  00072	51		 push	 ecx
  00073	8b 55 14	 mov	 edx, DWORD PTR __Al$[ebp]
  00076	52		 push	 edx
  00077	8b 45 e0	 mov	 eax, DWORD PTR __UDest$[ebp]
  0007a	50		 push	 eax
  0007b	8b 4d ec	 mov	 ecx, DWORD PTR __ULast$[ebp]
  0007e	51		 push	 ecx
  0007f	8b 55 f8	 mov	 edx, DWORD PTR __UFirst$[ebp]
  00082	52		 push	 edx
  00083	e8 00 00 00 00	 call	 ??$_Uninitialized_move_al_unchecked@IIV?$allocator@I@std@@@std@@YAPAIQAI00AAV?$allocator@I@0@U_Really_trivial_ptr_iterator_tag@0@U?$integral_constant@_N$00@0@@Z ; std::_Uninitialized_move_al_unchecked<unsigned int,unsigned int,std::allocator<unsigned int> >
  00088	83 c4 18	 add	 esp, 24			; 00000018H
  0008b	50		 push	 eax
  0008c	8d 45 10	 lea	 eax, DWORD PTR __Dest$[ebp]
  0008f	50		 push	 eax
  00090	e8 00 00 00 00	 call	 ??$_Seek_wrapped@I@std@@YAXAAPAIQAI@Z ; std::_Seek_wrapped<unsigned int>
  00095	83 c4 08	 add	 esp, 8

; 257  : 		_Uninitialized_move_al_unchecked(_UFirst, _ULast, _UDest, _Al,
; 258  : 			_Ptr_move_cat(_UFirst, _UDest),
; 259  : 			_Uses_default_construct_t<_Alloc, decltype(_Unfancy(_UDest)), decltype(_STD move(*_UFirst))>()));
; 260  : 	return (_Dest);

  00098	8b 45 10	 mov	 eax, DWORD PTR __Dest$[ebp]

; 261  : 	}

  0009b	52		 push	 edx
  0009c	8b cd		 mov	 ecx, ebp
  0009e	50		 push	 eax
  0009f	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN7@Uninitiali
  000a5	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000aa	58		 pop	 eax
  000ab	5a		 pop	 edx
  000ac	5f		 pop	 edi
  000ad	83 c4 28	 add	 esp, 40			; 00000028H
  000b0	3b ec		 cmp	 ebp, esp
  000b2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b7	8b e5		 mov	 esp, ebp
  000b9	5d		 pop	 ebp
  000ba	c3		 ret	 0
  000bb	90		 npad	 1
$LN7@Uninitiali:
  000bc	03 00 00 00	 DD	 3
  000c0	00 00 00 00	 DD	 $LN6@Uninitiali
$LN6@Uninitiali:
  000c4	f8 ff ff ff	 DD	 -8			; fffffff8H
  000c8	04 00 00 00	 DD	 4
  000cc	00 00 00 00	 DD	 $LN3@Uninitiali
  000d0	ec ff ff ff	 DD	 -20			; ffffffecH
  000d4	04 00 00 00	 DD	 4
  000d8	00 00 00 00	 DD	 $LN4@Uninitiali
  000dc	e0 ff ff ff	 DD	 -32			; ffffffe0H
  000e0	04 00 00 00	 DD	 4
  000e4	00 00 00 00	 DD	 $LN5@Uninitiali
$LN5@Uninitiali:
  000e8	5f		 DB	 95			; 0000005fH
  000e9	55		 DB	 85			; 00000055H
  000ea	44		 DB	 68			; 00000044H
  000eb	65		 DB	 101			; 00000065H
  000ec	73		 DB	 115			; 00000073H
  000ed	74		 DB	 116			; 00000074H
  000ee	00		 DB	 0
$LN4@Uninitiali:
  000ef	5f		 DB	 95			; 0000005fH
  000f0	55		 DB	 85			; 00000055H
  000f1	4c		 DB	 76			; 0000004cH
  000f2	61		 DB	 97			; 00000061H
  000f3	73		 DB	 115			; 00000073H
  000f4	74		 DB	 116			; 00000074H
  000f5	00		 DB	 0
$LN3@Uninitiali:
  000f6	5f		 DB	 95			; 0000005fH
  000f7	55		 DB	 85			; 00000055H
  000f8	46		 DB	 70			; 00000046H
  000f9	69		 DB	 105			; 00000069H
  000fa	72		 DB	 114			; 00000072H
  000fb	73		 DB	 115			; 00000073H
  000fc	74		 DB	 116			; 00000074H
  000fd	00		 DB	 0
??$_Uninitialized_move@PAIPAIV?$allocator@I@std@@@std@@YAPAIQAI0PAIAAV?$allocator@I@0@@Z ENDP ; std::_Uninitialized_move<unsigned int *,unsigned int *,std::allocator<unsigned int> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$_Idl_distance@PAIPAI@std@@YAHABQAI0@Z
_TEXT	SEGMENT
$T1 = -1						; size = 1
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
??$_Idl_distance@PAIPAI@std@@YAHABQAI0@Z PROC		; std::_Idl_distance<unsigned int *,unsigned int *>, COMDAT

; 974  : 	{	// tries to get the distance between _First and _Last if they are random-access iterators

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 975  : 	return (_Idl_distance1<_Checked>(_First, _Last, _Iter_cat_t<_Iter>()));

  0000b	0f b6 45 ff	 movzx	 eax, BYTE PTR $T1[ebp]
  0000f	50		 push	 eax
  00010	8b 4d 0c	 mov	 ecx, DWORD PTR __Last$[ebp]
  00013	51		 push	 ecx
  00014	8b 55 08	 mov	 edx, DWORD PTR __First$[ebp]
  00017	52		 push	 edx
  00018	e8 00 00 00 00	 call	 ??$_Idl_distance1@PAIPAI@std@@YAHABQAI0Urandom_access_iterator_tag@0@@Z ; std::_Idl_distance1<unsigned int *,unsigned int *>
  0001d	83 c4 0c	 add	 esp, 12			; 0000000cH

; 976  : 	}

  00020	83 c4 04	 add	 esp, 4
  00023	3b ec		 cmp	 ebp, esp
  00025	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002a	8b e5		 mov	 esp, ebp
  0002c	5d		 pop	 ebp
  0002d	c3		 ret	 0
??$_Idl_distance@PAIPAI@std@@YAHABQAI0@Z ENDP		; std::_Idl_distance<unsigned int *,unsigned int *>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$_Get_unwrapped@I@std@@YAPAIQAI@Z
_TEXT	SEGMENT
__Ptr$ = 8						; size = 4
??$_Get_unwrapped@I@std@@YAPAIQAI@Z PROC		; std::_Get_unwrapped<unsigned int>, COMDAT

; 662  : 	{	// special case already-unwrapped pointers

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 663  : 	return (_Ptr);

  00003	8b 45 08	 mov	 eax, DWORD PTR __Ptr$[ebp]

; 664  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$_Get_unwrapped@I@std@@YAPAIQAI@Z ENDP		; std::_Get_unwrapped<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$_Idl_distance1@PAIPAI@std@@YAHABQAI0Urandom_access_iterator_tag@0@@Z
_TEXT	SEGMENT
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
___formal$ = 16						; size = 1
??$_Idl_distance1@PAIPAI@std@@YAHABQAI0Urandom_access_iterator_tag@0@@Z PROC ; std::_Idl_distance1<unsigned int *,unsigned int *>, COMDAT

; 967  : 	{	// _Idl_distance for random-access iterators

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 968  : 	return (static_cast<_Iter_diff_t<_Checked>>(_Last - _First));

  00003	8b 45 0c	 mov	 eax, DWORD PTR __Last$[ebp]
  00006	8b 4d 08	 mov	 ecx, DWORD PTR __First$[ebp]
  00009	8b 00		 mov	 eax, DWORD PTR [eax]
  0000b	2b 01		 sub	 eax, DWORD PTR [ecx]
  0000d	c1 f8 02	 sar	 eax, 2

; 969  : 	}

  00010	5d		 pop	 ebp
  00011	c3		 ret	 0
??$_Idl_distance1@PAIPAI@std@@YAHABQAI0Urandom_access_iterator_tag@0@@Z ENDP ; std::_Idl_distance1<unsigned int *,unsigned int *>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$_Get_unwrapped_n@IH$0A@@std@@YAPAIQAIH@Z
_TEXT	SEGMENT
__Src$ = 8						; size = 4
___formal$ = 12						; size = 4
??$_Get_unwrapped_n@IH$0A@@std@@YAPAIQAIH@Z PROC	; std::_Get_unwrapped_n<unsigned int,int,0>, COMDAT

; 808  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 809  : 	return (_Src);

  00003	8b 45 08	 mov	 eax, DWORD PTR __Src$[ebp]

; 810  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$_Get_unwrapped_n@IH$0A@@std@@YAPAIQAIH@Z ENDP	; std::_Get_unwrapped_n<unsigned int,int,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$_Ptr_move_cat@II@std@@YA?AU_Really_trivial_ptr_iterator_tag@0@ABQAI0@Z
_TEXT	SEGMENT
___$ReturnUdt$ = 8					; size = 4
___formal$ = 12						; size = 4
___formal$ = 16						; size = 4
??$_Ptr_move_cat@II@std@@YA?AU_Really_trivial_ptr_iterator_tag@0@ABQAI0@Z PROC ; std::_Ptr_move_cat<unsigned int,unsigned int>, COMDAT

; 1086 : 	{	// return pointer move optimization category for pointers

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1087 : 	return {};

  00003	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1088 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$_Ptr_move_cat@II@std@@YA?AU_Really_trivial_ptr_iterator_tag@0@ABQAI0@Z ENDP ; std::_Ptr_move_cat<unsigned int,unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory
;	COMDAT ??$_Uninitialized_move_al_unchecked@IIV?$allocator@I@std@@@std@@YAPAIQAI00AAV?$allocator@I@0@U_Really_trivial_ptr_iterator_tag@0@U?$integral_constant@_N$00@0@@Z
_TEXT	SEGMENT
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
__Dest$ = 16						; size = 4
___formal$ = 20						; size = 4
___formal$ = 24						; size = 1
___formal$ = 28						; size = 1
??$_Uninitialized_move_al_unchecked@IIV?$allocator@I@std@@@std@@YAPAIQAI00AAV?$allocator@I@0@U_Really_trivial_ptr_iterator_tag@0@U?$integral_constant@_N$00@0@@Z PROC ; std::_Uninitialized_move_al_unchecked<unsigned int,unsigned int,std::allocator<unsigned int> >, COMDAT

; 243  : 	{	// move [_First, _Last) to raw _Dest, using default _Alloc construct, memmove optimization

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 244  : 	return (_Copy_memmove(_First, _Last, _Dest));

  00003	8b 45 10	 mov	 eax, DWORD PTR __Dest$[ebp]
  00006	50		 push	 eax
  00007	8b 4d 0c	 mov	 ecx, DWORD PTR __Last$[ebp]
  0000a	51		 push	 ecx
  0000b	8b 55 08	 mov	 edx, DWORD PTR __First$[ebp]
  0000e	52		 push	 edx
  0000f	e8 00 00 00 00	 call	 ??$_Copy_memmove@PAIPAI@std@@YAPAIPAI00@Z ; std::_Copy_memmove<unsigned int *,unsigned int *>
  00014	83 c4 0c	 add	 esp, 12			; 0000000cH

; 245  : 	}

  00017	3b ec		 cmp	 ebp, esp
  00019	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001e	5d		 pop	 ebp
  0001f	c3		 ret	 0
??$_Uninitialized_move_al_unchecked@IIV?$allocator@I@std@@@std@@YAPAIQAI00AAV?$allocator@I@0@U_Really_trivial_ptr_iterator_tag@0@U?$integral_constant@_N$00@0@@Z ENDP ; std::_Uninitialized_move_al_unchecked<unsigned int,unsigned int,std::allocator<unsigned int> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$_Seek_wrapped@I@std@@YAXAAPAIQAI@Z
_TEXT	SEGMENT
__It$ = 8						; size = 4
__UIt$ = 12						; size = 4
??$_Seek_wrapped@I@std@@YAXAAPAIQAI@Z PROC		; std::_Seek_wrapped<unsigned int>, COMDAT

; 871  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 872  : 	_It = _UIt;

  00003	8b 45 08	 mov	 eax, DWORD PTR __It$[ebp]
  00006	8b 4d 0c	 mov	 ecx, DWORD PTR __UIt$[ebp]
  00009	89 08		 mov	 DWORD PTR [eax], ecx

; 873  : 	}

  0000b	5d		 pop	 ebp
  0000c	c3		 ret	 0
??$_Seek_wrapped@I@std@@YAXAAPAIQAI@Z ENDP		; std::_Seek_wrapped<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ??$_Copy_memmove@PAIPAI@std@@YAPAIPAI00@Z
_TEXT	SEGMENT
__Count$ = -16						; size = 4
__Dest_ch$ = -12					; size = 4
__Last_ch$ = -8						; size = 4
__First_ch$ = -4					; size = 4
__First$ = 8						; size = 4
__Last$ = 12						; size = 4
__Dest$ = 16						; size = 4
??$_Copy_memmove@PAIPAI@std@@YAPAIPAI00@Z PROC		; std::_Copy_memmove<unsigned int *,unsigned int *>, COMDAT

; 2398 : 	{	// implement copy-like function as memmove

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 10	 sub	 esp, 16			; 00000010H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0000e	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00011	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00014	89 45 fc	 mov	 DWORD PTR [ebp-4], eax

; 2399 : 	const char * const _First_ch = const_cast<const char *>(reinterpret_cast<const volatile char *>(_First));

  00017	8b 45 08	 mov	 eax, DWORD PTR __First$[ebp]
  0001a	89 45 fc	 mov	 DWORD PTR __First_ch$[ebp], eax

; 2400 : 	const char * const _Last_ch = const_cast<const char *>(reinterpret_cast<const volatile char *>(_Last));

  0001d	8b 4d 0c	 mov	 ecx, DWORD PTR __Last$[ebp]
  00020	89 4d f8	 mov	 DWORD PTR __Last_ch$[ebp], ecx

; 2401 : 	char * const _Dest_ch = const_cast<char *>(reinterpret_cast<volatile char *>(_Dest));

  00023	8b 55 10	 mov	 edx, DWORD PTR __Dest$[ebp]
  00026	89 55 f4	 mov	 DWORD PTR __Dest_ch$[ebp], edx

; 2402 : 	const auto _Count = static_cast<size_t>(_Last_ch - _First_ch);

  00029	8b 45 f8	 mov	 eax, DWORD PTR __Last_ch$[ebp]
  0002c	2b 45 fc	 sub	 eax, DWORD PTR __First_ch$[ebp]
  0002f	89 45 f0	 mov	 DWORD PTR __Count$[ebp], eax

; 2403 : 	_CSTD memmove(_Dest_ch, _First_ch, _Count);

  00032	8b 4d f0	 mov	 ecx, DWORD PTR __Count$[ebp]
  00035	51		 push	 ecx
  00036	8b 55 fc	 mov	 edx, DWORD PTR __First_ch$[ebp]
  00039	52		 push	 edx
  0003a	8b 45 f4	 mov	 eax, DWORD PTR __Dest_ch$[ebp]
  0003d	50		 push	 eax
  0003e	e8 00 00 00 00	 call	 _memmove
  00043	83 c4 0c	 add	 esp, 12			; 0000000cH

; 2404 : 	return (reinterpret_cast<_OutIt>(_Dest_ch + _Count));

  00046	8b 45 f4	 mov	 eax, DWORD PTR __Dest_ch$[ebp]
  00049	03 45 f0	 add	 eax, DWORD PTR __Count$[ebp]

; 2405 : 	}

  0004c	83 c4 10	 add	 esp, 16			; 00000010H
  0004f	3b ec		 cmp	 ebp, esp
  00051	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00056	8b e5		 mov	 esp, ebp
  00058	5d		 pop	 ebp
  00059	c3		 ret	 0
??$_Copy_memmove@PAIPAI@std@@YAPAIPAI00@Z ENDP		; std::_Copy_memmove<unsigned int *,unsigned int *>
_TEXT	ENDS
END
