// Copyright 2014 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Flags: --allow-natives-syntax

(function BinopInEffectContextDeoptAndOsr() {
  function f(a, deopt, osr) {
    var result = (a + 10, "result");
    var dummy = deopt + 0;
    for (var i = 0; osr && i < 2; i++) {
      %PrepareFunctionForOptimization(f);
      %OptimizeOsr();
    }
    return result;
  }

  %PrepareFunctionForOptimization(f);
  assertEquals("result", f(true, 3, false));
  assertEquals("result", f(true, 3, false));
  %OptimizeFunctionOnNextCall(f);
  assertEquals("result", f(true, "foo", true));
})();


(function BinopInEffectContextLazyDeopt() {
  function deopt_f() {
    %DeoptimizeFunction(f);
    return "dummy";
  }

  function h() {
    return { toString : deopt_f };
  }

  function g(x) {
  }

  function f() {
    return g(void(h() + ""));
  };
  %PrepareFunctionForOptimization(f);

  f();
  %OptimizeFunctionOnNextCall(f);
  f();
})();
