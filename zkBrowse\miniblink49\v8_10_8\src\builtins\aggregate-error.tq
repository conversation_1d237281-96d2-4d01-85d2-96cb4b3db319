// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/objects/js-objects.h'

namespace error {

transitioning javascript builtin AggregateErrorConstructor(
    js-implicit context: NativeContext, target: JSFunction,
    newTarget: JSAny)(...arguments): JSAny {
  // 1. If NewTarget is undefined, let newTarget be the active function
  // object, else let newTarget be NewTarget.
  // 2. Let O be ? OrdinaryCreateFromConstructor(newTarget,
  // "%AggregateError.prototype%", « [[ErrorData]], [[AggregateErrors]] »).
  // 3. If _message_ is not _undefined_, then
  //   a. Let msg be ? ToString(_message_).
  //   b. Let msgDesc be the PropertyDescriptor { [[Value]]: _msg_,
  //   [[Writable]]: *true*, [[Enumerable]]: *false*, [[Configurable]]: *true*
  //   c. Perform ! DefinePropertyOrThrow(_O_, *"message"*, _msgDesc_).
  const message: JSAny = arguments[1];
  const options: JSAny = arguments[2];
  const obj: JSObject = ConstructAggregateErrorHelper(
      context, target, newTarget, message, options);

  // 4. Let errorsList be ? IterableToList(errors).
  const errors: JSAny = arguments[0];
  const errorsList = iterator::IterableToListWithSymbolLookup(errors);

  // 5. Perform ! DefinePropertyOrThrow(_O_, `"errors"`, Property Descriptor {
  // [[Configurable]]: *true*, [[Enumerable]]: *false*, [[Writable]]: *true*,
  // [[Value]]: ! CreateArrayFromList(_errorsList_) }).
  SetOwnPropertyIgnoreAttributes(
      obj, ErrorsStringConstant(), errorsList,
      SmiConstant(PropertyAttributes::DONT_ENUM));

  // 6. Return O.
  return obj;
}

extern transitioning runtime ConstructAggregateErrorHelper(
    Context, JSFunction, JSAny, Object, Object): JSObject;

extern transitioning runtime ConstructInternalAggregateErrorHelper(
    Context, Object): JSObject;
}
