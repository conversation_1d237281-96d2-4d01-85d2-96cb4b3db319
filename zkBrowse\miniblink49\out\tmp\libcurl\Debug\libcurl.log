﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  ares_cancel.c
  ares_create_query.c
  ares_data.c
  ares_destroy.c
  ares_expand_name.c
  ares_expand_string.c
  ares_fds.c
  ares_free_hostent.c
  ares_free_string.c
  ares_getenv.c
  ares_gethostbyaddr.c
  ares_gethostbyname.c
  ares_getnameinfo.c
  ares_getopt.c
  ares_getsock.c
  ares_init.c
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\node\cares\src\ares_create_query.c(185): warning C4018: “>”: 有符号/无符号不匹配
  ares_library_init.c
  ares_llist.c
  ares_mkquery.c
  ares_nowarn.c
  ares_options.c
  ares_parse_aaaa_reply.c
  ares_parse_a_reply.c
  ares_parse_mx_reply.c
  ares_parse_naptr_reply.c
  ares_parse_ns_reply.c
  ares_parse_ptr_reply.c
  ares_parse_soa_reply.c
  ares_parse_srv_reply.c
  ares_parse_txt_reply.c
  ares_platform.c
  ares_process.c
  ares_query.c
  ares_search.c
  ares_send.c
  ares_strcasecmp.c
  ares_strdup.c
  ares_strerror.c
  ares_timeout.c
  ares_version.c
  ares_writev.c
  ares__close_sockets.c
  ares__get_hostent.c
  ares__read_line.c
  ares__timeval.c
  bitncmp.c
  inet_net_pton.c
  inet_ntop_cares.c
  windows_port.c
  amigaos.c
  asyn-ares.c
  base64.c
  conncache.c
  connect.c
  content_encoding.c
  cookie.c
  curl_addrinfo.c
  curl_ctype.c
  curl_des.c
  curl_endian.c
  curl_fnmatch.c
  curl_gethostname.c
  curl_gssapi.c
  curl_memrchr.c
  curl_multibyte.c
  curl_ntlm_core.c
  curl_ntlm_wb.c
  curl_range.c
  curl_rtmp.c
  curl_sasl.c
  curl_sspi.c
  curl_threads.c
  dict.c
  dotdot.c
  easy.c
  escape.c
  file.c
  fileinfo.c
  formdata.c
  ftp.c
  ftplistparser.c
  getenv.c
  getinfo.c
  gopher.c
  hash.c
  hmac.c
  hostasyn.c
  hostcheck.c
  hostip.c
  hostip4.c
  hostip6.c
  hostsyn.c
  http.c
  http2.c
  http_chunks.c
  http_digest.c
  http_negotiate.c
  http_ntlm.c
  http_proxy.c
  idn_win32.c
  if2ip.c
  imap.c
  inet_ntop.c
  inet_pton.c
  krb5.c
  ldap.c
  llist.c
  md4.c
  md5.c
  memdebug.c
  mime.c
  mprintf.c
  multi.c
  netrc.c
  non-ascii.c
  nonblock.c
  nwlib.c
  nwos.c
  openldap.c
  parsedate.c
  pingpong.c
  pipeline.c
  pop3.c
  progress.c
  rand.c
  rtsp.c
  security.c
  select.c
  sendf.c
  setopt.c
  sha256.c
  share.c
  slist.c
  smb.c
  smtp.c
  socks.c
  socks_gssapi.c
  socks_sspi.c
  speedcheck.c
  splay.c
  strcase.c
  strdup.c
  strerror.c
  strtok.c
  strtoofft.c
  system_win32.c
  telnet.c
  tftp.c
  timeval.c
  transfer.c
  url.c
  cleartext.c
  cram.c
  digest.c
  digest_sspi.c
  krb5_gssapi.c
  krb5_sspi.c
  ntlm.c
  ntlm_sspi.c
  oauth2.c
  spnego_gssapi.c
  spnego_sspi.c
  vauth.c
  version.c
  cyassl.c
  gskit.c
  gtls.c
  mbedtls.c
  nss.c
  openssl.c
  polarssl.c
  polarssl_threadlock.c
  schannel.c
  schannel_verify.c
  vtls.c
  warnless.c
  wildcard.c
  x509asn1.c
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\libcurl\src\vtls\schannel_verify.c(280): warning C4090: “函数”: 不同的“const”限定符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\libcurl\src\vtls\openssl.c(3878): warning C4018: “<”: 有符号/无符号不匹配
polarssl_threadlock.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
polarssl.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
nss.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
mbedtls.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
gtls.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
gskit.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
cyassl.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
spnego_gssapi.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
ntlm.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
krb5_gssapi.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
socks_gssapi.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
security.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
openldap.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
nwos.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
nwlib.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
non-ascii.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
memdebug.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
md4.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
krb5.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
idn_win32.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
hostsyn.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
hostip6.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
curl_rtmp.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
curl_ntlm_wb.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
curl_gssapi.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
curl_des.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
amigaos.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
windows_port.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
ares_getenv.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
  libcurl.vcxproj -> E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\libcurl.lib
