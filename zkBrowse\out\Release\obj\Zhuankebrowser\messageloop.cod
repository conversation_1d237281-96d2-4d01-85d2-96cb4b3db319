; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	??_C@_0BB@MOGOBHAF@list?$DMT?$DO?5too?5long@	; `string'
EXTRN	__Mtx_unlock:PROC
EXTRN	__Query_perf_counter:PROC
EXTRN	__imp__PeekMessageW@20:PROC
EXTRN	__Mtx_lock:PROC
EXTRN	?_Throw_C_error@std@@YAXH@Z:PROC		; std::_Throw_C_error
EXTRN	__Query_perf_frequency:PROC
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
_BSS	ENDS
;	COMDAT ??_C@_0BB@MOGOBHAF@list?$DMT?$DO?5too?5long@
CONST	SEGMENT
??_C@_0BB@MOGOBHAF@list?$DMT?$DO?5too?5long@ DB 'list<T> too long', 00H ; `string'
	ORG $+1
$SG4294457737 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294457730 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
$SG4294457736 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294457738 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294457728 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457729 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457731 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294457732 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457733 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294457734 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457735 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294457720 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294457721 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294457722 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294457723 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294457724 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294457725 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294457726 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457727 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294457712 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457713 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457714 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294457715 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457716 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294457717 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294457718 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294457719 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294457704 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457705 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294457706 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457707 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457708 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457709 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457710 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457711 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457696 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457697 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457698 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457699 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457700 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457701 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457702 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457703 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457688 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294457689 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294457690 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457691 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457692 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457693 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457694 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457695 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457680 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457681 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457682 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457683 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457684 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294457685 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457686 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457687 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294457672 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457673 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457674 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294457675 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294457676 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294457677 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457678 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457679 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294457664 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294457665 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294457666 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457667 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457668 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294457669 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457670 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457671 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294457656 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457657 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457658 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457659 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457660 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457661 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457662 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457663 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294457648 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294457649 DB 00H, 00H
	ORG $+2
$SG4294457650 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294457651 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294457652 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294457653 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457654 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457655 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457640 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294457641 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294457642 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294457643 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294457644 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294457645 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294457646 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294457647 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294457632 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294457633 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294457634 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294457635 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294457636 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294457637 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294457638 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294457639 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294457624 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294457625 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294457626 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294457627 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294457628 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294457629 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294457630 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294457631 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294457616 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294457617 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294457618 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294457619 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294457620 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294457621 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294457622 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294457623 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294457608 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457609 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294457610 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294457611 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294457612 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294457613 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294457614 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294457615 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294457600 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294457601 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294457602 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294457603 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294457604 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294457605 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294457606 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294457607 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294457592 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457593 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294457594 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294457595 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294457596 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294457597 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294457598 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294457599 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457584 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457585 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457586 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457587 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457588 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457589 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457590 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457591 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294457576 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457577 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457578 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457579 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294457580 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294457581 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294457582 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457583 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294457568 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294457569 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294457570 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294457571 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294457572 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294457573 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294457574 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294457575 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457560 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457561 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294457562 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457563 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294457564 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294457565 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294457566 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294457567 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294457552 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294457553 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294457554 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294457555 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294457556 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457557 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457558 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294457559 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294457544 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294457545 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294457546 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294457547 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457548 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294457549 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457550 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294457551 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294457536 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294457537 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294457538 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294457539 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457540 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294457541 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294457542 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294457543 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294457528 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294457529 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294457530 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294457531 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457532 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457533 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294457534 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294457535 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294457520 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457521 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457522 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294457523 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294457524 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294457525 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457526 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457527 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294457512 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457513 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294457514 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294457515 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294457516 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457517 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294457518 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294457519 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294457504 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294457505 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294457506 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294457507 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294457508 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294457509 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294457510 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294457511 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294457496 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294457497 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294457498 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294457499 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294457500 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294457501 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294457502 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294457503 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294457488 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457489 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457490 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294457491 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294457492 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294457493 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294457494 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294457495 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294457480 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294457481 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294457482 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294457483 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294457484 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457485 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294457486 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294457487 DB 00H, 00H
	ORG $+2
$SG4294457472 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294457473 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294457474 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294457475 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294457476 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294457477 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294457478 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294457479 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294457464 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457465 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457466 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457467 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294457468 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294457469 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294457470 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294457471 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294457456 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457457 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294457458 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457459 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457460 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457461 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294457462 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294457463 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294457448 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294457449 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294457450 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294457451 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457452 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294457453 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457454 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294457455 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294457440 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294457441 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457442 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294457443 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457444 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457445 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294457446 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294457447 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294457432 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294457433 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294457434 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294457435 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294457436 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294457437 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294457438 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294457439 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294457424 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294457425 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294457426 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294457427 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457428 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294457429 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294457430 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294457431 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294457416 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294457417 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294457418 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294457419 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294457420 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294457421 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294457422 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294457423 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294457408 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294457409 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294457410 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294457411 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294457412 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294457413 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294457414 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294457415 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294457400 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294457401 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294457402 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294457403 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457404 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294457405 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457406 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294457407 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457392 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294457393 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294457394 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294457395 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294457396 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294457397 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457398 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294457399 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457384 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294457385 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294457386 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457387 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457388 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457389 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294457390 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457391 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294457376 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294457377 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294457378 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294457379 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457380 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294457381 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294457382 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457383 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457368 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457369 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294457370 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294457371 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457372 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294457373 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457374 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294457375 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294457360 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457361 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457362 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457363 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294457364 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457365 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294457366 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457367 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294457352 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294457353 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294457354 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457355 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294457356 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294457357 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294457358 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457359 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294457344 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457345 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294457346 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457347 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457348 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294457349 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457350 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294457351 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294457336 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294457337 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457338 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294457339 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294457340 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294457341 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294457342 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294457343 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457328 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294457329 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457330 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457331 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457332 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294457333 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457334 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294457335 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294457320 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294457321 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294457322 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457323 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457324 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457325 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294457326 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294457327 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457312 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457313 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294457314 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294457315 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294457316 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457317 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457318 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294457319 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294457304 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457305 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457306 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457307 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457308 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457309 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294457310 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457311 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294457296 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457297 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457298 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457299 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457300 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457301 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294457302 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294457303 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457288 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294457289 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457290 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294457291 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294457292 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294457293 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294457294 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294457295 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294457280 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294457281 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457282 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294457283 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294457284 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294457285 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457286 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457287 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294457272 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294457273 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294457274 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294457275 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294457276 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294457277 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294457278 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294457279 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294457264 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294457265 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294457266 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457267 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457268 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294457269 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294457270 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294457271 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294457260 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294457261 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294457262 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294457263 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294457224 DB 'B', 00H, 00H, 00H
$SG4294457225 DB 'D', 00H, 00H, 00H
$SG4294457226 DB 'M', 00H, 00H, 00H
$SG4294457227 DB 'S', 00H, 00H, 00H
$SG4294457216 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457217 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294457218 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294457219 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294457220 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457221 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457222 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294457223 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294457208 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294457209 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457210 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294457211 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294457212 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294457213 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457214 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294457215 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294457176 DB 00H, 00H
	ORG $+2
$SG4294457177 DB ':', 00H, 00H, 00H
$SG4294457175 DB 00H, 00H
	ORG $+2
$SG4294457084 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294456370 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294456371 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
PUBLIC	??$construct@UTask@CMessageLoop2@@U12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@$$QAU34@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<CMessageLoop2::Task,CMessageLoop2::Task>
PUBLIC	??$?M_JU?$ratio@$00$0DLJKMKAA@@std@@_JU01@@chrono@std@@YA_NABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@0@Z ; std::chrono::operator<<__int64,std::ratio<1,1000000000>,__int64,std::ratio<1,1000000000> >
PUBLIC	?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000> >::count
PUBLIC	?time_since_epoch@?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QBE?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@XZ ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_since_epoch
PUBLIC	??$forward@AAPAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ; std::forward<std::_List_node<CMessageLoop2::Task,void *> * &>
PUBLIC	??$destroy@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<std::_List_node<CMessageLoop2::Task,void *> *>
PUBLIC	??$_Buynode@UTask@CMessageLoop2@@@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PAU21@0$$QAUTask@CMessageLoop2@@@Z ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Buynode<CMessageLoop2::Task>
PUBLIC	??$forward@UTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::forward<CMessageLoop2::Task>
PUBLIC	??$?MUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z ; std::chrono::operator<<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
PUBLIC	??$duration_cast@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@_JU?$ratio@$00$0DOI@@3@X@chrono@std@@YA?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@01@@Z ; std::chrono::duration_cast<std::chrono::duration<__int64,std::ratio<1,1000000000> >,__int64,std::ratio<1,1000>,void>
PUBLIC	?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first
PUBLIC	?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
PUBLIC	?max_size@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAIABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::max_size
PUBLIC	?max_size@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBEIXZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::max_size
PUBLIC	?count@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::count
PUBLIC	?_Incsize@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXI@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Incsize
PUBLIC	?deallocate@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@QAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::deallocate
PUBLIC	??_GTask@CMessageLoop2@@QAEPAXI@Z		; CMessageLoop2::Task::`scalar deleting destructor'
PUBLIC	??$_Get_size_of_n@$0DI@@std@@YAII@Z		; std::_Get_size_of_n<56>
PUBLIC	??$construct@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@AAPAU12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@AAPAU31@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<std::_List_node<CMessageLoop2::Task,void *> *,std::_List_node<CMessageLoop2::Task,void *> * &>
PUBLIC	??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ; std::addressof<std::_List_node<CMessageLoop2::Task,void *> *>
PUBLIC	??$addressof@$$CBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@ABV10@@Z ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > const >
PUBLIC	??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z ; std::_List_node<CMessageLoop2::Task,void *>::_Freenode0<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >
PUBLIC	??$destroy@UTask@CMessageLoop2@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<CMessageLoop2::Task>
PUBLIC	??$addressof@UTask@CMessageLoop2@@@std@@YAPAUTask@CMessageLoop2@@AAU12@@Z ; std::addressof<CMessageLoop2::Task>
PUBLIC	??$addressof@Vmutex@std@@@std@@YAPAVmutex@0@AAV10@@Z ; std::addressof<std::mutex>
PUBLIC	??$move@AAV?$function@$$A6AXXZ@std@@@std@@YA$$QAV?$function@$$A6AXXZ@0@AAV10@@Z ; std::move<std::function<void __cdecl(void)> &>
PUBLIC	??$_Insert@UTask@CMessageLoop2@@@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@1@$$QAUTask@CMessageLoop2@@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Insert<CMessageLoop2::Task>
PUBLIC	??$addressof@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@AAV10@@Z ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
PUBLIC	??$move@AAUTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::move<CMessageLoop2::Task &>
PUBLIC	??$?NUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z ; std::chrono::operator<=<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
PUBLIC	??$?0_JU?$ratio@$00$0DOI@@std@@X@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@12@@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,std::ratio<1,1000>,void>
PUBLIC	??$?0HX@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QAE@ABH@Z ; std::chrono::duration<__int64,std::ratio<1,1000> >::duration<__int64,std::ratio<1,1000> ><int,void>
PUBLIC	??R?$_Func_class@X$$V@std@@QBEXXZ		; std::_Func_class<void>::operator()
PUBLIC	??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
PUBLIC	?deallocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEXQAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::deallocate
PUBLIC	?allocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::allocate
PUBLIC	?_Buynode0@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@PAU32@0@Z ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Buynode0
PUBLIC	??0?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>
PUBLIC	?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first
PUBLIC	?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second
PUBLIC	?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
PUBLIC	?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
PUBLIC	?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAIXZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
PUBLIC	??0?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
PUBLIC	??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
PUBLIC	??0?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
PUBLIC	?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second
PUBLIC	??Y?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAEAAV012@ABV012@@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::operator+=
PUBLIC	?_Unchecked_end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unchecked_end
PUBLIC	?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Make_iter
PUBLIC	?_Unlinknode@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unlinknode
PUBLIC	?_Freenode@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@@Z ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Freenode
PUBLIC	?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
PUBLIC	?_Myhead@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Myhead
PUBLIC	?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABIXZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
PUBLIC	??0?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
PUBLIC	??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
PUBLIC	??D?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABUTask@CMessageLoop2@@XZ ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*
PUBLIC	??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
PUBLIC	??8?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator==
PUBLIC	?pointer_to@?$pointer_traits@PAUTask@CMessageLoop2@@@std@@SAPAUTask@CMessageLoop2@@AAU34@@Z ; std::pointer_traits<CMessageLoop2::Task *>::pointer_to
PUBLIC	?zero@?$duration_values@_J@chrono@std@@SA_JXZ	; std::chrono::duration_values<__int64>::zero
PUBLIC	?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::zero
PUBLIC	??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
PUBLIC	??Y?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAEAAV012@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::operator+=
PUBLIC	??0?$unique_lock@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::unique_lock<std::mutex>::unique_lock<std::mutex>
PUBLIC	??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ	; std::unique_lock<std::mutex>::~unique_lock<std::mutex>
PUBLIC	??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
PUBLIC	??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ	; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
PUBLIC	??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z	; std::function<void __cdecl(void)>::function<void __cdecl(void)>
PUBLIC	?push_back@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEX$$QAUTask@CMessageLoop2@@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::push_back
PUBLIC	?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::begin
PUBLIC	?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::end
PUBLIC	?empty@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE_NXZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::empty
PUBLIC	?erase@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::erase
PUBLIC	??D?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEAAUTask@CMessageLoop2@@XZ ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*
PUBLIC	??C?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEPAUTask@CMessageLoop2@@XZ ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator->
PUBLIC	??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
PUBLIC	??9?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator!=
PUBLIC	?DoMessageLoop@CMessageLoop2@@SAIXZ		; CMessageLoop2::DoMessageLoop
PUBLIC	?Run@CMessageLoop2@@QAEIXZ			; CMessageLoop2::Run
PUBLIC	?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z ; CMessageLoop2::postDelayedTask
PUBLIC	?Release@CMessageLoop2@@QAEXXZ			; CMessageLoop2::Release
PUBLIC	?AddRef@CMessageLoop2@@QAEXXZ			; CMessageLoop2::AddRef
PUBLIC	??1Task@CMessageLoop2@@QAE@XZ			; CMessageLoop2::Task::~Task
PUBLIC	??0Task@CMessageLoop2@@QAE@$$QAU01@@Z		; CMessageLoop2::Task::Task
PUBLIC	?doTasks@CMessageLoop2@@AAE_NXZ			; CMessageLoop2::doTasks
PUBLIC	??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@XZ ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
PUBLIC	??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z ; CMessageLoop2::Task::Task
PUBLIC	??F_Atomic_int@std@@QAEHH@Z			; std::_Atomic_int::operator--
PUBLIC	??E_Atomic_int@std@@QAEHH@Z			; std::_Atomic_int::operator++
PUBLIC	?atomic_fetch_sub@std@@YAHPAU_Atomic_int@1@H@Z	; std::atomic_fetch_sub
PUBLIC	?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z ; std::atomic_fetch_sub_explicit
PUBLIC	?atomic_fetch_add@std@@YAHPAU_Atomic_int@1@H@Z	; std::atomic_fetch_add
PUBLIC	?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z ; std::atomic_fetch_add_explicit
PUBLIC	??B_Atomic_int@std@@QBEHXZ			; std::_Atomic_int::operator int
PUBLIC	?atomic_load@std@@YAHPBU_Atomic_int@1@@Z	; std::atomic_load
PUBLIC	?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z ; std::atomic_load_explicit
PUBLIC	?_Atomic_fetch_sub_4@std@@YAKPCKKW4memory_order@1@@Z ; std::_Atomic_fetch_sub_4
PUBLIC	?_Atomic_fetch_add_4@std@@YAKPCKKW4memory_order@1@@Z ; std::_Atomic_fetch_add_4
PUBLIC	?_Fetch_add_release_4@std@@YAKPCKK@Z		; std::_Fetch_add_release_4
PUBLIC	?_Fetch_add_acquire_4@std@@YAKPCKK@Z		; std::_Fetch_add_acquire_4
PUBLIC	?_Fetch_add_relaxed_4@std@@YAKPCKK@Z		; std::_Fetch_add_relaxed_4
PUBLIC	?_Fetch_add_seq_cst_4@std@@YAKPCKK@Z		; std::_Fetch_add_seq_cst_4
PUBLIC	?_Atomic_load_4@std@@YAKPCKW4memory_order@1@@Z	; std::_Atomic_load_4
PUBLIC	?_Load_acquire_4@std@@YAKPCK@Z			; std::_Load_acquire_4
PUBLIC	?_Load_relaxed_4@std@@YAKPCK@Z			; std::_Load_relaxed_4
PUBLIC	?_Load_seq_cst_4@std@@YAKPCK@Z			; std::_Load_seq_cst_4
PUBLIC	?_Mymtx@_Mutex_base@std@@AAEPAU_Mtx_internal_imp_t@@XZ ; std::_Mutex_base::_Mymtx
PUBLIC	?unlock@_Mutex_base@std@@QAEXXZ			; std::_Mutex_base::unlock
PUBLIC	?lock@_Mutex_base@std@@QAEXXZ			; std::_Mutex_base::lock
PUBLIC	?_Mtx_unlockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z ; std::_Mtx_unlockX
PUBLIC	?_Mtx_lockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z	; std::_Mtx_lockX
PUBLIC	?_Check_C_return@std@@YAHH@Z			; std::_Check_C_return
PUBLIC	?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ ; std::chrono::steady_clock::now
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1Task@CMessageLoop2@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1Task@CMessageLoop2@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1Task@CMessageLoop2@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1Task@CMessageLoop2@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__ehfuncinfo$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z$0
	DD	00H
	DD	FLAT:__unwindfunclet$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z$1
__ehfuncinfo$?doTasks@CMessageLoop2@@AAE_NXZ DD 019930522H
	DD	04H
	DD	FLAT:__unwindtable$?doTasks@CMessageLoop2@@AAE_NXZ
	DD	01H
	DD	FLAT:__tryblocktable$?doTasks@CMessageLoop2@@AAE_NXZ
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?doTasks@CMessageLoop2@@AAE_NXZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?doTasks@CMessageLoop2@@AAE_NXZ$2
	DD	00H
	DD	FLAT:__unwindfunclet$?doTasks@CMessageLoop2@@AAE_NXZ$3
	DD	01H
	DD	00H
	DD	01H
	DD	00H
__tryblocktable$?doTasks@CMessageLoop2@@AAE_NXZ DD 02H
	DD	02H
	DD	03H
	DD	01H
	DD	FLAT:__catchsym$?doTasks@CMessageLoop2@@AAE_NXZ$4
__catchsym$?doTasks@CMessageLoop2@@AAE_NXZ$4 DD 00H
	DD	00H
	DD	00H
	DD	FLAT:__catch$?doTasks@CMessageLoop2@@AAE_NXZ$0
__ehfuncinfo$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z$0
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ
_TEXT	SEGMENT
$T2 = -60						; size = 8
$T3 = -52						; size = 8
__Part$ = -44						; size = 8
__Whole$ = -36						; size = 8
__Ctr$ = -28						; size = 8
__Freq$ = -20						; size = 8
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ PROC ; std::chrono::steady_clock::now, COMDAT

; 799  : 		{	// get current time

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 30	 sub	 esp, 48			; 00000030H
  0001b	57		 push	 edi
  0001c	8d 7d c4	 lea	 edi, DWORD PTR [ebp-60]
  0001f	b9 0c 00 00 00	 mov	 ecx, 12			; 0000000cH
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd

; 800  : 		const long long _Freq = _Query_perf_frequency();	// doesn't change after system boot

  0002b	e8 00 00 00 00	 call	 __Query_perf_frequency
  00030	89 45 ec	 mov	 DWORD PTR __Freq$[ebp], eax
  00033	89 55 f0	 mov	 DWORD PTR __Freq$[ebp+4], edx

; 801  : 		const long long _Ctr = _Query_perf_counter();

  00036	e8 00 00 00 00	 call	 __Query_perf_counter
  0003b	89 45 e4	 mov	 DWORD PTR __Ctr$[ebp], eax
  0003e	89 55 e8	 mov	 DWORD PTR __Ctr$[ebp+4], edx

; 802  : 		static_assert(period::num == 1, "This assumes period::num == 1.");
; 803  : 		const long long _Whole = (_Ctr / _Freq) * period::den;

  00041	8b 45 f0	 mov	 eax, DWORD PTR __Freq$[ebp+4]
  00044	50		 push	 eax
  00045	8b 4d ec	 mov	 ecx, DWORD PTR __Freq$[ebp]
  00048	51		 push	 ecx
  00049	8b 55 e8	 mov	 edx, DWORD PTR __Ctr$[ebp+4]
  0004c	52		 push	 edx
  0004d	8b 45 e4	 mov	 eax, DWORD PTR __Ctr$[ebp]
  00050	50		 push	 eax
  00051	e8 00 00 00 00	 call	 __alldiv
  00056	6a 00		 push	 0
  00058	68 00 ca 9a 3b	 push	 1000000000		; 3b9aca00H
  0005d	52		 push	 edx
  0005e	50		 push	 eax
  0005f	e8 00 00 00 00	 call	 __allmul
  00064	89 45 dc	 mov	 DWORD PTR __Whole$[ebp], eax
  00067	89 55 e0	 mov	 DWORD PTR __Whole$[ebp+4], edx

; 804  : 		const long long _Part = (_Ctr % _Freq) * period::den / _Freq;

  0006a	8b 4d f0	 mov	 ecx, DWORD PTR __Freq$[ebp+4]
  0006d	51		 push	 ecx
  0006e	8b 55 ec	 mov	 edx, DWORD PTR __Freq$[ebp]
  00071	52		 push	 edx
  00072	8b 45 e8	 mov	 eax, DWORD PTR __Ctr$[ebp+4]
  00075	50		 push	 eax
  00076	8b 4d e4	 mov	 ecx, DWORD PTR __Ctr$[ebp]
  00079	51		 push	 ecx
  0007a	e8 00 00 00 00	 call	 __allrem
  0007f	6a 00		 push	 0
  00081	68 00 ca 9a 3b	 push	 1000000000		; 3b9aca00H
  00086	52		 push	 edx
  00087	50		 push	 eax
  00088	e8 00 00 00 00	 call	 __allmul
  0008d	8b 4d f0	 mov	 ecx, DWORD PTR __Freq$[ebp+4]
  00090	51		 push	 ecx
  00091	8b 4d ec	 mov	 ecx, DWORD PTR __Freq$[ebp]
  00094	51		 push	 ecx
  00095	52		 push	 edx
  00096	50		 push	 eax
  00097	e8 00 00 00 00	 call	 __alldiv
  0009c	89 45 d4	 mov	 DWORD PTR __Part$[ebp], eax
  0009f	89 55 d8	 mov	 DWORD PTR __Part$[ebp+4], edx

; 805  : 		return (time_point(duration(_Whole + _Part)));

  000a2	8b 55 dc	 mov	 edx, DWORD PTR __Whole$[ebp]
  000a5	03 55 d4	 add	 edx, DWORD PTR __Part$[ebp]
  000a8	8b 45 e0	 mov	 eax, DWORD PTR __Whole$[ebp+4]
  000ab	13 45 d8	 adc	 eax, DWORD PTR __Part$[ebp+4]
  000ae	89 55 c4	 mov	 DWORD PTR $T2[ebp], edx
  000b1	89 45 c8	 mov	 DWORD PTR $T2[ebp+4], eax
  000b4	8d 4d c4	 lea	 ecx, DWORD PTR $T2[ebp]
  000b7	51		 push	 ecx
  000b8	8d 4d cc	 lea	 ecx, DWORD PTR $T3[ebp]
  000bb	e8 00 00 00 00	 call	 ??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
  000c0	50		 push	 eax
  000c1	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  000c4	e8 00 00 00 00	 call	 ??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
  000c9	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 806  : 		}

  000cc	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000cf	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000d6	5f		 pop	 edi
  000d7	83 c4 3c	 add	 esp, 60			; 0000003cH
  000da	3b ec		 cmp	 ebp, esp
  000dc	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000e1	8b e5		 mov	 esp, ebp
  000e3	5d		 pop	 ebp
  000e4	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ ENDP ; std::chrono::steady_clock::now
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\thr\xthread
;	COMDAT ?_Check_C_return@std@@YAHH@Z
_TEXT	SEGMENT
__Res$ = 8						; size = 4
?_Check_C_return@std@@YAHH@Z PROC			; std::_Check_C_return, COMDAT

; 35   : 	{	// throw exception on failure

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 36   : 	if (_Res != _Thrd_success)

  00003	83 7d 08 00	 cmp	 DWORD PTR __Res$[ebp], 0
  00007	74 0c		 je	 SHORT $LN2@Check_C_re

; 37   : 		_Throw_C_error(_Res);

  00009	8b 45 08	 mov	 eax, DWORD PTR __Res$[ebp]
  0000c	50		 push	 eax
  0000d	e8 00 00 00 00	 call	 ?_Throw_C_error@std@@YAXH@Z ; std::_Throw_C_error
  00012	83 c4 04	 add	 esp, 4
$LN2@Check_C_re:

; 38   : 	return (_Res);

  00015	8b 45 08	 mov	 eax, DWORD PTR __Res$[ebp]

; 39   : 	}

  00018	3b ec		 cmp	 ebp, esp
  0001a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001f	5d		 pop	 ebp
  00020	c3		 ret	 0
?_Check_C_return@std@@YAHH@Z ENDP			; std::_Check_C_return
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\thr\xthread
;	COMDAT ?_Mtx_lockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z
_TEXT	SEGMENT
__Mtx$ = 8						; size = 4
?_Mtx_lockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z PROC	; std::_Mtx_lockX, COMDAT

; 71   : 	{	// throw exception on failure

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 72   : 	return (_Check_C_return(_Mtx_lock(_Mtx)));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Mtx$[ebp]
  00006	50		 push	 eax
  00007	e8 00 00 00 00	 call	 __Mtx_lock
  0000c	83 c4 04	 add	 esp, 4
  0000f	50		 push	 eax
  00010	e8 00 00 00 00	 call	 ?_Check_C_return@std@@YAHH@Z ; std::_Check_C_return
  00015	83 c4 04	 add	 esp, 4

; 73   : 	}

  00018	3b ec		 cmp	 ebp, esp
  0001a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001f	5d		 pop	 ebp
  00020	c3		 ret	 0
?_Mtx_lockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z ENDP	; std::_Mtx_lockX
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\thr\xthread
;	COMDAT ?_Mtx_unlockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z
_TEXT	SEGMENT
__Mtx$ = 8						; size = 4
?_Mtx_unlockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z PROC	; std::_Mtx_unlockX, COMDAT

; 86   : 	{	// throw exception on failure

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 87   : 	return (_Check_C_return(_Mtx_unlock(_Mtx)));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Mtx$[ebp]
  00006	50		 push	 eax
  00007	e8 00 00 00 00	 call	 __Mtx_unlock
  0000c	83 c4 04	 add	 esp, 4
  0000f	50		 push	 eax
  00010	e8 00 00 00 00	 call	 ?_Check_C_return@std@@YAHH@Z ; std::_Check_C_return
  00015	83 c4 04	 add	 esp, 4

; 88   : 	}

  00018	3b ec		 cmp	 ebp, esp
  0001a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001f	5d		 pop	 ebp
  00020	c3		 ret	 0
?_Mtx_unlockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z ENDP	; std::_Mtx_unlockX
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\mutex
;	COMDAT ?lock@_Mutex_base@std@@QAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?lock@_Mutex_base@std@@QAEXXZ PROC			; std::_Mutex_base::lock, COMDAT
; _this$ = ecx

; 48   : 		{	// lock the mutex

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 49   : 		_Mtx_lockX(_Mymtx());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Mymtx@_Mutex_base@std@@AAEPAU_Mtx_internal_imp_t@@XZ ; std::_Mutex_base::_Mymtx
  00016	50		 push	 eax
  00017	e8 00 00 00 00	 call	 ?_Mtx_lockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z ; std::_Mtx_lockX
  0001c	83 c4 04	 add	 esp, 4

; 50   : 		}

  0001f	83 c4 04	 add	 esp, 4
  00022	3b ec		 cmp	 ebp, esp
  00024	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00029	8b e5		 mov	 esp, ebp
  0002b	5d		 pop	 ebp
  0002c	c3		 ret	 0
?lock@_Mutex_base@std@@QAEXXZ ENDP			; std::_Mutex_base::lock
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\mutex
;	COMDAT ?unlock@_Mutex_base@std@@QAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?unlock@_Mutex_base@std@@QAEXXZ PROC			; std::_Mutex_base::unlock, COMDAT
; _this$ = ecx

; 58   : 		{	// unlock the mutex

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 59   : 		_Mtx_unlockX(_Mymtx());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Mymtx@_Mutex_base@std@@AAEPAU_Mtx_internal_imp_t@@XZ ; std::_Mutex_base::_Mymtx
  00016	50		 push	 eax
  00017	e8 00 00 00 00	 call	 ?_Mtx_unlockX@std@@YAHPAU_Mtx_internal_imp_t@@@Z ; std::_Mtx_unlockX
  0001c	83 c4 04	 add	 esp, 4

; 60   : 		}

  0001f	83 c4 04	 add	 esp, 4
  00022	3b ec		 cmp	 ebp, esp
  00024	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00029	8b e5		 mov	 esp, ebp
  0002b	5d		 pop	 ebp
  0002c	c3		 ret	 0
?unlock@_Mutex_base@std@@QAEXXZ ENDP			; std::_Mutex_base::unlock
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\mutex
;	COMDAT ?_Mymtx@_Mutex_base@std@@AAEPAU_Mtx_internal_imp_t@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Mymtx@_Mutex_base@std@@AAEPAU_Mtx_internal_imp_t@@XZ PROC ; std::_Mutex_base::_Mymtx, COMDAT
; _this$ = ecx

; 77   : 		{	// get pointer to _Mtx_internal_imp_t inside _Mtx_storage

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 78   : 		return (reinterpret_cast<_Mtx_t>(&_Mtx_storage));

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 79   : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Mymtx@_Mutex_base@std@@AAEPAU_Mtx_internal_imp_t@@XZ ENDP ; std::_Mutex_base::_Mymtx
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Load_seq_cst_4@std@@YAKPCK@Z
_TEXT	SEGMENT
__Value$ = -4						; size = 4
__Tgt$ = 8						; size = 4
?_Load_seq_cst_4@std@@YAKPCK@Z PROC			; std::_Load_seq_cst_4, COMDAT

; 1316 : 	{	/* load from *_Tgt atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 1317 : 			sequentially consistent memory order */
; 1318 : 	_Uint4_t _Value;
; 1319 : 
; 1320 :  #if defined(_M_ARM) || defined(_M_ARM64)
; 1321 : 	_Value = static_cast<_Uint4_t>(__iso_volatile_load32((volatile int *)_Tgt));
; 1322 : 	_Memory_barrier();
; 1323 : 
; 1324 :  #else
; 1325 : 	_Value = *_Tgt;

  0000b	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  0000e	8b 08		 mov	 ecx, DWORD PTR [eax]
  00010	89 4d fc	 mov	 DWORD PTR __Value$[ebp], ecx

; 1326 : 	_Compiler_barrier();
; 1327 :  #endif
; 1328 : 
; 1329 : 	return (_Value);

  00013	8b 45 fc	 mov	 eax, DWORD PTR __Value$[ebp]

; 1330 : 	}

  00016	8b e5		 mov	 esp, ebp
  00018	5d		 pop	 ebp
  00019	c3		 ret	 0
?_Load_seq_cst_4@std@@YAKPCK@Z ENDP			; std::_Load_seq_cst_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Load_relaxed_4@std@@YAKPCK@Z
_TEXT	SEGMENT
__Value$ = -4						; size = 4
__Tgt$ = 8						; size = 4
?_Load_relaxed_4@std@@YAKPCK@Z PROC			; std::_Load_relaxed_4, COMDAT

; 1333 : 	{	/* load from *_Tgt atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 1334 : 			relaxed memory order */
; 1335 : 	_Uint4_t _Value;
; 1336 : 
; 1337 :  #if defined(_M_ARM) || defined(_M_ARM64)
; 1338 : 	_Value = static_cast<_Uint4_t>(__iso_volatile_load32((volatile int *)_Tgt));
; 1339 : 
; 1340 :  #else
; 1341 : 	_Value = *_Tgt;

  0000b	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  0000e	8b 08		 mov	 ecx, DWORD PTR [eax]
  00010	89 4d fc	 mov	 DWORD PTR __Value$[ebp], ecx

; 1342 :  #endif
; 1343 : 
; 1344 : 	return (_Value);

  00013	8b 45 fc	 mov	 eax, DWORD PTR __Value$[ebp]

; 1345 : 	}

  00016	8b e5		 mov	 esp, ebp
  00018	5d		 pop	 ebp
  00019	c3		 ret	 0
?_Load_relaxed_4@std@@YAKPCK@Z ENDP			; std::_Load_relaxed_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Load_acquire_4@std@@YAKPCK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
?_Load_acquire_4@std@@YAKPCK@Z PROC			; std::_Load_acquire_4, COMDAT

; 1348 : 	{	/* load from *_Tgt atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1349 : 			acquire memory order */
; 1350 : 
; 1351 : 	return (_Load_seq_cst_4(_Tgt));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  00006	50		 push	 eax
  00007	e8 00 00 00 00	 call	 ?_Load_seq_cst_4@std@@YAKPCK@Z ; std::_Load_seq_cst_4
  0000c	83 c4 04	 add	 esp, 4

; 1352 : 	}

  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?_Load_acquire_4@std@@YAKPCK@Z ENDP			; std::_Load_acquire_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Atomic_load_4@std@@YAKPCKW4memory_order@1@@Z
_TEXT	SEGMENT
tv64 = -4						; size = 4
__Tgt$ = 8						; size = 4
__Order$ = 12						; size = 4
?_Atomic_load_4@std@@YAKPCKW4memory_order@1@@Z PROC	; std::_Atomic_load_4, COMDAT

; 1356 : 	{	/* load from *_Tgt atomically */

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 1357 : 	switch (_Order)

  0000b	8b 45 0c	 mov	 eax, DWORD PTR __Order$[ebp]
  0000e	89 45 fc	 mov	 DWORD PTR tv64[ebp], eax
  00011	83 7d fc 05	 cmp	 DWORD PTR tv64[ebp], 5
  00015	77 34		 ja	 SHORT $LN7@Atomic_loa
  00017	8b 4d fc	 mov	 ecx, DWORD PTR tv64[ebp]
  0001a	ff 24 8d 00 00
	00 00		 jmp	 DWORD PTR $LN9@Atomic_loa[ecx*4]
$LN4@Atomic_loa:

; 1358 : 		{
; 1359 : 		case memory_order_relaxed:
; 1360 : 			return (_Load_relaxed_4(_Tgt));

  00021	8b 55 08	 mov	 edx, DWORD PTR __Tgt$[ebp]
  00024	52		 push	 edx
  00025	e8 00 00 00 00	 call	 ?_Load_relaxed_4@std@@YAKPCK@Z ; std::_Load_relaxed_4
  0002a	83 c4 04	 add	 esp, 4
  0002d	eb 1e		 jmp	 SHORT $LN1@Atomic_loa
$LN5@Atomic_loa:

; 1361 : 
; 1362 : 		case memory_order_consume:
; 1363 : 		case memory_order_acquire:
; 1364 : 			return (_Load_acquire_4(_Tgt));

  0002f	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  00032	50		 push	 eax
  00033	e8 00 00 00 00	 call	 ?_Load_acquire_4@std@@YAKPCK@Z ; std::_Load_acquire_4
  00038	83 c4 04	 add	 esp, 4
  0003b	eb 10		 jmp	 SHORT $LN1@Atomic_loa
$LN6@Atomic_loa:

; 1365 : 
; 1366 : 		case memory_order_seq_cst:
; 1367 : 			return (_Load_seq_cst_4(_Tgt));

  0003d	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  00040	51		 push	 ecx
  00041	e8 00 00 00 00	 call	 ?_Load_seq_cst_4@std@@YAKPCK@Z ; std::_Load_seq_cst_4
  00046	83 c4 04	 add	 esp, 4
  00049	eb 02		 jmp	 SHORT $LN1@Atomic_loa
$LN7@Atomic_loa:

; 1368 : 
; 1369 : 		case memory_order_release:
; 1370 : 		case memory_order_acq_rel:
; 1371 : 		default:
; 1372 : 			_INVALID_MEMORY_ORDER;
; 1373 : 			return (0);

  0004b	33 c0		 xor	 eax, eax
$LN1@Atomic_loa:

; 1374 : 		}
; 1375 : 	}

  0004d	83 c4 04	 add	 esp, 4
  00050	3b ec		 cmp	 ebp, esp
  00052	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00057	8b e5		 mov	 esp, ebp
  00059	5d		 pop	 ebp
  0005a	c3		 ret	 0
  0005b	90		 npad	 1
$LN9@Atomic_loa:
  0005c	00 00 00 00	 DD	 $LN4@Atomic_loa
  00060	00 00 00 00	 DD	 $LN5@Atomic_loa
  00064	00 00 00 00	 DD	 $LN5@Atomic_loa
  00068	00 00 00 00	 DD	 $LN7@Atomic_loa
  0006c	00 00 00 00	 DD	 $LN7@Atomic_loa
  00070	00 00 00 00	 DD	 $LN6@Atomic_loa
?_Atomic_load_4@std@@YAKPCKW4memory_order@1@@Z ENDP	; std::_Atomic_load_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Fetch_add_seq_cst_4@std@@YAKPCKK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
?_Fetch_add_seq_cst_4@std@@YAKPCKK@Z PROC		; std::_Fetch_add_seq_cst_4, COMDAT

; 1541 : 	{	/* add _Value to *_Tgt atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1542 : 			sequentially consistent memory order */
; 1543 : 	return (static_cast<_Uint4_t>(

  00003	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00006	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  00009	f0 0f c1 01	 lock	  xadd	 DWORD PTR [ecx], eax

; 1544 : 		_INTRIN_SEQ_CST(_InterlockedExchangeAdd)((volatile long *)_Tgt, static_cast<long>(_Value))));
; 1545 : 	}

  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
?_Fetch_add_seq_cst_4@std@@YAKPCKK@Z ENDP		; std::_Fetch_add_seq_cst_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Fetch_add_relaxed_4@std@@YAKPCKK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
?_Fetch_add_relaxed_4@std@@YAKPCKK@Z PROC		; std::_Fetch_add_relaxed_4, COMDAT

; 1548 : 	{	/* add _Value to *_Tgt atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1549 : 			relaxed memory order */
; 1550 : 	return (static_cast<_Uint4_t>(

  00003	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00006	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  00009	f0 0f c1 01	 lock	  xadd	 DWORD PTR [ecx], eax

; 1551 : 		_INTRIN_RELAXED(_InterlockedExchangeAdd)((volatile long *)_Tgt, static_cast<long>(_Value))));
; 1552 : 	}

  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
?_Fetch_add_relaxed_4@std@@YAKPCKK@Z ENDP		; std::_Fetch_add_relaxed_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Fetch_add_acquire_4@std@@YAKPCKK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
?_Fetch_add_acquire_4@std@@YAKPCKK@Z PROC		; std::_Fetch_add_acquire_4, COMDAT

; 1555 : 	{	/* add _Value to *_Tgt atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1556 : 			acquire memory order */
; 1557 : 	return (static_cast<_Uint4_t>(

  00003	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00006	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  00009	f0 0f c1 01	 lock	  xadd	 DWORD PTR [ecx], eax

; 1558 : 		_INTRIN_ACQUIRE(_InterlockedExchangeAdd)((volatile long *)_Tgt, static_cast<long>(_Value))));
; 1559 : 	}

  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
?_Fetch_add_acquire_4@std@@YAKPCKK@Z ENDP		; std::_Fetch_add_acquire_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Fetch_add_release_4@std@@YAKPCKK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
?_Fetch_add_release_4@std@@YAKPCKK@Z PROC		; std::_Fetch_add_release_4, COMDAT

; 1562 : 	{	/* add _Value to *_Tgt atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1563 : 			release memory order */
; 1564 : 	return (static_cast<_Uint4_t>(

  00003	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00006	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  00009	f0 0f c1 01	 lock	  xadd	 DWORD PTR [ecx], eax

; 1565 : 		_INTRIN_RELEASE(_InterlockedExchangeAdd)((volatile long *)_Tgt, static_cast<long>(_Value))));
; 1566 : 	}

  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
?_Fetch_add_release_4@std@@YAKPCKK@Z ENDP		; std::_Fetch_add_release_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Atomic_fetch_add_4@std@@YAKPCKKW4memory_order@1@@Z
_TEXT	SEGMENT
tv64 = -4						; size = 4
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
__Order$ = 16						; size = 4
?_Atomic_fetch_add_4@std@@YAKPCKKW4memory_order@1@@Z PROC ; std::_Atomic_fetch_add_4, COMDAT

; 1570 : 	{	/* add _Value to *_Tgt atomically */

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 1571 : 	switch (_Order)

  0000b	8b 45 10	 mov	 eax, DWORD PTR __Order$[ebp]
  0000e	89 45 fc	 mov	 DWORD PTR tv64[ebp], eax
  00011	83 7d fc 05	 cmp	 DWORD PTR tv64[ebp], 5
  00015	77 52		 ja	 SHORT $LN8@Atomic_fet
  00017	8b 4d fc	 mov	 ecx, DWORD PTR tv64[ebp]
  0001a	ff 24 8d 00 00
	00 00		 jmp	 DWORD PTR $LN10@Atomic_fet[ecx*4]
$LN4@Atomic_fet:

; 1572 : 		{
; 1573 : 		case memory_order_relaxed:
; 1574 : 			return (_Fetch_add_relaxed_4(_Tgt, _Value));

  00021	8b 55 0c	 mov	 edx, DWORD PTR __Value$[ebp]
  00024	52		 push	 edx
  00025	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  00028	50		 push	 eax
  00029	e8 00 00 00 00	 call	 ?_Fetch_add_relaxed_4@std@@YAKPCKK@Z ; std::_Fetch_add_relaxed_4
  0002e	83 c4 08	 add	 esp, 8
  00031	eb 38		 jmp	 SHORT $LN1@Atomic_fet
$LN5@Atomic_fet:

; 1575 : 
; 1576 : 		case memory_order_consume:
; 1577 : 		case memory_order_acquire:
; 1578 : 			return (_Fetch_add_acquire_4(_Tgt, _Value));

  00033	8b 4d 0c	 mov	 ecx, DWORD PTR __Value$[ebp]
  00036	51		 push	 ecx
  00037	8b 55 08	 mov	 edx, DWORD PTR __Tgt$[ebp]
  0003a	52		 push	 edx
  0003b	e8 00 00 00 00	 call	 ?_Fetch_add_acquire_4@std@@YAKPCKK@Z ; std::_Fetch_add_acquire_4
  00040	83 c4 08	 add	 esp, 8
  00043	eb 26		 jmp	 SHORT $LN1@Atomic_fet
$LN6@Atomic_fet:

; 1579 : 
; 1580 : 		case memory_order_release:
; 1581 : 			return (_Fetch_add_release_4(_Tgt, _Value));

  00045	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00048	50		 push	 eax
  00049	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  0004c	51		 push	 ecx
  0004d	e8 00 00 00 00	 call	 ?_Fetch_add_release_4@std@@YAKPCKK@Z ; std::_Fetch_add_release_4
  00052	83 c4 08	 add	 esp, 8
  00055	eb 14		 jmp	 SHORT $LN1@Atomic_fet
$LN7@Atomic_fet:

; 1582 : 
; 1583 : 		case memory_order_acq_rel:
; 1584 : 		case memory_order_seq_cst:
; 1585 : 			return (_Fetch_add_seq_cst_4(_Tgt, _Value));

  00057	8b 55 0c	 mov	 edx, DWORD PTR __Value$[ebp]
  0005a	52		 push	 edx
  0005b	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  0005e	50		 push	 eax
  0005f	e8 00 00 00 00	 call	 ?_Fetch_add_seq_cst_4@std@@YAKPCKK@Z ; std::_Fetch_add_seq_cst_4
  00064	83 c4 08	 add	 esp, 8
  00067	eb 02		 jmp	 SHORT $LN1@Atomic_fet
$LN8@Atomic_fet:

; 1586 : 
; 1587 : 		default:
; 1588 : 			_INVALID_MEMORY_ORDER;
; 1589 : 			return (0);

  00069	33 c0		 xor	 eax, eax
$LN1@Atomic_fet:

; 1590 : 		}
; 1591 : 	}

  0006b	83 c4 04	 add	 esp, 4
  0006e	3b ec		 cmp	 ebp, esp
  00070	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00075	8b e5		 mov	 esp, ebp
  00077	5d		 pop	 ebp
  00078	c3		 ret	 0
  00079	0f 1f 00	 npad	 3
$LN10@Atomic_fet:
  0007c	00 00 00 00	 DD	 $LN4@Atomic_fet
  00080	00 00 00 00	 DD	 $LN5@Atomic_fet
  00084	00 00 00 00	 DD	 $LN5@Atomic_fet
  00088	00 00 00 00	 DD	 $LN6@Atomic_fet
  0008c	00 00 00 00	 DD	 $LN7@Atomic_fet
  00090	00 00 00 00	 DD	 $LN7@Atomic_fet
?_Atomic_fetch_add_4@std@@YAKPCKKW4memory_order@1@@Z ENDP ; std::_Atomic_fetch_add_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Atomic_fetch_sub_4@std@@YAKPCKKW4memory_order@1@@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
__Order$ = 16						; size = 4
?_Atomic_fetch_sub_4@std@@YAKPCKKW4memory_order@1@@Z PROC ; std::_Atomic_fetch_sub_4, COMDAT

; 1595 : 	{	/* subtract _Value from *_Tgt atomically */

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1596 : 	return (_Atomic_fetch_add_4(_Tgt, 0 - _Value, _Order));

  00003	8b 45 10	 mov	 eax, DWORD PTR __Order$[ebp]
  00006	50		 push	 eax
  00007	33 c9		 xor	 ecx, ecx
  00009	2b 4d 0c	 sub	 ecx, DWORD PTR __Value$[ebp]
  0000c	51		 push	 ecx
  0000d	8b 55 08	 mov	 edx, DWORD PTR __Tgt$[ebp]
  00010	52		 push	 edx
  00011	e8 00 00 00 00	 call	 ?_Atomic_fetch_add_4@std@@YAKPCKKW4memory_order@1@@Z ; std::_Atomic_fetch_add_4
  00016	83 c4 0c	 add	 esp, 12			; 0000000cH

; 1597 : 	}

  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	5d		 pop	 ebp
  00021	c3		 ret	 0
?_Atomic_fetch_sub_4@std@@YAKPCKKW4memory_order@1@@Z ENDP ; std::_Atomic_fetch_sub_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z
_TEXT	SEGMENT
__$EHRec$ = -12						; size = 12
__Atom$ = 8						; size = 4
__Order$ = 12						; size = 4
?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z PROC ; std::atomic_load_explicit, COMDAT

; 494  : 	{	// return value held in *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp

; 495  : 	return (_ATOMIC_LOAD(_Atom, _Order));

  00018	8b 45 0c	 mov	 eax, DWORD PTR __Order$[ebp]
  0001b	50		 push	 eax
  0001c	8b 4d 08	 mov	 ecx, DWORD PTR __Atom$[ebp]
  0001f	51		 push	 ecx
  00020	e8 00 00 00 00	 call	 ?_Atomic_load_4@std@@YAKPCKW4memory_order@1@@Z ; std::_Atomic_load_4
  00025	83 c4 08	 add	 esp, 8

; 496  : 	}

  00028	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0002b	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00032	83 c4 0c	 add	 esp, 12			; 0000000cH
  00035	3b ec		 cmp	 ebp, esp
  00037	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003c	8b e5		 mov	 esp, ebp
  0003e	5d		 pop	 ebp
  0003f	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z ENDP ; std::atomic_load_explicit
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_load@std@@YAHPBU_Atomic_int@1@@Z
_TEXT	SEGMENT
__Atom$ = 8						; size = 4
?atomic_load@std@@YAHPBU_Atomic_int@1@@Z PROC		; std::atomic_load, COMDAT

; 504  : 	{	// return value stored in *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 505  : 	return (atomic_load_explicit(_Atom, memory_order_seq_cst));

  00003	6a 05		 push	 5
  00005	8b 45 08	 mov	 eax, DWORD PTR __Atom$[ebp]
  00008	50		 push	 eax
  00009	e8 00 00 00 00	 call	 ?atomic_load_explicit@std@@YAHPBU_Atomic_int@1@W4memory_order@1@@Z ; std::atomic_load_explicit
  0000e	83 c4 08	 add	 esp, 8

; 506  : 	}

  00011	3b ec		 cmp	 ebp, esp
  00013	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00018	5d		 pop	 ebp
  00019	c3		 ret	 0
?atomic_load@std@@YAHPBU_Atomic_int@1@@Z ENDP		; std::atomic_load
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ??B_Atomic_int@std@@QBEHXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??B_Atomic_int@std@@QBEHXZ PROC				; std::_Atomic_int::operator int, COMDAT
; _this$ = ecx

; 639  : 	{	// return value held in *this

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 640  : 	return (atomic_load(this));

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	50		 push	 eax
  00012	e8 00 00 00 00	 call	 ?atomic_load@std@@YAHPBU_Atomic_int@1@@Z ; std::atomic_load
  00017	83 c4 04	 add	 esp, 4

; 641  : 	}

  0001a	83 c4 04	 add	 esp, 4
  0001d	3b ec		 cmp	 ebp, esp
  0001f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00024	8b e5		 mov	 esp, ebp
  00026	5d		 pop	 ebp
  00027	c3		 ret	 0
??B_Atomic_int@std@@QBEHXZ ENDP				; std::_Atomic_int::operator int
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z
_TEXT	SEGMENT
__$EHRec$ = -12						; size = 12
__Atom$ = 8						; size = 4
__Value$ = 12						; size = 4
__Order$ = 16						; size = 4
?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z PROC ; std::atomic_fetch_add_explicit, COMDAT

; 909  : 	{	// add _Value to value stored in *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp

; 910  : 	return (_ATOMIC_FETCH_ADD(_Atom, _Value, _Order));

  00018	8b 45 10	 mov	 eax, DWORD PTR __Order$[ebp]
  0001b	50		 push	 eax
  0001c	8b 4d 0c	 mov	 ecx, DWORD PTR __Value$[ebp]
  0001f	51		 push	 ecx
  00020	8b 55 08	 mov	 edx, DWORD PTR __Atom$[ebp]
  00023	52		 push	 edx
  00024	e8 00 00 00 00	 call	 ?_Atomic_fetch_add_4@std@@YAKPCKKW4memory_order@1@@Z ; std::_Atomic_fetch_add_4
  00029	83 c4 0c	 add	 esp, 12			; 0000000cH

; 911  : 	}

  0002c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0002f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00036	83 c4 0c	 add	 esp, 12			; 0000000cH
  00039	3b ec		 cmp	 ebp, esp
  0003b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00040	8b e5		 mov	 esp, ebp
  00042	5d		 pop	 ebp
  00043	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z ENDP ; std::atomic_fetch_add_explicit
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_fetch_add@std@@YAHPAU_Atomic_int@1@H@Z
_TEXT	SEGMENT
__Atom$ = 8						; size = 4
__Value$ = 12						; size = 4
?atomic_fetch_add@std@@YAHPAU_Atomic_int@1@H@Z PROC	; std::atomic_fetch_add, COMDAT

; 919  : 	{	// add _Value to value stored in *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 920  : 	return (atomic_fetch_add_explicit(_Atom, _Value, memory_order_seq_cst));

  00003	6a 05		 push	 5
  00005	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00008	50		 push	 eax
  00009	8b 4d 08	 mov	 ecx, DWORD PTR __Atom$[ebp]
  0000c	51		 push	 ecx
  0000d	e8 00 00 00 00	 call	 ?atomic_fetch_add_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z ; std::atomic_fetch_add_explicit
  00012	83 c4 0c	 add	 esp, 12			; 0000000cH

; 921  : 	}

  00015	3b ec		 cmp	 ebp, esp
  00017	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001c	5d		 pop	 ebp
  0001d	c3		 ret	 0
?atomic_fetch_add@std@@YAHPAU_Atomic_int@1@H@Z ENDP	; std::atomic_fetch_add
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z
_TEXT	SEGMENT
__$EHRec$ = -12						; size = 12
__Atom$ = 8						; size = 4
__Value$ = 12						; size = 4
__Order$ = 16						; size = 4
?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z PROC ; std::atomic_fetch_sub_explicit, COMDAT

; 930  : 	{	// subtract _Value from value stored in *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp

; 931  : 	return (_ATOMIC_FETCH_SUB(_Atom, _Value, _Order));

  00018	8b 45 10	 mov	 eax, DWORD PTR __Order$[ebp]
  0001b	50		 push	 eax
  0001c	8b 4d 0c	 mov	 ecx, DWORD PTR __Value$[ebp]
  0001f	51		 push	 ecx
  00020	8b 55 08	 mov	 edx, DWORD PTR __Atom$[ebp]
  00023	52		 push	 edx
  00024	e8 00 00 00 00	 call	 ?_Atomic_fetch_sub_4@std@@YAKPCKKW4memory_order@1@@Z ; std::_Atomic_fetch_sub_4
  00029	83 c4 0c	 add	 esp, 12			; 0000000cH

; 932  : 	}

  0002c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0002f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00036	83 c4 0c	 add	 esp, 12			; 0000000cH
  00039	3b ec		 cmp	 ebp, esp
  0003b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00040	8b e5		 mov	 esp, ebp
  00042	5d		 pop	 ebp
  00043	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z ENDP ; std::atomic_fetch_sub_explicit
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_fetch_sub@std@@YAHPAU_Atomic_int@1@H@Z
_TEXT	SEGMENT
__Atom$ = 8						; size = 4
__Value$ = 12						; size = 4
?atomic_fetch_sub@std@@YAHPAU_Atomic_int@1@H@Z PROC	; std::atomic_fetch_sub, COMDAT

; 940  : 	{	// subtract _Value from value stored in *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 941  : 	return (atomic_fetch_sub_explicit(_Atom, _Value, memory_order_seq_cst));

  00003	6a 05		 push	 5
  00005	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00008	50		 push	 eax
  00009	8b 4d 08	 mov	 ecx, DWORD PTR __Atom$[ebp]
  0000c	51		 push	 ecx
  0000d	e8 00 00 00 00	 call	 ?atomic_fetch_sub_explicit@std@@YAHPAU_Atomic_int@1@HW4memory_order@1@@Z ; std::atomic_fetch_sub_explicit
  00012	83 c4 0c	 add	 esp, 12			; 0000000cH

; 942  : 	}

  00015	3b ec		 cmp	 ebp, esp
  00017	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001c	5d		 pop	 ebp
  0001d	c3		 ret	 0
?atomic_fetch_sub@std@@YAHPAU_Atomic_int@1@H@Z ENDP	; std::atomic_fetch_sub
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ??E_Atomic_int@std@@QAEHH@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 4
??E_Atomic_int@std@@QAEHH@Z PROC			; std::_Atomic_int::operator++, COMDAT
; _this$ = ecx

; 1014 : 	{	// increment value stored in *this

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1015 : 	return (atomic_fetch_add(this, 1));

  0000e	6a 01		 push	 1
  00010	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00013	50		 push	 eax
  00014	e8 00 00 00 00	 call	 ?atomic_fetch_add@std@@YAHPAU_Atomic_int@1@H@Z ; std::atomic_fetch_add
  00019	83 c4 08	 add	 esp, 8

; 1016 : 	}

  0001c	83 c4 04	 add	 esp, 4
  0001f	3b ec		 cmp	 ebp, esp
  00021	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00026	8b e5		 mov	 esp, ebp
  00028	5d		 pop	 ebp
  00029	c2 04 00	 ret	 4
??E_Atomic_int@std@@QAEHH@Z ENDP			; std::_Atomic_int::operator++
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ??F_Atomic_int@std@@QAEHH@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 4
??F_Atomic_int@std@@QAEHH@Z PROC			; std::_Atomic_int::operator--, COMDAT
; _this$ = ecx

; 1024 : 	{	// decrement value stored in *this

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1025 : 	return (atomic_fetch_sub(this, 1));

  0000e	6a 01		 push	 1
  00010	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00013	50		 push	 eax
  00014	e8 00 00 00 00	 call	 ?atomic_fetch_sub@std@@YAHPAU_Atomic_int@1@H@Z ; std::atomic_fetch_sub
  00019	83 c4 08	 add	 esp, 8

; 1026 : 	}

  0001c	83 c4 04	 add	 esp, 4
  0001f	3b ec		 cmp	 ebp, esp
  00021	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00026	8b e5		 mov	 esp, ebp
  00028	5d		 pop	 ebp
  00029	c2 04 00	 ret	 4
??F_Atomic_int@std@@QAEHH@Z ENDP			; std::_Atomic_int::operator--
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
_TEXT	SEGMENT
$T2 = -56						; size = 8
_dur$ = -44						; size = 8
_now$ = -28						; size = 8
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_fn$ = 8						; size = 4
_delayMs$ = 12						; size = 4
??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z PROC ; CMessageLoop2::Task::Task
; _this$ = ecx

; 6    : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 2c	 sub	 esp, 44			; 0000002cH
  0001b	57		 push	 edi
  0001c	51		 push	 ecx
  0001d	8d 7d c8	 lea	 edi, DWORD PTR [ebp-56]
  00020	b9 0b 00 00 00	 mov	 ecx, 11			; 0000000bH
  00025	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002a	f3 ab		 rep stosd
  0002c	59		 pop	 ecx
  0002d	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00030	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00033	e8 00 00 00 00	 call	 ??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@XZ ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >

; 5    : CMessageLoop2::Task::Task(const std::function<void()>& fn, int delayMs) :fn_(fn)

  00038	8b 45 08	 mov	 eax, DWORD PTR _fn$[ebp]
  0003b	50		 push	 eax
  0003c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003f	83 c1 08	 add	 ecx, 8
  00042	e8 00 00 00 00	 call	 ??0?$function@$$A6AXXZ@std@@QAE@ABV01@@Z ; std::function<void __cdecl(void)>::function<void __cdecl(void)>
  00047	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 7    : 	using namespace std;
; 8    : 	auto now = std::chrono::steady_clock::now();

  0004e	8d 4d e4	 lea	 ecx, DWORD PTR _now$[ebp]
  00051	51		 push	 ecx
  00052	e8 00 00 00 00	 call	 ?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ ; std::chrono::steady_clock::now
  00057	83 c4 04	 add	 esp, 4

; 9    : 	auto dur = std::chrono::milliseconds(delayMs);

  0005a	8d 55 0c	 lea	 edx, DWORD PTR _delayMs$[ebp]
  0005d	52		 push	 edx
  0005e	8d 4d d4	 lea	 ecx, DWORD PTR _dur$[ebp]
  00061	e8 00 00 00 00	 call	 ??$?0HX@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QAE@ABH@Z ; std::chrono::duration<__int64,std::ratio<1,1000> >::duration<__int64,std::ratio<1,1000> ><int,void>

; 10   : 	endtime = now.operator+=(dur);

  00066	8d 45 d4	 lea	 eax, DWORD PTR _dur$[ebp]
  00069	50		 push	 eax
  0006a	8d 4d c8	 lea	 ecx, DWORD PTR $T2[ebp]
  0006d	e8 00 00 00 00	 call	 ??$?0_JU?$ratio@$00$0DOI@@std@@X@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@12@@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,std::ratio<1,1000>,void>
  00072	8d 4d c8	 lea	 ecx, DWORD PTR $T2[ebp]
  00075	51		 push	 ecx
  00076	8d 4d e4	 lea	 ecx, DWORD PTR _now$[ebp]
  00079	e8 00 00 00 00	 call	 ??Y?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAEAAV012@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::operator+=
  0007e	8b 10		 mov	 edx, DWORD PTR [eax]
  00080	8b 40 04	 mov	 eax, DWORD PTR [eax+4]
  00083	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00086	89 11		 mov	 DWORD PTR [ecx], edx
  00088	89 41 04	 mov	 DWORD PTR [ecx+4], eax

; 11   : }

  0008b	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00092	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00095	52		 push	 edx
  00096	8b cd		 mov	 ecx, ebp
  00098	50		 push	 eax
  00099	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN8@Task
  0009f	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000a4	58		 pop	 eax
  000a5	5a		 pop	 edx
  000a6	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000a9	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000b0	5f		 pop	 edi
  000b1	83 c4 38	 add	 esp, 56			; 00000038H
  000b4	3b ec		 cmp	 ebp, esp
  000b6	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000bb	8b e5		 mov	 esp, ebp
  000bd	5d		 pop	 ebp
  000be	c2 08 00	 ret	 8
  000c1	0f 1f 00	 npad	 3
$LN8@Task:
  000c4	02 00 00 00	 DD	 2
  000c8	00 00 00 00	 DD	 $LN7@Task
$LN7@Task:
  000cc	e4 ff ff ff	 DD	 -28			; ffffffe4H
  000d0	08 00 00 00	 DD	 8
  000d4	00 00 00 00	 DD	 $LN4@Task
  000d8	d4 ff ff ff	 DD	 -44			; ffffffd4H
  000dc	08 00 00 00	 DD	 8
  000e0	00 00 00 00	 DD	 $LN5@Task
$LN5@Task:
  000e4	64		 DB	 100			; 00000064H
  000e5	75		 DB	 117			; 00000075H
  000e6	72		 DB	 114			; 00000072H
  000e7	00		 DB	 0
$LN4@Task:
  000e8	6e		 DB	 110			; 0000006eH
  000e9	6f		 DB	 111			; 0000006fH
  000ea	77		 DB	 119			; 00000077H
  000eb	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 08	 add	 ecx, 8
  00006	e9 00 00 00 00	 jmp	 ??1?$function@$$A6AXXZ@std@@QAE@XZ
__ehhandler$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z:
  0000b	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z
  00010	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z ENDP ; CMessageLoop2::Task::Task
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
;	COMDAT ??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@XZ PROC ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >, COMDAT
; _this$ = ecx

; 11   : }

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono

; 249  : 	_Duration _MyDur{duration::zero()};	// duration since the epoch

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	50		 push	 eax
  00012	e8 00 00 00 00	 call	 ?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::zero
  00017	83 c4 04	 add	 esp, 4
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp

; 11   : }

  0001a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001d	83 c4 04	 add	 esp, 4
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	8b e5		 mov	 esp, ebp
  00029	5d		 pop	 ebp
  0002a	c3		 ret	 0
??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@XZ ENDP ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
_TEXT	SEGMENT
$T2 = -154						; size = 1
$T3 = -153						; size = 1
$T4 = -152						; size = 4
$T5 = -148						; size = 4
$T6 = -144						; size = 4
$T7 = -140						; size = 4
_task$8 = -132						; size = 48
_now$9 = -76						; size = 8
_endtime$10 = -60					; size = 8
_it$11 = -44						; size = 4
_lock$ = -32						; size = 8
_this$ = -20						; size = 4
__$EHRec$ = -16						; size = 16
?doTasks@CMessageLoop2@@AAE_NXZ PROC			; CMessageLoop2::doTasks
; _this$ = ecx

; 13   : bool CMessageLoop2::doTasks() {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?doTasks@CMessageLoop2@@AAE_NXZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	81 ec 8c 00 00
	00		 sub	 esp, 140		; 0000008cH
  0001f	53		 push	 ebx
  00020	56		 push	 esi
  00021	57		 push	 edi
  00022	51		 push	 ecx
  00023	8d bd 64 ff ff
	ff		 lea	 edi, DWORD PTR [ebp-156]
  00029	b9 23 00 00 00	 mov	 ecx, 35			; 00000023H
  0002e	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00033	f3 ab		 rep stosd
  00035	59		 pop	 ecx
  00036	89 65 f0	 mov	 DWORD PTR __$EHRec$[ebp], esp
  00039	89 4d ec	 mov	 DWORD PTR _this$[ebp], ecx

; 14   : 	using namespace std;
; 15   : 	unique_lock<mutex> lock(m_mutex_tasks);

  0003c	8b 45 ec	 mov	 eax, DWORD PTR _this$[ebp]
  0003f	83 c0 08	 add	 eax, 8
  00042	50		 push	 eax
  00043	8d 4d e0	 lea	 ecx, DWORD PTR _lock$[ebp]
  00046	e8 00 00 00 00	 call	 ??0?$unique_lock@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::unique_lock<std::mutex>::unique_lock<std::mutex>
  0004b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+12], 0

; 16   : 	if(!m_tasks.empty())	

  00052	8b 4d ec	 mov	 ecx, DWORD PTR _this$[ebp]
  00055	e8 00 00 00 00	 call	 ?empty@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE_NXZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::empty
  0005a	0f b6 c8	 movzx	 ecx, al
  0005d	85 c9		 test	 ecx, ecx
  0005f	0f 85 f7 00 00
	00		 jne	 $LN5@doTasks

; 17   : 	{
; 18   : 		for (auto it=m_tasks.begin();it!=m_tasks.end();)

  00065	8d 55 d4	 lea	 edx, DWORD PTR _it$11[ebp]
  00068	52		 push	 edx
  00069	8b 4d ec	 mov	 ecx, DWORD PTR _this$[ebp]
  0006c	e8 00 00 00 00	 call	 ?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::begin
$LN4@doTasks:
  00071	8d 85 74 ff ff
	ff		 lea	 eax, DWORD PTR $T7[ebp]
  00077	50		 push	 eax
  00078	8b 4d ec	 mov	 ecx, DWORD PTR _this$[ebp]
  0007b	e8 00 00 00 00	 call	 ?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::end
  00080	50		 push	 eax
  00081	8d 4d d4	 lea	 ecx, DWORD PTR _it$11[ebp]
  00084	e8 00 00 00 00	 call	 ??9?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator!=
  00089	0f b6 c8	 movzx	 ecx, al
  0008c	85 c9		 test	 ecx, ecx
  0008e	0f 84 c6 00 00
	00		 je	 $LN3@doTasks

; 19   : 		{
; 20   : 			auto endtime = it->endtime;

  00094	8d 4d d4	 lea	 ecx, DWORD PTR _it$11[ebp]
  00097	e8 00 00 00 00	 call	 ??C?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEPAUTask@CMessageLoop2@@XZ ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator->
  0009c	8b 10		 mov	 edx, DWORD PTR [eax]
  0009e	8b 40 04	 mov	 eax, DWORD PTR [eax+4]
  000a1	89 55 c4	 mov	 DWORD PTR _endtime$10[ebp], edx
  000a4	89 45 c8	 mov	 DWORD PTR _endtime$10[ebp+4], eax

; 21   : 			auto now = std::chrono::steady_clock::now();

  000a7	8d 4d b4	 lea	 ecx, DWORD PTR _now$9[ebp]
  000aa	51		 push	 ecx
  000ab	e8 00 00 00 00	 call	 ?now@steady_clock@chrono@std@@SA?AV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@23@XZ ; std::chrono::steady_clock::now
  000b0	83 c4 04	 add	 esp, 4

; 22   : 			if (endtime <= now)

  000b3	8d 55 b4	 lea	 edx, DWORD PTR _now$9[ebp]
  000b6	52		 push	 edx
  000b7	8d 45 c4	 lea	 eax, DWORD PTR _endtime$10[ebp]
  000ba	50		 push	 eax
  000bb	e8 00 00 00 00	 call	 ??$?NUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z ; std::chrono::operator<=<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
  000c0	83 c4 08	 add	 esp, 8
  000c3	0f b6 c8	 movzx	 ecx, al
  000c6	85 c9		 test	 ecx, ecx
  000c8	74 7a		 je	 SHORT $LN7@doTasks

; 23   : 			{
; 24   : 				auto task(std::move(*it));	

  000ca	8d 4d d4	 lea	 ecx, DWORD PTR _it$11[ebp]
  000cd	e8 00 00 00 00	 call	 ??D?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEAAUTask@CMessageLoop2@@XZ ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*
  000d2	50		 push	 eax
  000d3	e8 00 00 00 00	 call	 ??$move@AAUTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::move<CMessageLoop2::Task &>
  000d8	83 c4 04	 add	 esp, 4
  000db	50		 push	 eax
  000dc	8d 8d 7c ff ff
	ff		 lea	 ecx, DWORD PTR _task$8[ebp]
  000e2	e8 00 00 00 00	 call	 ??0Task@CMessageLoop2@@QAE@$$QAU01@@Z
  000e7	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+12], 1

; 25   : 				it=m_tasks.erase(it);

  000eb	8b 55 d4	 mov	 edx, DWORD PTR _it$11[ebp]
  000ee	89 95 70 ff ff
	ff		 mov	 DWORD PTR $T6[ebp], edx
  000f4	8b 85 70 ff ff
	ff		 mov	 eax, DWORD PTR $T6[ebp]
  000fa	50		 push	 eax
  000fb	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  00101	51		 push	 ecx
  00102	8b 4d ec	 mov	 ecx, DWORD PTR _this$[ebp]
  00105	e8 00 00 00 00	 call	 ?erase@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::erase
  0010a	8b 10		 mov	 edx, DWORD PTR [eax]
  0010c	89 55 d4	 mov	 DWORD PTR _it$11[ebp], edx

; 26   : 				try {

  0010f	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+12], 2

; 27   : 					task.fn_();

  00113	8d 4d 84	 lea	 ecx, DWORD PTR _task$8[ebp+8]
  00116	e8 00 00 00 00	 call	 ??R?$_Func_class@X$$V@std@@QBEXXZ ; std::_Func_class<void>::operator()
  0011b	eb 06		 jmp	 SHORT $LN11@doTasks
__catch$?doTasks@CMessageLoop2@@AAE_NXZ$0:

; 28   : 				}catch (...) {
; 29   : 				}

  0011d	b8 00 00 00 00	 mov	 eax, $LN16@doTasks
  00122	c3		 ret	 0
$LN11@doTasks:
  00123	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+12], 1
  0012a	eb 07		 jmp	 SHORT __tryend$?doTasks@CMessageLoop2@@AAE_NXZ$1
$LN16@doTasks:
  0012c	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+12], 1
__tryend$?doTasks@CMessageLoop2@@AAE_NXZ$1:

; 30   : 				//return true;
; 31   : 			}else{

  00133	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+12], 0
  00137	8d 8d 7c ff ff
	ff		 lea	 ecx, DWORD PTR _task$8[ebp]
  0013d	e8 00 00 00 00	 call	 ??1Task@CMessageLoop2@@QAE@XZ
  00142	eb 11		 jmp	 SHORT $LN8@doTasks
$LN7@doTasks:

; 32   : 				it++;

  00144	6a 00		 push	 0
  00146	8d 85 68 ff ff
	ff		 lea	 eax, DWORD PTR $T4[ebp]
  0014c	50		 push	 eax
  0014d	8d 4d d4	 lea	 ecx, DWORD PTR _it$11[ebp]
  00150	e8 00 00 00 00	 call	 ??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
$LN8@doTasks:

; 33   : 			}
; 34   : 		}

  00155	e9 17 ff ff ff	 jmp	 $LN4@doTasks
$LN3@doTasks:

; 35   : 	}else {

  0015a	eb 1e		 jmp	 SHORT $LN6@doTasks
$LN5@doTasks:

; 36   : 		return false;

  0015c	c6 85 67 ff ff
	ff 00		 mov	 BYTE PTR $T3[ebp], 0
  00163	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+12], -1
  0016a	8d 4d e0	 lea	 ecx, DWORD PTR _lock$[ebp]
  0016d	e8 00 00 00 00	 call	 ??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ ; std::unique_lock<std::mutex>::~unique_lock<std::mutex>
  00172	8a 85 67 ff ff
	ff		 mov	 al, BYTE PTR $T3[ebp]
  00178	eb 1c		 jmp	 SHORT $LN1@doTasks
$LN6@doTasks:

; 37   : 	}
; 38   : 	return false;

  0017a	c6 85 66 ff ff
	ff 00		 mov	 BYTE PTR $T2[ebp], 0
  00181	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+12], -1
  00188	8d 4d e0	 lea	 ecx, DWORD PTR _lock$[ebp]
  0018b	e8 00 00 00 00	 call	 ??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ ; std::unique_lock<std::mutex>::~unique_lock<std::mutex>
  00190	8a 85 66 ff ff
	ff		 mov	 al, BYTE PTR $T2[ebp]
$LN1@doTasks:

; 39   : }

  00196	52		 push	 edx
  00197	8b cd		 mov	 ecx, ebp
  00199	50		 push	 eax
  0019a	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN24@doTasks
  001a0	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  001a5	58		 pop	 eax
  001a6	5a		 pop	 edx
  001a7	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp+4]
  001aa	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  001b1	5f		 pop	 edi
  001b2	5e		 pop	 esi
  001b3	5b		 pop	 ebx
  001b4	81 c4 9c 00 00
	00		 add	 esp, 156		; 0000009cH
  001ba	3b ec		 cmp	 ebp, esp
  001bc	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001c1	8b e5		 mov	 esp, ebp
  001c3	5d		 pop	 ebp
  001c4	c3		 ret	 0
  001c5	0f 1f 00	 npad	 3
$LN24@doTasks:
  001c8	05 00 00 00	 DD	 5
  001cc	00 00 00 00	 DD	 $LN23@doTasks
$LN23@doTasks:
  001d0	e0 ff ff ff	 DD	 -32			; ffffffe0H
  001d4	08 00 00 00	 DD	 8
  001d8	00 00 00 00	 DD	 $LN17@doTasks
  001dc	d4 ff ff ff	 DD	 -44			; ffffffd4H
  001e0	04 00 00 00	 DD	 4
  001e4	00 00 00 00	 DD	 $LN18@doTasks
  001e8	c4 ff ff ff	 DD	 -60			; ffffffc4H
  001ec	08 00 00 00	 DD	 8
  001f0	00 00 00 00	 DD	 $LN19@doTasks
  001f4	b4 ff ff ff	 DD	 -76			; ffffffb4H
  001f8	08 00 00 00	 DD	 8
  001fc	00 00 00 00	 DD	 $LN20@doTasks
  00200	7c ff ff ff	 DD	 -132			; ffffff7cH
  00204	30 00 00 00	 DD	 48			; 00000030H
  00208	00 00 00 00	 DD	 $LN21@doTasks
$LN21@doTasks:
  0020c	74		 DB	 116			; 00000074H
  0020d	61		 DB	 97			; 00000061H
  0020e	73		 DB	 115			; 00000073H
  0020f	6b		 DB	 107			; 0000006bH
  00210	00		 DB	 0
$LN20@doTasks:
  00211	6e		 DB	 110			; 0000006eH
  00212	6f		 DB	 111			; 0000006fH
  00213	77		 DB	 119			; 00000077H
  00214	00		 DB	 0
$LN19@doTasks:
  00215	65		 DB	 101			; 00000065H
  00216	6e		 DB	 110			; 0000006eH
  00217	64		 DB	 100			; 00000064H
  00218	74		 DB	 116			; 00000074H
  00219	69		 DB	 105			; 00000069H
  0021a	6d		 DB	 109			; 0000006dH
  0021b	65		 DB	 101			; 00000065H
  0021c	00		 DB	 0
$LN18@doTasks:
  0021d	69		 DB	 105			; 00000069H
  0021e	74		 DB	 116			; 00000074H
  0021f	00		 DB	 0
$LN17@doTasks:
  00220	6c		 DB	 108			; 0000006cH
  00221	6f		 DB	 111			; 0000006fH
  00222	63		 DB	 99			; 00000063H
  00223	6b		 DB	 107			; 0000006bH
  00224	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?doTasks@CMessageLoop2@@AAE_NXZ$2:
  00000	8d 4d e0	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ ; std::unique_lock<std::mutex>::~unique_lock<std::mutex>
__unwindfunclet$?doTasks@CMessageLoop2@@AAE_NXZ$3:
  00008	8d 8d 7c ff ff
	ff		 lea	 ecx, DWORD PTR _task$8[ebp]
  0000e	e9 00 00 00 00	 jmp	 ??1Task@CMessageLoop2@@QAE@XZ
__ehhandler$?doTasks@CMessageLoop2@@AAE_NXZ:
  00013	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?doTasks@CMessageLoop2@@AAE_NXZ
  00018	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?doTasks@CMessageLoop2@@AAE_NXZ ENDP			; CMessageLoop2::doTasks
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??0Task@CMessageLoop2@@QAE@$$QAU01@@Z
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___that$ = 8						; size = 4
??0Task@CMessageLoop2@@QAE@$$QAU01@@Z PROC		; CMessageLoop2::Task::Task, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 45 08	 mov	 eax, DWORD PTR ___that$[ebp]
  00026	8b 08		 mov	 ecx, DWORD PTR [eax]
  00028	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  0002b	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0002e	89 08		 mov	 DWORD PTR [eax], ecx
  00030	89 50 04	 mov	 DWORD PTR [eax+4], edx
  00033	8b 4d 08	 mov	 ecx, DWORD PTR ___that$[ebp]
  00036	83 c1 08	 add	 ecx, 8
  00039	51		 push	 ecx
  0003a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003d	83 c1 08	 add	 ecx, 8
  00040	e8 00 00 00 00	 call	 ??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z ; std::function<void __cdecl(void)>::function<void __cdecl(void)>
  00045	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0004c	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00053	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00056	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00059	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00060	83 c4 10	 add	 esp, 16			; 00000010H
  00063	3b ec		 cmp	 ebp, esp
  00065	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0006a	8b e5		 mov	 esp, ebp
  0006c	5d		 pop	 ebp
  0006d	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 08	 add	 ecx, 8
  00006	e9 00 00 00 00	 jmp	 ??1?$function@$$A6AXXZ@std@@QAE@XZ
__ehhandler$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z:
  0000b	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0Task@CMessageLoop2@@QAE@$$QAU01@@Z
  00010	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0Task@CMessageLoop2@@QAE@$$QAU01@@Z ENDP		; CMessageLoop2::Task::Task
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1Task@CMessageLoop2@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1Task@CMessageLoop2@@QAE@XZ PROC			; CMessageLoop2::Task::~Task, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1Task@CMessageLoop2@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0002a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	83 c1 08	 add	 ecx, 8
  00037	e8 00 00 00 00	 call	 ??1?$function@$$A6AXXZ@std@@QAE@XZ
  0003c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00046	83 c4 10	 add	 esp, 16			; 00000010H
  00049	3b ec		 cmp	 ebp, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00050	8b e5		 mov	 esp, ebp
  00052	5d		 pop	 ebp
  00053	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1Task@CMessageLoop2@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 08	 add	 ecx, 8
  00006	e9 00 00 00 00	 jmp	 ??1?$function@$$A6AXXZ@std@@QAE@XZ
__ehhandler$??1Task@CMessageLoop2@@QAE@XZ:
  0000b	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1Task@CMessageLoop2@@QAE@XZ
  00010	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1Task@CMessageLoop2@@QAE@XZ ENDP			; CMessageLoop2::Task::~Task
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?AddRef@CMessageLoop2@@QAEXXZ PROC			; CMessageLoop2::AddRef
; _this$ = ecx

; 41   : void CMessageLoop2::AddRef() {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 42   : 	m_refCount++;

  0000e	6a 00		 push	 0
  00010	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00013	83 c1 3c	 add	 ecx, 60			; 0000003cH
  00016	e8 00 00 00 00	 call	 ??E_Atomic_int@std@@QAEHH@Z ; std::_Atomic_int::operator++

; 43   : }

  0001b	83 c4 04	 add	 esp, 4
  0001e	3b ec		 cmp	 ebp, esp
  00020	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00025	8b e5		 mov	 esp, ebp
  00027	5d		 pop	 ebp
  00028	c3		 ret	 0
?AddRef@CMessageLoop2@@QAEXXZ ENDP			; CMessageLoop2::AddRef
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?Release@CMessageLoop2@@QAEXXZ PROC			; CMessageLoop2::Release
; _this$ = ecx

; 45   : void CMessageLoop2::Release() {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 46   : 	m_refCount--;

  0000f	6a 00		 push	 0
  00011	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00014	83 c1 3c	 add	 ecx, 60			; 0000003cH
  00017	e8 00 00 00 00	 call	 ??F_Atomic_int@std@@QAEHH@Z ; std::_Atomic_int::operator--

; 47   : 	if (m_refCount <= 0) {		

  0001c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001f	83 c1 3c	 add	 ecx, 60			; 0000003cH
  00022	e8 00 00 00 00	 call	 ??B_Atomic_int@std@@QBEHXZ ; std::_Atomic_int::operator int
  00027	85 c0		 test	 eax, eax
  00029	7f 11		 jg	 SHORT $LN1@Release

; 48   : 		PostQuitMessage(1);		

  0002b	8b f4		 mov	 esi, esp
  0002d	6a 01		 push	 1
  0002f	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__PostQuitMessage@4
  00035	3b f4		 cmp	 esi, esp
  00037	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@Release:

; 49   : 	}
; 50   : }

  0003c	5e		 pop	 esi
  0003d	83 c4 04	 add	 esp, 4
  00040	3b ec		 cmp	 ebp, esp
  00042	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00047	8b e5		 mov	 esp, ebp
  00049	5d		 pop	 ebp
  0004a	c3		 ret	 0
?Release@CMessageLoop2@@QAEXXZ ENDP			; CMessageLoop2::Release
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
_TEXT	SEGMENT
$T2 = -85						; size = 1
_lock$3 = -80						; size = 4
_task$ = -68						; size = 48
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_delayMs$ = 8						; size = 4
_fn$ = 12						; size = 4
?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z PROC ; CMessageLoop2::postDelayedTask
; _this$ = ecx

; 53   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 4c	 sub	 esp, 76			; 0000004cH
  0001b	57		 push	 edi
  0001c	51		 push	 ecx
  0001d	8d 7d a8	 lea	 edi, DWORD PTR [ebp-88]
  00020	b9 13 00 00 00	 mov	 ecx, 19			; 00000013H
  00025	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002a	f3 ab		 rep stosd
  0002c	59		 pop	 ecx
  0002d	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 54   : 	using namespace std;
; 55   : 	Task task(fn,delayMs);

  00030	8b 45 08	 mov	 eax, DWORD PTR _delayMs$[ebp]
  00033	50		 push	 eax
  00034	8b 4d 0c	 mov	 ecx, DWORD PTR _fn$[ebp]
  00037	51		 push	 ecx
  00038	8d 4d bc	 lea	 ecx, DWORD PTR _task$[ebp]
  0003b	e8 00 00 00 00	 call	 ??0Task@CMessageLoop2@@QAE@ABV?$function@$$A6AXXZ@std@@H@Z ; CMessageLoop2::Task::Task
  00040	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 56   : 	{
; 57   : 		lock_guard<mutex> lock(m_mutex_tasks);

  00047	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0004a	83 c2 08	 add	 edx, 8
  0004d	52		 push	 edx
  0004e	8d 4d b0	 lea	 ecx, DWORD PTR _lock$3[ebp]
  00051	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00056	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1

; 58   : 		m_tasks.push_back(std::move(task));

  0005a	8d 45 bc	 lea	 eax, DWORD PTR _task$[ebp]
  0005d	50		 push	 eax
  0005e	e8 00 00 00 00	 call	 ??$move@AAUTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::move<CMessageLoop2::Task &>
  00063	83 c4 04	 add	 esp, 4
  00066	50		 push	 eax
  00067	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0006a	e8 00 00 00 00	 call	 ?push_back@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEX$$QAUTask@CMessageLoop2@@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::push_back

; 59   : 	}

  0006f	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00073	8d 4d b0	 lea	 ecx, DWORD PTR _lock$3[ebp]
  00076	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>

; 60   : 	return true;

  0007b	c6 45 ab 01	 mov	 BYTE PTR $T2[ebp], 1
  0007f	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00086	8d 4d bc	 lea	 ecx, DWORD PTR _task$[ebp]
  00089	e8 00 00 00 00	 call	 ??1Task@CMessageLoop2@@QAE@XZ
  0008e	8a 45 ab	 mov	 al, BYTE PTR $T2[ebp]

; 61   : }

  00091	52		 push	 edx
  00092	8b cd		 mov	 ecx, ebp
  00094	50		 push	 eax
  00095	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN9@postDelaye
  0009b	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000a0	58		 pop	 eax
  000a1	5a		 pop	 edx
  000a2	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000a5	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000ac	5f		 pop	 edi
  000ad	83 c4 58	 add	 esp, 88			; 00000058H
  000b0	3b ec		 cmp	 ebp, esp
  000b2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b7	8b e5		 mov	 esp, ebp
  000b9	5d		 pop	 ebp
  000ba	c2 08 00	 ret	 8
  000bd	0f 1f 00	 npad	 3
$LN9@postDelaye:
  000c0	02 00 00 00	 DD	 2
  000c4	00 00 00 00	 DD	 $LN8@postDelaye
$LN8@postDelaye:
  000c8	bc ff ff ff	 DD	 -68			; ffffffbcH
  000cc	30 00 00 00	 DD	 48			; 00000030H
  000d0	00 00 00 00	 DD	 $LN5@postDelaye
  000d4	b0 ff ff ff	 DD	 -80			; ffffffb0H
  000d8	04 00 00 00	 DD	 4
  000dc	00 00 00 00	 DD	 $LN6@postDelaye
$LN6@postDelaye:
  000e0	6c		 DB	 108			; 0000006cH
  000e1	6f		 DB	 111			; 0000006fH
  000e2	63		 DB	 99			; 00000063H
  000e3	6b		 DB	 107			; 0000006bH
  000e4	00		 DB	 0
$LN5@postDelaye:
  000e5	74		 DB	 116			; 00000074H
  000e6	61		 DB	 97			; 00000061H
  000e7	73		 DB	 115			; 00000073H
  000e8	6b		 DB	 107			; 0000006bH
  000e9	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z$0:
  00000	8d 4d bc	 lea	 ecx, DWORD PTR _task$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1Task@CMessageLoop2@@QAE@XZ
__unwindfunclet$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z$1:
  00008	8d 4d b0	 lea	 ecx, DWORD PTR _lock$3[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__ehhandler$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z:
  00010	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z
  00015	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?postDelayedTask@CMessageLoop2@@QAE_NHABV?$function@$$A6AXXZ@std@@@Z ENDP ; CMessageLoop2::postDelayedTask
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
_TEXT	SEGMENT
_bRet$ = -56						; size = 4
_msg$ = -48						; size = 28
_handles$ = -12						; size = 4
_this$ = -4						; size = 4
?Run@CMessageLoop2@@QAEIXZ PROC				; CMessageLoop2::Run
; _this$ = ecx

; 66   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 38	 sub	 esp, 56			; 00000038H
  00006	56		 push	 esi
  00007	57		 push	 edi
  00008	51		 push	 ecx
  00009	8d 7d c8	 lea	 edi, DWORD PTR [ebp-56]
  0000c	b9 0e 00 00 00	 mov	 ecx, 14			; 0000000eH
  00011	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00016	f3 ab		 rep stosd
  00018	59		 pop	 ecx
  00019	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 67   : 	using namespace std;
; 68   : 	HANDLE handles[] = {GetCurrentProcess()};

  0001c	8b f4		 mov	 esi, esp
  0001e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetCurrentProcess@0
  00024	3b f4		 cmp	 esi, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	89 45 f4	 mov	 DWORD PTR _handles$[ebp], eax

; 69   : 	MSG msg = { 0 }; BOOL bRet = FALSE;

  0002e	33 c0		 xor	 eax, eax
  00030	89 45 d0	 mov	 DWORD PTR _msg$[ebp], eax
  00033	89 45 d4	 mov	 DWORD PTR _msg$[ebp+4], eax
  00036	89 45 d8	 mov	 DWORD PTR _msg$[ebp+8], eax
  00039	89 45 dc	 mov	 DWORD PTR _msg$[ebp+12], eax
  0003c	89 45 e0	 mov	 DWORD PTR _msg$[ebp+16], eax
  0003f	89 45 e4	 mov	 DWORD PTR _msg$[ebp+20], eax
  00042	89 45 e8	 mov	 DWORD PTR _msg$[ebp+24], eax
  00045	c7 45 c8 00 00
	00 00		 mov	 DWORD PTR _bRet$[ebp], 0
$LN5@Run:

; 70   : 	for (;;) {
; 71   : 		while (!PeekMessage(&msg, nullptr, 0, 0, PM_NOREMOVE))

  0004c	8b f4		 mov	 esi, esp
  0004e	6a 00		 push	 0
  00050	6a 00		 push	 0
  00052	6a 00		 push	 0
  00054	6a 00		 push	 0
  00056	8d 4d d0	 lea	 ecx, DWORD PTR _msg$[ebp]
  00059	51		 push	 ecx
  0005a	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__PeekMessageW@20
  00060	3b f4		 cmp	 esi, esp
  00062	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00067	85 c0		 test	 eax, eax
  00069	75 35		 jne	 SHORT $LN6@Run

; 72   : 		{
; 73   : 			//NO MESSAGE
; 74   : 			if (!doTasks()) {

  0006b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0006e	e8 00 00 00 00	 call	 ?doTasks@CMessageLoop2@@AAE_NXZ ; CMessageLoop2::doTasks
  00073	0f b6 d0	 movzx	 edx, al
  00076	85 d2		 test	 edx, edx
  00078	75 13		 jne	 SHORT $LN7@Run

; 75   : 				Sleep(1);

  0007a	8b f4		 mov	 esi, esp
  0007c	6a 01		 push	 1
  0007e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__Sleep@4
  00084	3b f4		 cmp	 esi, esp
  00086	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 76   : 			}else {

  0008b	eb 11		 jmp	 SHORT $LN8@Run
$LN7@Run:

; 77   : 				Sleep(1);

  0008d	8b f4		 mov	 esi, esp
  0008f	6a 01		 push	 1
  00091	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__Sleep@4
  00097	3b f4		 cmp	 esi, esp
  00099	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN8@Run:

; 78   : 			}
; 79   : 		}

  0009e	eb ac		 jmp	 SHORT $LN5@Run
$LN6@Run:

; 80   : 
; 81   : 		bRet = GetMessage(&msg, NULL, 0, 0);

  000a0	8b f4		 mov	 esi, esp
  000a2	6a 00		 push	 0
  000a4	6a 00		 push	 0
  000a6	6a 00		 push	 0
  000a8	8d 45 d0	 lea	 eax, DWORD PTR _msg$[ebp]
  000ab	50		 push	 eax
  000ac	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetMessageW@16
  000b2	3b f4		 cmp	 esi, esp
  000b4	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b9	89 45 c8	 mov	 DWORD PTR _bRet$[ebp], eax

; 82   : 		if (bRet == -1) break;//error

  000bc	83 7d c8 ff	 cmp	 DWORD PTR _bRet$[ebp], -1
  000c0	75 02		 jne	 SHORT $LN9@Run
  000c2	eb 33		 jmp	 SHORT $LN3@Run
$LN9@Run:

; 83   : 		if (!bRet) break;//WM_QUIT

  000c4	83 7d c8 00	 cmp	 DWORD PTR _bRet$[ebp], 0
  000c8	75 02		 jne	 SHORT $LN10@Run
  000ca	eb 2b		 jmp	 SHORT $LN3@Run
$LN10@Run:

; 84   : 
; 85   : 		TranslateMessage(&msg);

  000cc	8b f4		 mov	 esi, esp
  000ce	8d 4d d0	 lea	 ecx, DWORD PTR _msg$[ebp]
  000d1	51		 push	 ecx
  000d2	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__TranslateMessage@4
  000d8	3b f4		 cmp	 esi, esp
  000da	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 86   : 		DispatchMessage(&msg);

  000df	8b f4		 mov	 esi, esp
  000e1	8d 55 d0	 lea	 edx, DWORD PTR _msg$[ebp]
  000e4	52		 push	 edx
  000e5	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__DispatchMessageW@4
  000eb	3b f4		 cmp	 esi, esp
  000ed	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 87   : 
; 88   : 	}

  000f2	e9 55 ff ff ff	 jmp	 $LN5@Run
$LN3@Run:

; 89   : 
; 90   : 	return msg.message;

  000f7	8b 45 d4	 mov	 eax, DWORD PTR _msg$[ebp+4]

; 91   : }

  000fa	52		 push	 edx
  000fb	8b cd		 mov	 ecx, ebp
  000fd	50		 push	 eax
  000fe	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN15@Run
  00104	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00109	58		 pop	 eax
  0010a	5a		 pop	 edx
  0010b	5f		 pop	 edi
  0010c	5e		 pop	 esi
  0010d	83 c4 38	 add	 esp, 56			; 00000038H
  00110	3b ec		 cmp	 ebp, esp
  00112	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00117	8b e5		 mov	 esp, ebp
  00119	5d		 pop	 ebp
  0011a	c3		 ret	 0
  0011b	90		 npad	 1
$LN15@Run:
  0011c	02 00 00 00	 DD	 2
  00120	00 00 00 00	 DD	 $LN14@Run
$LN14@Run:
  00124	f4 ff ff ff	 DD	 -12			; fffffff4H
  00128	04 00 00 00	 DD	 4
  0012c	00 00 00 00	 DD	 $LN12@Run
  00130	d0 ff ff ff	 DD	 -48			; ffffffd0H
  00134	1c 00 00 00	 DD	 28			; 0000001cH
  00138	00 00 00 00	 DD	 $LN13@Run
$LN13@Run:
  0013c	6d		 DB	 109			; 0000006dH
  0013d	73		 DB	 115			; 00000073H
  0013e	67		 DB	 103			; 00000067H
  0013f	00		 DB	 0
$LN12@Run:
  00140	68		 DB	 104			; 00000068H
  00141	61		 DB	 97			; 00000061H
  00142	6e		 DB	 110			; 0000006eH
  00143	64		 DB	 100			; 00000064H
  00144	6c		 DB	 108			; 0000006cH
  00145	65		 DB	 101			; 00000065H
  00146	73		 DB	 115			; 00000073H
  00147	00		 DB	 0
?Run@CMessageLoop2@@QAEIXZ ENDP				; CMessageLoop2::Run
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\messageloop.cpp
_TEXT	SEGMENT
_bRet$ = -40						; size = 4
_msg$ = -32						; size = 28
?DoMessageLoop@CMessageLoop2@@SAIXZ PROC		; CMessageLoop2::DoMessageLoop

; 94   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 28	 sub	 esp, 40			; 00000028H
  00006	56		 push	 esi
  00007	57		 push	 edi
  00008	8d 7d d8	 lea	 edi, DWORD PTR [ebp-40]
  0000b	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  00010	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00015	f3 ab		 rep stosd

; 95   : 	using namespace std;
; 96   : 
; 97   : 	MSG msg = { 0 }; BOOL bRet = FALSE;

  00017	33 c0		 xor	 eax, eax
  00019	89 45 e0	 mov	 DWORD PTR _msg$[ebp], eax
  0001c	89 45 e4	 mov	 DWORD PTR _msg$[ebp+4], eax
  0001f	89 45 e8	 mov	 DWORD PTR _msg$[ebp+8], eax
  00022	89 45 ec	 mov	 DWORD PTR _msg$[ebp+12], eax
  00025	89 45 f0	 mov	 DWORD PTR _msg$[ebp+16], eax
  00028	89 45 f4	 mov	 DWORD PTR _msg$[ebp+20], eax
  0002b	89 45 f8	 mov	 DWORD PTR _msg$[ebp+24], eax
  0002e	c7 45 d8 00 00
	00 00		 mov	 DWORD PTR _bRet$[ebp], 0
$LN4@DoMessageL:

; 98   : 	for (;;) {
; 99   : 		bRet = GetMessage(&msg, NULL, 0, 0);

  00035	8b f4		 mov	 esi, esp
  00037	6a 00		 push	 0
  00039	6a 00		 push	 0
  0003b	6a 00		 push	 0
  0003d	8d 4d e0	 lea	 ecx, DWORD PTR _msg$[ebp]
  00040	51		 push	 ecx
  00041	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetMessageW@16
  00047	3b f4		 cmp	 esi, esp
  00049	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004e	89 45 d8	 mov	 DWORD PTR _bRet$[ebp], eax

; 100  : 		if (bRet == -1) break;//error

  00051	83 7d d8 ff	 cmp	 DWORD PTR _bRet$[ebp], -1
  00055	75 02		 jne	 SHORT $LN5@DoMessageL
  00057	eb 30		 jmp	 SHORT $LN3@DoMessageL
$LN5@DoMessageL:

; 101  : 		if (!bRet) break;//WM_QUIT

  00059	83 7d d8 00	 cmp	 DWORD PTR _bRet$[ebp], 0
  0005d	75 02		 jne	 SHORT $LN6@DoMessageL
  0005f	eb 28		 jmp	 SHORT $LN3@DoMessageL
$LN6@DoMessageL:

; 102  : 
; 103  : 		TranslateMessage(&msg);

  00061	8b f4		 mov	 esi, esp
  00063	8d 55 e0	 lea	 edx, DWORD PTR _msg$[ebp]
  00066	52		 push	 edx
  00067	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__TranslateMessage@4
  0006d	3b f4		 cmp	 esi, esp
  0006f	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 104  : 		DispatchMessage(&msg);

  00074	8b f4		 mov	 esi, esp
  00076	8d 45 e0	 lea	 eax, DWORD PTR _msg$[ebp]
  00079	50		 push	 eax
  0007a	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__DispatchMessageW@4
  00080	3b f4		 cmp	 esi, esp
  00082	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 105  : 	}

  00087	eb ac		 jmp	 SHORT $LN4@DoMessageL
$LN3@DoMessageL:

; 106  : 
; 107  : 	return msg.message;

  00089	8b 45 e4	 mov	 eax, DWORD PTR _msg$[ebp+4]

; 108  : }

  0008c	52		 push	 edx
  0008d	8b cd		 mov	 ecx, ebp
  0008f	50		 push	 eax
  00090	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN10@DoMessageL
  00096	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0009b	58		 pop	 eax
  0009c	5a		 pop	 edx
  0009d	5f		 pop	 edi
  0009e	5e		 pop	 esi
  0009f	83 c4 28	 add	 esp, 40			; 00000028H
  000a2	3b ec		 cmp	 ebp, esp
  000a4	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000a9	8b e5		 mov	 esp, ebp
  000ab	5d		 pop	 ebp
  000ac	c3		 ret	 0
  000ad	0f 1f 00	 npad	 3
$LN10@DoMessageL:
  000b0	01 00 00 00	 DD	 1
  000b4	00 00 00 00	 DD	 $LN9@DoMessageL
$LN9@DoMessageL:
  000b8	e0 ff ff ff	 DD	 -32			; ffffffe0H
  000bc	1c 00 00 00	 DD	 28			; 0000001cH
  000c0	00 00 00 00	 DD	 $LN8@DoMessageL
$LN8@DoMessageL:
  000c4	6d		 DB	 109			; 0000006dH
  000c5	73		 DB	 115			; 00000073H
  000c6	67		 DB	 103			; 00000067H
  000c7	00		 DB	 0
?DoMessageLoop@CMessageLoop2@@SAIXZ ENDP		; CMessageLoop2::DoMessageLoop
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??9?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z
_TEXT	SEGMENT
tv69 = -8						; size = 4
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
??9?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z PROC ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator!=, COMDAT
; _this$ = ecx

; 247  : 		{	// test for iterator inequality

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 248  : 		return (!(*this == _Right));

  00017	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  0001a	50		 push	 eax
  0001b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001e	e8 00 00 00 00	 call	 ??8?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator==
  00023	0f b6 c8	 movzx	 ecx, al
  00026	85 c9		 test	 ecx, ecx
  00028	75 09		 jne	 SHORT $LN3@operator
  0002a	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv69[ebp], 1
  00031	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  00033	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv69[ebp], 0
$LN4@operator:
  0003a	8a 45 f8	 mov	 al, BYTE PTR tv69[ebp]

; 249  : 		}

  0003d	83 c4 08	 add	 esp, 8
  00040	3b ec		 cmp	 ebp, esp
  00042	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00047	8b e5		 mov	 esp, ebp
  00049	5d		 pop	 ebp
  0004a	c2 04 00	 ret	 4
??9?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z ENDP ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator!=
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z
_TEXT	SEGMENT
__Tmp$ = -12						; size = 4
_this$ = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
___formal$ = 12						; size = 4
??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z PROC ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++, COMDAT
; _this$ = ecx

; 311  : 		{	// postincrement

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 10	 sub	 esp, 16			; 00000010H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0000e	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00011	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00014	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  00017	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 312  : 		_List_iterator _Tmp = *this;

  0001a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001d	8b 08		 mov	 ecx, DWORD PTR [eax]
  0001f	89 4d f4	 mov	 DWORD PTR __Tmp$[ebp], ecx

; 313  : 		++*this;

  00022	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00025	e8 00 00 00 00	 call	 ??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++

; 314  : 		return (_Tmp);

  0002a	8b 55 08	 mov	 edx, DWORD PTR ___$ReturnUdt$[ebp]
  0002d	8b 45 f4	 mov	 eax, DWORD PTR __Tmp$[ebp]
  00030	89 02		 mov	 DWORD PTR [edx], eax
  00032	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 315  : 		}

  00035	52		 push	 edx
  00036	8b cd		 mov	 ecx, ebp
  00038	50		 push	 eax
  00039	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN5@operator
  0003f	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00044	58		 pop	 eax
  00045	5a		 pop	 edx
  00046	83 c4 10	 add	 esp, 16			; 00000010H
  00049	3b ec		 cmp	 ebp, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00050	8b e5		 mov	 esp, ebp
  00052	5d		 pop	 ebp
  00053	c2 08 00	 ret	 8
  00056	66 90		 npad	 2
$LN5@operator:
  00058	01 00 00 00	 DD	 1
  0005c	00 00 00 00	 DD	 $LN4@operator
$LN4@operator:
  00060	f4 ff ff ff	 DD	 -12			; fffffff4H
  00064	04 00 00 00	 DD	 4
  00068	00 00 00 00	 DD	 $LN3@operator
$LN3@operator:
  0006c	5f		 DB	 95			; 0000005fH
  0006d	54		 DB	 84			; 00000054H
  0006e	6d		 DB	 109			; 0000006dH
  0006f	70		 DB	 112			; 00000070H
  00070	00		 DB	 0
??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z ENDP ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??C?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEPAUTask@CMessageLoop2@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??C?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEPAUTask@CMessageLoop2@@XZ PROC ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator->, COMDAT
; _this$ = ecx

; 300  : 		{	// return pointer to class object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 301  : 		return (pointer_traits<pointer>::pointer_to(**this));

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??D?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEAAUTask@CMessageLoop2@@XZ ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*
  00016	50		 push	 eax
  00017	e8 00 00 00 00	 call	 ?pointer_to@?$pointer_traits@PAUTask@CMessageLoop2@@@std@@SAPAUTask@CMessageLoop2@@AAU34@@Z ; std::pointer_traits<CMessageLoop2::Task *>::pointer_to
  0001c	83 c4 04	 add	 esp, 4

; 302  : 		}

  0001f	83 c4 04	 add	 esp, 4
  00022	3b ec		 cmp	 ebp, esp
  00024	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00029	8b e5		 mov	 esp, ebp
  0002b	5d		 pop	 ebp
  0002c	c3		 ret	 0
??C?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEPAUTask@CMessageLoop2@@XZ ENDP ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator->
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??D?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEAAUTask@CMessageLoop2@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??D?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEAAUTask@CMessageLoop2@@XZ PROC ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*, COMDAT
; _this$ = ecx

; 295  : 		{	// return designated value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 296  : 		return ((reference)**(_Mybase *)this);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??D?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABUTask@CMessageLoop2@@XZ ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*

; 297  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
??D?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEAAUTask@CMessageLoop2@@XZ ENDP ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?erase@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z
_TEXT	SEGMENT
$T1 = -12						; size = 4
__Pnode$ = -8						; size = 4
_this$ = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
__Where$ = 12						; size = 4
?erase@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::erase, COMDAT
; _this$ = ecx

; 1349 : 		{	// erase element at _Where

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1350 : 		_Nodeptr _Pnode = _Unlinknode(_Where++);

  0001e	6a 00		 push	 0
  00020	8d 45 f4	 lea	 eax, DWORD PTR $T1[ebp]
  00023	50		 push	 eax
  00024	8d 4d 0c	 lea	 ecx, DWORD PTR __Where$[ebp]
  00027	e8 00 00 00 00	 call	 ??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
  0002c	8b 08		 mov	 ecx, DWORD PTR [eax]
  0002e	51		 push	 ecx
  0002f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00032	e8 00 00 00 00	 call	 ?_Unlinknode@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unlinknode
  00037	89 45 f8	 mov	 DWORD PTR __Pnode$[ebp], eax

; 1351 : 		this->_Freenode(_Pnode);

  0003a	8b 55 f8	 mov	 edx, DWORD PTR __Pnode$[ebp]
  0003d	52		 push	 edx
  0003e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00041	e8 00 00 00 00	 call	 ?_Freenode@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@@Z ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Freenode

; 1352 : 		return (_Make_iter(_Where));

  00046	8b 45 0c	 mov	 eax, DWORD PTR __Where$[ebp]
  00049	50		 push	 eax
  0004a	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0004d	51		 push	 ecx
  0004e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00051	e8 00 00 00 00	 call	 ?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Make_iter
  00056	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1353 : 		}

  00059	83 c4 0c	 add	 esp, 12			; 0000000cH
  0005c	3b ec		 cmp	 ebp, esp
  0005e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00063	8b e5		 mov	 esp, ebp
  00065	5d		 pop	 ebp
  00066	c2 08 00	 ret	 8
?erase@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::erase
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?empty@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE_NXZ
_TEXT	SEGMENT
tv71 = -8						; size = 4
_this$ = -4						; size = 4
?empty@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE_NXZ PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::empty, COMDAT
; _this$ = ecx

; 1134 : 		{	// test if sequence is empty

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1135 : 		return (this->_Mysize() == 0);

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABIXZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
  0001f	83 38 00	 cmp	 DWORD PTR [eax], 0
  00022	75 09		 jne	 SHORT $LN3@empty
  00024	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv71[ebp], 1
  0002b	eb 07		 jmp	 SHORT $LN4@empty
$LN3@empty:
  0002d	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv71[ebp], 0
$LN4@empty:
  00034	8a 45 f8	 mov	 al, BYTE PTR tv71[ebp]

; 1136 : 		}

  00037	83 c4 08	 add	 esp, 8
  0003a	3b ec		 cmp	 ebp, esp
  0003c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00041	8b e5		 mov	 esp, ebp
  00043	5d		 pop	 ebp
  00044	c3		 ret	 0
?empty@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE_NXZ ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::empty
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::end, COMDAT
; _this$ = ecx

; 1016 : 		{	// return iterator for end of mutable sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1017 : 		return (iterator(this->_Myhead(), _STD addressof(this->_Get_data())));

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
  0002b	50		 push	 eax
  0002c	e8 00 00 00 00	 call	 ??$addressof@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@AAV10@@Z ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
  00031	83 c4 04	 add	 esp, 4
  00034	50		 push	 eax
  00035	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00038	e8 00 00 00 00	 call	 ?_Myhead@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Myhead
  0003d	8b 00		 mov	 eax, DWORD PTR [eax]
  0003f	50		 push	 eax
  00040	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00043	e8 00 00 00 00	 call	 ??0?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
  00048	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1018 : 		}

  0004b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0004e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00055	83 c4 10	 add	 esp, 16			; 00000010H
  00058	3b ec		 cmp	 ebp, esp
  0005a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005f	8b e5		 mov	 esp, ebp
  00061	5d		 pop	 ebp
  00062	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::end
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::begin, COMDAT
; _this$ = ecx

; 1006 : 		{	// return iterator for beginning of mutable sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1007 : 		return (iterator(this->_Myhead()->_Next, _STD addressof(this->_Get_data())));

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
  0002b	50		 push	 eax
  0002c	e8 00 00 00 00	 call	 ??$addressof@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@AAV10@@Z ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
  00031	83 c4 04	 add	 esp, 4
  00034	50		 push	 eax
  00035	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00038	e8 00 00 00 00	 call	 ?_Myhead@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Myhead
  0003d	8b 00		 mov	 eax, DWORD PTR [eax]
  0003f	8b 08		 mov	 ecx, DWORD PTR [eax]
  00041	51		 push	 ecx
  00042	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00045	e8 00 00 00 00	 call	 ??0?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
  0004a	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1008 : 		}

  0004d	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00050	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00057	83 c4 10	 add	 esp, 16			; 00000010H
  0005a	3b ec		 cmp	 ebp, esp
  0005c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00061	8b e5		 mov	 esp, ebp
  00063	5d		 pop	 ebp
  00064	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?begin@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::begin
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?push_back@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEX$$QAUTask@CMessageLoop2@@@Z
_TEXT	SEGMENT
$T1 = -12						; size = 4
$T2 = -8						; size = 4
_this$ = -4						; size = 4
__Val$ = 8						; size = 4
?push_back@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEX$$QAUTask@CMessageLoop2@@@Z PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::push_back, COMDAT
; _this$ = ecx

; 905  : 		{	// insert element at end

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 906  : 		_Insert(_Unchecked_end(), _STD move(_Val));

  0001e	8d 45 f4	 lea	 eax, DWORD PTR $T1[ebp]
  00021	50		 push	 eax
  00022	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00025	e8 00 00 00 00	 call	 ?_Unchecked_end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unchecked_end
  0002a	8b 08		 mov	 ecx, DWORD PTR [eax]
  0002c	89 4d f8	 mov	 DWORD PTR $T2[ebp], ecx
  0002f	8b 55 08	 mov	 edx, DWORD PTR __Val$[ebp]
  00032	52		 push	 edx
  00033	e8 00 00 00 00	 call	 ??$move@AAUTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::move<CMessageLoop2::Task &>
  00038	83 c4 04	 add	 esp, 4
  0003b	50		 push	 eax
  0003c	8b 45 f8	 mov	 eax, DWORD PTR $T2[ebp]
  0003f	50		 push	 eax
  00040	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00043	e8 00 00 00 00	 call	 ??$_Insert@UTask@CMessageLoop2@@@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@1@$$QAUTask@CMessageLoop2@@@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Insert<CMessageLoop2::Task>

; 907  : 		}

  00048	83 c4 0c	 add	 esp, 12			; 0000000cH
  0004b	3b ec		 cmp	 ebp, esp
  0004d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00052	8b e5		 mov	 esp, ebp
  00054	5d		 pop	 ebp
  00055	c2 04 00	 ret	 4
?push_back@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEX$$QAUTask@CMessageLoop2@@@Z ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::push_back
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
__Right$ = 8						; size = 4
??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z PROC		; std::function<void __cdecl(void)>::function<void __cdecl(void)>, COMDAT
; _this$ = ecx

; 1537 : 	function(function&& _Right)

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1538 : 		{	// construct holding moved copy of _Right

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0?$_Func_class@X$$V@std@@QAE@XZ ; std::_Func_class<void>::_Func_class<void>
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 1539 : 		this->_Reset_move(_STD move(_Right));

  00032	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  00035	50		 push	 eax
  00036	e8 00 00 00 00	 call	 ??$move@AAV?$function@$$A6AXXZ@std@@@std@@YA$$QAV?$function@$$A6AXXZ@0@AAV10@@Z ; std::move<std::function<void __cdecl(void)> &>
  0003b	83 c4 04	 add	 esp, 4
  0003e	50		 push	 eax
  0003f	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00042	e8 00 00 00 00	 call	 ?_Reset_move@?$_Func_class@X$$V@std@@IAEX$$QAV12@@Z ; std::_Func_class<void>::_Reset_move

; 1540 : 		}

  00047	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0004e	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00051	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00054	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0005b	83 c4 10	 add	 esp, 16			; 00000010H
  0005e	3b ec		 cmp	 ebp, esp
  00060	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00065	8b e5		 mov	 esp, ebp
  00067	5d		 pop	 ebp
  00068	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$_Func_class@X$$V@std@@QAE@XZ ; std::_Func_class<void>::~_Func_class<void>
__ehhandler$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$function@$$A6AXXZ@std@@QAE@$$QAV01@@Z ENDP		; std::function<void __cdecl(void)>::function<void __cdecl(void)>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\mutex
;	COMDAT ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ PROC		; std::lock_guard<std::mutex>::~lock_guard<std::mutex>, COMDAT
; _this$ = ecx

; 526  : 		{	// unlock

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 527  : 		_MyMutex.unlock();

  00023	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00026	8b 08		 mov	 ecx, DWORD PTR [eax]
  00028	e8 00 00 00 00	 call	 ?unlock@_Mutex_base@std@@QAEXXZ ; std::_Mutex_base::unlock

; 528  : 		}

  0002d	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00030	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00037	83 c4 10	 add	 esp, 16			; 00000010H
  0003a	3b ec		 cmp	 ebp, esp
  0003c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00041	8b e5		 mov	 esp, ebp
  00043	5d		 pop	 ebp
  00044	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ENDP		; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\mutex
;	COMDAT ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Mtx$ = 8						; size = 4
??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z PROC ; std::lock_guard<std::mutex>::lock_guard<std::mutex>, COMDAT
; _this$ = ecx

; 516  : 		{	// construct and lock

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 515  : 		: _MyMutex(_Mtx)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Mtx$[ebp]
  00014	89 08		 mov	 DWORD PTR [eax], ecx

; 517  : 		_MyMutex.lock();

  00016	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00019	8b 0a		 mov	 ecx, DWORD PTR [edx]
  0001b	e8 00 00 00 00	 call	 ?lock@_Mutex_base@std@@QAEXXZ ; std::_Mutex_base::lock

; 518  : 		}

  00020	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00023	83 c4 04	 add	 esp, 4
  00026	3b ec		 cmp	 ebp, esp
  00028	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002d	8b e5		 mov	 esp, ebp
  0002f	5d		 pop	 ebp
  00030	c2 04 00	 ret	 4
??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ENDP ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\mutex
;	COMDAT ??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ PROC		; std::unique_lock<std::mutex>::~unique_lock<std::mutex>, COMDAT
; _this$ = ecx

; 213  : 		{	// clean up

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 214  : 		if (_Owns)

  00023	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00026	0f b6 48 04	 movzx	 ecx, BYTE PTR [eax+4]
  0002a	85 c9		 test	 ecx, ecx
  0002c	74 0a		 je	 SHORT $LN1@unique_loc

; 215  : 			_Pmtx->unlock();

  0002e	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00031	8b 0a		 mov	 ecx, DWORD PTR [edx]
  00033	e8 00 00 00 00	 call	 ?unlock@_Mutex_base@std@@QAEXXZ ; std::_Mutex_base::unlock
$LN1@unique_loc:

; 216  : 		}

  00038	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003b	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00042	83 c4 10	 add	 esp, 16			; 00000010H
  00045	3b ec		 cmp	 ebp, esp
  00047	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004c	8b e5		 mov	 esp, ebp
  0004e	5d		 pop	 ebp
  0004f	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$unique_lock@Vmutex@std@@@std@@QAE@XZ ENDP		; std::unique_lock<std::mutex>::~unique_lock<std::mutex>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\mutex
;	COMDAT ??0?$unique_lock@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Mtx$ = 8						; size = 4
??0?$unique_lock@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z PROC ; std::unique_lock<std::mutex>::unique_lock<std::mutex>, COMDAT
; _this$ = ecx

; 149  : 		{	// construct and lock

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 148  : 		: _Pmtx(_STD addressof(_Mtx)), _Owns(false)

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Mtx$[ebp]
  00011	50		 push	 eax
  00012	e8 00 00 00 00	 call	 ??$addressof@Vmutex@std@@@std@@YAPAVmutex@0@AAV10@@Z ; std::addressof<std::mutex>
  00017	83 c4 04	 add	 esp, 4
  0001a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001d	89 01		 mov	 DWORD PTR [ecx], eax
  0001f	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00022	c6 42 04 00	 mov	 BYTE PTR [edx+4], 0

; 150  : 		_Pmtx->lock();

  00026	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00029	8b 08		 mov	 ecx, DWORD PTR [eax]
  0002b	e8 00 00 00 00	 call	 ?lock@_Mutex_base@std@@QAEXXZ ; std::_Mutex_base::lock

; 151  : 		_Owns = true;

  00030	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00033	c6 41 04 01	 mov	 BYTE PTR [ecx+4], 1

; 152  : 		}

  00037	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0003a	83 c4 04	 add	 esp, 4
  0003d	3b ec		 cmp	 ebp, esp
  0003f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00044	8b e5		 mov	 esp, ebp
  00046	5d		 pop	 ebp
  00047	c2 04 00	 ret	 4
??0?$unique_lock@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ENDP ; std::unique_lock<std::mutex>::unique_lock<std::mutex>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??Y?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAEAAV012@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Dur$ = 8						; size = 4
??Y?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAEAAV012@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z PROC ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::operator+=, COMDAT
; _this$ = ecx

; 227  : 		{	// increment by duration

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 228  : 		_MyDur += _Dur;

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Dur$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	e8 00 00 00 00	 call	 ??Y?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAEAAV012@ABV012@@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::operator+=

; 229  : 		return (*this);

  0001a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 230  : 		}

  0001d	83 c4 04	 add	 esp, 4
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	8b e5		 mov	 esp, ebp
  00029	5d		 pop	 ebp
  0002a	c2 04 00	 ret	 4
??Y?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAEAAV012@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z ENDP ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::operator+=
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Other$ = 8						; size = 4
??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z PROC ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >, COMDAT
; _this$ = ecx

; 211  : 		{	// construct from a duration

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 210  : 		: _MyDur(_Other)

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Other$[ebp]
  00011	8b 08		 mov	 ecx, DWORD PTR [eax]
  00013	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  00016	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00019	89 08		 mov	 DWORD PTR [eax], ecx
  0001b	89 50 04	 mov	 DWORD PTR [eax+4], edx

; 212  : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	8b e5		 mov	 esp, ebp
  00023	5d		 pop	 ebp
  00024	c2 04 00	 ret	 4
??0?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@@Z ENDP ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ
_TEXT	SEGMENT
$T2 = -20						; size = 8
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ PROC ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::zero, COMDAT

; 176  : 		{	// get zero value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 08	 sub	 esp, 8
  0001b	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00022	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH

; 177  : 		return (duration(duration_values<_Rep>::zero()));

  00029	e8 00 00 00 00	 call	 ?zero@?$duration_values@_J@chrono@std@@SA_JXZ ; std::chrono::duration_values<__int64>::zero
  0002e	89 45 ec	 mov	 DWORD PTR $T2[ebp], eax
  00031	89 55 f0	 mov	 DWORD PTR $T2[ebp+4], edx
  00034	8d 45 ec	 lea	 eax, DWORD PTR $T2[ebp]
  00037	50		 push	 eax
  00038	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0003b	e8 00 00 00 00	 call	 ??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
  00040	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 178  : 		}

  00043	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00046	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0004d	83 c4 14	 add	 esp, 20			; 00000014H
  00050	3b ec		 cmp	 ebp, esp
  00052	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00057	8b e5		 mov	 esp, ebp
  00059	5d		 pop	 ebp
  0005a	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?zero@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@SA?AV123@XZ ENDP ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::zero
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ?zero@?$duration_values@_J@chrono@std@@SA_JXZ
_TEXT	SEGMENT
?zero@?$duration_values@_J@chrono@std@@SA_JXZ PROC	; std::chrono::duration_values<__int64>::zero, COMDAT

; 36   : 		{	// get zero value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 37   : 		return (_Rep(0));

  00003	33 c0		 xor	 eax, eax
  00005	33 d2		 xor	 edx, edx

; 38   : 		}

  00007	5d		 pop	 ebp
  00008	c3		 ret	 0
?zero@?$duration_values@_J@chrono@std@@SA_JXZ ENDP	; std::chrono::duration_values<__int64>::zero
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?pointer_to@?$pointer_traits@PAUTask@CMessageLoop2@@@std@@SAPAUTask@CMessageLoop2@@AAU34@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
?pointer_to@?$pointer_traits@PAUTask@CMessageLoop2@@@std@@SAPAUTask@CMessageLoop2@@AAU34@@Z PROC ; std::pointer_traits<CMessageLoop2::Task *>::pointer_to, COMDAT

; 341  : 		{	// convert raw reference to pointer

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 342  : 		return (_STD addressof(_Val));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]
  00006	50		 push	 eax
  00007	e8 00 00 00 00	 call	 ??$addressof@UTask@CMessageLoop2@@@std@@YAPAUTask@CMessageLoop2@@AAU12@@Z ; std::addressof<CMessageLoop2::Task>
  0000c	83 c4 04	 add	 esp, 4

; 343  : 		}

  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?pointer_to@?$pointer_traits@PAUTask@CMessageLoop2@@@std@@SAPAUTask@CMessageLoop2@@AAU34@@Z ENDP ; std::pointer_traits<CMessageLoop2::Task *>::pointer_to
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??8?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z
_TEXT	SEGMENT
tv75 = -8						; size = 4
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
??8?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z PROC ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator==, COMDAT
; _this$ = ecx

; 238  : 		{	// test for iterator equality

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 239  :  #if _ITERATOR_DEBUG_LEVEL != 0
; 240  : 		_STL_VERIFY(this->_Getcont() == _Right._Getcont(), "list iterators incompatible");
; 241  :  #endif /* _ITERATOR_DEBUG_LEVEL != 0 */
; 242  : 
; 243  : 		return (this->_Ptr == _Right._Ptr);

  00017	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001a	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  0001d	8b 10		 mov	 edx, DWORD PTR [eax]
  0001f	3b 11		 cmp	 edx, DWORD PTR [ecx]
  00021	75 09		 jne	 SHORT $LN3@operator
  00023	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv75[ebp], 1
  0002a	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  0002c	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv75[ebp], 0
$LN4@operator:
  00033	8a 45 f8	 mov	 al, BYTE PTR tv75[ebp]

; 244  : 		}

  00036	8b e5		 mov	 esp, ebp
  00038	5d		 pop	 ebp
  00039	c2 04 00	 ret	 4
??8?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBE_NABV01@@Z ENDP ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator==
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z
_TEXT	SEGMENT
__Tmp$ = -12						; size = 4
_this$ = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
___formal$ = 12						; size = 4
??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z PROC ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++, COMDAT
; _this$ = ecx

; 211  : 		{	// postincrement

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 10	 sub	 esp, 16			; 00000010H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0000e	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00011	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00014	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  00017	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 212  : 		_List_const_iterator _Tmp = *this;

  0001a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001d	8b 08		 mov	 ecx, DWORD PTR [eax]
  0001f	89 4d f4	 mov	 DWORD PTR __Tmp$[ebp], ecx

; 213  : 		++*this;

  00022	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00025	e8 00 00 00 00	 call	 ??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++

; 214  : 		return (_Tmp);

  0002a	8b 55 08	 mov	 edx, DWORD PTR ___$ReturnUdt$[ebp]
  0002d	8b 45 f4	 mov	 eax, DWORD PTR __Tmp$[ebp]
  00030	89 02		 mov	 DWORD PTR [edx], eax
  00032	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 215  : 		}

  00035	52		 push	 edx
  00036	8b cd		 mov	 ecx, ebp
  00038	50		 push	 eax
  00039	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN5@operator
  0003f	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00044	58		 pop	 eax
  00045	5a		 pop	 edx
  00046	83 c4 10	 add	 esp, 16			; 00000010H
  00049	3b ec		 cmp	 ebp, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00050	8b e5		 mov	 esp, ebp
  00052	5d		 pop	 ebp
  00053	c2 08 00	 ret	 8
  00056	66 90		 npad	 2
$LN5@operator:
  00058	01 00 00 00	 DD	 1
  0005c	00 00 00 00	 DD	 $LN4@operator
$LN4@operator:
  00060	f4 ff ff ff	 DD	 -12			; fffffff4H
  00064	04 00 00 00	 DD	 4
  00068	00 00 00 00	 DD	 $LN3@operator
$LN3@operator:
  0006c	5f		 DB	 95			; 0000005fH
  0006d	54		 DB	 84			; 00000054H
  0006e	6d		 DB	 109			; 0000006dH
  0006f	70		 DB	 112			; 00000070H
  00070	00		 DB	 0
??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE?AV01@H@Z ENDP ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??D?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABUTask@CMessageLoop2@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??D?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABUTask@CMessageLoop2@@XZ PROC ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*, COMDAT
; _this$ = ecx

; 183  : 		{	// return designated value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 184  :  #if _ITERATOR_DEBUG_LEVEL != 0
; 185  : 		const auto _Mycont = static_cast<const _Mylist *>(this->_Getcont());
; 186  : 		_STL_ASSERT(_Mycont, "cannot dereference value-initialized list iterator");
; 187  : 		_STL_VERIFY(this->_Ptr != _Mycont->_Myhead, "cannot dereference end list iterator");
; 188  :  #endif /* _ITERATOR_DEBUG_LEVEL != 0 */
; 189  : 
; 190  : 		return (this->_Ptr->_Myval);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 00		 mov	 eax, DWORD PTR [eax]
  00013	83 c0 08	 add	 eax, 8

; 191  : 		}

  00016	8b e5		 mov	 esp, ebp
  00018	5d		 pop	 ebp
  00019	c3		 ret	 0
??D?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABUTask@CMessageLoop2@@XZ ENDP ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator*
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ PROC ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++, COMDAT
; _this$ = ecx

; 305  : 		{	// preincrement

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 306  : 		++(*(_Mybase *)this);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++

; 307  : 		return (*this);

  00016	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 308  : 		}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
??E?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ ENDP ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??0?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
__Plist$ = 12						; size = 4
??0?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z PROC ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >, COMDAT
; _this$ = ecx

; 291  : 		{	// construct with node pointer _Pnode

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 290  : 		: _Mybase(_Pnode, _Plist)

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Plist$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ??0?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >

; 292  : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 08 00	 ret	 8
??0?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ENDP ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABIXZ PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize, COMDAT
; _this$ = ecx

; 676  : 		{	// return const reference to _Mysize

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 677  : 		return (_Get_data()._Mysize);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
  00016	83 c0 04	 add	 eax, 4

; 678  : 		}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABIXZ ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Myhead@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Myhead@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@XZ PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Myhead, COMDAT
; _this$ = ecx

; 661  : 		{	// return reference to _Myhead

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 662  : 		return (_Get_data()._Myhead);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data

; 663  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Myhead@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@XZ ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Myhead
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data, COMDAT
; _this$ = ecx

; 651  : 		{	// return reference to _List_val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 652  : 		return (_Mypair._Get_second());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second

; 653  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Freenode@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@@Z
_TEXT	SEGMENT
__Al$ = -8						; size = 4
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
?_Freenode@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@@Z PROC ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Freenode, COMDAT
; _this$ = ecx

; 728  : 		{	// give node back

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 729  : 		_Alnode& _Al = this->_Getal();

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
  0001f	89 45 f8	 mov	 DWORD PTR __Al$[ebp], eax

; 730  : 		_Alnode_traits::destroy(_Al, _STD addressof(_Pnode->_Myval));

  00022	8b 45 08	 mov	 eax, DWORD PTR __Pnode$[ebp]
  00025	83 c0 08	 add	 eax, 8
  00028	50		 push	 eax
  00029	e8 00 00 00 00	 call	 ??$addressof@UTask@CMessageLoop2@@@std@@YAPAUTask@CMessageLoop2@@AAU12@@Z ; std::addressof<CMessageLoop2::Task>
  0002e	83 c4 04	 add	 esp, 4
  00031	50		 push	 eax
  00032	8b 4d f8	 mov	 ecx, DWORD PTR __Al$[ebp]
  00035	51		 push	 ecx
  00036	e8 00 00 00 00	 call	 ??$destroy@UTask@CMessageLoop2@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<CMessageLoop2::Task>
  0003b	83 c4 08	 add	 esp, 8

; 731  : 		_Node::_Freenode0(_Al, _Pnode);

  0003e	8b 55 08	 mov	 edx, DWORD PTR __Pnode$[ebp]
  00041	52		 push	 edx
  00042	8b 45 f8	 mov	 eax, DWORD PTR __Al$[ebp]
  00045	50		 push	 eax
  00046	e8 00 00 00 00	 call	 ??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z ; std::_List_node<CMessageLoop2::Task,void *>::_Freenode0<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >
  0004b	83 c4 08	 add	 esp, 8

; 732  : 		}

  0004e	83 c4 08	 add	 esp, 8
  00051	3b ec		 cmp	 ebp, esp
  00053	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00058	8b e5		 mov	 esp, ebp
  0005a	5d		 pop	 ebp
  0005b	c2 04 00	 ret	 4
?_Freenode@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@@Z ENDP ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Freenode
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Unlinknode@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z
_TEXT	SEGMENT
tv81 = -12						; size = 4
__Pnode$ = -8						; size = 4
_this$ = -4						; size = 4
__Where$ = 8						; size = 4
?_Unlinknode@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unlinknode, COMDAT
; _this$ = ecx

; 1322 : 		{	// unlink node at _Where from the list

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1323 :  #if _ITERATOR_DEBUG_LEVEL == 2
; 1324 : 		_STL_VERIFY(_Where._Getcont() == _STD addressof(this->_Get_data()), "list erase iterator outside range");
; 1325 : 		_Nodeptr _Pnode = _Where._Ptr;
; 1326 : 		_Orphan_ptr(_Pnode);
; 1327 :  #else /* _ITERATOR_DEBUG_LEVEL == 2 */
; 1328 : 		_Nodeptr _Pnode = _Where._Ptr;

  0001e	8b 45 08	 mov	 eax, DWORD PTR __Where$[ebp]
  00021	89 45 f8	 mov	 DWORD PTR __Pnode$[ebp], eax

; 1329 :  #endif /* _ITERATOR_DEBUG_LEVEL == 2 */
; 1330 : 
; 1331 : 		_Pnode->_Prev->_Next = _Pnode->_Next;

  00024	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00027	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0002a	8b 45 f8	 mov	 eax, DWORD PTR __Pnode$[ebp]
  0002d	8b 08		 mov	 ecx, DWORD PTR [eax]
  0002f	89 0a		 mov	 DWORD PTR [edx], ecx

; 1332 : 		_Pnode->_Next->_Prev = _Pnode->_Prev;

  00031	8b 55 f8	 mov	 edx, DWORD PTR __Pnode$[ebp]
  00034	8b 02		 mov	 eax, DWORD PTR [edx]
  00036	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00039	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0003c	89 50 04	 mov	 DWORD PTR [eax+4], edx

; 1333 : 
; 1334 : 		--this->_Mysize();

  0003f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00042	e8 00 00 00 00	 call	 ?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAIXZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
  00047	89 45 f4	 mov	 DWORD PTR tv81[ebp], eax
  0004a	8b 45 f4	 mov	 eax, DWORD PTR tv81[ebp]
  0004d	8b 08		 mov	 ecx, DWORD PTR [eax]
  0004f	83 e9 01	 sub	 ecx, 1
  00052	8b 55 f4	 mov	 edx, DWORD PTR tv81[ebp]
  00055	89 0a		 mov	 DWORD PTR [edx], ecx

; 1335 : 		return (_Pnode);

  00057	8b 45 f8	 mov	 eax, DWORD PTR __Pnode$[ebp]

; 1336 : 		}

  0005a	83 c4 0c	 add	 esp, 12			; 0000000cH
  0005d	3b ec		 cmp	 ebp, esp
  0005f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00064	8b e5		 mov	 esp, ebp
  00066	5d		 pop	 ebp
  00067	c2 04 00	 ret	 4
?_Unlinknode@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unlinknode
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
__Where$ = 12						; size = 4
?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Make_iter, COMDAT
; _this$ = ecx

; 1046 : 		{	// make iterator from const_iterator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1047 : 		return (iterator(_Where._Ptr, _STD addressof(this->_Get_data())));

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
  0002b	50		 push	 eax
  0002c	e8 00 00 00 00	 call	 ??$addressof@$$CBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@ABV10@@Z ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > const >
  00031	83 c4 04	 add	 esp, 4
  00034	50		 push	 eax
  00035	8b 45 0c	 mov	 eax, DWORD PTR __Where$[ebp]
  00038	50		 push	 eax
  00039	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0003c	e8 00 00 00 00	 call	 ??0?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
  00041	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1048 : 		}

  00044	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00047	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0004e	83 c4 10	 add	 esp, 16			; 00000010H
  00051	3b ec		 cmp	 ebp, esp
  00053	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00058	8b e5		 mov	 esp, ebp
  0005a	5d		 pop	 ebp
  0005b	c2 08 00	 ret	 8
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?_Make_iter@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBE?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@@Z ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Make_iter
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Unchecked_end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
?_Unchecked_end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unchecked_end, COMDAT
; _this$ = ecx

; 1036 : 		{	// return unchecked iterator for end of mutable sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1037 : 		return (_Unchecked_iterator(this->_Myhead(), nullptr));

  0000e	6a 00		 push	 0
  00010	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00013	e8 00 00 00 00	 call	 ?_Myhead@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Myhead
  00018	8b 00		 mov	 eax, DWORD PTR [eax]
  0001a	50		 push	 eax
  0001b	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001e	e8 00 00 00 00	 call	 ??0?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
  00023	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1038 : 		}

  00026	83 c4 04	 add	 esp, 4
  00029	3b ec		 cmp	 ebp, esp
  0002b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00030	8b e5		 mov	 esp, ebp
  00032	5d		 pop	 ebp
  00033	c2 04 00	 ret	 4
?_Unchecked_end@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAE?AV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@2@XZ ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Unchecked_end
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??Y?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAEAAV012@ABV012@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
??Y?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAEAAV012@ABV012@@Z PROC ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::operator+=, COMDAT
; _this$ = ecx

; 140  : 		{	// add _Right to rep

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 141  : 		_MyRep += _Right._MyRep;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  00014	8b 10		 mov	 edx, DWORD PTR [eax]
  00016	03 11		 add	 edx, DWORD PTR [ecx]
  00018	8b 40 04	 mov	 eax, DWORD PTR [eax+4]
  0001b	13 41 04	 adc	 eax, DWORD PTR [ecx+4]
  0001e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00021	89 11		 mov	 DWORD PTR [ecx], edx
  00023	89 41 04	 mov	 DWORD PTR [ecx+4], eax

; 142  : 		return (*this);

  00026	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 143  : 		}

  00029	8b e5		 mov	 esp, ebp
  0002b	5d		 pop	 ebp
  0002c	c2 04 00	 ret	 4
??Y?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAEAAV012@ABV012@@Z ENDP ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::operator+=
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second, COMDAT
; _this$ = ecx

; 301  : 		{	// return reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 302  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 303  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??0?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
__Plist$ = 12						; size = 4
??0?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z PROC ; std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >, COMDAT
; _this$ = ecx

; 117  : 		{	// construct with node pointer _Pnode

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 116  : 		: _Mybase(_Pnode, _Plist)

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Plist$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ??0?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>

; 118  : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 08 00	 ret	 8
??0?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ENDP ; std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ PROC ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++, COMDAT
; _this$ = ecx

; 199  : 		{	// preincrement

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 200  :  #if _ITERATOR_DEBUG_LEVEL != 0
; 201  : 		const auto _Mycont = static_cast<const _Mylist *>(this->_Getcont());
; 202  : 		_STL_ASSERT(_Mycont, "cannot increment value-initialized list iterator");
; 203  : 		_STL_VERIFY(this->_Ptr != _Mycont->_Myhead, "cannot increment end list iterator");
; 204  :  #endif /* _ITERATOR_DEBUG_LEVEL != 0 */
; 205  : 
; 206  : 		this->_Ptr = this->_Ptr->_Next;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 08		 mov	 ecx, DWORD PTR [eax]
  00013	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00016	8b 01		 mov	 eax, DWORD PTR [ecx]
  00018	89 02		 mov	 DWORD PTR [edx], eax

; 207  : 		return (*this);

  0001a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 208  : 		}

  0001d	8b e5		 mov	 esp, ebp
  0001f	5d		 pop	 ebp
  00020	c3		 ret	 0
??E?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV01@XZ ENDP ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::operator++
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??0?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
__Plist$ = 12						; size = 4
??0?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z PROC ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >, COMDAT
; _this$ = ecx

; 179  : 		{	// construct with node pointer _Pnode

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 178  : 		: _Mybase(_Pnode, _Plist)

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Plist$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ??0?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ; std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>

; 180  : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 08 00	 ret	 8
??0?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ENDP ; std::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >::_List_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAIXZ PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize, COMDAT
; _this$ = ecx

; 671  : 		{	// return reference to _Mysize

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 672  : 		return (_Get_data()._Mysize);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
  00016	83 c0 04	 add	 eax, 4

; 673  : 		}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAIXZ ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data, COMDAT
; _this$ = ecx

; 656  : 		{	// return const reference to _List_val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 657  : 		return (_Mypair._Get_second());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second

; 658  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Get_data@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Get_data
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal, COMDAT
; _this$ = ecx

; 641  : 		{	// return reference to allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 642  : 		return (_Mypair._Get_first());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first

; 643  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second, COMDAT
; _this$ = ecx

; 306  : 		{	// return const reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 307  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 308  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first, COMDAT
; _this$ = ecx

; 291  : 		{	// return reference to first

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 292  : 		return (*this);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 293  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??0?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
__Plist$ = 12						; size = 4
??0?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z PROC ; std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>, COMDAT
; _this$ = ecx

; 43   : 		{	// construct with node pointer _Pnode

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 42   : 		: _Ptr(_Pnode)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00014	89 08		 mov	 DWORD PTR [eax], ecx

; 44   : 		this->_Adopt(_Plist);

  00016	8b 55 0c	 mov	 edx, DWORD PTR __Plist$[ebp]
  00019	52		 push	 edx
  0001a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001d	e8 00 00 00 00	 call	 ?_Adopt@_Iterator_base0@std@@QAEXPBX@Z ; std::_Iterator_base0::_Adopt

; 45   : 		}

  00022	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00025	83 c4 04	 add	 esp, 4
  00028	3b ec		 cmp	 ebp, esp
  0002a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002f	8b e5		 mov	 esp, ebp
  00031	5d		 pop	 ebp
  00032	c2 08 00	 ret	 8
??0?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@1@@Z ENDP ; std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,std::_Iterator_base0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Buynode0@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@PAU32@0@Z
_TEXT	SEGMENT
__Al$ = -12						; size = 4
__Pnode$ = -8						; size = 4
_this$ = -4						; size = 4
__Next$ = 8						; size = 4
__Prev$ = 12						; size = 4
?_Buynode0@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@PAU32@0@Z PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Buynode0, COMDAT
; _this$ = ecx

; 609  : 		{	// allocate a node and set links

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 610  : 		_Nodeptr _Pnode = _Getal().allocate(1);

  0001e	6a 01		 push	 1
  00020	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00023	e8 00 00 00 00	 call	 ?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
  00028	8b c8		 mov	 ecx, eax
  0002a	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::allocate
  0002f	89 45 f8	 mov	 DWORD PTR __Pnode$[ebp], eax

; 611  : 
; 612  : 		if (_Next == _Nodeptr())

  00032	83 7d 08 00	 cmp	 DWORD PTR __Next$[ebp], 0
  00036	75 0c		 jne	 SHORT $LN2@Buynode0

; 613  : 			{	// point at self
; 614  : 			_Next = _Pnode;

  00038	8b 45 f8	 mov	 eax, DWORD PTR __Pnode$[ebp]
  0003b	89 45 08	 mov	 DWORD PTR __Next$[ebp], eax

; 615  : 			_Prev = _Pnode;

  0003e	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00041	89 4d 0c	 mov	 DWORD PTR __Prev$[ebp], ecx
$LN2@Buynode0:

; 616  : 			}
; 617  : 
; 618  : 		_Alnode& _Al = _Getal();

  00044	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00047	e8 00 00 00 00	 call	 ?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
  0004c	89 45 f4	 mov	 DWORD PTR __Al$[ebp], eax

; 619  : 		_TRY_BEGIN

  0004f	ba 01 00 00 00	 mov	 edx, 1
  00054	85 d2		 test	 edx, edx
  00056	74 3f		 je	 SHORT $LN3@Buynode0

; 620  : 		_Alnode_traits::construct(_Al, _STD addressof(_Pnode->_Next), _Next);

  00058	8d 45 08	 lea	 eax, DWORD PTR __Next$[ebp]
  0005b	50		 push	 eax
  0005c	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  0005f	51		 push	 ecx
  00060	e8 00 00 00 00	 call	 ??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ; std::addressof<std::_List_node<CMessageLoop2::Task,void *> *>
  00065	83 c4 04	 add	 esp, 4
  00068	50		 push	 eax
  00069	8b 55 f4	 mov	 edx, DWORD PTR __Al$[ebp]
  0006c	52		 push	 edx
  0006d	e8 00 00 00 00	 call	 ??$construct@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@AAPAU12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@AAPAU31@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<std::_List_node<CMessageLoop2::Task,void *> *,std::_List_node<CMessageLoop2::Task,void *> * &>
  00072	83 c4 0c	 add	 esp, 12			; 0000000cH

; 621  : 		_Alnode_traits::construct(_Al, _STD addressof(_Pnode->_Prev), _Prev);

  00075	8d 45 0c	 lea	 eax, DWORD PTR __Prev$[ebp]
  00078	50		 push	 eax
  00079	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  0007c	83 c1 04	 add	 ecx, 4
  0007f	51		 push	 ecx
  00080	e8 00 00 00 00	 call	 ??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ; std::addressof<std::_List_node<CMessageLoop2::Task,void *> *>
  00085	83 c4 04	 add	 esp, 4
  00088	50		 push	 eax
  00089	8b 55 f4	 mov	 edx, DWORD PTR __Al$[ebp]
  0008c	52		 push	 edx
  0008d	e8 00 00 00 00	 call	 ??$construct@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@AAPAU12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@AAPAU31@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<std::_List_node<CMessageLoop2::Task,void *> *,std::_List_node<CMessageLoop2::Task,void *> * &>
  00092	83 c4 0c	 add	 esp, 12			; 0000000cH
  00095	eb 12		 jmp	 SHORT $LN4@Buynode0
$LN3@Buynode0:

; 622  : 		_CATCH_ALL

  00097	33 c0		 xor	 eax, eax
  00099	74 0e		 je	 SHORT $LN4@Buynode0

; 623  : 		_Al.deallocate(_Pnode, 1);

  0009b	6a 01		 push	 1
  0009d	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  000a0	51		 push	 ecx
  000a1	8b 4d f4	 mov	 ecx, DWORD PTR __Al$[ebp]
  000a4	e8 00 00 00 00	 call	 ?deallocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEXQAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::deallocate
$LN4@Buynode0:

; 624  : 		_RERAISE;
; 625  : 		_CATCH_END
; 626  : 
; 627  : 		return (_Pnode);

  000a9	8b 45 f8	 mov	 eax, DWORD PTR __Pnode$[ebp]

; 628  : 		}

  000ac	83 c4 0c	 add	 esp, 12			; 0000000cH
  000af	3b ec		 cmp	 ebp, esp
  000b1	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b6	8b e5		 mov	 esp, ebp
  000b8	5d		 pop	 ebp
  000b9	c2 08 00	 ret	 8
?_Buynode0@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@PAU32@0@Z ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Buynode0
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?allocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Count$ = 8						; size = 4
?allocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z PROC ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::allocate, COMDAT
; _this$ = ecx

; 996  : 		{	// allocate array of _Count elements

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 997  : 		return (static_cast<_Ty *>(_Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count))));

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Count$[ebp]
  00011	50		 push	 eax
  00012	e8 00 00 00 00	 call	 ??$_Get_size_of_n@$0DI@@std@@YAII@Z ; std::_Get_size_of_n<56>
  00017	83 c4 04	 add	 esp, 4
  0001a	50		 push	 eax
  0001b	e8 00 00 00 00	 call	 ??$_Allocate@$07U_Default_allocate_traits@std@@$0A@@std@@YAPAXI@Z ; std::_Allocate<8,std::_Default_allocate_traits,0>
  00020	83 c4 04	 add	 esp, 4

; 998  : 		}

  00023	83 c4 04	 add	 esp, 4
  00026	3b ec		 cmp	 ebp, esp
  00028	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002d	8b e5		 mov	 esp, ebp
  0002f	5d		 pop	 ebp
  00030	c2 04 00	 ret	 4
?allocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ENDP ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?deallocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEXQAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Ptr$ = 8						; size = 4
__Count$ = 12						; size = 4
?deallocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEXQAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z PROC ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::deallocate, COMDAT
; _this$ = ecx

; 990  : 		{	// deallocate object at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 991  : 		// no overflow check on the following multiply; we assume _Allocate did that check
; 992  : 		_Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0000e	6b 45 0c 38	 imul	 eax, DWORD PTR __Count$[ebp], 56
  00012	50		 push	 eax
  00013	8b 4d 08	 mov	 ecx, DWORD PTR __Ptr$[ebp]
  00016	51		 push	 ecx
  00017	e8 00 00 00 00	 call	 ??$_Deallocate@$07$0A@@std@@YAXPAXI@Z ; std::_Deallocate<8,0>
  0001c	83 c4 08	 add	 esp, 8

; 993  : 		}

  0001f	83 c4 04	 add	 esp, 4
  00022	3b ec		 cmp	 ebp, esp
  00024	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00029	8b e5		 mov	 esp, ebp
  0002b	5d		 pop	 ebp
  0002c	c2 08 00	 ret	 8
?deallocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEXQAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ENDP ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::deallocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Val$ = 8						; size = 4
??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z PROC ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>, COMDAT
; _this$ = ecx

; 89   : 		{	// construct from representation

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 88   : 			: _MyRep(static_cast<_Rep>(_Val))

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Val$[ebp]
  00014	8b 11		 mov	 edx, DWORD PTR [ecx]
  00016	89 10		 mov	 DWORD PTR [eax], edx
  00018	8b 49 04	 mov	 ecx, DWORD PTR [ecx+4]
  0001b	89 48 04	 mov	 DWORD PTR [eax+4], ecx

; 90   : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	8b e5		 mov	 esp, ebp
  00023	5d		 pop	 ebp
  00024	c2 04 00	 ret	 4
??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ENDP ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??R?$_Func_class@X$$V@std@@QBEXXZ
_TEXT	SEGMENT
__Impl$ = -8						; size = 4
_this$ = -4						; size = 4
??R?$_Func_class@X$$V@std@@QBEXXZ PROC			; std::_Func_class<void>::operator(), COMDAT
; _this$ = ecx
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  00018	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001b	e8 00 00 00 00	 call	 ?_Empty@?$_Func_class@X$$V@std@@IBE_NXZ ; std::_Func_class<void>::_Empty
  00020	0f b6 c0	 movzx	 eax, al
  00023	85 c0		 test	 eax, eax
  00025	74 05		 je	 SHORT $LN2@operator
  00027	e8 00 00 00 00	 call	 ?_Xbad_function_call@std@@YAXXZ ; std::_Xbad_function_call
$LN2@operator:
  0002c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002f	e8 00 00 00 00	 call	 ?_Getimpl@?$_Func_class@X$$V@std@@ABEPAV?$_Func_base@X$$V@2@XZ ; std::_Func_class<void>::_Getimpl
  00034	89 45 f8	 mov	 DWORD PTR __Impl$[ebp], eax
  00037	8b 4d f8	 mov	 ecx, DWORD PTR __Impl$[ebp]
  0003a	8b 11		 mov	 edx, DWORD PTR [ecx]
  0003c	8b f4		 mov	 esi, esp
  0003e	8b 4d f8	 mov	 ecx, DWORD PTR __Impl$[ebp]
  00041	8b 42 08	 mov	 eax, DWORD PTR [edx+8]
  00044	ff d0		 call	 eax
  00046	3b f4		 cmp	 esi, esp
  00048	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN3@operator:
  0004d	5e		 pop	 esi
  0004e	83 c4 08	 add	 esp, 8
  00051	3b ec		 cmp	 ebp, esp
  00053	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00058	8b e5		 mov	 esp, ebp
  0005a	5d		 pop	 ebp
  0005b	c3		 ret	 0
??R?$_Func_class@X$$V@std@@QBEXXZ ENDP			; std::_Func_class<void>::operator()
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??$?0HX@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QAE@ABH@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Val$ = 8						; size = 4
??$?0HX@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QAE@ABH@Z PROC ; std::chrono::duration<__int64,std::ratio<1,1000> >::duration<__int64,std::ratio<1,1000> ><int,void>, COMDAT
; _this$ = ecx

; 89   : 		{	// construct from representation

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 88   : 			: _MyRep(static_cast<_Rep>(_Val))

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]
  00011	8b 00		 mov	 eax, DWORD PTR [eax]
  00013	99		 cdq
  00014	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00017	89 01		 mov	 DWORD PTR [ecx], eax
  00019	89 51 04	 mov	 DWORD PTR [ecx+4], edx

; 90   : 		}

  0001c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001f	8b e5		 mov	 esp, ebp
  00021	5d		 pop	 ebp
  00022	c2 04 00	 ret	 4
??$?0HX@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QAE@ABH@Z ENDP ; std::chrono::duration<__int64,std::ratio<1,1000> >::duration<__int64,std::ratio<1,1000> ><int,void>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??$?0_JU?$ratio@$00$0DOI@@std@@X@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@12@@Z
_TEXT	SEGMENT
$T1 = -12						; size = 8
_this$ = -4						; size = 4
__Dur$ = 8						; size = 4
??$?0_JU?$ratio@$00$0DOI@@std@@X@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@12@@Z PROC ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,std::ratio<1,1000>,void>, COMDAT
; _this$ = ecx

; 99   : 		{	// construct from a duration

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 98   : 			: _MyRep(chrono::duration_cast<duration>(_Dur).count())

  0001e	8b 45 08	 mov	 eax, DWORD PTR __Dur$[ebp]
  00021	50		 push	 eax
  00022	8d 4d f4	 lea	 ecx, DWORD PTR $T1[ebp]
  00025	51		 push	 ecx
  00026	e8 00 00 00 00	 call	 ??$duration_cast@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@_JU?$ratio@$00$0DOI@@3@X@chrono@std@@YA?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@01@@Z ; std::chrono::duration_cast<std::chrono::duration<__int64,std::ratio<1,1000000000> >,__int64,std::ratio<1,1000>,void>
  0002b	83 c4 08	 add	 esp, 8
  0002e	8b c8		 mov	 ecx, eax
  00030	e8 00 00 00 00	 call	 ?count@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::count
  00035	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00038	89 01		 mov	 DWORD PTR [ecx], eax
  0003a	89 51 04	 mov	 DWORD PTR [ecx+4], edx

; 100  : 		}

  0003d	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00040	83 c4 0c	 add	 esp, 12			; 0000000cH
  00043	3b ec		 cmp	 ebp, esp
  00045	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004a	8b e5		 mov	 esp, ebp
  0004c	5d		 pop	 ebp
  0004d	c2 04 00	 ret	 4
??$?0_JU?$ratio@$00$0DOI@@std@@X@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@12@@Z ENDP ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,std::ratio<1,1000>,void>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??$?NUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z
_TEXT	SEGMENT
tv69 = -4						; size = 4
__Left$ = 8						; size = 4
__Right$ = 12						; size = 4
??$?NUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z PROC ; std::chrono::operator<=<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >, COMDAT

; 693  : 	{	// test for time_point <= time_point

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 694  : 	return (!(_Right < _Left));

  0000b	8b 45 08	 mov	 eax, DWORD PTR __Left$[ebp]
  0000e	50		 push	 eax
  0000f	8b 4d 0c	 mov	 ecx, DWORD PTR __Right$[ebp]
  00012	51		 push	 ecx
  00013	e8 00 00 00 00	 call	 ??$?MUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z ; std::chrono::operator<<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
  00018	83 c4 08	 add	 esp, 8
  0001b	0f b6 d0	 movzx	 edx, al
  0001e	85 d2		 test	 edx, edx
  00020	75 09		 jne	 SHORT $LN3@operator
  00022	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR tv69[ebp], 1
  00029	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR tv69[ebp], 0
$LN4@operator:
  00032	8a 45 fc	 mov	 al, BYTE PTR tv69[ebp]

; 695  : 	}

  00035	83 c4 04	 add	 esp, 4
  00038	3b ec		 cmp	 ebp, esp
  0003a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003f	8b e5		 mov	 esp, ebp
  00041	5d		 pop	 ebp
  00042	c3		 ret	 0
??$?NUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z ENDP ; std::chrono::operator<=<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$move@AAUTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$move@AAUTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z PROC ; std::move<CMessageLoop2::Task &>, COMDAT

; 1588 : 	{	// forward _Arg as movable

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1589 : 	return (static_cast<remove_reference_t<_Ty>&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1590 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$move@AAUTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ENDP ; std::move<CMessageLoop2::Task &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstddef
;	COMDAT ??$addressof@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@AAV10@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
??$addressof@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@AAV10@@Z PROC ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >, COMDAT

; 329  : 	{	// return address of _Val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 330  : 	return (__builtin_addressof(_Val));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]

; 331  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$addressof@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPAV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@AAV10@@Z ENDP ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??$_Insert@UTask@CMessageLoop2@@@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@1@$$QAUTask@CMessageLoop2@@@Z
_TEXT	SEGMENT
__Newnode$ = -16					; size = 4
__Leftnode$ = -12					; size = 4
__Rightnode$ = -8					; size = 4
_this$ = -4						; size = 4
__Where$ = 8						; size = 4
_<_Val_0>$ = 12						; size = 4
??$_Insert@UTask@CMessageLoop2@@@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@1@$$QAUTask@CMessageLoop2@@@Z PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Insert<CMessageLoop2::Task>, COMDAT
; _this$ = ecx

; 947  : 		{	// insert element at _Where

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 10	 sub	 esp, 16			; 00000010H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0000e	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00011	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00014	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  00017	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 948  : 		const _Nodeptr _Rightnode = _Where._Ptr;

  0001a	8b 45 08	 mov	 eax, DWORD PTR __Where$[ebp]
  0001d	89 45 f8	 mov	 DWORD PTR __Rightnode$[ebp], eax

; 949  : 		const _Nodeptr _Leftnode = _Rightnode->_Prev;

  00020	8b 4d f8	 mov	 ecx, DWORD PTR __Rightnode$[ebp]
  00023	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  00026	89 55 f4	 mov	 DWORD PTR __Leftnode$[ebp], edx

; 950  : 		const _Nodeptr _Newnode = this->_Buynode(_Rightnode, _Leftnode, _STD forward<_Valty>(_Val)...);

  00029	8b 45 0c	 mov	 eax, DWORD PTR _<_Val_0>$[ebp]
  0002c	50		 push	 eax
  0002d	e8 00 00 00 00	 call	 ??$forward@UTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::forward<CMessageLoop2::Task>
  00032	83 c4 04	 add	 esp, 4
  00035	50		 push	 eax
  00036	8b 4d f4	 mov	 ecx, DWORD PTR __Leftnode$[ebp]
  00039	51		 push	 ecx
  0003a	8b 55 f8	 mov	 edx, DWORD PTR __Rightnode$[ebp]
  0003d	52		 push	 edx
  0003e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00041	e8 00 00 00 00	 call	 ??$_Buynode@UTask@CMessageLoop2@@@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PAU21@0$$QAUTask@CMessageLoop2@@@Z ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Buynode<CMessageLoop2::Task>
  00046	89 45 f0	 mov	 DWORD PTR __Newnode$[ebp], eax

; 951  : 		_Incsize(1);

  00049	6a 01		 push	 1
  0004b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0004e	e8 00 00 00 00	 call	 ?_Incsize@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXI@Z ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Incsize

; 952  : 		_Rightnode->_Prev = _Newnode;

  00053	8b 45 f8	 mov	 eax, DWORD PTR __Rightnode$[ebp]
  00056	8b 4d f0	 mov	 ecx, DWORD PTR __Newnode$[ebp]
  00059	89 48 04	 mov	 DWORD PTR [eax+4], ecx

; 953  : 		_Leftnode->_Next = _Newnode;

  0005c	8b 55 f4	 mov	 edx, DWORD PTR __Leftnode$[ebp]
  0005f	8b 45 f0	 mov	 eax, DWORD PTR __Newnode$[ebp]
  00062	89 02		 mov	 DWORD PTR [edx], eax

; 954  : 		}

  00064	83 c4 10	 add	 esp, 16			; 00000010H
  00067	3b ec		 cmp	 ebp, esp
  00069	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0006e	8b e5		 mov	 esp, ebp
  00070	5d		 pop	 ebp
  00071	c2 08 00	 ret	 8
??$_Insert@UTask@CMessageLoop2@@@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXV?$_List_unchecked_const_iterator@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@U_Iterator_base0@2@@1@$$QAUTask@CMessageLoop2@@@Z ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Insert<CMessageLoop2::Task>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$move@AAV?$function@$$A6AXXZ@std@@@std@@YA$$QAV?$function@$$A6AXXZ@0@AAV10@@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$move@AAV?$function@$$A6AXXZ@std@@@std@@YA$$QAV?$function@$$A6AXXZ@0@AAV10@@Z PROC ; std::move<std::function<void __cdecl(void)> &>, COMDAT

; 1588 : 	{	// forward _Arg as movable

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1589 : 	return (static_cast<remove_reference_t<_Ty>&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1590 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$move@AAV?$function@$$A6AXXZ@std@@@std@@YA$$QAV?$function@$$A6AXXZ@0@AAV10@@Z ENDP ; std::move<std::function<void __cdecl(void)> &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstddef
;	COMDAT ??$addressof@Vmutex@std@@@std@@YAPAVmutex@0@AAV10@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
??$addressof@Vmutex@std@@@std@@YAPAVmutex@0@AAV10@@Z PROC ; std::addressof<std::mutex>, COMDAT

; 329  : 	{	// return address of _Val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 330  : 	return (__builtin_addressof(_Val));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]

; 331  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$addressof@Vmutex@std@@@std@@YAPAVmutex@0@AAV10@@Z ENDP ; std::addressof<std::mutex>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstddef
;	COMDAT ??$addressof@UTask@CMessageLoop2@@@std@@YAPAUTask@CMessageLoop2@@AAU12@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
??$addressof@UTask@CMessageLoop2@@@std@@YAPAUTask@CMessageLoop2@@AAU12@@Z PROC ; std::addressof<CMessageLoop2::Task>, COMDAT

; 329  : 	{	// return address of _Val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 330  : 	return (__builtin_addressof(_Val));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]

; 331  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$addressof@UTask@CMessageLoop2@@@std@@YAPAUTask@CMessageLoop2@@AAU12@@Z ENDP ; std::addressof<CMessageLoop2::Task>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$destroy@UTask@CMessageLoop2@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
__Ptr$ = 12						; size = 4
??$destroy@UTask@CMessageLoop2@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@@Z PROC ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<CMessageLoop2::Task>, COMDAT

; 886  : 		{	// destroy object at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 887  : 		_Ptr->~_Uty();

  00003	6a 00		 push	 0
  00005	8b 4d 0c	 mov	 ecx, DWORD PTR __Ptr$[ebp]
  00008	e8 00 00 00 00	 call	 ??_GTask@CMessageLoop2@@QAEPAXI@Z

; 888  : 		}

  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??$destroy@UTask@CMessageLoop2@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@@Z ENDP ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<CMessageLoop2::Task>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z
_TEXT	SEGMENT
__Node_alloc$ = -17					; size = 1
__$EHRec$ = -12						; size = 12
__Al$ = 8						; size = 4
__Ptr$ = 12						; size = 4
??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z PROC ; std::_List_node<CMessageLoop2::Task,void *>::_Freenode0<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >, COMDAT

; 368  : 			{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 0c	 sub	 esp, 12			; 0000000cH
  0001b	c7 45 e8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-24], -858993460 ; ccccccccH
  00022	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00029	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH

; 369  : 			using _Alnode = _Rebind_alloc_t<_Alloc, _List_node>;
; 370  : 			using _Alnode_traits = allocator_traits<_Alnode>;
; 371  : 			_Alnode _Node_alloc(_Al);
; 372  : 			_Alnode_traits::destroy(_Node_alloc, _STD addressof(_Ptr->_Next));

  00030	8b 45 0c	 mov	 eax, DWORD PTR __Ptr$[ebp]
  00033	50		 push	 eax
  00034	e8 00 00 00 00	 call	 ??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ; std::addressof<std::_List_node<CMessageLoop2::Task,void *> *>
  00039	83 c4 04	 add	 esp, 4
  0003c	50		 push	 eax
  0003d	8d 4d ef	 lea	 ecx, DWORD PTR __Node_alloc$[ebp]
  00040	51		 push	 ecx
  00041	e8 00 00 00 00	 call	 ??$destroy@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<std::_List_node<CMessageLoop2::Task,void *> *>
  00046	83 c4 08	 add	 esp, 8

; 373  : 			_Alnode_traits::destroy(_Node_alloc, _STD addressof(_Ptr->_Prev));

  00049	8b 55 0c	 mov	 edx, DWORD PTR __Ptr$[ebp]
  0004c	83 c2 04	 add	 edx, 4
  0004f	52		 push	 edx
  00050	e8 00 00 00 00	 call	 ??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ; std::addressof<std::_List_node<CMessageLoop2::Task,void *> *>
  00055	83 c4 04	 add	 esp, 4
  00058	50		 push	 eax
  00059	8d 45 ef	 lea	 eax, DWORD PTR __Node_alloc$[ebp]
  0005c	50		 push	 eax
  0005d	e8 00 00 00 00	 call	 ??$destroy@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<std::_List_node<CMessageLoop2::Task,void *> *>
  00062	83 c4 08	 add	 esp, 8

; 374  : 			_Alnode_traits::deallocate(_Node_alloc, _Ptr, 1);

  00065	6a 01		 push	 1
  00067	8b 4d 0c	 mov	 ecx, DWORD PTR __Ptr$[ebp]
  0006a	51		 push	 ecx
  0006b	8d 55 ef	 lea	 edx, DWORD PTR __Node_alloc$[ebp]
  0006e	52		 push	 edx
  0006f	e8 00 00 00 00	 call	 ?deallocate@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@QAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::deallocate
  00074	83 c4 0c	 add	 esp, 12			; 0000000cH

; 375  : 			}

  00077	52		 push	 edx
  00078	8b cd		 mov	 ecx, ebp
  0007a	50		 push	 eax
  0007b	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN6@Freenode0
  00081	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00086	58		 pop	 eax
  00087	5a		 pop	 edx
  00088	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0008b	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00092	83 c4 18	 add	 esp, 24			; 00000018H
  00095	3b ec		 cmp	 ebp, esp
  00097	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0009c	8b e5		 mov	 esp, ebp
  0009e	5d		 pop	 ebp
  0009f	c3		 ret	 0
$LN6@Freenode0:
  000a0	01 00 00 00	 DD	 1
  000a4	00 00 00 00	 DD	 $LN5@Freenode0
$LN5@Freenode0:
  000a8	ef ff ff ff	 DD	 -17			; ffffffefH
  000ac	01 00 00 00	 DD	 1
  000b0	00 00 00 00	 DD	 $LN3@Freenode0
$LN3@Freenode0:
  000b4	5f		 DB	 95			; 0000005fH
  000b5	4e		 DB	 78			; 0000004eH
  000b6	6f		 DB	 111			; 0000006fH
  000b7	64		 DB	 100			; 00000064H
  000b8	65		 DB	 101			; 00000065H
  000b9	5f		 DB	 95			; 0000005fH
  000ba	61		 DB	 97			; 00000061H
  000bb	6c		 DB	 108			; 0000006cH
  000bc	6c		 DB	 108			; 0000006cH
  000bd	6f		 DB	 111			; 0000006fH
  000be	63		 DB	 99			; 00000063H
  000bf	00		 DB	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??$_Freenode0@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@?$_List_node@UTask@CMessageLoop2@@PAX@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@PAU01@@Z ENDP ; std::_List_node<CMessageLoop2::Task,void *>::_Freenode0<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstddef
;	COMDAT ??$addressof@$$CBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@ABV10@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
??$addressof@$$CBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@ABV10@@Z PROC ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > const >, COMDAT

; 329  : 	{	// return address of _Val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 330  : 	return (__builtin_addressof(_Val));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]

; 331  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$addressof@$$CBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@std@@@std@@YAPBV?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@0@ABV10@@Z ENDP ; std::addressof<std::_List_val<std::_List_simple_types<CMessageLoop2::Task> > const >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstddef
;	COMDAT ??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z PROC ; std::addressof<std::_List_node<CMessageLoop2::Task,void *> *>, COMDAT

; 329  : 	{	// return address of _Val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 330  : 	return (__builtin_addressof(_Val));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]

; 331  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$addressof@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAPAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ENDP ; std::addressof<std::_List_node<CMessageLoop2::Task,void *> *>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$construct@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@AAPAU12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@AAPAU31@@Z
_TEXT	SEGMENT
$T1 = -4						; size = 4
___formal$ = 8						; size = 4
__Ptr$ = 12						; size = 4
_<_Args_0>$ = 16					; size = 4
??$construct@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@AAPAU12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@AAPAU31@@Z PROC ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<std::_List_node<CMessageLoop2::Task,void *> *,std::_List_node<CMessageLoop2::Task,void *> * &>, COMDAT

; 879  : 		{	// construct _Objty(_Types...) at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 880  : 		::new (const_cast<void *>(static_cast<const volatile void *>(_Ptr)))

  0000b	8b 45 0c	 mov	 eax, DWORD PTR __Ptr$[ebp]
  0000e	50		 push	 eax
  0000f	6a 04		 push	 4
  00011	e8 00 00 00 00	 call	 ??2@YAPAXIPAX@Z		; operator new
  00016	83 c4 08	 add	 esp, 8
  00019	89 45 fc	 mov	 DWORD PTR $T1[ebp], eax
  0001c	8b 4d 10	 mov	 ecx, DWORD PTR _<_Args_0>$[ebp]
  0001f	51		 push	 ecx
  00020	e8 00 00 00 00	 call	 ??$forward@AAPAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ; std::forward<std::_List_node<CMessageLoop2::Task,void *> * &>
  00025	83 c4 04	 add	 esp, 4
  00028	8b 55 fc	 mov	 edx, DWORD PTR $T1[ebp]
  0002b	8b 00		 mov	 eax, DWORD PTR [eax]
  0002d	89 02		 mov	 DWORD PTR [edx], eax

; 881  : 			_Objty(_STD forward<_Types>(_Args)...);
; 882  : 		}

  0002f	83 c4 04	 add	 esp, 4
  00032	3b ec		 cmp	 ebp, esp
  00034	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00039	8b e5		 mov	 esp, ebp
  0003b	5d		 pop	 ebp
  0003c	c3		 ret	 0
??$construct@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@AAPAU12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@AAPAU31@@Z ENDP ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<std::_List_node<CMessageLoop2::Task,void *> *,std::_List_node<CMessageLoop2::Task,void *> * &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$_Get_size_of_n@$0DI@@std@@YAII@Z
_TEXT	SEGMENT
__Result$ = -8						; size = 4
__Max_possible$ = -4					; size = 4
__Count$ = 8						; size = 4
??$_Get_size_of_n@$0DI@@std@@YAII@Z PROC		; std::_Get_size_of_n<56>, COMDAT

; 23   : 	{	// gets the size of _Count copies of a type sized _Ty_size

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 24   : 	constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00014	c7 45 fc 24 49
	92 04		 mov	 DWORD PTR __Max_possible$[ebp], 76695844 ; 04924924H

; 25   : 	size_t _Result = _Count * _Ty_size;

  0001b	6b 45 08 38	 imul	 eax, DWORD PTR __Count$[ebp], 56
  0001f	89 45 f8	 mov	 DWORD PTR __Result$[ebp], eax

; 26   : 	if (_Max_possible < _Count)

  00022	81 7d 08 24 49
	92 04		 cmp	 DWORD PTR __Count$[ebp], 76695844 ; 04924924H
  00029	76 07		 jbe	 SHORT $LN2@Get_size_o

; 27   : 		{	// multiply overflow, try allocating all of memory and assume the
; 28   : 			// allocation function will throw bad_alloc
; 29   : 		_Result = static_cast<size_t>(-1);

  0002b	c7 45 f8 ff ff
	ff ff		 mov	 DWORD PTR __Result$[ebp], -1
$LN2@Get_size_o:

; 30   : 		}
; 31   : 
; 32   : 	return (_Result);

  00032	8b 45 f8	 mov	 eax, DWORD PTR __Result$[ebp]

; 33   : 	}

  00035	8b e5		 mov	 esp, ebp
  00037	5d		 pop	 ebp
  00038	c3		 ret	 0
??$_Get_size_of_n@$0DI@@std@@YAII@Z ENDP		; std::_Get_size_of_n<56>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_GTask@CMessageLoop2@@QAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___flags$ = 8						; size = 4
??_GTask@CMessageLoop2@@QAEPAXI@Z PROC			; CMessageLoop2::Task::`scalar deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??1Task@CMessageLoop2@@QAE@XZ
  00016	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  00019	83 e0 01	 and	 eax, 1
  0001c	74 0e		 je	 SHORT $LN2@scalar
  0001e	6a 30		 push	 48			; 00000030H
  00020	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00023	51		 push	 ecx
  00024	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  00029	83 c4 08	 add	 esp, 8
$LN2@scalar:
  0002c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002f	83 c4 04	 add	 esp, 4
  00032	3b ec		 cmp	 ebp, esp
  00034	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00039	8b e5		 mov	 esp, ebp
  0003b	5d		 pop	 ebp
  0003c	c2 04 00	 ret	 4
??_GTask@CMessageLoop2@@QAEPAXI@Z ENDP			; CMessageLoop2::Task::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?deallocate@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@QAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
__Ptr$ = 12						; size = 4
__Count$ = 16						; size = 4
?deallocate@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@QAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z PROC ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::deallocate, COMDAT

; 871  : 		{	// deallocate _Count elements at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 872  : 		// no overflow check on the following multiply; we assume _Allocate did that check
; 873  : 		_Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00003	6b 45 10 38	 imul	 eax, DWORD PTR __Count$[ebp], 56
  00007	50		 push	 eax
  00008	8b 4d 0c	 mov	 ecx, DWORD PTR __Ptr$[ebp]
  0000b	51		 push	 ecx
  0000c	e8 00 00 00 00	 call	 ??$_Deallocate@$07$0A@@std@@YAXPAXI@Z ; std::_Deallocate<8,0>
  00011	83 c4 08	 add	 esp, 8

; 874  : 		}

  00014	3b ec		 cmp	 ebp, esp
  00016	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001b	5d		 pop	 ebp
  0001c	c3		 ret	 0
?deallocate@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@QAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ENDP ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::deallocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Incsize@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXI@Z
_TEXT	SEGMENT
tv82 = -8						; size = 4
_this$ = -4						; size = 4
__Count$ = 8						; size = 4
?_Incsize@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXI@Z PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Incsize, COMDAT
; _this$ = ecx

; 1796 : 		{	// alter element count, with checking

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1797 : 		if (max_size() - this->_Mysize() - 1 < _Count)

  00018	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001b	e8 00 00 00 00	 call	 ?max_size@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBEIXZ ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::max_size
  00020	8b f0		 mov	 esi, eax
  00022	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00025	e8 00 00 00 00	 call	 ?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAIXZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
  0002a	2b 30		 sub	 esi, DWORD PTR [eax]
  0002c	83 ee 01	 sub	 esi, 1
  0002f	3b 75 08	 cmp	 esi, DWORD PTR __Count$[ebp]
  00032	73 0a		 jae	 SHORT $LN2@Incsize

; 1798 : 			_Xlength_error("list<T> too long");

  00034	68 00 00 00 00	 push	 OFFSET ??_C@_0BB@MOGOBHAF@list?$DMT?$DO?5too?5long@
  00039	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPBD@Z ; std::_Xlength_error
$LN2@Incsize:

; 1799 : 		this->_Mysize() += _Count;

  0003e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00041	e8 00 00 00 00	 call	 ?_Mysize@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAIXZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Mysize
  00046	89 45 f8	 mov	 DWORD PTR tv82[ebp], eax
  00049	8b 45 f8	 mov	 eax, DWORD PTR tv82[ebp]
  0004c	8b 08		 mov	 ecx, DWORD PTR [eax]
  0004e	03 4d 08	 add	 ecx, DWORD PTR __Count$[ebp]
  00051	8b 55 f8	 mov	 edx, DWORD PTR tv82[ebp]
  00054	89 0a		 mov	 DWORD PTR [edx], ecx
$LN3@Incsize:

; 1800 : 		}

  00056	5e		 pop	 esi
  00057	83 c4 08	 add	 esp, 8
  0005a	3b ec		 cmp	 ebp, esp
  0005c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00061	8b e5		 mov	 esp, ebp
  00063	5d		 pop	 ebp
  00064	c2 04 00	 ret	 4
?_Incsize@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEXI@Z ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Incsize
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ?count@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QBE_JXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?count@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QBE_JXZ PROC ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::count, COMDAT
; _this$ = ecx

; 103  : 		{	// get stored rep

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 104  : 		return (_MyRep);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	8b 01		 mov	 eax, DWORD PTR [ecx]
  00013	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]

; 105  : 		}

  00016	8b e5		 mov	 esp, ebp
  00018	5d		 pop	 ebp
  00019	c3		 ret	 0
?count@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QBE_JXZ ENDP ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::count
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?max_size@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBEIXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?max_size@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBEIXZ PROC ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::max_size, COMDAT
; _this$ = ecx

; 1129 : 		{	// return maximum possible length of sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1130 : 		return (_Alnode_traits::max_size(this->_Getal()));

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
  00016	50		 push	 eax
  00017	e8 00 00 00 00	 call	 ?max_size@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAIABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::max_size
  0001c	83 c4 04	 add	 esp, 4

; 1131 : 		}

  0001f	83 c4 04	 add	 esp, 4
  00022	3b ec		 cmp	 ebp, esp
  00024	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00029	8b e5		 mov	 esp, ebp
  0002b	5d		 pop	 ebp
  0002c	c3		 ret	 0
?max_size@?$list@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QBEIXZ ENDP ; std::list<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ?max_size@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAIABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
?max_size@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAIABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@@Z PROC ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::max_size, COMDAT

; 891  : 		{	// get maximum size

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 892  : 		return (static_cast<size_t>(-1) / sizeof(value_type));

  00003	b8 24 49 92 04	 mov	 eax, 76695844		; 04924924H

; 893  : 		}

  00008	5d		 pop	 ebp
  00009	c3		 ret	 0
?max_size@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAIABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@@Z ENDP ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ PROC ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal, COMDAT
; _this$ = ecx

; 646  : 		{	// return const reference to allocator

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 647  : 		return (_Mypair._Get_first());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first

; 648  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ENDP ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first, COMDAT
; _this$ = ecx

; 296  : 		{	// return const reference to first

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 297  : 		return (*this);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 298  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_first@?$_Compressed_pair@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@V?$_List_val@U?$_List_simple_types@UTask@CMessageLoop2@@@std@@@2@$00@std@@QBEABV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<std::_List_node<CMessageLoop2::Task,void *> >,std::_List_val<std::_List_simple_types<CMessageLoop2::Task> >,1>::_Get_first
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??$duration_cast@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@_JU?$ratio@$00$0DOI@@3@X@chrono@std@@YA?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@01@@Z
_TEXT	SEGMENT
tv87 = -104						; size = 4
tv90 = -100						; size = 4
tv93 = -96						; size = 4
$T1 = -92						; size = 8
$T2 = -84						; size = 8
$T3 = -76						; size = 8
$T4 = -68						; size = 8
$T5 = -60						; size = 4
$T6 = -56						; size = 8
$T7 = -48						; size = 8
$T8 = -40						; size = 8
$T9 = -32						; size = 4
$T10 = -28						; size = 8
$T11 = -20						; size = 8
$T12 = -12						; size = 8
$T13 = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
__Dur$ = 12						; size = 4
??$duration_cast@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@_JU?$ratio@$00$0DOI@@3@X@chrono@std@@YA?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@01@@Z PROC ; std::chrono::duration_cast<std::chrono::duration<__int64,std::ratio<1,1000000000> >,__int64,std::ratio<1,1000>,void>, COMDAT

; 499  : 	{	// convert duration to another duration; truncate

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 68	 sub	 esp, 104		; 00000068H
  00006	57		 push	 edi
  00007	8d 7d 98	 lea	 edi, DWORD PTR [ebp-104]
  0000a	b9 1a 00 00 00	 mov	 ecx, 26			; 0000001aH
  0000f	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00014	f3 ab		 rep stosd

; 500  : 	using _CF = ratio_divide<_Period, typename _To::period>;
; 501  : 
; 502  : 	using _ToRep = typename _To::rep;
; 503  : 	using _CR = common_type_t<_ToRep, _Rep, intmax_t>;
; 504  : 
; 505  : #pragma warning(push)
; 506  : #pragma warning(disable: 6326)	// Potential comparison of a constant with another constant.
; 507  : 	return (_CF::num == 1 && _CF::den == 1

  00016	33 c0		 xor	 eax, eax
  00018	74 22		 je	 SHORT $LN7@duration_c
  0001a	8b 4d 0c	 mov	 ecx, DWORD PTR __Dur$[ebp]
  0001d	e8 00 00 00 00	 call	 ?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000> >::count
  00022	89 45 ec	 mov	 DWORD PTR $T11[ebp], eax
  00025	89 55 f0	 mov	 DWORD PTR $T11[ebp+4], edx
  00028	8d 4d ec	 lea	 ecx, DWORD PTR $T11[ebp]
  0002b	51		 push	 ecx
  0002c	8d 4d f4	 lea	 ecx, DWORD PTR $T12[ebp]
  0002f	e8 00 00 00 00	 call	 ??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
  00034	89 45 a0	 mov	 DWORD PTR tv93[ebp], eax
  00037	e9 b8 00 00 00	 jmp	 $LN8@duration_c
$LN7@duration_c:
  0003c	ba 01 00 00 00	 mov	 edx, 1
  00041	85 d2		 test	 edx, edx
  00043	74 2d		 je	 SHORT $LN5@duration_c
  00045	8b 4d 0c	 mov	 ecx, DWORD PTR __Dur$[ebp]
  00048	e8 00 00 00 00	 call	 ?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000> >::count
  0004d	6a 00		 push	 0
  0004f	68 40 42 0f 00	 push	 1000000			; 000f4240H
  00054	52		 push	 edx
  00055	50		 push	 eax
  00056	e8 00 00 00 00	 call	 __allmul
  0005b	89 45 d0	 mov	 DWORD PTR $T7[ebp], eax
  0005e	89 55 d4	 mov	 DWORD PTR $T7[ebp+4], edx
  00061	8d 45 d0	 lea	 eax, DWORD PTR $T7[ebp]
  00064	50		 push	 eax
  00065	8d 4d d8	 lea	 ecx, DWORD PTR $T8[ebp]
  00068	e8 00 00 00 00	 call	 ??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
  0006d	89 45 9c	 mov	 DWORD PTR tv90[ebp], eax
  00070	eb 68		 jmp	 SHORT $LN6@duration_c
$LN5@duration_c:
  00072	33 c9		 xor	 ecx, ecx
  00074	74 1f		 je	 SHORT $LN3@duration_c
  00076	8b 4d 0c	 mov	 ecx, DWORD PTR __Dur$[ebp]
  00079	e8 00 00 00 00	 call	 ?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000> >::count
  0007e	89 45 b4	 mov	 DWORD PTR $T3[ebp], eax
  00081	89 55 b8	 mov	 DWORD PTR $T3[ebp+4], edx
  00084	8d 55 b4	 lea	 edx, DWORD PTR $T3[ebp]
  00087	52		 push	 edx
  00088	8d 4d bc	 lea	 ecx, DWORD PTR $T4[ebp]
  0008b	e8 00 00 00 00	 call	 ??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
  00090	89 45 98	 mov	 DWORD PTR tv87[ebp], eax
  00093	eb 2b		 jmp	 SHORT $LN4@duration_c
$LN3@duration_c:
  00095	8b 4d 0c	 mov	 ecx, DWORD PTR __Dur$[ebp]
  00098	e8 00 00 00 00	 call	 ?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000> >::count
  0009d	6a 00		 push	 0
  0009f	68 40 42 0f 00	 push	 1000000			; 000f4240H
  000a4	52		 push	 edx
  000a5	50		 push	 eax
  000a6	e8 00 00 00 00	 call	 __allmul
  000ab	89 45 a4	 mov	 DWORD PTR $T1[ebp], eax
  000ae	89 55 a8	 mov	 DWORD PTR $T1[ebp+4], edx
  000b1	8d 45 a4	 lea	 eax, DWORD PTR $T1[ebp]
  000b4	50		 push	 eax
  000b5	8d 4d ac	 lea	 ecx, DWORD PTR $T2[ebp]
  000b8	e8 00 00 00 00	 call	 ??$?0_JX@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QAE@AB_J@Z ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::duration<__int64,std::ratio<1,1000000000> ><__int64,void>
  000bd	89 45 98	 mov	 DWORD PTR tv87[ebp], eax
$LN4@duration_c:
  000c0	8b 4d 98	 mov	 ecx, DWORD PTR tv87[ebp]
  000c3	89 4d c4	 mov	 DWORD PTR $T5[ebp], ecx
  000c6	8b 55 c4	 mov	 edx, DWORD PTR $T5[ebp]
  000c9	8b 02		 mov	 eax, DWORD PTR [edx]
  000cb	8b 4a 04	 mov	 ecx, DWORD PTR [edx+4]
  000ce	89 45 c8	 mov	 DWORD PTR $T6[ebp], eax
  000d1	89 4d cc	 mov	 DWORD PTR $T6[ebp+4], ecx
  000d4	8d 55 c8	 lea	 edx, DWORD PTR $T6[ebp]
  000d7	89 55 9c	 mov	 DWORD PTR tv90[ebp], edx
$LN6@duration_c:
  000da	8b 45 9c	 mov	 eax, DWORD PTR tv90[ebp]
  000dd	89 45 e0	 mov	 DWORD PTR $T9[ebp], eax
  000e0	8b 4d e0	 mov	 ecx, DWORD PTR $T9[ebp]
  000e3	8b 11		 mov	 edx, DWORD PTR [ecx]
  000e5	8b 41 04	 mov	 eax, DWORD PTR [ecx+4]
  000e8	89 55 e4	 mov	 DWORD PTR $T10[ebp], edx
  000eb	89 45 e8	 mov	 DWORD PTR $T10[ebp+4], eax
  000ee	8d 4d e4	 lea	 ecx, DWORD PTR $T10[ebp]
  000f1	89 4d a0	 mov	 DWORD PTR tv93[ebp], ecx
$LN8@duration_c:
  000f4	8b 55 a0	 mov	 edx, DWORD PTR tv93[ebp]
  000f7	89 55 fc	 mov	 DWORD PTR $T13[ebp], edx
  000fa	8b 45 fc	 mov	 eax, DWORD PTR $T13[ebp]
  000fd	8b 08		 mov	 ecx, DWORD PTR [eax]
  000ff	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  00102	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  00105	89 08		 mov	 DWORD PTR [eax], ecx
  00107	89 50 04	 mov	 DWORD PTR [eax+4], edx
  0010a	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 508  : 			? static_cast<_To>(static_cast<_ToRep>(_Dur.count()))
; 509  : 		: _CF::num != 1 && _CF::den == 1
; 510  : 			? static_cast<_To>(static_cast<_ToRep>(
; 511  : 				static_cast<_CR>(
; 512  : 					_Dur.count()) * static_cast<_CR>(_CF::num)))
; 513  : 		: _CF::num == 1 && _CF::den != 1
; 514  : 			? static_cast<_To>(static_cast<_ToRep>(
; 515  : 				static_cast<_CR>(_Dur.count())
; 516  : 					/ static_cast<_CR>(_CF::den)))
; 517  : 		: static_cast<_To>(static_cast<_ToRep>(
; 518  : 			static_cast<_CR>(_Dur.count()) * static_cast<_CR>(_CF::num)
; 519  : 				/ static_cast<_CR>(_CF::den))));
; 520  : #pragma warning(pop)
; 521  : 	}

  0010d	5f		 pop	 edi
  0010e	83 c4 68	 add	 esp, 104		; 00000068H
  00111	3b ec		 cmp	 ebp, esp
  00113	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00118	8b e5		 mov	 esp, ebp
  0011a	5d		 pop	 ebp
  0011b	c3		 ret	 0
??$duration_cast@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@_JU?$ratio@$00$0DOI@@3@X@chrono@std@@YA?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@ABV?$duration@_JU?$ratio@$00$0DOI@@std@@@01@@Z ENDP ; std::chrono::duration_cast<std::chrono::duration<__int64,std::ratio<1,1000000000> >,__int64,std::ratio<1,1000>,void>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??$?MUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z
_TEXT	SEGMENT
$T1 = -16						; size = 8
$T2 = -8						; size = 8
__Left$ = 8						; size = 4
__Right$ = 12						; size = 4
??$?MUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z PROC ; std::chrono::operator<<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >, COMDAT

; 683  : 	{	// test for time_point < time_point

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 10	 sub	 esp, 16			; 00000010H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0000e	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00011	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00014	89 45 fc	 mov	 DWORD PTR [ebp-4], eax

; 684  : 	return (_Left.time_since_epoch() < _Right.time_since_epoch());

  00017	8d 45 f8	 lea	 eax, DWORD PTR $T2[ebp]
  0001a	50		 push	 eax
  0001b	8b 4d 0c	 mov	 ecx, DWORD PTR __Right$[ebp]
  0001e	e8 00 00 00 00	 call	 ?time_since_epoch@?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QBE?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@XZ ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_since_epoch
  00023	50		 push	 eax
  00024	8d 4d f0	 lea	 ecx, DWORD PTR $T1[ebp]
  00027	51		 push	 ecx
  00028	8b 4d 08	 mov	 ecx, DWORD PTR __Left$[ebp]
  0002b	e8 00 00 00 00	 call	 ?time_since_epoch@?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QBE?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@XZ ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_since_epoch
  00030	50		 push	 eax
  00031	e8 00 00 00 00	 call	 ??$?M_JU?$ratio@$00$0DLJKMKAA@@std@@_JU01@@chrono@std@@YA_NABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@0@Z ; std::chrono::operator<<__int64,std::ratio<1,1000000000>,__int64,std::ratio<1,1000000000> >
  00036	83 c4 08	 add	 esp, 8

; 685  : 	}

  00039	83 c4 10	 add	 esp, 16			; 00000010H
  0003c	3b ec		 cmp	 ebp, esp
  0003e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00043	8b e5		 mov	 esp, ebp
  00045	5d		 pop	 ebp
  00046	c3		 ret	 0
??$?MUsteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@12@V312@@chrono@std@@YA_NABV?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@01@0@Z ENDP ; std::chrono::operator<<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> >,std::chrono::duration<__int64,std::ratio<1,1000000000> > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$forward@UTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$forward@UTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z PROC ; std::forward<CMessageLoop2::Task>, COMDAT

; 1573 : 	{	// forward an lvalue as either an lvalue or an rvalue

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1574 : 	return (static_cast<_Ty&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1575 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$forward@UTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ENDP ; std::forward<CMessageLoop2::Task>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\list
;	COMDAT ??$_Buynode@UTask@CMessageLoop2@@@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PAU21@0$$QAUTask@CMessageLoop2@@@Z
_TEXT	SEGMENT
__Al$ = -12						; size = 4
__Pnode$ = -8						; size = 4
_this$ = -4						; size = 4
__Next$ = 8						; size = 4
__Prev$ = 12						; size = 4
_<_Val_0>$ = 16						; size = 4
??$_Buynode@UTask@CMessageLoop2@@@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PAU21@0$$QAUTask@CMessageLoop2@@@Z PROC ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Buynode<CMessageLoop2::Task>, COMDAT
; _this$ = ecx

; 711  : 		{	// allocate a node and set links and value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 712  : 		_Nodeptr _Pnode = this->_Buynode0(_Next, _Prev);

  0001e	8b 45 0c	 mov	 eax, DWORD PTR __Prev$[ebp]
  00021	50		 push	 eax
  00022	8b 4d 08	 mov	 ecx, DWORD PTR __Next$[ebp]
  00025	51		 push	 ecx
  00026	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00029	e8 00 00 00 00	 call	 ?_Buynode0@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@2@PAU32@0@Z ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Buynode0
  0002e	89 45 f8	 mov	 DWORD PTR __Pnode$[ebp], eax

; 713  : 		_Alnode& _Al = this->_Getal();

  00031	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	e8 00 00 00 00	 call	 ?_Getal@?$_List_alloc@U?$_List_base_types@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@@std@@QAEAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@2@XZ ; std::_List_alloc<std::_List_base_types<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> > >::_Getal
  00039	89 45 f4	 mov	 DWORD PTR __Al$[ebp], eax

; 714  : 
; 715  : 		_TRY_BEGIN

  0003c	ba 01 00 00 00	 mov	 edx, 1
  00041	85 d2		 test	 edx, edx
  00043	74 2b		 je	 SHORT $LN2@Buynode

; 716  : 		_Alnode_traits::construct(_Al,

  00045	8b 45 10	 mov	 eax, DWORD PTR _<_Val_0>$[ebp]
  00048	50		 push	 eax
  00049	e8 00 00 00 00	 call	 ??$forward@UTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::forward<CMessageLoop2::Task>
  0004e	83 c4 04	 add	 esp, 4
  00051	50		 push	 eax
  00052	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00055	83 c1 08	 add	 ecx, 8
  00058	51		 push	 ecx
  00059	e8 00 00 00 00	 call	 ??$addressof@UTask@CMessageLoop2@@@std@@YAPAUTask@CMessageLoop2@@AAU12@@Z ; std::addressof<CMessageLoop2::Task>
  0005e	83 c4 04	 add	 esp, 4
  00061	50		 push	 eax
  00062	8b 55 f4	 mov	 edx, DWORD PTR __Al$[ebp]
  00065	52		 push	 edx
  00066	e8 00 00 00 00	 call	 ??$construct@UTask@CMessageLoop2@@U12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@$$QAU34@@Z ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<CMessageLoop2::Task,CMessageLoop2::Task>
  0006b	83 c4 0c	 add	 esp, 12			; 0000000cH
  0006e	eb 12		 jmp	 SHORT $LN3@Buynode
$LN2@Buynode:

; 717  : 			_STD addressof(_Pnode->_Myval),
; 718  : 			_STD forward<_Valty>(_Val)...);
; 719  : 		_CATCH_ALL

  00070	33 c0		 xor	 eax, eax
  00072	74 0e		 je	 SHORT $LN3@Buynode

; 720  : 		_Al.deallocate(_Pnode, 1);

  00074	6a 01		 push	 1
  00076	8b 4d f8	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00079	51		 push	 ecx
  0007a	8b 4d f4	 mov	 ecx, DWORD PTR __Al$[ebp]
  0007d	e8 00 00 00 00	 call	 ?deallocate@?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@QAEXQAU?$_List_node@UTask@CMessageLoop2@@PAX@2@I@Z ; std::allocator<std::_List_node<CMessageLoop2::Task,void *> >::deallocate
$LN3@Buynode:

; 721  : 		_RERAISE;
; 722  : 		_CATCH_END
; 723  : 
; 724  : 		return (_Pnode);

  00082	8b 45 f8	 mov	 eax, DWORD PTR __Pnode$[ebp]

; 725  : 		}

  00085	83 c4 0c	 add	 esp, 12			; 0000000cH
  00088	3b ec		 cmp	 ebp, esp
  0008a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0008f	8b e5		 mov	 esp, ebp
  00091	5d		 pop	 ebp
  00092	c2 0c 00	 ret	 12			; 0000000cH
??$_Buynode@UTask@CMessageLoop2@@@?$_List_buy@UTask@CMessageLoop2@@V?$allocator@UTask@CMessageLoop2@@@std@@@std@@QAEPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@PAU21@0$$QAUTask@CMessageLoop2@@@Z ENDP ; std::_List_buy<CMessageLoop2::Task,std::allocator<CMessageLoop2::Task> >::_Buynode<CMessageLoop2::Task>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$destroy@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
__Ptr$ = 12						; size = 4
??$destroy@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@@Z PROC ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<std::_List_node<CMessageLoop2::Task,void *> *>, COMDAT

; 886  : 		{	// destroy object at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 887  : 		_Ptr->~_Uty();
; 888  : 		}

  00003	5d		 pop	 ebp
  00004	c3		 ret	 0
??$destroy@PAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAPAU?$_List_node@UTask@CMessageLoop2@@PAX@1@@Z ENDP ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::destroy<std::_List_node<CMessageLoop2::Task,void *> *>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$forward@AAPAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$forward@AAPAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z PROC ; std::forward<std::_List_node<CMessageLoop2::Task,void *> * &>, COMDAT

; 1573 : 	{	// forward an lvalue as either an lvalue or an rvalue

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1574 : 	return (static_cast<_Ty&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1575 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$forward@AAPAU?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@YAAAPAU?$_List_node@UTask@CMessageLoop2@@PAX@0@AAPAU10@@Z ENDP ; std::forward<std::_List_node<CMessageLoop2::Task,void *> * &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ?time_since_epoch@?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QBE?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
?time_since_epoch@?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QBE?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@XZ PROC ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_since_epoch, COMDAT
; _this$ = ecx

; 222  : 		{	// get duration from epoch

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 223  : 		return (_MyDur);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 08		 mov	 ecx, DWORD PTR [eax]
  00013	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  00016	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  00019	89 08		 mov	 DWORD PTR [eax], ecx
  0001b	89 50 04	 mov	 DWORD PTR [eax+4], edx
  0001e	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 224  : 		}

  00021	8b e5		 mov	 esp, ebp
  00023	5d		 pop	 ebp
  00024	c2 04 00	 ret	 4
?time_since_epoch@?$time_point@Usteady_clock@chrono@std@@V?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@@chrono@std@@QBE?AV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@23@XZ ENDP ; std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >::time_since_epoch
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ PROC ; std::chrono::duration<__int64,std::ratio<1,1000> >::count, COMDAT
; _this$ = ecx

; 103  : 		{	// get stored rep

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 104  : 		return (_MyRep);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	8b 01		 mov	 eax, DWORD PTR [ecx]
  00013	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]

; 105  : 		}

  00016	8b e5		 mov	 esp, ebp
  00018	5d		 pop	 ebp
  00019	c3		 ret	 0
?count@?$duration@_JU?$ratio@$00$0DOI@@std@@@chrono@std@@QBE_JXZ ENDP ; std::chrono::duration<__int64,std::ratio<1,1000> >::count
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\chrono
;	COMDAT ??$?M_JU?$ratio@$00$0DLJKMKAA@@std@@_JU01@@chrono@std@@YA_NABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@0@Z
_TEXT	SEGMENT
tv71 = -36						; size = 4
tv94 = -32						; size = 8
tv91 = -24						; size = 8
$T1 = -16						; size = 8
$T2 = -8						; size = 8
__Left$ = 8						; size = 4
__Right$ = 12						; size = 4
??$?M_JU?$ratio@$00$0DLJKMKAA@@std@@_JU01@@chrono@std@@YA_NABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@0@Z PROC ; std::chrono::operator<<__int64,std::ratio<1,1000000000>,__int64,std::ratio<1,1000000000> >, COMDAT

; 455  : 	{	// test if duration < duration

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 24	 sub	 esp, 36			; 00000024H
  00006	56		 push	 esi
  00007	57		 push	 edi
  00008	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000d	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  00010	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00013	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00016	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00019	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0001c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0001f	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00022	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00025	89 45 fc	 mov	 DWORD PTR [ebp-4], eax

; 456  : 	using _CT = common_type_t<duration<_Rep1, _Period1>, duration<_Rep2, _Period2>>;
; 457  : 	return (_CT(_Left).count() < _CT(_Right).count());

  00028	8b 45 08	 mov	 eax, DWORD PTR __Left$[ebp]
  0002b	8b 08		 mov	 ecx, DWORD PTR [eax]
  0002d	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  00030	89 4d f8	 mov	 DWORD PTR $T2[ebp], ecx
  00033	89 55 fc	 mov	 DWORD PTR $T2[ebp+4], edx
  00036	8b 45 0c	 mov	 eax, DWORD PTR __Right$[ebp]
  00039	8b 08		 mov	 ecx, DWORD PTR [eax]
  0003b	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  0003e	89 4d f0	 mov	 DWORD PTR $T1[ebp], ecx
  00041	89 55 f4	 mov	 DWORD PTR $T1[ebp+4], edx
  00044	8d 4d f8	 lea	 ecx, DWORD PTR $T2[ebp]
  00047	e8 00 00 00 00	 call	 ?count@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::count
  0004c	8b f0		 mov	 esi, eax
  0004e	8b fa		 mov	 edi, edx
  00050	8d 4d f0	 lea	 ecx, DWORD PTR $T1[ebp]
  00053	e8 00 00 00 00	 call	 ?count@?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@chrono@std@@QBE_JXZ ; std::chrono::duration<__int64,std::ratio<1,1000000000> >::count
  00058	89 75 e8	 mov	 DWORD PTR tv91[ebp], esi
  0005b	89 7d ec	 mov	 DWORD PTR tv91[ebp+4], edi
  0005e	89 45 e0	 mov	 DWORD PTR tv94[ebp], eax
  00061	89 55 e4	 mov	 DWORD PTR tv94[ebp+4], edx
  00064	8b 45 ec	 mov	 eax, DWORD PTR tv91[ebp+4]
  00067	3b 45 e4	 cmp	 eax, DWORD PTR tv94[ebp+4]
  0006a	7f 13		 jg	 SHORT $LN3@operator
  0006c	7c 08		 jl	 SHORT $LN5@operator
  0006e	8b 4d e8	 mov	 ecx, DWORD PTR tv91[ebp]
  00071	3b 4d e0	 cmp	 ecx, DWORD PTR tv94[ebp]
  00074	73 09		 jae	 SHORT $LN3@operator
$LN5@operator:
  00076	c7 45 dc 01 00
	00 00		 mov	 DWORD PTR tv71[ebp], 1
  0007d	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  0007f	c7 45 dc 00 00
	00 00		 mov	 DWORD PTR tv71[ebp], 0
$LN4@operator:
  00086	8a 45 dc	 mov	 al, BYTE PTR tv71[ebp]

; 458  : 	}

  00089	5f		 pop	 edi
  0008a	5e		 pop	 esi
  0008b	83 c4 24	 add	 esp, 36			; 00000024H
  0008e	3b ec		 cmp	 ebp, esp
  00090	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00095	8b e5		 mov	 esp, ebp
  00097	5d		 pop	 ebp
  00098	c3		 ret	 0
??$?M_JU?$ratio@$00$0DLJKMKAA@@std@@_JU01@@chrono@std@@YA_NABV?$duration@_JU?$ratio@$00$0DLJKMKAA@@std@@@01@0@Z ENDP ; std::chrono::operator<<__int64,std::ratio<1,1000000000>,__int64,std::ratio<1,1000000000> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xmemory0
;	COMDAT ??$construct@UTask@CMessageLoop2@@U12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@$$QAU34@@Z
_TEXT	SEGMENT
$T1 = -4						; size = 4
___formal$ = 8						; size = 4
__Ptr$ = 12						; size = 4
_<_Args_0>$ = 16					; size = 4
??$construct@UTask@CMessageLoop2@@U12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@$$QAU34@@Z PROC ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<CMessageLoop2::Task,CMessageLoop2::Task>, COMDAT

; 879  : 		{	// construct _Objty(_Types...) at _Ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 880  : 		::new (const_cast<void *>(static_cast<const volatile void *>(_Ptr)))

  0000b	8b 45 0c	 mov	 eax, DWORD PTR __Ptr$[ebp]
  0000e	50		 push	 eax
  0000f	6a 30		 push	 48			; 00000030H
  00011	e8 00 00 00 00	 call	 ??2@YAPAXIPAX@Z		; operator new
  00016	83 c4 08	 add	 esp, 8
  00019	89 45 fc	 mov	 DWORD PTR $T1[ebp], eax
  0001c	8b 4d 10	 mov	 ecx, DWORD PTR _<_Args_0>$[ebp]
  0001f	51		 push	 ecx
  00020	e8 00 00 00 00	 call	 ??$forward@UTask@CMessageLoop2@@@std@@YA$$QAUTask@CMessageLoop2@@AAU12@@Z ; std::forward<CMessageLoop2::Task>
  00025	83 c4 04	 add	 esp, 4
  00028	50		 push	 eax
  00029	8b 4d fc	 mov	 ecx, DWORD PTR $T1[ebp]
  0002c	e8 00 00 00 00	 call	 ??0Task@CMessageLoop2@@QAE@$$QAU01@@Z

; 881  : 			_Objty(_STD forward<_Types>(_Args)...);
; 882  : 		}

  00031	83 c4 04	 add	 esp, 4
  00034	3b ec		 cmp	 ebp, esp
  00036	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003b	8b e5		 mov	 esp, ebp
  0003d	5d		 pop	 ebp
  0003e	c3		 ret	 0
??$construct@UTask@CMessageLoop2@@U12@@?$_Default_allocator_traits@V?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@std@@@std@@SAXAAV?$allocator@U?$_List_node@UTask@CMessageLoop2@@PAX@std@@@1@QAUTask@CMessageLoop2@@$$QAU34@@Z ENDP ; std::_Default_allocator_traits<std::allocator<std::_List_node<CMessageLoop2::Task,void *> > >::construct<CMessageLoop2::Task,CMessageLoop2::Task>
_TEXT	ENDS
END
