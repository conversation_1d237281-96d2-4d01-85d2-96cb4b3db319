// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace string {
// https://tc39.es/proposal-item-method/#sec-string.prototype.at
transitioning javascript builtin StringPrototypeAt(
    js-implicit context: NativeContext, receiver: JSAny)(index: JSAny): JSAny {
  // 1. Let O be ? RequireObjectCoercible(this value).
  // 2. Let S be ? ToString(O).
  const s = ToThisString(receiver, 'String.prototype.at');
  // 3. Let len be the length of S.
  const len = s.length_smi;
  // 4. Let relativeIndex be ? ToInteger(index).
  const relativeIndex = ToInteger_Inline(index);
  // 5. If relativeIndex ≥ 0, then
  //   a. Let k be relativeIndex.
  // 6. Else,
  //   a. Let k be len + relativeIndex.
  const k = relativeIndex >= 0 ? relativeIndex : len + relativeIndex;
  // 7. If k < 0 or k ≥ len, then return undefined.
  if (k < 0 || k >= len) {
    return Undefined;
  }
  // 8. Return the String value consisting of only the code unit at position k
  // in S.
  return StringFromSingleCharCode(StringCharCodeAt(s, Convert<uintptr>(k)));
}
}
