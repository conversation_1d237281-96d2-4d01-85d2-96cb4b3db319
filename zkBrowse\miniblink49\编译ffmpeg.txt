配置 FFmpeg 编译参数

# FFmpeg 专用配置
ffmpeg_branding = "Chrome"  # 包含专利编解码器
is_component_ffmpeg = true  # 编译为 DLL
proprietary_codecs = true    # 启用专利编解码器
enable_platform_hevc = true # 启用 HEVC 支持

1. 生成构建文件
POWERSHELL
# 生成 GN 配置# 手动生成 GN 文件（跳过 gn_helper）
gn gen out/Releasefmg --script-executable=python3 --args="is_debug=false target_cpu=\"x86\" ffmpeg_branding=\"Chrome\" is_component_ffmpeg=true"

# 生成 Ninja 文件
2. 编译 FFmpeg DLL

# 只编译 FFmpeg 目标
ninja -C out/Release third_party/ffmpeg:ffmpeg

