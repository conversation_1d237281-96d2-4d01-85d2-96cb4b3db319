﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
LINK : warning LNK4044: 无法识别的选项“/Bv”；已忽略
LINK : warning LNK4075: 忽略“/INCREMENTAL”(由于“/LTCG”规范)
    正在创建库 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\out\Release_D\miniblink_4975.lib 和对象 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\out\Release_D\miniblink_4975.exp
  正在生成代码
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\libxml\src\xpath.c(2769): warning C4789: 缓冲区“work”(大小为 28 字节)将溢出；1 字节将在偏移 98 时开始写入
  已完成代码的生成
  miniblinkNew.vcxproj -> E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\out\Release_D\miniblink_4975_x32.dll
BSCMAKE : error BK1505: 无法从文件“E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\out\Release_D\miniblink_4975_x32.bsc”中读取
