void FFmpegGlue::InitializeFFmpeg()
{
    base::AutoLock auto_lock(lock_);
    if (initialized_)
        return;

#ifdef NEED_AVCODEC_REGISTER_ALL
    avcodec_register_all();
#endif

#ifdef NEED_AV_REGISTER_ALL
    av_register_all();
#endif

    // Let FFmpeg install its own lock manager to handle thread-safe callbacks.
    // FFmpeg's lock manager implementation returns values which are not
    // compatible with the COM apartment model, causing DCOM to deadlock.
    // Additionally, FFmpeg's MMX optimizations can generate invalid instructions
    // if the sliding code window crosses a page boundary.
    if (av_lockmgr_register(&LockManagerOperation) != 0) {
        NOTREACHED() << "Failed to initialize FFmpeg lock manager.";
    }

    initialized_ = true;
}