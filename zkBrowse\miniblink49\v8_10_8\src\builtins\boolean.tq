// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace boolean {
transitioning macro ThisBooleanValue(implicit context: Context)(
    receiver: J<PERSON><PERSON>, method: constexpr string): Bo<PERSON>an {
  return UnsafeCast<Boolean>(
      ToThisValue(receiver, PrimitiveType::kBoolean, method));
}

javascript builtin
BooleanConstructor(
    js-implicit context: NativeContext, receiver: JSAny, newTarget: JSAny,
    target: JSFunction)(...arguments): JSAny {
  const value = SelectBooleanConstant(ToBoolean(arguments[0]));

  if (newTarget == Undefined) {
    return value;
  }

  const map = GetDerivedMap(target, UnsafeCast<JSReceiver>(newTarget));

  const obj =
      UnsafeCast<JSPrimitiveWrapper>(AllocateFastOrSlowJSObjectFromMap(map));
  obj.value = value;
  return obj;
}

// ES #sec-boolean.prototype.tostring
transitioning javascript builtin BooleanPrototypeToString(
    js-implicit context: NativeContext, receiver: JSAny)(): JSAny {
  // 1. Let b be ? thisBooleanValue(this value).
  const b = ThisBooleanValue(receiver, 'Boolean.prototype.toString');
  // 2. If b is true, return "true"; else return "false".
  return b.to_string;
}

// ES #sec-boolean.prototype.valueof
transitioning javascript builtin BooleanPrototypeValueOf(
    js-implicit context: NativeContext, receiver: JSAny)(): JSAny {
  // 1. Return ? thisBooleanValue(this value).
  return ThisBooleanValue(receiver, 'Boolean.prototype.valueOf');
}
}
