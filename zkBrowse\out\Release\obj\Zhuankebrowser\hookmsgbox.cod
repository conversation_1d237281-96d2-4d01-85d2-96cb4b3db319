; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\hookmsgbox.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	?g_hTrd@?A0x985c38d4@@3PAXA			; `anonymous namespace'::g_hTrd
EXTRN	__imp__MessageBoxA@16:PROC
EXTRN	_DetourDetach@8:PROC
EXTRN	_DetourUpdateThread@4:PROC
EXTRN	_DetourTransactionCommit@0:PROC
EXTRN	_DetourAttach@8:PROC
EXTRN	__imp__lstrcmpiA@8:PROC
EXTRN	_DetourTransactionBegin@0:PROC
EXTRN	__imp__GetCurrentThread@0:PROC
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?g_hTrd@?A0x985c38d4@@3PAXA DD 01H DUP (?)		; `anonymous namespace'::g_hTrd
_BSS	ENDS
	ORG $+2
$SG4294465032 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294465033 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294465024 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294465025 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294465026 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294465027 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294465028 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294465029 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294465030 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294465031 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294465016 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294465017 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294465018 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294465019 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294465020 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294465021 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294465022 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294465023 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294465008 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294465009 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294465010 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294465011 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294465012 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294465013 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294465014 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294465015 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294465000 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294465001 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294465002 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294465003 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294465004 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294465005 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294465006 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294465007 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464992 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464993 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464994 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464995 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464996 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294464997 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464998 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464999 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464984 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294464985 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464986 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464987 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464988 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464989 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464990 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464991 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464976 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464977 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464978 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464979 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294464980 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464981 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464982 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294464983 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294464968 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464969 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294464970 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294464971 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294464972 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464973 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464974 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294464975 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464960 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294464961 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464962 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294464963 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294464964 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464965 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464966 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294464967 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464952 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464953 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464954 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464955 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464956 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464957 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464958 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294464959 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294464944 DB 00H, 00H
	ORG $+2
$SG4294464945 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294464946 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294464947 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294464948 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464949 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464950 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464951 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464936 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294464937 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294464938 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294464939 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294464940 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294464941 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294464942 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294464943 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294464928 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294464929 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294464930 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294464931 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294464932 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294464933 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294464934 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294464935 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294464920 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294464921 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294464922 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294464923 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294464924 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294464925 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294464926 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294464927 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294464912 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294464913 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294464914 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294464915 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294464916 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294464917 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294464918 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294464919 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294464904 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294464905 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294464906 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294464907 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294464908 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294464909 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294464910 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294464911 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294464896 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294464897 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294464898 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294464899 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294464900 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294464901 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294464902 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294464903 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464888 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294464889 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294464890 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294464891 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294464892 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294464893 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294464894 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464895 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294464880 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464881 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464882 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464883 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464884 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464885 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464886 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294464887 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464872 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464873 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464874 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294464875 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294464876 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294464877 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464878 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464879 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294464864 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294464865 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294464866 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294464867 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294464868 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294464869 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294464870 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464871 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464856 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294464857 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464858 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294464859 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294464860 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294464861 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294464862 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294464863 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294464848 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294464849 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294464850 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294464851 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464852 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464853 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294464854 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294464855 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464840 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294464841 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294464842 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464843 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294464844 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464845 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294464846 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294464847 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294464832 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294464833 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294464834 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464835 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294464836 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294464837 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294464838 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294464839 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294464824 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294464825 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294464826 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464827 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464828 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294464829 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294464830 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294464831 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294464816 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464817 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294464818 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294464819 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294464820 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464821 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464822 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294464823 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294464808 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294464809 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294464810 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294464811 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464812 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294464813 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294464814 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294464815 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464800 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294464801 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294464802 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294464803 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294464804 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294464805 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294464806 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294464807 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464792 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294464793 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294464794 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294464795 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294464796 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294464797 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294464798 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294464799 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294464784 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464785 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294464786 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294464787 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294464788 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294464789 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294464790 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294464791 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294464776 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294464777 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294464778 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294464779 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464780 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294464781 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294464782 DB 00H, 00H
	ORG $+2
$SG4294464783 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464768 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294464769 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294464770 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294464771 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294464772 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294464773 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294464774 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294464775 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294464760 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464761 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464762 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294464763 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294464764 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294464765 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294464766 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294464767 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294464752 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294464753 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464754 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464755 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464756 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294464757 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294464758 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294464759 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464744 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294464745 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294464746 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464747 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294464748 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464749 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294464750 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294464751 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294464736 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464737 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294464738 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464739 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464740 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294464741 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294464742 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294464743 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294464728 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294464729 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294464730 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294464731 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294464732 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294464733 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294464734 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294464735 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294464720 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294464721 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294464722 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294464723 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294464724 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294464725 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294464726 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294464727 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294464712 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294464713 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294464714 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294464715 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294464716 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294464717 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294464718 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294464719 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294464704 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294464705 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294464706 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294464707 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294464708 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294464709 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294464710 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294464711 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294464696 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294464697 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294464698 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464699 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294464700 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464701 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294464702 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464703 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294464688 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294464689 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294464690 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294464691 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294464692 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464693 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294464694 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464695 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294464680 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294464681 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464682 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464683 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464684 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294464685 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464686 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294464687 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294464672 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294464673 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294464674 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464675 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294464676 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294464677 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464678 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464679 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294464664 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294464665 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294464666 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464667 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294464668 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294464669 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294464670 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294464671 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294464656 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464657 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464658 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294464659 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464660 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294464661 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464662 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294464663 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464648 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294464649 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464650 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294464651 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294464652 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294464653 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464654 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294464655 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464640 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294464641 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464642 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464643 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294464644 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464645 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294464646 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294464647 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294464632 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464633 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294464634 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294464635 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294464636 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294464637 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294464638 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464639 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464624 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464625 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464626 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464627 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294464628 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464629 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294464630 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294464631 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294464616 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294464617 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464618 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464619 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464620 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294464621 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294464622 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464623 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294464608 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294464609 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294464610 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294464611 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464612 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464613 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294464614 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294464615 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294464600 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464601 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464602 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464603 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464604 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294464605 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464606 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294464607 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464592 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464593 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464594 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464595 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464596 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294464597 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294464598 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464599 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464584 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464585 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294464586 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294464587 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294464588 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294464589 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294464590 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294464591 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464576 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464577 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294464578 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294464579 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294464580 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464581 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464582 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294464583 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294464568 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294464569 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294464570 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294464571 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294464572 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294464573 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294464574 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294464575 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294464560 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294464561 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464562 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464563 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294464564 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294464565 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294464566 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294464567 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294464555 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294464556 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294464557 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294464558 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294464559 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294464520 DB 'D', 00H, 00H, 00H
$SG4294464521 DB 'M', 00H, 00H, 00H
$SG4294464522 DB 'S', 00H, 00H, 00H
$SG4294464512 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294464513 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294464514 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294464515 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464516 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464517 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294464518 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294464519 DB 'B', 00H, 00H, 00H
$SG4294464504 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464505 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294464506 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294464507 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294464508 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464509 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464510 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294464511 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294464503 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294464472 DB ':', 00H, 00H, 00H
$SG4294464470 DB 00H, 00H
	ORG $+2
$SG4294464471 DB 00H, 00H
	ORG $+2
$SG4294464379 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294463665 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294463666 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294463217 DB 'r', 00H, 'e', 00H, 'g', 00H, 's', 00H, 'v', 00H, 'r', 00H
	DB	'3', 00H, '2', 00H, 00H, 00H
	ORG $+2
$SG4294463218 DB 'r', 00H, 'u', 00H, 'n', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294463219 DB 'regsvr32', 00H
	ORG $+3
$SG4294463220 DB 'rundll', 00H
	ORG $+2
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
PUBLIC	?Hook@HookMsgBox@@YAXPAX@Z			; HookMsgBox::Hook
PUBLIC	?g_fnOldMessageBoxW@?A0x985c38d4@@3P6GHPAUHWND__@@PB_W1I@ZA ; `anonymous namespace'::g_fnOldMessageBoxW
PUBLIC	?g_fnOldMessageBoxA@?A0x985c38d4@@3P6GHPAUHWND__@@PBD1I@ZA ; `anonymous namespace'::g_fnOldMessageBoxA
?g_fnOldMessageBoxW@?A0x985c38d4@@3P6GHPAUHWND__@@PB_W1I@ZA DD 01H DUP (?) ; `anonymous namespace'::g_fnOldMessageBoxW
?g_fnOldMessageBoxA@?A0x985c38d4@@3P6GHPAUHWND__@@PBD1I@ZA DD 01H DUP (?) ; `anonymous namespace'::g_fnOldMessageBoxA
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
?g_fnOldMessageBoxA$initializer$@?A0x985c38d4@@3P6AXXZA DD FLAT:??__Eg_fnOldMessageBoxA@?A0x985c38d4@@YAXXZ ; `anonymous namespace'::g_fnOldMessageBoxA$initializer$
?g_fnOldMessageBoxW$initializer$@?A0x985c38d4@@3P6AXXZA DD FLAT:??__Eg_fnOldMessageBoxW@?A0x985c38d4@@YAXXZ ; `anonymous namespace'::g_fnOldMessageBoxW$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\include\detours.h
_TEXT	SEGMENT
_ppOld$ = 8						; size = 4
_pNew$ = 12						; size = 4
_bHook$ = 16						; size = 4
?HookApi@@YAHPAPAXPAXH@Z PROC				; HookApi

; 502  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 503  : 	DetourTransactionBegin();

  00004	e8 00 00 00 00	 call	 _DetourTransactionBegin@0

; 504  : 	DetourUpdateThread(GetCurrentThread());

  00009	8b f4		 mov	 esi, esp
  0000b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetCurrentThread@0
  00011	3b f4		 cmp	 esi, esp
  00013	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00018	50		 push	 eax
  00019	e8 00 00 00 00	 call	 _DetourUpdateThread@4

; 505  : 	if(bHook){

  0001e	83 7d 10 00	 cmp	 DWORD PTR _bHook$[ebp], 0
  00022	74 0f		 je	 SHORT $LN2@HookApi

; 506  : 		DetourAttach(ppOld, pNew);

  00024	8b 45 0c	 mov	 eax, DWORD PTR _pNew$[ebp]
  00027	50		 push	 eax
  00028	8b 4d 08	 mov	 ecx, DWORD PTR _ppOld$[ebp]
  0002b	51		 push	 ecx
  0002c	e8 00 00 00 00	 call	 _DetourAttach@8

; 507  : 	}else{

  00031	eb 0d		 jmp	 SHORT $LN3@HookApi
$LN2@HookApi:

; 508  : 		DetourDetach(ppOld, pNew);

  00033	8b 55 0c	 mov	 edx, DWORD PTR _pNew$[ebp]
  00036	52		 push	 edx
  00037	8b 45 08	 mov	 eax, DWORD PTR _ppOld$[ebp]
  0003a	50		 push	 eax
  0003b	e8 00 00 00 00	 call	 _DetourDetach@8
$LN3@HookApi:

; 509  : 	}
; 510  : 	DetourTransactionCommit();

  00040	e8 00 00 00 00	 call	 _DetourTransactionCommit@0

; 511  : 	return TRUE;

  00045	b8 01 00 00 00	 mov	 eax, 1

; 512  : }

  0004a	5e		 pop	 esi
  0004b	3b ec		 cmp	 ebp, esp
  0004d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00052	5d		 pop	 ebp
  00053	c3		 ret	 0
?HookApi@@YAHPAPAXPAXH@Z ENDP				; HookApi
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\hookmsgbox.cpp
;	COMDAT ??__Eg_fnOldMessageBoxA@?A0x985c38d4@@YAXXZ
text$di	SEGMENT
??__Eg_fnOldMessageBoxA@?A0x985c38d4@@YAXXZ PROC	; `anonymous namespace'::`dynamic initializer for 'g_fnOldMessageBoxA'', COMDAT

; 10   : 	t_MessageBoxA g_fnOldMessageBoxA = MessageBoxA;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	a1 00 00 00 00	 mov	 eax, DWORD PTR __imp__MessageBoxA@16
  00008	a3 00 00 00 00	 mov	 DWORD PTR ?g_fnOldMessageBoxA@?A0x985c38d4@@3P6GHPAUHWND__@@PBD1I@ZA, eax ; `anonymous namespace'::g_fnOldMessageBoxA
  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
??__Eg_fnOldMessageBoxA@?A0x985c38d4@@YAXXZ ENDP	; `anonymous namespace'::`dynamic initializer for 'g_fnOldMessageBoxA''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\hookmsgbox.cpp
;	COMDAT ??__Eg_fnOldMessageBoxW@?A0x985c38d4@@YAXXZ
text$di	SEGMENT
??__Eg_fnOldMessageBoxW@?A0x985c38d4@@YAXXZ PROC	; `anonymous namespace'::`dynamic initializer for 'g_fnOldMessageBoxW'', COMDAT

; 11   : 	t_MessageBoxW g_fnOldMessageBoxW = MessageBoxW;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	a1 00 00 00 00	 mov	 eax, DWORD PTR __imp__MessageBoxW@16
  00008	a3 00 00 00 00	 mov	 DWORD PTR ?g_fnOldMessageBoxW@?A0x985c38d4@@3P6GHPAUHWND__@@PB_W1I@ZA, eax ; `anonymous namespace'::g_fnOldMessageBoxW
  0000d	5d		 pop	 ebp
  0000e	c3		 ret	 0
??__Eg_fnOldMessageBoxW@?A0x985c38d4@@YAXXZ ENDP	; `anonymous namespace'::`dynamic initializer for 'g_fnOldMessageBoxW''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\hookmsgbox.cpp
_TEXT	SEGMENT
_hWnd$ = 8						; size = 4
_lpText$ = 12						; size = 4
_lpCaption$ = 16					; size = 4
_uType$ = 20						; size = 4
?my_MessageBoxA@?A0x985c38d4@@YGHPAUHWND__@@PBD1I@Z PROC ; `anonymous namespace'::my_MessageBoxA

; 14   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 15   : 		if (lstrcmpiA(lpCaption, "rundll") == 0 || lstrcmpiA(lpCaption, "regsvr32") == 0)

  00004	8b f4		 mov	 esi, esp
  00006	68 00 00 00 00	 push	 OFFSET $SG4294463220
  0000b	8b 45 10	 mov	 eax, DWORD PTR _lpCaption$[ebp]
  0000e	50		 push	 eax
  0000f	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcmpiA@8
  00015	3b f4		 cmp	 esi, esp
  00017	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001c	85 c0		 test	 eax, eax
  0001e	74 1c		 je	 SHORT $LN3@my_Message
  00020	8b f4		 mov	 esi, esp
  00022	68 00 00 00 00	 push	 OFFSET $SG4294463219
  00027	8b 4d 10	 mov	 ecx, DWORD PTR _lpCaption$[ebp]
  0002a	51		 push	 ecx
  0002b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcmpiA@8
  00031	3b f4		 cmp	 esi, esp
  00033	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00038	85 c0		 test	 eax, eax
  0003a	75 25		 jne	 SHORT $LN2@my_Message
$LN3@my_Message:

; 16   : 		{
; 17   : 			if (g_hTrd) WaitForSingleObject(g_hTrd, INFINITE);

  0003c	83 3d 00 00 00
	00 00		 cmp	 DWORD PTR ?g_hTrd@?A0x985c38d4@@3PAXA, 0 ; `anonymous namespace'::g_hTrd
  00043	74 18		 je	 SHORT $LN4@my_Message
  00045	8b f4		 mov	 esi, esp
  00047	6a ff		 push	 -1
  00049	8b 15 00 00 00
	00		 mov	 edx, DWORD PTR ?g_hTrd@?A0x985c38d4@@3PAXA ; `anonymous namespace'::g_hTrd
  0004f	52		 push	 edx
  00050	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__WaitForSingleObject@8
  00056	3b f4		 cmp	 esi, esp
  00058	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN4@my_Message:

; 18   : 
; 19   : 			return 0;

  0005d	33 c0		 xor	 eax, eax
  0005f	eb 1f		 jmp	 SHORT $LN1@my_Message
$LN2@my_Message:

; 20   : 		}
; 21   : 		return g_fnOldMessageBoxA(hWnd, lpText, lpCaption, uType);

  00061	8b f4		 mov	 esi, esp
  00063	8b 45 14	 mov	 eax, DWORD PTR _uType$[ebp]
  00066	50		 push	 eax
  00067	8b 4d 10	 mov	 ecx, DWORD PTR _lpCaption$[ebp]
  0006a	51		 push	 ecx
  0006b	8b 55 0c	 mov	 edx, DWORD PTR _lpText$[ebp]
  0006e	52		 push	 edx
  0006f	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  00072	50		 push	 eax
  00073	ff 15 00 00 00
	00		 call	 DWORD PTR ?g_fnOldMessageBoxA@?A0x985c38d4@@3P6GHPAUHWND__@@PBD1I@ZA ; `anonymous namespace'::g_fnOldMessageBoxA
  00079	3b f4		 cmp	 esi, esp
  0007b	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@my_Message:

; 22   : 
; 23   : 	}

  00080	5e		 pop	 esi
  00081	3b ec		 cmp	 ebp, esp
  00083	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00088	5d		 pop	 ebp
  00089	c2 10 00	 ret	 16			; 00000010H
?my_MessageBoxA@?A0x985c38d4@@YGHPAUHWND__@@PBD1I@Z ENDP ; `anonymous namespace'::my_MessageBoxA
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\hookmsgbox.cpp
_TEXT	SEGMENT
_hWnd$ = 8						; size = 4
_lpText$ = 12						; size = 4
_lpCaption$ = 16					; size = 4
_uType$ = 20						; size = 4
?my_MessageBoxW@?A0x985c38d4@@YGHPAUHWND__@@PB_W1I@Z PROC ; `anonymous namespace'::my_MessageBoxW

; 26   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 27   : 		if (lstrcmpiW(lpCaption, L"rundll") == 0 || lstrcmpiW(lpCaption, L"regsvr32") == 0)

  00004	8b f4		 mov	 esi, esp
  00006	68 00 00 00 00	 push	 OFFSET $SG4294463218
  0000b	8b 45 10	 mov	 eax, DWORD PTR _lpCaption$[ebp]
  0000e	50		 push	 eax
  0000f	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcmpiW@8
  00015	3b f4		 cmp	 esi, esp
  00017	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001c	85 c0		 test	 eax, eax
  0001e	74 1c		 je	 SHORT $LN3@my_Message
  00020	8b f4		 mov	 esi, esp
  00022	68 00 00 00 00	 push	 OFFSET $SG4294463217
  00027	8b 4d 10	 mov	 ecx, DWORD PTR _lpCaption$[ebp]
  0002a	51		 push	 ecx
  0002b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcmpiW@8
  00031	3b f4		 cmp	 esi, esp
  00033	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00038	85 c0		 test	 eax, eax
  0003a	75 25		 jne	 SHORT $LN2@my_Message
$LN3@my_Message:

; 28   : 		{
; 29   : 			if (g_hTrd) WaitForSingleObject(g_hTrd, INFINITE);

  0003c	83 3d 00 00 00
	00 00		 cmp	 DWORD PTR ?g_hTrd@?A0x985c38d4@@3PAXA, 0 ; `anonymous namespace'::g_hTrd
  00043	74 18		 je	 SHORT $LN4@my_Message
  00045	8b f4		 mov	 esi, esp
  00047	6a ff		 push	 -1
  00049	8b 15 00 00 00
	00		 mov	 edx, DWORD PTR ?g_hTrd@?A0x985c38d4@@3PAXA ; `anonymous namespace'::g_hTrd
  0004f	52		 push	 edx
  00050	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__WaitForSingleObject@8
  00056	3b f4		 cmp	 esi, esp
  00058	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN4@my_Message:

; 30   : 
; 31   : 			return 0;

  0005d	33 c0		 xor	 eax, eax
  0005f	eb 1f		 jmp	 SHORT $LN1@my_Message
$LN2@my_Message:

; 32   : 		}
; 33   : 		return g_fnOldMessageBoxW(hWnd, lpText, lpCaption, uType);

  00061	8b f4		 mov	 esi, esp
  00063	8b 45 14	 mov	 eax, DWORD PTR _uType$[ebp]
  00066	50		 push	 eax
  00067	8b 4d 10	 mov	 ecx, DWORD PTR _lpCaption$[ebp]
  0006a	51		 push	 ecx
  0006b	8b 55 0c	 mov	 edx, DWORD PTR _lpText$[ebp]
  0006e	52		 push	 edx
  0006f	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  00072	50		 push	 eax
  00073	ff 15 00 00 00
	00		 call	 DWORD PTR ?g_fnOldMessageBoxW@?A0x985c38d4@@3P6GHPAUHWND__@@PB_W1I@ZA ; `anonymous namespace'::g_fnOldMessageBoxW
  00079	3b f4		 cmp	 esi, esp
  0007b	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@my_Message:

; 34   : 	}

  00080	5e		 pop	 esi
  00081	3b ec		 cmp	 ebp, esp
  00083	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00088	5d		 pop	 ebp
  00089	c2 10 00	 ret	 16			; 00000010H
?my_MessageBoxW@?A0x985c38d4@@YGHPAUHWND__@@PB_W1I@Z ENDP ; `anonymous namespace'::my_MessageBoxW
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\hookmsgbox.cpp
_TEXT	SEGMENT
_waitThread$ = 8					; size = 4
?Hook@HookMsgBox@@YAXPAX@Z PROC				; HookMsgBox::Hook

; 41   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 42   : 		g_hTrd = waitThread;

  00003	8b 45 08	 mov	 eax, DWORD PTR _waitThread$[ebp]
  00006	a3 00 00 00 00	 mov	 DWORD PTR ?g_hTrd@?A0x985c38d4@@3PAXA, eax ; `anonymous namespace'::g_hTrd

; 43   : 		HookApi(&(PVOID&)g_fnOldMessageBoxA, my_MessageBoxA, TRUE);

  0000b	6a 01		 push	 1
  0000d	68 00 00 00 00	 push	 OFFSET ?my_MessageBoxA@?A0x985c38d4@@YGHPAUHWND__@@PBD1I@Z ; `anonymous namespace'::my_MessageBoxA
  00012	68 00 00 00 00	 push	 OFFSET ?g_fnOldMessageBoxA@?A0x985c38d4@@3P6GHPAUHWND__@@PBD1I@ZA ; `anonymous namespace'::g_fnOldMessageBoxA
  00017	e8 00 00 00 00	 call	 ?HookApi@@YAHPAPAXPAXH@Z ; HookApi
  0001c	83 c4 0c	 add	 esp, 12			; 0000000cH

; 44   : 		HookApi(&(PVOID&)g_fnOldMessageBoxW, my_MessageBoxW, TRUE);

  0001f	6a 01		 push	 1
  00021	68 00 00 00 00	 push	 OFFSET ?my_MessageBoxW@?A0x985c38d4@@YGHPAUHWND__@@PB_W1I@Z ; `anonymous namespace'::my_MessageBoxW
  00026	68 00 00 00 00	 push	 OFFSET ?g_fnOldMessageBoxW@?A0x985c38d4@@3P6GHPAUHWND__@@PB_W1I@ZA ; `anonymous namespace'::g_fnOldMessageBoxW
  0002b	e8 00 00 00 00	 call	 ?HookApi@@YAHPAPAXPAXH@Z ; HookApi
  00030	83 c4 0c	 add	 esp, 12			; 0000000cH

; 45   : 	}

  00033	3b ec		 cmp	 ebp, esp
  00035	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003a	5d		 pop	 ebp
  0003b	c3		 ret	 0
?Hook@HookMsgBox@@YAXPAX@Z ENDP				; HookMsgBox::Hook
_TEXT	ENDS
END
