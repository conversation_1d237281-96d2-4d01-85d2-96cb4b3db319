; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	??_7CSqliteDataMgr@@6B@				; CSqliteDataMgr::`vftable'
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
_BSS	ENDS
;	COMDAT ??_7CSqliteDataMgr@@6B@
CONST	SEGMENT
??_7CSqliteDataMgr@@6B@ DD FLAT:?Init@CDbSqlite@@UAEHPBDH@Z ; CSqliteDataMgr::`vftable'
$SG4294588842 DB 00H
$SG4294588845 DB 00H
$SG4294588835 DB 00H
$SG4294588832 DB 00H
$SG4294590458 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294590459 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294590456 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590457 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590462 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590463 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590460 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294590461 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294590450 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294590451 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590448 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294590449 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294590454 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294590455 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294590452 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590453 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294590442 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294590443 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590440 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294590441 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294590446 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294590447 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590444 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294590445 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294590434 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294590435 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294590432 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294590433 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294590438 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294590439 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294590436 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294590437 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294590426 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294590427 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294590424 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294590425 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294590430 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294590431 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294590428 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294590429 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294590418 DB 00H, 00H
	ORG $+2
$SG4294590419 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590416 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294590417 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294590422 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294590423 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294590420 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590421 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294590410 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294590411 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294590408 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294590409 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294590414 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294590415 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590412 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294590413 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294590402 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294590403 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294590400 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294590401 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294590406 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294590407 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294590404 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294590405 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294590394 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294590395 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590392 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294590393 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294590398 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294590399 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294590396 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590397 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590386 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294590387 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590384 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590385 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294590390 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590391 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590388 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294590389 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590378 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294590379 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294590376 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294590377 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294590382 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590383 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294590380 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294590381 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294590370 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294590371 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294590368 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294590369 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294590374 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590375 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590372 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590373 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294590362 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294590363 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294590360 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294590361 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294590366 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294590367 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294590364 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294590365 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294590354 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294590355 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294590352 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294590353 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294590358 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590359 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294590356 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294590357 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294590346 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294590347 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294590344 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294590345 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294590350 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294590351 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294590348 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294590349 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294590338 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590339 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294590336 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590337 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294590342 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294590343 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294590340 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294590341 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294590330 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590331 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294590328 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590329 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294590334 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590335 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294590332 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294590333 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294590322 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294590323 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294590320 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294590321 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590326 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294590327 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294590324 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294590325 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294590314 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590315 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294590312 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294590313 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590318 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590319 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590316 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294590317 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590306 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294590307 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294590304 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590305 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294590310 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590311 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294590308 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294590309 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294590298 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294590299 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590296 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294590297 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590302 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590303 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294590300 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294590301 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294590290 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294590291 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590288 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294590289 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590294 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294590295 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590292 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590293 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590282 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294590283 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294590280 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590281 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294590286 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294590287 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294590284 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294590285 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590274 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590275 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590272 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294590273 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590278 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590279 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294590276 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294590277 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590266 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294590267 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294590264 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590265 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294590270 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294590271 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294590268 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590269 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294590258 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590259 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294590256 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294590257 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294590262 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590263 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294590260 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590261 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590250 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590251 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294590248 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590249 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294590254 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590255 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590252 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590253 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590242 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294590243 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590240 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294590241 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590246 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294590247 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590244 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294590245 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294590234 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590235 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590232 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294590233 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294590238 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590239 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590236 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590237 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590226 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294590227 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590224 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294590225 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294590230 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590231 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590228 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590229 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590218 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590219 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590216 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294590217 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590222 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294590223 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294590220 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590221 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294590210 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590211 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294590208 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590209 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294590214 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294590215 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294590212 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590213 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294590202 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590203 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590200 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294590201 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294590206 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590207 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294590204 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294590205 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294590194 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294590195 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294590192 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294590193 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294590198 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590199 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294590196 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294590197 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590191 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294590154 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294590155 DB 'B', 00H, 00H, 00H
$SG4294590152 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590153 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294590158 DB 'S', 00H, 00H, 00H
$SG4294590156 DB 'D', 00H, 00H, 00H
$SG4294590157 DB 'M', 00H, 00H, 00H
$SG4294590146 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294590147 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590144 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590145 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590150 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294590151 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590148 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294590149 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294590139 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294590142 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294590143 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294590140 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590141 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590106 DB 00H, 00H
	ORG $+2
$SG4294590107 DB 00H, 00H
	ORG $+2
$SG4294590108 DB ':', 00H, 00H, 00H
$SG4294590015 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294589302 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294589301 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294588856 DB 'ConfigTable', 00H
$SG4294588850 DB 'INSERT INTO %s (ver) VALUES (%d)', 00H
	ORG $+3
$SG4294588854 DB 'webfav1', 00H
$SG4294588843 DB 00H, 00H
	ORG $+2
$SG4294588851 DB 'create table [webfav1]([url] varchar(2560) NOT NULL,[pu'
	DB	'b_day] int NOT NULL,', 09H, 09H, '[title] varchar(256),[icon]'
	DB	' varchar(260), [pub_time] int, [id] int,', 09H, 09H, 'CONSTRA'
	DB	'INT pk_PersonID PRIMARY KEY (url,pub_day))', 00H
$SG4294588840 DB '%4d-%2d-%2d', 00H
$SG4294588848 DB 'INSERT INTO %s (url,pub_day,title,icon,pub_time,id) VAL'
	DB	'UES (''%s'',%d,''%s'',''%s'',%d,%d)', 00H
	ORG $+3
$SG4294588849 DB 'INSERT INTO %s (url,pub_day,title,pub_time,id) VALUES ('
	DB	'''%s'',%d,''%s'',%d,%d)', 00H
	ORG $+1
$SG4294588855 DB 'history', 00H
$SG4294588844 DB 00H, 00H
	ORG $+2
$SG4294588852 DB 'create table [history]([url] varchar(2560) NOT NULL,[pu'
	DB	'b_day] int NOT NULL,', 09H, 09H, '[title] varchar(256), [pub_'
	DB	'time] int, [id] int,', 09H, 09H, 'CONSTRAINT pk_PersonID PRIM'
	DB	'ARY KEY (url,pub_day))', 00H
$SG4294588853 DB 'CREATE TABLE [ConfigTable]([ver] [float] NOT NULL PRIMA'
	DB	'RY KEY,UNIQUE (ver))', 00H
$SG4294588841 DB '%4d-%2d-%2d %2d:%2d:%2d', 00H
$SG4294588834 DB '\', 00H
	ORG $+2
$SG4294588833 DB 'zbrowse.db', 00H
	ORG $+1
$SG4294588838 DB '%04d-%02d-%02d', 00H
	ORG $+1
$SG4294588839 DB '%04d-%02d-%02d %02d:%02d:%02d ', 00H
	ORG $+1
$SG4294588836 DB '%04d-%02d-%02d', 00H
	ORG $+1
$SG4294588837 DB '%04d:%02d:%02d %02d:%02d:%02d ', 00H
	ORG $+1
?g_strCreateTb@@3PAPBDA DD FLAT:$SG4294588853		; g_strCreateTb
	DD	FLAT:$SG4294588852
	DD	FLAT:$SG4294588851
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
?g_strTableName@@3PAPBDA DD FLAT:$SG4294588856		; g_strTableName
	DD	FLAT:$SG4294588855
	DD	FLAT:$SG4294588854
?g_strInsert@@3PAPBDA DD FLAT:$SG4294588850		; g_strInsert
	DD	FLAT:$SG4294588849
	DD	FLAT:$SG4294588848
$SG4294590666 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294590667 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294590664 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294590665 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590668 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294590669 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294590658 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294590659 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590656 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294590657 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590662 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294590663 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590660 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590661 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590650 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294590651 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294590648 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294590649 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294590654 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294590655 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294590652 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294590653 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294590642 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590643 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590640 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590641 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590646 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590647 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294590644 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590645 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294590634 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590635 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590632 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590633 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590638 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590639 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590636 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294590637 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590626 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590627 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590624 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590625 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590630 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590631 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590628 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590629 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590618 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294590619 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294590616 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590617 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590622 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590623 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590620 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294590621 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590610 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294590611 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590608 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590609 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590614 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590615 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294590612 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590613 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590602 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294590603 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590600 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294590601 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590606 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294590607 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294590604 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590605 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294590594 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294590595 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294590592 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590593 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294590598 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294590599 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294590596 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294590597 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294590586 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590587 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294590584 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590585 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590590 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294590591 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294590588 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590589 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294590578 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294590579 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294590576 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294590577 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294590582 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294590583 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294590580 DB 00H, 00H
	ORG $+2
$SG4294590581 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294590570 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294590571 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294590568 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294590569 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294590574 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294590575 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294590572 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294590573 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294590562 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294590563 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294590560 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294590561 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294590566 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294590567 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294590564 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294590565 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294590554 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294590555 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294590552 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294590553 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294590558 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294590559 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294590556 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294590557 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294590546 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294590547 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294590544 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294590545 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294590550 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294590551 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294590548 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294590549 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294590538 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294590539 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590536 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294590537 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294590542 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294590543 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294590540 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294590541 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294590530 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590531 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294590528 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294590529 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294590534 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294590535 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294590532 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294590533 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294590522 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294590523 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590520 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590521 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590526 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294590527 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294590524 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294590525 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294590514 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590515 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590512 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294590513 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590518 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590519 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590516 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590517 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294590506 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590507 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590504 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294590505 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294590510 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294590511 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294590508 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590509 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590498 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294590499 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294590496 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294590497 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294590502 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294590503 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294590500 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294590501 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294590490 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294590491 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590488 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590489 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294590494 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294590495 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294590492 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294590493 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294590482 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294590483 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294590480 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294590481 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294590486 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294590487 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590484 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294590485 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294590474 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294590475 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294590472 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294590473 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294590478 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590479 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294590476 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294590477 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294590466 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294590467 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294590464 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294590465 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294590470 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294590471 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294590468 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294590469 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
PUBLIC	?OnHistoryItemClick@CSqliteDataMgr@@QAEXHHH@Z	; CSqliteDataMgr::OnHistoryItemClick
PUBLIC	?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; CSqliteDataMgr::GetDataFilePathA
PUBLIC	?GetWebfavSetString@CSqliteDataMgr@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; CSqliteDataMgr::GetWebfavSetString
PUBLIC	?GetHistorySetString@CSqliteDataMgr@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; CSqliteDataMgr::GetHistorySetString
PUBLIC	?AddHistoryItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z ; CSqliteDataMgr::AddHistoryItem
PUBLIC	?sqlistcallback@@YAHPAXHPAPAD1@Z		; sqlistcallback
PUBLIC	?GetFavCount@CWebfavProtocols@@QAEHXZ		; CWebfavProtocols::GetFavCount
PUBLIC	?WebfavFind@CSqliteDataMgr@@QAEHPB_W@Z		; CSqliteDataMgr::WebfavFind
PUBLIC	?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; CSqliteDataMgr::GetHomePageUrl
PUBLIC	?GetFavAt@CSqliteDataMgr@@QAEPAUWebfavItem@@H@Z	; CSqliteDataMgr::GetFavAt
PUBLIC	?GetWebfavCount@CSqliteDataMgr@@QAEHXZ		; CSqliteDataMgr::GetWebfavCount
PUBLIC	?UpdateWebfavItemId@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; CSqliteDataMgr::UpdateWebfavItemId
PUBLIC	?DelWebfavItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; CSqliteDataMgr::DelWebfavItem
PUBLIC	?AddWebfavItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@00@Z ; CSqliteDataMgr::AddWebfavItem
PUBLIC	?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ	; CSqliteDataMgr::LoadHistroyRecord
PUBLIC	?InitDataDB@CSqliteDataMgr@@QAEXXZ		; CSqliteDataMgr::InitDataDB
PUBLIC	??1CSqliteDataMgr@@QAE@XZ			; CSqliteDataMgr::~CSqliteDataMgr
PUBLIC	??0CSqliteDataMgr@@QAE@XZ			; CSqliteDataMgr::CSqliteDataMgr
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
__ehfuncinfo$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0
__ehfuncinfo$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$1
	DD	00H
	DD	FLAT:__unwindfunclet$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$0
__ehfuncinfo$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ$0
__ehfuncinfo$?InitDataDB@CSqliteDataMgr@@QAEXXZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?InitDataDB@CSqliteDataMgr@@QAEXXZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?InitDataDB@CSqliteDataMgr@@QAEXXZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?InitDataDB@CSqliteDataMgr@@QAEXXZ$0
__ehfuncinfo$??1CSqliteDataMgr@@QAE@XZ DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$??1CSqliteDataMgr@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1CSqliteDataMgr@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1CSqliteDataMgr@@QAE@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??1CSqliteDataMgr@@QAE@XZ$1
	DD	01H
	DD	FLAT:__unwindfunclet$??1CSqliteDataMgr@@QAE@XZ$2
__ehfuncinfo$??0CSqliteDataMgr@@QAE@XZ DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$??0CSqliteDataMgr@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0CSqliteDataMgr@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0CSqliteDataMgr@@QAE@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??0CSqliteDataMgr@@QAE@XZ$1
	DD	01H
	DD	FLAT:__unwindfunclet$??0CSqliteDataMgr@@QAE@XZ$2
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0CSqliteDataMgr@@QAE@XZ PROC				; CSqliteDataMgr::CSqliteDataMgr
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0CSqliteDataMgr@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0CDbSqlite@@QAE@XZ	; CDbSqlite::CDbSqlite
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CSqliteDataMgr@@6B@
  0003b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003e	83 c1 68	 add	 ecx, 104		; 00000068H
  00041	e8 00 00 00 00	 call	 ??0CHistoryProtocols@@QAE@XZ ; CHistoryProtocols::CHistoryProtocols
  00046	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  0004a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0004d	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  00053	e8 00 00 00 00	 call	 ??0CWebfavProtocols@@QAE@XZ ; CWebfavProtocols::CWebfavProtocols
  00058	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2

  0005c	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00063	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00066	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00069	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00070	83 c4 10	 add	 esp, 16			; 00000010H
  00073	3b ec		 cmp	 ebp, esp
  00075	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0007a	8b e5		 mov	 esp, ebp
  0007c	5d		 pop	 ebp
  0007d	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??0CSqliteDataMgr@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1CDbSqlite@@QAE@XZ	; CDbSqlite::~CDbSqlite
__unwindfunclet$??0CSqliteDataMgr@@QAE@XZ$1:
  00008	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000b	83 c1 68	 add	 ecx, 104		; 00000068H
  0000e	e9 00 00 00 00	 jmp	 ??1CHistoryProtocols@@QAE@XZ ; CHistoryProtocols::~CHistoryProtocols
__unwindfunclet$??0CSqliteDataMgr@@QAE@XZ$2:
  00013	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00016	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0001c	e9 00 00 00 00	 jmp	 ??1CWebfavProtocols@@QAE@XZ ; CWebfavProtocols::~CWebfavProtocols
__ehhandler$??0CSqliteDataMgr@@QAE@XZ:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0CSqliteDataMgr@@QAE@XZ
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0CSqliteDataMgr@@QAE@XZ ENDP				; CSqliteDataMgr::CSqliteDataMgr
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1CSqliteDataMgr@@QAE@XZ PROC				; CSqliteDataMgr::~CSqliteDataMgr
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1CSqliteDataMgr@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00026	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CSqliteDataMgr@@6B@
  0002c	c7 45 fc 02 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 2

  00033	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00037	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003a	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  00040	e8 00 00 00 00	 call	 ??1CWebfavProtocols@@QAE@XZ ; CWebfavProtocols::~CWebfavProtocols
  00045	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00049	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0004c	83 c1 68	 add	 ecx, 104		; 00000068H
  0004f	e8 00 00 00 00	 call	 ??1CHistoryProtocols@@QAE@XZ ; CHistoryProtocols::~CHistoryProtocols
  00054	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0005b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0005e	e8 00 00 00 00	 call	 ??1CDbSqlite@@QAE@XZ	; CDbSqlite::~CDbSqlite
  00063	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00066	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0006d	83 c4 10	 add	 esp, 16			; 00000010H
  00070	3b ec		 cmp	 ebp, esp
  00072	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00077	8b e5		 mov	 esp, ebp
  00079	5d		 pop	 ebp
  0007a	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??1CSqliteDataMgr@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1CDbSqlite@@QAE@XZ	; CDbSqlite::~CDbSqlite
__unwindfunclet$??1CSqliteDataMgr@@QAE@XZ$1:
  00008	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000b	83 c1 68	 add	 ecx, 104		; 00000068H
  0000e	e9 00 00 00 00	 jmp	 ??1CHistoryProtocols@@QAE@XZ ; CHistoryProtocols::~CHistoryProtocols
__unwindfunclet$??1CSqliteDataMgr@@QAE@XZ$2:
  00013	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00016	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0001c	e9 00 00 00 00	 jmp	 ??1CWebfavProtocols@@QAE@XZ ; CWebfavProtocols::~CWebfavProtocols
__ehhandler$??1CSqliteDataMgr@@QAE@XZ:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1CSqliteDataMgr@@QAE@XZ
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1CSqliteDataMgr@@QAE@XZ ENDP				; CSqliteDataMgr::~CSqliteDataMgr
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_sData$ = -44						; size = 24
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
?InitDataDB@CSqliteDataMgr@@QAEXXZ PROC			; CSqliteDataMgr::InitDataDB
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?InitDataDB@CSqliteDataMgr@@QAEXXZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 24	 sub	 esp, 36			; 00000024H
  0001b	56		 push	 esi
  0001c	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00021	89 45 d0	 mov	 DWORD PTR [ebp-48], eax
  00024	89 45 d4	 mov	 DWORD PTR [ebp-44], eax
  00027	89 45 d8	 mov	 DWORD PTR [ebp-40], eax
  0002a	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0002d	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00030	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00033	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00036	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00039	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0003c	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

  0003f	8d 4d d4	 lea	 ecx, DWORD PTR _sData$[ebp]
  00042	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00047	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

  0004e	8d 45 d4	 lea	 eax, DWORD PTR _sData$[ebp]
  00051	50		 push	 eax
  00052	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00055	e8 00 00 00 00	 call	 ?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; CSqliteDataMgr::GetDataFilePathA

  0005a	8b f4		 mov	 esi, esp
  0005c	6a ff		 push	 -1
  0005e	8d 4d d4	 lea	 ecx, DWORD PTR _sData$[ebp]
  00061	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00066	50		 push	 eax
  00067	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0006a	8b 11		 mov	 edx, DWORD PTR [ecx]
  0006c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0006f	8b 02		 mov	 eax, DWORD PTR [edx]
  00071	ff d0		 call	 eax
  00073	3b f4		 cmp	 esi, esp
  00075	e8 00 00 00 00	 call	 __RTC_CheckEsp

  0007a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00081	8d 4d d4	 lea	 ecx, DWORD PTR _sData$[ebp]
  00084	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00089	52		 push	 edx
  0008a	8b cd		 mov	 ecx, ebp
  0008c	50		 push	 eax
  0008d	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN7@InitDataDB
  00093	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00098	58		 pop	 eax
  00099	5a		 pop	 edx
  0009a	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0009d	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000a4	5e		 pop	 esi
  000a5	83 c4 30	 add	 esp, 48			; 00000030H
  000a8	3b ec		 cmp	 ebp, esp
  000aa	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000af	8b e5		 mov	 esp, ebp
  000b1	5d		 pop	 ebp
  000b2	c3		 ret	 0
  000b3	90		 npad	 1
$LN7@InitDataDB:
  000b4	01 00 00 00	 DD	 1
  000b8	00 00 00 00	 DD	 $LN6@InitDataDB
$LN6@InitDataDB:
  000bc	d4 ff ff ff	 DD	 -44			; ffffffd4H
  000c0	18 00 00 00	 DD	 24			; 00000018H
  000c4	00 00 00 00	 DD	 $LN4@InitDataDB
$LN4@InitDataDB:
  000c8	73		 DB	 115			; 00000073H
  000c9	44		 DB	 68			; 00000044H
  000ca	61		 DB	 97			; 00000061H
  000cb	74		 DB	 116			; 00000074H
  000cc	61		 DB	 97			; 00000061H
  000cd	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?InitDataDB@CSqliteDataMgr@@QAEXXZ$0:
  00000	8d 4d d4	 lea	 ecx, DWORD PTR _sData$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__ehhandler$?InitDataDB@CSqliteDataMgr@@QAEXXZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?InitDataDB@CSqliteDataMgr@@QAEXXZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?InitDataDB@CSqliteDataMgr@@QAEXXZ ENDP			; CSqliteDataMgr::InitDataDB
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
$T2 = -52						; size = 4
_sData$ = -44						; size = 24
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ PROC		; CSqliteDataMgr::LoadHistroyRecord
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 28	 sub	 esp, 40			; 00000028H
  0001b	57		 push	 edi
  0001c	51		 push	 ecx
  0001d	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  00020	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  00025	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002a	f3 ab		 rep stosd
  0002c	59		 pop	 ecx
  0002d	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

  00030	8d 4d d4	 lea	 ecx, DWORD PTR _sData$[ebp]
  00033	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00038	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

  0003f	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00042	50		 push	 eax
  00043	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00046	83 c1 68	 add	 ecx, 104		; 00000068H
  00049	e8 00 00 00 00	 call	 ?LoadData@CHistoryProtocols@@QAEHPAVCDbSqlite@@@Z ; CHistoryProtocols::LoadData

  0004e	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00051	51		 push	 ecx
  00052	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00055	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0005b	e8 00 00 00 00	 call	 ?LoadData@CWebfavProtocols@@QAEHPAVCDbSqlite@@@Z ; CWebfavProtocols::LoadData

  00060	c7 45 cc 01 00
	00 00		 mov	 DWORD PTR $T2[ebp], 1
  00067	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0006e	8d 4d d4	 lea	 ecx, DWORD PTR _sData$[ebp]
  00071	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00076	8b 45 cc	 mov	 eax, DWORD PTR $T2[ebp]

  00079	52		 push	 edx
  0007a	8b cd		 mov	 ecx, ebp
  0007c	50		 push	 eax
  0007d	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN7@LoadHistro
  00083	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00088	58		 pop	 eax
  00089	5a		 pop	 edx
  0008a	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0008d	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00094	5f		 pop	 edi
  00095	83 c4 34	 add	 esp, 52			; 00000034H
  00098	3b ec		 cmp	 ebp, esp
  0009a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0009f	8b e5		 mov	 esp, ebp
  000a1	5d		 pop	 ebp
  000a2	c3		 ret	 0
  000a3	90		 npad	 1
$LN7@LoadHistro:
  000a4	01 00 00 00	 DD	 1
  000a8	00 00 00 00	 DD	 $LN6@LoadHistro
$LN6@LoadHistro:
  000ac	d4 ff ff ff	 DD	 -44			; ffffffd4H
  000b0	18 00 00 00	 DD	 24			; 00000018H
  000b4	00 00 00 00	 DD	 $LN4@LoadHistro
$LN4@LoadHistro:
  000b8	73		 DB	 115			; 00000073H
  000b9	44		 DB	 68			; 00000044H
  000ba	61		 DB	 97			; 00000061H
  000bb	74		 DB	 116			; 00000074H
  000bc	61		 DB	 97			; 00000061H
  000bd	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ$0:
  00000	8d 4d d4	 lea	 ecx, DWORD PTR _sData$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__ehhandler$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?LoadHistroyRecord@CSqliteDataMgr@@QAEHXZ ENDP		; CSqliteDataMgr::LoadHistroyRecord
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_sUrl$ = 8						; size = 4
_sTitle$ = 12						; size = 4
_sIcon$ = 16						; size = 4
?AddWebfavItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@00@Z PROC ; CSqliteDataMgr::AddWebfavItem
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 45 10	 mov	 eax, DWORD PTR _sIcon$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 0c	 mov	 ecx, DWORD PTR _sTitle$[ebp]
  00015	51		 push	 ecx
  00016	8b 55 08	 mov	 edx, DWORD PTR _sUrl$[ebp]
  00019	52		 push	 edx
  0001a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001d	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  00023	e8 00 00 00 00	 call	 ?AddItem@CWebfavProtocols@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@00@Z ; CWebfavProtocols::AddItem

  00028	83 c4 04	 add	 esp, 4
  0002b	3b ec		 cmp	 ebp, esp
  0002d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00032	8b e5		 mov	 esp, ebp
  00034	5d		 pop	 ebp
  00035	c2 0c 00	 ret	 12			; 0000000cH
?AddWebfavItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@00@Z ENDP ; CSqliteDataMgr::AddWebfavItem
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_sUrl$ = 8						; size = 4
?DelWebfavItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z PROC ; CSqliteDataMgr::DelWebfavItem
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 45 08	 mov	 eax, DWORD PTR _sUrl$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0001b	e8 00 00 00 00	 call	 ?DelItem@CWebfavProtocols@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; CWebfavProtocols::DelItem

  00020	83 c4 04	 add	 esp, 4
  00023	3b ec		 cmp	 ebp, esp
  00025	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002a	8b e5		 mov	 esp, ebp
  0002c	5d		 pop	 ebp
  0002d	c2 04 00	 ret	 4
?DelWebfavItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ENDP ; CSqliteDataMgr::DelWebfavItem
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_sUrl$ = 8						; size = 4
_NewId$ = 12						; size = 4
?UpdateWebfavItemId@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z PROC ; CSqliteDataMgr::UpdateWebfavItemId
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 45 0c	 mov	 eax, DWORD PTR _NewId$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR _sUrl$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0001f	e8 00 00 00 00	 call	 ?UpdateWebfavItemId@CWebfavProtocols@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; CWebfavProtocols::UpdateWebfavItemId

  00024	83 c4 04	 add	 esp, 4
  00027	3b ec		 cmp	 ebp, esp
  00029	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002e	8b e5		 mov	 esp, ebp
  00030	5d		 pop	 ebp
  00031	c2 08 00	 ret	 8
?UpdateWebfavItemId@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ENDP ; CSqliteDataMgr::UpdateWebfavItemId
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetWebfavCount@CSqliteDataMgr@@QAEHXZ PROC		; CSqliteDataMgr::GetWebfavCount
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  00017	e8 00 00 00 00	 call	 ?GetFavCount@CWebfavProtocols@@QAEHXZ ; CWebfavProtocols::GetFavCount

  0001c	83 c4 04	 add	 esp, 4
  0001f	3b ec		 cmp	 ebp, esp
  00021	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00026	8b e5		 mov	 esp, ebp
  00028	5d		 pop	 ebp
  00029	c3		 ret	 0
?GetWebfavCount@CSqliteDataMgr@@QAEHXZ ENDP		; CSqliteDataMgr::GetWebfavCount
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_Index$ = 8						; size = 4
?GetFavAt@CSqliteDataMgr@@QAEPAUWebfavItem@@H@Z PROC	; CSqliteDataMgr::GetFavAt
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 45 08	 mov	 eax, DWORD PTR _Index$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0001b	e8 00 00 00 00	 call	 ?GetFavAt@CWebfavProtocols@@QAEPAUWebfavItem@@H@Z ; CWebfavProtocols::GetFavAt

  00020	83 c4 04	 add	 esp, 4
  00023	3b ec		 cmp	 ebp, esp
  00025	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002a	8b e5		 mov	 esp, ebp
  0002c	5d		 pop	 ebp
  0002d	c2 04 00	 ret	 4
?GetFavAt@CSqliteDataMgr@@QAEPAUWebfavItem@@H@Z ENDP	; CSqliteDataMgr::GetFavAt
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
$T2 = -52						; size = 4
_szUrl$ = -44						; size = 24
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ PROC ; CSqliteDataMgr::GetHomePageUrl
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 28	 sub	 esp, 40			; 00000028H
  0001b	57		 push	 edi
  0001c	51		 push	 ecx
  0001d	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  00020	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  00025	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002a	f3 ab		 rep stosd
  0002c	59		 pop	 ecx
  0002d	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00030	c7 45 cc 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

  00037	68 00 00 00 00	 push	 OFFSET $SG4294588832
  0003c	8d 4d d4	 lea	 ecx, DWORD PTR _szUrl$[ebp]
  0003f	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00044	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

  0004b	8d 45 d4	 lea	 eax, DWORD PTR _szUrl$[ebp]
  0004e	50		 push	 eax
  0004f	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00052	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00057	8b 4d cc	 mov	 ecx, DWORD PTR $T2[ebp]
  0005a	83 c9 01	 or	 ecx, 1
  0005d	89 4d cc	 mov	 DWORD PTR $T2[ebp], ecx
  00060	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00064	8d 4d d4	 lea	 ecx, DWORD PTR _szUrl$[ebp]
  00067	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  0006c	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

  0006f	52		 push	 edx
  00070	8b cd		 mov	 ecx, ebp
  00072	50		 push	 eax
  00073	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN9@GetHomePag
  00079	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0007e	58		 pop	 eax
  0007f	5a		 pop	 edx
  00080	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00083	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0008a	5f		 pop	 edi
  0008b	83 c4 34	 add	 esp, 52			; 00000034H
  0008e	3b ec		 cmp	 ebp, esp
  00090	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00095	8b e5		 mov	 esp, ebp
  00097	5d		 pop	 ebp
  00098	c2 04 00	 ret	 4
  0009b	90		 npad	 1
$LN9@GetHomePag:
  0009c	01 00 00 00	 DD	 1
  000a0	00 00 00 00	 DD	 $LN8@GetHomePag
$LN8@GetHomePag:
  000a4	d4 ff ff ff	 DD	 -44			; ffffffd4H
  000a8	18 00 00 00	 DD	 24			; 00000018H
  000ac	00 00 00 00	 DD	 $LN6@GetHomePag
$LN6@GetHomePag:
  000b0	73		 DB	 115			; 00000073H
  000b1	7a		 DB	 122			; 0000007aH
  000b2	55		 DB	 85			; 00000055H
  000b3	72		 DB	 114			; 00000072H
  000b4	6c		 DB	 108			; 0000006cH
  000b5	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$0:
  00000	8d 4d d4	 lea	 ecx, DWORD PTR _szUrl$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$1:
  00008	8b 45 cc	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN5@GetHomePag
  00014	83 65 cc fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN5@GetHomePag:
  00020	c3		 ret	 0
__ehhandler$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?GetHomePageUrl@CSqliteDataMgr@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ENDP ; CSqliteDataMgr::GetHomePageUrl
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_Url$ = 8						; size = 4
?WebfavFind@CSqliteDataMgr@@QAEHPB_W@Z PROC		; CSqliteDataMgr::WebfavFind
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 45 08	 mov	 eax, DWORD PTR _Url$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0001b	e8 00 00 00 00	 call	 ?WebfavFind@CWebfavProtocols@@QAEHPB_W@Z ; CWebfavProtocols::WebfavFind

  00020	83 c4 04	 add	 esp, 4
  00023	3b ec		 cmp	 ebp, esp
  00025	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002a	8b e5		 mov	 esp, ebp
  0002c	5d		 pop	 ebp
  0002d	c2 04 00	 ret	 4
?WebfavFind@CSqliteDataMgr@@QAEHPB_W@Z ENDP		; CSqliteDataMgr::WebfavFind
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\customprotocols\webfavprotocols.h
;	COMDAT ?GetFavCount@CWebfavProtocols@@QAEHXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetFavCount@CWebfavProtocols@@QAEHXZ PROC		; CWebfavProtocols::GetFavCount, COMDAT
; _this$ = ecx

; 38   : 	int GetFavCount() { return m_vecHistory.size(); };

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	81 c1 c0 00 00
	00		 add	 ecx, 192		; 000000c0H
  00017	e8 00 00 00 00	 call	 ?size@?$vector@PAUBaseItem@@V?$allocator@PAUBaseItem@@@std@@@std@@QBEIXZ ; std::vector<BaseItem *,std::allocator<BaseItem *> >::size
  0001c	83 c4 04	 add	 esp, 4
  0001f	3b ec		 cmp	 ebp, esp
  00021	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00026	8b e5		 mov	 esp, ebp
  00028	5d		 pop	 ebp
  00029	c3		 ret	 0
?GetFavCount@CWebfavProtocols@@QAEHXZ ENDP		; CWebfavProtocols::GetFavCount
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_p$ = 8							; size = 4
_nCount$ = 12						; size = 4
_pValues$ = 16						; size = 4
_pKey$ = 20						; size = 4
?sqlistcallback@@YAHPAXHPAPAD1@Z PROC			; sqlistcallback

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

  00003	b8 01 00 00 00	 mov	 eax, 1

  00008	5d		 pop	 ebp
  00009	c3		 ret	 0
?sqlistcallback@@YAHPAXHPAPAD1@Z ENDP			; sqlistcallback
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_sUrl$ = 8						; size = 4
_sTitle$ = 12						; size = 4
?AddHistoryItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z PROC ; CSqliteDataMgr::AddHistoryItem
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 45 0c	 mov	 eax, DWORD PTR _sTitle$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR _sUrl$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	83 c1 68	 add	 ecx, 104		; 00000068H
  0001c	e8 00 00 00 00	 call	 ?AddItem@CHistoryProtocols@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z ; CHistoryProtocols::AddItem

  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 08 00	 ret	 8
?AddHistoryItem@CSqliteDataMgr@@QAEHAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z ENDP ; CSqliteDataMgr::AddHistoryItem
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetHistorySetString@CSqliteDataMgr@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ PROC ; CSqliteDataMgr::GetHistorySetString
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	83 c1 68	 add	 ecx, 104		; 00000068H
  00014	e8 00 00 00 00	 call	 ?GetHistorySetString@CHistoryProtocols@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; CHistoryProtocols::GetHistorySetString

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?GetHistorySetString@CSqliteDataMgr@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ENDP ; CSqliteDataMgr::GetHistorySetString
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetWebfavSetString@CSqliteDataMgr@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ PROC ; CSqliteDataMgr::GetWebfavSetString
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  00017	e8 00 00 00 00	 call	 ?GetWebfavSetString@CWebfavProtocols@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; CWebfavProtocols::GetWebfavSetString

  0001c	83 c4 04	 add	 esp, 4
  0001f	3b ec		 cmp	 ebp, esp
  00021	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00026	8b e5		 mov	 esp, ebp
  00028	5d		 pop	 ebp
  00029	c3		 ret	 0
?GetWebfavSetString@CSqliteDataMgr@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ENDP ; CSqliteDataMgr::GetWebfavSetString
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
tv82 = -48						; size = 4
tv83 = -44						; size = 4
$T2 = -40						; size = 24
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_sPath$ = 8						; size = 4
?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z PROC ; CSqliteDataMgr::GetDataFilePathA
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 24	 sub	 esp, 36			; 00000024H
  0001b	56		 push	 esi
  0001c	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00021	89 45 d0	 mov	 DWORD PTR [ebp-48], eax
  00024	89 45 d4	 mov	 DWORD PTR [ebp-44], eax
  00027	89 45 d8	 mov	 DWORD PTR [ebp-40], eax
  0002a	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0002d	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00030	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00033	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00036	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00039	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0003c	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

  0003f	68 00 00 00 00	 push	 OFFSET $SG4294588835
  00044	8b 4d 08	 mov	 ecx, DWORD PTR _sPath$[ebp]
  00047	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=

  0004c	8d 45 d8	 lea	 eax, DWORD PTR $T2[ebp]
  0004f	50		 push	 eax
  00050	e8 00 00 00 00	 call	 ?GetConfigFolderPathA@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; GetConfigFolderPathA
  00055	83 c4 04	 add	 esp, 4
  00058	89 45 d4	 mov	 DWORD PTR tv83[ebp], eax
  0005b	8b 4d d4	 mov	 ecx, DWORD PTR tv83[ebp]
  0005e	89 4d d0	 mov	 DWORD PTR tv82[ebp], ecx
  00061	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00068	8b 55 d0	 mov	 edx, DWORD PTR tv82[ebp]
  0006b	52		 push	 edx
  0006c	8b 4d 08	 mov	 ecx, DWORD PTR _sPath$[ebp]
  0006f	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
  00074	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0007b	8d 4d d8	 lea	 ecx, DWORD PTR $T2[ebp]
  0007e	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

  00083	68 00 00 00 00	 push	 OFFSET $SG4294588834
  00088	8b 4d 08	 mov	 ecx, DWORD PTR _sPath$[ebp]
  0008b	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=

  00090	68 00 00 00 00	 push	 OFFSET $SG4294588833
  00095	8b 4d 08	 mov	 ecx, DWORD PTR _sPath$[ebp]
  00098	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=

  0009d	8b 4d 08	 mov	 ecx, DWORD PTR _sPath$[ebp]
  000a0	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  000a5	8b f4		 mov	 esi, esp
  000a7	50		 push	 eax
  000a8	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__PathFileExistsA@4
  000ae	3b f4		 cmp	 esi, esp
  000b0	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b5	85 c0		 test	 eax, eax
  000b7	74 07		 je	 SHORT $LN2@GetDataFil

  000b9	b8 01 00 00 00	 mov	 eax, 1
  000be	eb 02		 jmp	 SHORT $LN1@GetDataFil
$LN2@GetDataFil:

  000c0	33 c0		 xor	 eax, eax
$LN1@GetDataFil:

  000c2	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000c5	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000cc	5e		 pop	 esi
  000cd	83 c4 30	 add	 esp, 48			; 00000030H
  000d0	3b ec		 cmp	 ebp, esp
  000d2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000d7	8b e5		 mov	 esp, ebp
  000d9	5d		 pop	 ebp
  000da	c2 04 00	 ret	 4
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR $T2[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__ehhandler$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?GetDataFilePathA@CSqliteDataMgr@@QAEHAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ENDP ; CSqliteDataMgr::GetDataFilePathA
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\sqlite\sqlitedatamgr.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_nId$ = 8						; size = 4
_bCheck$ = 12						; size = 4
_type$ = 16						; size = 4
?OnHistoryItemClick@CSqliteDataMgr@@QAEXHHH@Z PROC	; CSqliteDataMgr::OnHistoryItemClick
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

  0000e	83 7d 10 00	 cmp	 DWORD PTR _type$[ebp], 0
  00012	75 15		 jne	 SHORT $LN2@OnHistoryI

  00014	8b 45 0c	 mov	 eax, DWORD PTR _bCheck$[ebp]
  00017	50		 push	 eax
  00018	8b 4d 08	 mov	 ecx, DWORD PTR _nId$[ebp]
  0001b	51		 push	 ecx
  0001c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001f	83 c1 68	 add	 ecx, 104		; 00000068H
  00022	e8 00 00 00 00	 call	 ?OnHistoryItemClick@CHistoryProtocols@@QAEXHH@Z ; CHistoryProtocols::OnHistoryItemClick

  00027	eb 16		 jmp	 SHORT $LN1@OnHistoryI
$LN2@OnHistoryI:

  00029	8b 55 0c	 mov	 edx, DWORD PTR _bCheck$[ebp]
  0002c	52		 push	 edx
  0002d	8b 45 08	 mov	 eax, DWORD PTR _nId$[ebp]
  00030	50		 push	 eax
  00031	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  0003a	e8 00 00 00 00	 call	 ?OnWebfavItemClick@CWebfavProtocols@@QAEXHH@Z ; CWebfavProtocols::OnWebfavItemClick
$LN1@OnHistoryI:

  0003f	83 c4 04	 add	 esp, 4
  00042	3b ec		 cmp	 ebp, esp
  00044	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00049	8b e5		 mov	 esp, ebp
  0004b	5d		 pop	 ebp
  0004c	c2 0c 00	 ret	 12			; 0000000cH
?OnHistoryItemClick@CSqliteDataMgr@@QAEXHHH@Z ENDP	; CSqliteDataMgr::OnHistoryItemClick
_TEXT	ENDS
END
