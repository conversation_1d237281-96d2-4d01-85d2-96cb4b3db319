// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace console {
extern builtin ConsoleAssert(implicit context: Context)(
    JSFunction, JSAny, int32): JSAny;

javascript builtin FastConsoleAssert(
    js-implicit context: NativeContext, receiver: J<PERSON>ny, newTarget: J<PERSON><PERSON>,
    target: JSFunction)(...arguments): JSAny {
  if (ToBoolean(arguments[0])) {
    return Undefined;
  } else {
    tail ConsoleAssert(
        target, newTarget, Convert<int32>(arguments.actual_count));
  }
}
}
