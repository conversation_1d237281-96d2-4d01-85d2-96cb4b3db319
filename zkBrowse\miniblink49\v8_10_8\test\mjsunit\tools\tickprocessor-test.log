shared-library,shell,0x08048000,0x081ee000,0
shared-library,/lib32/libm-2.7.so,0xf7db6000,0xf7dd9000,0
shared-library,ffffe000-fffff000,0xffffe000,0xfffff000,0
profiler,begin,1
code-creation,Stub,0,100,0xf540a100,474,CEntryStub
code-creation,Scrip<PERSON>,0,101,0xf541cd80,736,exp.js
code-creation,Stub,0,102,0xf541d0e0,47,RuntimeStub_Math_exp
code-creation,LazyCompile,0,103,0xf541d120,145,exp native math.js:41
function-creation,0xf441d280,0xf541d120
code-creation,LoadIC,0,104,0xf541d280,117,j
code-creation,LoadIC,0,105,0xf541d360,63,i
tick,0x80f82d1,0,0,0,0,0xf541<PERSON>5c
tick,0x80f89a1,0,0,0,0,0xf541ce5c
tick,0x8123b5c,0,0,0,0,0xf541d1a1,0xf541ceea
tick,0x8123b65,0,0,0,0,0xf541d1a1,0xf541ceea
tick,0xf541d2be,0,0,0,0
tick,0xf541d320,0,0,0,0
tick,0xf541d384,0,0,0,0
tick,0xf7db94da,0,0,0,0,0xf541d1a1,0xf541ceea
tick,0xf7db951c,0,0,0,0,0xf541d1a1,0xf541ceea
tick,0xf7dbc508,0,0,0,0,0xf541d1a1,0xf541ceea
tick,0xf7dbff21,0,0,0,0,0xf541d1a1,0xf541ceea
tick,0xf7edec90,0,0,0,0,0xf541d1a1,0xf541ceea
tick,0xffffe402,0,0,0,0
profiler,end
