﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
LINK : warning LNK4044: 无法识别的选项“/Bv”；已忽略
LINK : warning LNK4075: 忽略“/INCREMENTAL”(由于“/LTCG”规范)
    正在创建库 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\miniblink_4975.lib 和对象 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\miniblink_4975.exp
orig_chrome.lib(ffmpeg_demuxer.obj) : error LNK2001: 无法解析的外部符号 "public: void __thiscall media::ScopedPtrAVFreePacket::operator()(void *)const " (??RScopedPtrAVFreePacket@media@@QBEXPAX@Z)
orig_chrome.lib(ffmpeg_audio_decoder.obj) : error LNK2001: 无法解析的外部符号 "public: void __thiscall media::ScopedPtrAVFreeFrame::operator()(void *)const " (??RScopedPtrAVFreeFrame@media@@QBEXPAX@Z)
orig_chrome.lib(ffmpeg_audio_decoder.obj) : error LNK2001: 无法解析的外部符号 "public: void __thiscall media::ScopedPtrAVFreeContext::operator()(void *)const " (??RScopedPtrAVFreeContext@media@@QBEXPAX@Z)
orig_chrome.lib(ffmpeg_glue.obj) : error LNK2001: 无法解析的外部符号 "public: void __thiscall media::ScopedPtrAVFree::operator()(void *)const " (??RScopedPtrAVFree@media@@QBEXPAX@Z)
E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\miniblink_4975_x32.dll : warning LNK4088: 因 /FORCE 选项生成了映像；映像可能不能运行
  正在生成代码
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\libxml\src\xpath.c(2769): warning C4789: 缓冲区“work”(大小为 28 字节)将溢出；1 字节将在偏移 98 时开始写入
  已完成代码的生成
orig_chrome.lib(ffmpeg_demuxer.obj) : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall media::ScopedPtrAVFreePacket::operator()(void *)const " (__imp_??RScopedPtrAVFreePacket@media@@QBEXPAX@Z)
orig_chrome.lib(ffmpeg_audio_decoder.obj) : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall media::ScopedPtrAVFreeContext::operator()(void *)const " (__imp_??RScopedPtrAVFreeContext@media@@QBEXPAX@Z)
orig_chrome.lib(ffmpeg_audio_decoder.obj) : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall media::ScopedPtrAVFreeFrame::operator()(void *)const " (__imp_??RScopedPtrAVFreeFrame@media@@QBEXPAX@Z)
orig_chrome.lib(ffmpeg_glue.obj) : error LNK2001: 无法解析的外部符号 "__declspec(dllimport) public: void __thiscall media::ScopedPtrAVFree::operator()(void *)const " (__imp_??RScopedPtrAVFree@media@@QBEXPAX@Z)
E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\miniblink_4975_x32.dll : warning LNK4088: 因 /FORCE 选项生成了映像；映像可能不能运行
