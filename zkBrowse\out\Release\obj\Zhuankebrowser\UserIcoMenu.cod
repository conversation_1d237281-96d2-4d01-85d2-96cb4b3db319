; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\control\userico\usericomenu.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	??_7CUserIcoMenuUIEx@@6BIContainerUI@DuiLib@@@	; CUserIcoMenuUIEx::`vftable'
PUBLIC	??_7CUserIcoMenuUIEx@@6BCControlUI@DuiLib@@@	; CUserIcoMenuUIEx::`vftable'
PUBLIC	??_C@_1CA@OLPILCOH@?$AAm?$AAe?$AAn?$AAu?$AA_?$AAs?$AAe?$AAl?$AAe?$AAc?$AAt?$AA?4?$AAp?$AAn?$AAg@ ; `string'
PUBLIC	??_7CUserIcoMenuUIEx@@6BINotifyUI@DuiLib@@@	; CUserIcoMenuUIEx::`vftable'
PUBLIC	??_7CUserIcoMenu@@6B@				; CUserIcoMenu::`vftable'
PUBLIC	??_7CUserIcoMenuUIEx@@6BCListUI@DuiLib@@@	; CUserIcoMenuUIEx::`vftable'
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
_BSS	ENDS
;	COMDAT ??_7CUserIcoMenuUIEx@@6BCListUI@DuiLib@@@
CONST	SEGMENT
??_7CUserIcoMenuUIEx@@6BCListUI@DuiLib@@@ DD FLAT:?GetListInfo@CListUI@DuiLib@@UAEPAUtagTListInfoUI@2@XZ ; CUserIcoMenuUIEx::`vftable'
	DD	FLAT:?GetCurSel@CListUI@DuiLib@@UBEHXZ
	DD	FLAT:?SelectItem@CListUI@DuiLib@@UAE_NH_N0@Z
	DD	FLAT:?DoEvent@CListUI@DuiLib@@WIGA@AEXAAUtagTEventUI@2@@Z
	DD	FLAT:?ExpandItem@CListUI@DuiLib@@UAE_NH_N@Z
	DD	FLAT:?GetExpandedItem@CListUI@DuiLib@@UBEHXZ
	DD	FLAT:?GetHeader@CListUI@DuiLib@@UBEPAVCListHeaderUI@2@XZ
	DD	FLAT:?GetList@CListUI@DuiLib@@UBEPAVCContainerUI@2@XZ
	DD	FLAT:?GetTextCallback@CListUI@DuiLib@@UBEPAVIListCallbackUI@2@XZ
	DD	FLAT:?SetTextCallback@CListUI@DuiLib@@UAEXPAVIListCallbackUI@2@@Z
CONST	ENDS
;	COMDAT ??_7CUserIcoMenu@@6B@
CONST	SEGMENT
??_7CUserIcoMenu@@6B@ DD FLAT:?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z ; CUserIcoMenu::`vftable'
CONST	ENDS
;	COMDAT ??_7CUserIcoMenuUIEx@@6BINotifyUI@DuiLib@@@
CONST	SEGMENT
??_7CUserIcoMenuUIEx@@6BINotifyUI@DuiLib@@@ DD FLAT:?Notify@CMenuUI@DuiLib@@UAEXAAUtagTNotifyUI@2@@Z ; CUserIcoMenuUIEx::`vftable'
CONST	ENDS
;	COMDAT ??_C@_1CA@OLPILCOH@?$AAm?$AAe?$AAn?$AAu?$AA_?$AAs?$AAe?$AAl?$AAe?$AAc?$AAt?$AA?4?$AAp?$AAn?$AAg@
CONST	SEGMENT
??_C@_1CA@OLPILCOH@?$AAm?$AAe?$AAn?$AAu?$AA_?$AAs?$AAe?$AAl?$AAe?$AAc?$AAt?$AA?4?$AAp?$AAn?$AAg@ DB 'm'
	DB	00H, 'e', 00H, 'n', 00H, 'u', 00H, '_', 00H, 's', 00H, 'e', 00H
	DB	'l', 00H, 'e', 00H, 'c', 00H, 't', 00H, '.', 00H, 'p', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H			; `string'
CONST	ENDS
;	COMDAT ??_7CUserIcoMenuUIEx@@6BCControlUI@DuiLib@@@
CONST	SEGMENT
??_7CUserIcoMenuUIEx@@6BCControlUI@DuiLib@@@ DD FLAT:?Delete@CControlUI@DuiLib@@UAEXXZ ; CUserIcoMenuUIEx::`vftable'
	DD	FLAT:??_ECUserIcoMenuUIEx@@UAEPAXI@Z
	DD	FLAT:?GetName@CControlUI@DuiLib@@UBE?AVCDuiString@2@XZ
	DD	FLAT:?SetName@CControlUI@DuiLib@@UAEXPB_W@Z
	DD	FLAT:?GetClass@CListUI@DuiLib@@UBEPB_WXZ
	DD	FLAT:?GetInterface@CMenuUI@DuiLib@@UAEPAXPB_W@Z
	DD	FLAT:?GetControlFlags@CListUI@DuiLib@@UBEIXZ
	DD	FLAT:?GetNativeWindow@CControlUI@DuiLib@@UBEPAUHWND__@@XZ
	DD	FLAT:?Activate@CControlUI@DuiLib@@UAE_NXZ
	DD	FLAT:?GetManager@CControlUI@DuiLib@@UBEPAVCPaintManagerUI@2@XZ
	DD	FLAT:?SetManager@CContainerUI@DuiLib@@UAEXPAVCPaintManagerUI@2@PAVCControlUI@2@_N@Z
	DD	FLAT:?GetParent@CControlUI@DuiLib@@UBEPAV12@XZ
	DD	FLAT:?GetCover@CControlUI@DuiLib@@UBEPAV12@XZ
	DD	FLAT:?SetCover@CControlUI@DuiLib@@UAEXPAV12@@Z
	DD	FLAT:?SetUserToolTipXml@CControlUI@DuiLib@@UAEXPB_W@Z
	DD	FLAT:?GetUserToolTipXml@CControlUI@DuiLib@@UBE?AVCDuiString@2@XZ
	DD	FLAT:?SetToolTipCallBack@CControlUI@DuiLib@@UAEXPAVIToolTipCallBack@2@@Z
	DD	FLAT:?GetToolTipCallback@CControlUI@DuiLib@@UBEPAVIToolTipCallBack@2@XZ
	DD	FLAT:?SetUserToolTipBkColor@CControlUI@DuiLib@@UAEXK@Z
	DD	FLAT:?GetUserToolTipBkColor@CControlUI@DuiLib@@UBEKXZ
	DD	FLAT:?GetText@CControlUI@DuiLib@@UBE?AVCDuiString@2@XZ
	DD	FLAT:?SetText@CControlUI@DuiLib@@UAEXPB_W@Z
	DD	FLAT:?GetPos@CControlUI@DuiLib@@UBEABUtagRECT@@XZ
	DD	FLAT:?GetRelativePos@CControlUI@DuiLib@@UBE?AUtagRECT@@XZ
	DD	FLAT:?GetClientPos@CContainerUI@DuiLib@@UBE?AUtagRECT@@XZ
	DD	FLAT:?SetPos@CListUI@DuiLib@@UAEXUtagRECT@@_N@Z
	DD	FLAT:?Move@CListUI@DuiLib@@UAEXUtagSIZE@@_N@Z
	DD	FLAT:?GetWidth@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?GetHeight@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?GetX@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?GetY@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?GetPadding@CControlUI@DuiLib@@UBE?AUtagRECT@@XZ
	DD	FLAT:?SetPadding@CControlUI@DuiLib@@UAEXUtagRECT@@@Z
	DD	FLAT:?GetFixedXY@CControlUI@DuiLib@@UBE?AUtagSIZE@@XZ
	DD	FLAT:?GetFixedXY2@CControlUI@DuiLib@@UBE?AUtagSIZE@@XZ
	DD	FLAT:?SetFixedXY@CControlUI@DuiLib@@UAEXUtagSIZE@@@Z
	DD	FLAT:?GetFloatPercent@CControlUI@DuiLib@@UBE?AUtagTPercentInfo@2@XZ
	DD	FLAT:?SetFloatPercent@CControlUI@DuiLib@@UAEXUtagTPercentInfo@2@@Z
	DD	FLAT:?GetFixedWidth@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?SetFixedWidth@CControlUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetFixedHeight@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?SetFixedHeight@CControlUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetMinWidth@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?SetMinWidth@CControlUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetMaxWidth@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?SetMaxWidth@CControlUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetMinHeight@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?SetMinHeight@CControlUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetMaxHeight@CControlUI@DuiLib@@UBEHXZ
	DD	FLAT:?SetMaxHeight@CControlUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetToolTip@CControlUI@DuiLib@@UBE?AVCDuiString@2@XZ
	DD	FLAT:?SetToolTip@CControlUI@DuiLib@@UAEXPB_W@Z
	DD	FLAT:?SetToolTipWidth@CControlUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetToolTipWidth@CControlUI@DuiLib@@UAEHXZ
	DD	FLAT:?GetShortcut@CControlUI@DuiLib@@UBE_WXZ
	DD	FLAT:?SetShortcut@CControlUI@DuiLib@@UAEX_W@Z
	DD	FLAT:?IsContextMenuUsed@CControlUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetContextMenuUsed@CControlUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?GetUserData@CControlUI@DuiLib@@UAEABVCDuiString@2@XZ
	DD	FLAT:?SetUserData@CControlUI@DuiLib@@UAEXPB_W@Z
	DD	FLAT:?GetTag@CControlUI@DuiLib@@UBEIXZ
	DD	FLAT:?SetTag@CControlUI@DuiLib@@UAEXI@Z
	DD	FLAT:?GetData@CControlUI@DuiLib@@UBEPAXXZ
	DD	FLAT:?SetData@CControlUI@DuiLib@@UAEXPAX@Z
	DD	FLAT:?IsVisible@CControlUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetVisible@CContainerUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?SetInternVisible@CContainerUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?IsEnabled@CControlUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetEnabled@CControlUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?IsMouseEnabled@CControlUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetMouseEnabled@CContainerUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?IsKeyboardEnabled@CControlUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetKeyboardEnabled@CControlUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?IsFocused@CControlUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetFocus@CControlUI@DuiLib@@UAEXXZ
	DD	FLAT:?IsFloat@CControlUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetFloat@CControlUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?FindControl@CContainerUI@DuiLib@@UAEPAVCControlUI@2@P6GPAV32@PAV32@PAX@Z1I@Z
	DD	FLAT:?Init@CControlUI@DuiLib@@UAEXXZ
	DD	FLAT:?DoInit@CControlUI@DuiLib@@UAEXXZ
	DD	FLAT:?Event@CControlUI@DuiLib@@UAEXAAUtagTEventUI@2@@Z
	DD	FLAT:?DoEvent@CListUI@DuiLib@@UAEXAAUtagTEventUI@2@@Z
	DD	FLAT:?GetAttribute@CControlUI@DuiLib@@UAE?AVCDuiString@2@PB_W@Z
	DD	FLAT:?SetAttribute@CMenuUI@DuiLib@@UAEXPB_W0@Z
	DD	FLAT:?GetAttributeList@CControlUI@DuiLib@@UAE?AVCDuiString@2@_N@Z
	DD	FLAT:?SetAttributeList@CControlUI@DuiLib@@UAEXPB_W@Z
	DD	FLAT:?EstimateSize@CControlUI@DuiLib@@UAE?AUtagSIZE@@U3@@Z
	DD	FLAT:?Paint@CControlUI@DuiLib@@UAE_NPAUHDC__@@ABUtagRECT@@PAV12@@Z
	DD	FLAT:?DoPaint@CContainerUI@DuiLib@@UAE_NPAUHDC__@@ABUtagRECT@@PAVCControlUI@2@@Z
	DD	FLAT:?PaintBkColor@CControlUI@DuiLib@@UAEXPAUHDC__@@@Z
	DD	FLAT:?PaintBkImage@CControlUI@DuiLib@@UAEXPAUHDC__@@@Z
	DD	FLAT:?PaintStatusImage@CControlUI@DuiLib@@UAEXPAUHDC__@@@Z
	DD	FLAT:?PaintText@CControlUI@DuiLib@@UAEXPAUHDC__@@@Z
	DD	FLAT:?PaintBorder@CControlUI@DuiLib@@UAEXPAUHDC__@@@Z
	DD	FLAT:?DoPostPaint@CVerticalLayoutUI@DuiLib@@UAEXPAUHDC__@@ABUtagRECT@@@Z
	DD	FLAT:?GetInset@CContainerUI@DuiLib@@UBE?AUtagRECT@@XZ
	DD	FLAT:?SetInset@CContainerUI@DuiLib@@UAEXUtagRECT@@@Z
	DD	FLAT:?GetChildPadding@CListUI@DuiLib@@UBEHXZ
	DD	FLAT:?SetChildPadding@CListUI@DuiLib@@UAEXH@Z
	DD	FLAT:?GetChildAlign@CContainerUI@DuiLib@@UBEIXZ
	DD	FLAT:?SetChildAlign@CContainerUI@DuiLib@@UAEXI@Z
	DD	FLAT:?GetChildVAlign@CContainerUI@DuiLib@@UBEIXZ
	DD	FLAT:?SetChildVAlign@CContainerUI@DuiLib@@UAEXI@Z
	DD	FLAT:?IsAutoDestroy@CContainerUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetAutoDestroy@CContainerUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?IsDelayedDestroy@CContainerUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetDelayedDestroy@CContainerUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?IsMouseChildEnabled@CContainerUI@DuiLib@@UBE_NXZ
	DD	FLAT:?SetMouseChildEnabled@CContainerUI@DuiLib@@UAEX_N@Z
	DD	FLAT:?FindSelectable@CContainerUI@DuiLib@@UBEHH_N@Z
	DD	FLAT:?GetScrollPos@CListUI@DuiLib@@UBE?AUtagSIZE@@XZ
	DD	FLAT:?GetScrollRange@CListUI@DuiLib@@UBE?AUtagSIZE@@XZ
	DD	FLAT:?SetScrollPos@CListUI@DuiLib@@UAEXUtagSIZE@@@Z
	DD	FLAT:?LineUp@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?LineDown@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?PageUp@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?PageDown@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?HomeUp@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?EndDown@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?LineLeft@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?LineRight@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?PageLeft@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?PageRight@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?HomeLeft@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?EndRight@CListUI@DuiLib@@UAEXXZ
	DD	FLAT:?EnableScrollBar@CListUI@DuiLib@@UAEX_N0@Z
	DD	FLAT:?GetVerticalScrollBar@CListUI@DuiLib@@UBEPAVCScrollBarUI@2@XZ
	DD	FLAT:?GetHorizontalScrollBar@CListUI@DuiLib@@UBEPAVCScrollBarUI@2@XZ
	DD	FLAT:?SetFloatPos@CContainerUI@DuiLib@@MAEXH@Z
	DD	FLAT:?ProcessScrollBar@CContainerUI@DuiLib@@MAEXUtagRECT@@HH@Z
	DD	FLAT:?OnItemHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z
	DD	FLAT:?OnItemUnHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z
CONST	ENDS
;	COMDAT ??_7CUserIcoMenuUIEx@@6BIContainerUI@DuiLib@@@
CONST	SEGMENT
??_7CUserIcoMenuUIEx@@6BIContainerUI@DuiLib@@@ DD FLAT:?GetItemAt@CListUI@DuiLib@@UBEPAVCControlUI@2@H@Z ; CUserIcoMenuUIEx::`vftable'
	DD	FLAT:?GetItemIndex@CListUI@DuiLib@@UBEHPAVCControlUI@2@@Z
	DD	FLAT:?SetItemIndex@CListUI@DuiLib@@UAE_NPAVCControlUI@2@H@Z
	DD	FLAT:?SetMultiItemIndex@CListUI@DuiLib@@UAE_NPAVCControlUI@2@HH@Z
	DD	FLAT:?GetCount@CListUI@DuiLib@@UBEHXZ
	DD	FLAT:?Add@CMenuUI@DuiLib@@UAE_NPAVCControlUI@2@@Z
	DD	FLAT:?AddAt@CListUI@DuiLib@@UAE_NPAVCControlUI@2@H@Z
	DD	FLAT:?Remove@CListUI@DuiLib@@UAE_NPAVCControlUI@2@_N@Z
	DD	FLAT:?RemoveAt@CListUI@DuiLib@@UAE_NH_N@Z
	DD	FLAT:?RemoveAll@CListUI@DuiLib@@UAEXXZ
$SG4294613690 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294613688 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294613689 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294613682 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613683 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294613680 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613681 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613686 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613687 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294613684 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613685 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294613674 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294613675 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294613672 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294613673 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294613678 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613679 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294613676 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294613677 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294613666 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294613667 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613664 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613665 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613670 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294613671 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294613668 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294613669 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294613658 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613659 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613656 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613657 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294613662 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613663 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613660 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613661 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613650 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613651 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613648 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613649 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613654 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613655 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613652 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613653 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613642 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613643 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613640 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294613641 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294613646 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613647 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613644 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613645 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613634 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613635 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613632 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613633 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613638 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613639 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294613636 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294613637 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613626 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294613627 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294613624 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613625 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613630 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613631 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294613628 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294613629 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613618 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613619 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613616 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294613617 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294613622 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613623 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294613620 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294613621 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613610 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613611 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613608 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613609 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613614 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613615 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294613612 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613613 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613602 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294613603 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294613600 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294613601 DB 00H, 00H
	ORG $+2
$SG4294613606 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613607 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613604 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294613605 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613594 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294613595 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294613592 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294613593 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294613598 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294613599 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294613596 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294613597 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294613586 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294613587 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294613584 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294613585 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294613590 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294613591 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294613588 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294613589 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294613578 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294613579 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294613576 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294613577 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294613582 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294613583 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294613580 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294613581 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294613570 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294613571 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294613568 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294613569 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294613574 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294613575 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294613572 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294613573 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294613562 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294613563 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294613560 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613561 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294613566 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294613567 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294613564 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294613565 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294613554 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294613555 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294613552 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294613553 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294613558 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294613559 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294613556 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294613557 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294613546 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294613547 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294613544 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613545 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294613550 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294613551 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613548 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294613549 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294613538 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613539 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613536 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613537 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613542 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613543 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294613540 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613541 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613530 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613531 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294613528 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613529 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613534 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613535 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294613532 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294613533 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294613522 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294613523 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294613520 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294613521 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294613526 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294613527 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613524 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294613525 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294613514 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613515 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294613512 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613513 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294613518 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294613519 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294613516 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294613517 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294613506 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294613507 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294613504 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294613505 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294613510 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294613511 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294613508 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613509 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613498 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294613499 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613496 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294613497 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294613502 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294613503 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294613500 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294613501 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613490 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294613491 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613488 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294613489 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294613494 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294613495 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294613492 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294613493 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294613482 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294613483 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613480 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294613481 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294613486 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294613487 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294613484 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613485 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294613474 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294613475 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294613472 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613473 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613478 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613479 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294613476 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294613477 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613466 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294613467 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294613464 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613465 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294613470 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294613471 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294613468 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613469 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294613458 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294613459 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294613456 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294613457 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294613462 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294613463 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294613460 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294613461 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294613450 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294613451 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294613448 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294613449 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294613454 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294613455 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294613452 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294613453 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294613442 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294613443 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294613440 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613441 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613446 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294613447 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294613444 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294613445 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294613434 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294613435 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294613432 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294613433 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294613438 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294613439 DB 00H, 00H
	ORG $+2
$SG4294613436 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613437 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294613426 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294613427 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294613424 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294613425 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294613430 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294613431 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294613428 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294613429 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294613418 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613419 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294613416 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613417 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613422 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294613423 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294613420 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294613421 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294613410 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613411 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613408 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613409 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294613414 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294613415 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294613412 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613413 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294613402 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294613403 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613400 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294613401 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294613406 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294613407 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294613404 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294613405 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613394 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294613395 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613392 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294613393 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613398 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294613399 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294613396 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613397 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294613386 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294613387 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294613384 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294613385 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294613390 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294613391 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294613388 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294613389 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294613378 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294613379 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613376 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294613377 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294613382 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294613383 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294613380 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294613381 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294613370 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294613371 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294613368 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294613369 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294613374 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294613375 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294613372 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294613373 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294613362 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294613363 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294613360 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294613361 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294613366 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294613367 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294613364 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294613365 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294613354 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294613355 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613352 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294613353 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294613358 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294613359 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613356 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294613357 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613346 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294613347 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294613344 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294613345 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294613350 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294613351 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613348 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294613349 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613338 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613339 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613336 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294613337 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294613342 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613343 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294613340 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613341 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294613330 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294613331 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613328 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294613329 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294613334 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613335 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613332 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294613333 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294613322 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294613323 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613320 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613321 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294613326 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294613327 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294613324 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294613325 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613314 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613315 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294613312 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613313 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613318 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613319 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294613316 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613317 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294613306 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613307 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294613304 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294613305 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294613310 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613311 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294613308 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294613309 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294613298 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613299 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613296 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613297 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294613302 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294613303 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294613300 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294613301 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613290 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294613291 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294613288 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294613289 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613294 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294613295 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613292 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294613293 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294613282 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613283 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613280 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294613281 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613286 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294613287 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294613284 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294613285 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613274 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613275 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613272 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294613273 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294613278 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294613279 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613276 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613277 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294613266 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294613267 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294613264 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613265 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294613270 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294613271 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294613268 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613269 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613258 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613259 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613256 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613257 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613262 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613263 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294613260 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613261 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294613250 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613251 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613248 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613249 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613254 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294613255 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613252 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613253 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294613242 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294613243 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294613240 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294613241 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613246 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294613247 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294613244 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294613245 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294613234 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294613235 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294613232 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294613233 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613238 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613239 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613236 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294613237 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294613226 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294613227 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294613224 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294613225 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294613230 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294613231 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294613228 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294613229 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294613218 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613219 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613216 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294613217 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294613222 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294613223 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294613220 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294613221 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294613214 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294613215 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294613212 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294613213 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294613178 DB 'M', 00H, 00H, 00H
$SG4294613179 DB 'S', 00H, 00H, 00H
$SG4294613176 DB 'B', 00H, 00H, 00H
$SG4294613177 DB 'D', 00H, 00H, 00H
$SG4294613170 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294613171 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294613168 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613169 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294613174 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294613175 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294613172 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613173 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613162 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294613163 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294613160 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294613161 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613166 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613167 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294613164 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294613165 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294613128 DB 00H, 00H
	ORG $+2
$SG4294613129 DB ':', 00H, 00H, 00H
$SG4294613127 DB 00H, 00H
	ORG $+2
$SG4294613036 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294612322 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294612323 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
PUBLIC	??1CUserIcoMenu@@QAE@XZ				; CUserIcoMenu::~CUserIcoMenu
PUBLIC	??1CUserIcoMenuUIEx@@UAE@XZ			; CUserIcoMenuUIEx::~CUserIcoMenuUIEx
PUBLIC	??_GCUserIcoMenuUIEx@@UAEPAXI@Z			; CUserIcoMenuUIEx::`scalar deleting destructor'
PUBLIC	??0CUserIcoMenuUIEx@@QAE@XZ			; CUserIcoMenuUIEx::CUserIcoMenuUIEx
PUBLIC	?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z ; CUserIcoMenu::CreateControl
PUBLIC	?OnItemUnHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z ; CUserIcoMenuUIEx::OnItemUnHot
PUBLIC	?OnItemHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z ; CUserIcoMenuUIEx::OnItemHot
PUBLIC	??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z ; CUserIcoMenu::CUserIcoMenu
EXTRN	??_ECUserIcoMenuUIEx@@UAEPAXI@Z:PROC		; CUserIcoMenuUIEx::`vector deleting destructor'
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0CUserIcoMenuUIEx@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0CUserIcoMenuUIEx@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0CUserIcoMenuUIEx@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0CUserIcoMenuUIEx@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1CUserIcoMenuUIEx@@UAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1CUserIcoMenuUIEx@@UAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1CUserIcoMenuUIEx@@UAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1CUserIcoMenuUIEx@@UAE@XZ$0
__ehfuncinfo$??1CUserIcoMenu@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1CUserIcoMenu@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1CUserIcoMenu@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1CUserIcoMenu@@QAE@XZ$0
__ehfuncinfo$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z$0
	DD	00H
	DD	FLAT:__unwindfunclet$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z$1
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\control\userico\usericomenu.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_xml$ = 8						; size = 132
_pPaintManager$ = 140					; size = 4
??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z PROC ; CUserIcoMenu::CUserIcoMenu
; _this$ = ecx

; 6    : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 5    : 	:CDuiMenu(xml, pPaintManager)

  0002a	8b 85 8c 00 00
	00		 mov	 eax, DWORD PTR _pPaintManager$[ebp]
  00030	50		 push	 eax
  00031	8d 4d 08	 lea	 ecx, DWORD PTR _xml$[ebp]
  00034	e8 00 00 00 00	 call	 ??BCDuiString@DuiLib@@QBEPB_WXZ ; DuiLib::CDuiString::operator wchar_t const *
  00039	50		 push	 eax
  0003a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003d	e8 00 00 00 00	 call	 ??0CDuiMenu@DuiLib@@QAE@PB_WPAVCPaintManagerUI@1@@Z ; DuiLib::CDuiMenu::CDuiMenu
  00042	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1

; 6    : {

  00046	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00049	c7 01 00 00 00
	00		 mov	 DWORD PTR [ecx], OFFSET ??_7CUserIcoMenu@@6B@

; 7    : }

  0004f	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00056	8d 4d 08	 lea	 ecx, DWORD PTR _xml$[ebp]
  00059	e8 00 00 00 00	 call	 ??1CDuiString@DuiLib@@QAE@XZ ; DuiLib::CDuiString::~CDuiString
  0005e	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00061	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00064	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0006b	83 c4 10	 add	 esp, 16			; 00000010H
  0006e	3b ec		 cmp	 ebp, esp
  00070	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00075	8b e5		 mov	 esp, ebp
  00077	5d		 pop	 ebp
  00078	c2 88 00	 ret	 136			; 00000088H
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z$0:
  00000	8d 4d 08	 lea	 ecx, DWORD PTR _xml$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1CDuiString@DuiLib@@QAE@XZ ; DuiLib::CDuiString::~CDuiString
__unwindfunclet$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z$1:
  00008	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1CDuiMenu@DuiLib@@QAE@XZ ; DuiLib::CDuiMenu::~CDuiMenu
__ehhandler$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z:
  00010	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z
  00015	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0CUserIcoMenu@@QAE@VCDuiString@DuiLib@@PAVCPaintManagerUI@2@@Z ENDP ; CUserIcoMenu::CUserIcoMenu
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\control\userico\usericomenu.h
;	COMDAT ?OnItemHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z
_TEXT	SEGMENT
_pExtend_Hover$ = -24					; size = 4
_pExtend$ = -20						; size = 4
_pIcoHover$ = -16					; size = 4
_pIco$ = -12						; size = 4
_pControl$ = -8						; size = 4
_this$ = -4						; size = 4
_pMenuItem$ = 8						; size = 4
?OnItemHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z PROC ; CUserIcoMenuUIEx::OnItemHot, COMDAT
; _this$ = ecx

; 9    : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 18	 sub	 esp, 24			; 00000018H
  00006	56		 push	 esi
  00007	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000c	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  0000f	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00012	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  00015	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00018	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  0001b	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  0001e	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 10   : 		CLabelUI* pControl = (CLabelUI*)m_pManager->FindSubControlByName(pMenuItem, L"menu_label");

  00021	68 00 00 00 00	 push	 OFFSET ??_C@_1BG@GBHPPKDH@?$AAm?$AAe?$AAn?$AAu?$AA_?$AAl?$AAa?$AAb?$AAe?$AAl@
  00026	8b 45 08	 mov	 eax, DWORD PTR _pMenuItem$[ebp]
  00029	50		 push	 eax
  0002a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002d	8b 49 5c	 mov	 ecx, DWORD PTR [ecx+92]
  00030	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  00035	89 45 f8	 mov	 DWORD PTR _pControl$[ebp], eax

; 11   : 
; 12   : //		pMenuItem->SetBkColor(0xff2092ee);//0xff0b1220);//(0xffb8b8b8);		
; 13   : 		pMenuItem->SetBkImage(_T("menu_select.png"));

  00038	6a 00		 push	 0
  0003a	68 00 00 00 00	 push	 OFFSET ??_C@_1CA@OLPILCOH@?$AAm?$AAe?$AAn?$AAu?$AA_?$AAs?$AAe?$AAl?$AAe?$AAc?$AAt?$AA?4?$AAp?$AAn?$AAg@
  0003f	8b 4d 08	 mov	 ecx, DWORD PTR _pMenuItem$[ebp]
  00042	e8 00 00 00 00	 call	 ?SetBkImage@CControlUI@DuiLib@@QAEXPB_WH@Z ; DuiLib::CControlUI::SetBkImage

; 14   : 		if (pControl->GetTextColor() == m_pManager->GetDefaultFontColor())//||

  00047	8b 4d f8	 mov	 ecx, DWORD PTR _pControl$[ebp]
  0004a	e8 00 00 00 00	 call	 ?GetTextColor@CLabelUI@DuiLib@@QBEKXZ ; DuiLib::CLabelUI::GetTextColor
  0004f	8b f0		 mov	 esi, eax
  00051	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00054	8b 4a 5c	 mov	 ecx, DWORD PTR [edx+92]
  00057	e8 00 00 00 00	 call	 ?GetDefaultFontColor@CPaintManagerUI@DuiLib@@QBEKXZ ; DuiLib::CPaintManagerUI::GetDefaultFontColor
  0005c	3b f0		 cmp	 esi, eax
  0005e	75 0a		 jne	 SHORT $LN2@OnItemHot

; 15   : 																		  //(pControl->GetTextColor()==0xff706a6a))//0xff000000))
; 16   : 			pControl->SetTextColor(0xffffffff);

  00060	6a ff		 push	 -1
  00062	8b 4d f8	 mov	 ecx, DWORD PTR _pControl$[ebp]
  00065	e8 00 00 00 00	 call	 ?SetTextColor@CLabelUI@DuiLib@@QAEXK@Z ; DuiLib::CLabelUI::SetTextColor
$LN2@OnItemHot:

; 17   : 
; 18   : 		CControlUI* pIco = m_pManager->FindSubControlByName(pMenuItem, L"ico");

  0006a	68 00 00 00 00	 push	 OFFSET ??_C@_17LIFMABKH@?$AAi?$AAc?$AAo@
  0006f	8b 45 08	 mov	 eax, DWORD PTR _pMenuItem$[ebp]
  00072	50		 push	 eax
  00073	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00076	8b 49 5c	 mov	 ecx, DWORD PTR [ecx+92]
  00079	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  0007e	89 45 f4	 mov	 DWORD PTR _pIco$[ebp], eax

; 19   : 		CControlUI* pIcoHover = m_pManager->FindSubControlByName(pMenuItem, L"ico_hover");

  00081	68 00 00 00 00	 push	 OFFSET ??_C@_1BE@BBGJNCJN@?$AAi?$AAc?$AAo?$AA_?$AAh?$AAo?$AAv?$AAe?$AAr@
  00086	8b 55 08	 mov	 edx, DWORD PTR _pMenuItem$[ebp]
  00089	52		 push	 edx
  0008a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0008d	8b 48 5c	 mov	 ecx, DWORD PTR [eax+92]
  00090	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  00095	89 45 f0	 mov	 DWORD PTR _pIcoHover$[ebp], eax

; 20   : 		if (pIco&&pIcoHover)

  00098	83 7d f4 00	 cmp	 DWORD PTR _pIco$[ebp], 0
  0009c	74 3c		 je	 SHORT $LN3@OnItemHot
  0009e	83 7d f0 00	 cmp	 DWORD PTR _pIcoHover$[ebp], 0
  000a2	74 36		 je	 SHORT $LN3@OnItemHot

; 21   : 		{
; 22   : 			pIco->SetVisible(false);

  000a4	8b f4		 mov	 esi, esp
  000a6	6a 00		 push	 0
  000a8	8b 4d f4	 mov	 ecx, DWORD PTR _pIco$[ebp]
  000ab	8b 11		 mov	 edx, DWORD PTR [ecx]
  000ad	8b 4d f4	 mov	 ecx, DWORD PTR _pIco$[ebp]
  000b0	8b 82 04 01 00
	00		 mov	 eax, DWORD PTR [edx+260]
  000b6	ff d0		 call	 eax
  000b8	3b f4		 cmp	 esi, esp
  000ba	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 23   : 			pIcoHover->SetVisible(true);

  000bf	8b f4		 mov	 esi, esp
  000c1	6a 01		 push	 1
  000c3	8b 4d f0	 mov	 ecx, DWORD PTR _pIcoHover$[ebp]
  000c6	8b 11		 mov	 edx, DWORD PTR [ecx]
  000c8	8b 4d f0	 mov	 ecx, DWORD PTR _pIcoHover$[ebp]
  000cb	8b 82 04 01 00
	00		 mov	 eax, DWORD PTR [edx+260]
  000d1	ff d0		 call	 eax
  000d3	3b f4		 cmp	 esi, esp
  000d5	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN3@OnItemHot:

; 24   : 		}
; 25   : 
; 26   : 		CControlUI* pExtend = m_pManager->FindSubControlByName(pMenuItem, L"extend");

  000da	68 00 00 00 00	 push	 OFFSET ??_C@_1O@OFGPBFDF@?$AAe?$AAx?$AAt?$AAe?$AAn?$AAd@
  000df	8b 4d 08	 mov	 ecx, DWORD PTR _pMenuItem$[ebp]
  000e2	51		 push	 ecx
  000e3	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  000e6	8b 4a 5c	 mov	 ecx, DWORD PTR [edx+92]
  000e9	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  000ee	89 45 ec	 mov	 DWORD PTR _pExtend$[ebp], eax

; 27   : 		CControlUI* pExtend_Hover = m_pManager->FindSubControlByName(pMenuItem, L"extend_hover");

  000f1	68 00 00 00 00	 push	 OFFSET ??_C@_1BK@IPHLOGEN@?$AAe?$AAx?$AAt?$AAe?$AAn?$AAd?$AA_?$AAh?$AAo?$AAv?$AAe?$AAr@
  000f6	8b 45 08	 mov	 eax, DWORD PTR _pMenuItem$[ebp]
  000f9	50		 push	 eax
  000fa	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000fd	8b 49 5c	 mov	 ecx, DWORD PTR [ecx+92]
  00100	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  00105	89 45 e8	 mov	 DWORD PTR _pExtend_Hover$[ebp], eax

; 28   : 
; 29   : 		if (pExtend&&pExtend_Hover)

  00108	83 7d ec 00	 cmp	 DWORD PTR _pExtend$[ebp], 0
  0010c	74 3c		 je	 SHORT $LN1@OnItemHot
  0010e	83 7d e8 00	 cmp	 DWORD PTR _pExtend_Hover$[ebp], 0
  00112	74 36		 je	 SHORT $LN1@OnItemHot

; 30   : 		{
; 31   : 			pExtend->SetVisible(false);

  00114	8b f4		 mov	 esi, esp
  00116	6a 00		 push	 0
  00118	8b 55 ec	 mov	 edx, DWORD PTR _pExtend$[ebp]
  0011b	8b 02		 mov	 eax, DWORD PTR [edx]
  0011d	8b 4d ec	 mov	 ecx, DWORD PTR _pExtend$[ebp]
  00120	8b 90 04 01 00
	00		 mov	 edx, DWORD PTR [eax+260]
  00126	ff d2		 call	 edx
  00128	3b f4		 cmp	 esi, esp
  0012a	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 32   : 			pExtend_Hover->SetVisible(true);

  0012f	8b f4		 mov	 esi, esp
  00131	6a 01		 push	 1
  00133	8b 45 e8	 mov	 eax, DWORD PTR _pExtend_Hover$[ebp]
  00136	8b 10		 mov	 edx, DWORD PTR [eax]
  00138	8b 4d e8	 mov	 ecx, DWORD PTR _pExtend_Hover$[ebp]
  0013b	8b 82 04 01 00
	00		 mov	 eax, DWORD PTR [edx+260]
  00141	ff d0		 call	 eax
  00143	3b f4		 cmp	 esi, esp
  00145	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@OnItemHot:

; 33   : 		}
; 34   : 	}

  0014a	5e		 pop	 esi
  0014b	83 c4 18	 add	 esp, 24			; 00000018H
  0014e	3b ec		 cmp	 ebp, esp
  00150	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00155	8b e5		 mov	 esp, ebp
  00157	5d		 pop	 ebp
  00158	c2 04 00	 ret	 4
?OnItemHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z ENDP ; CUserIcoMenuUIEx::OnItemHot
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\control\userico\usericomenu.h
;	COMDAT ?OnItemUnHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z
_TEXT	SEGMENT
_pExtend_Hover$ = -24					; size = 4
_pExtend$ = -20						; size = 4
_pIcoHover$ = -16					; size = 4
_pIco$ = -12						; size = 4
_pControl$ = -8						; size = 4
_this$ = -4						; size = 4
_pMenuItem$ = 8						; size = 4
?OnItemUnHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z PROC ; CUserIcoMenuUIEx::OnItemUnHot, COMDAT
; _this$ = ecx

; 37   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 18	 sub	 esp, 24			; 00000018H
  00006	56		 push	 esi
  00007	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000c	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  0000f	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00012	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  00015	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00018	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  0001b	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  0001e	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 38   : 		CLabelUI* pControl = (CLabelUI*)m_pManager->FindSubControlByName(pMenuItem, L"menu_label");

  00021	68 00 00 00 00	 push	 OFFSET ??_C@_1BG@GBHPPKDH@?$AAm?$AAe?$AAn?$AAu?$AA_?$AAl?$AAa?$AAb?$AAe?$AAl@
  00026	8b 45 08	 mov	 eax, DWORD PTR _pMenuItem$[ebp]
  00029	50		 push	 eax
  0002a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002d	8b 49 5c	 mov	 ecx, DWORD PTR [ecx+92]
  00030	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  00035	89 45 f8	 mov	 DWORD PTR _pControl$[ebp], eax

; 39   : 		pMenuItem->SetBkImage(_T(""));

  00038	6a 00		 push	 0
  0003a	68 00 00 00 00	 push	 OFFSET ??_C@_11LOCGONAA@@
  0003f	8b 4d 08	 mov	 ecx, DWORD PTR _pMenuItem$[ebp]
  00042	e8 00 00 00 00	 call	 ?SetBkImage@CControlUI@DuiLib@@QAEXPB_WH@Z ; DuiLib::CControlUI::SetBkImage

; 40   : 		pMenuItem->SetBkColor(0x00ffffff);

  00047	68 ff ff ff 00	 push	 16777215		; 00ffffffH
  0004c	8b 4d 08	 mov	 ecx, DWORD PTR _pMenuItem$[ebp]
  0004f	e8 00 00 00 00	 call	 ?SetBkColor@CControlUI@DuiLib@@QAEXK@Z ; DuiLib::CControlUI::SetBkColor

; 41   : 		if (pControl->GetTextColor() == 0xffffffff)

  00054	8b 4d f8	 mov	 ecx, DWORD PTR _pControl$[ebp]
  00057	e8 00 00 00 00	 call	 ?GetTextColor@CLabelUI@DuiLib@@QBEKXZ ; DuiLib::CLabelUI::GetTextColor
  0005c	83 f8 ff	 cmp	 eax, -1
  0005f	75 14		 jne	 SHORT $LN2@OnItemUnHo

; 42   : 			pControl->SetTextColor(m_pManager->GetDefaultFontColor());//0xff706a6a);//0xff000000);

  00061	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00064	8b 4a 5c	 mov	 ecx, DWORD PTR [edx+92]
  00067	e8 00 00 00 00	 call	 ?GetDefaultFontColor@CPaintManagerUI@DuiLib@@QBEKXZ ; DuiLib::CPaintManagerUI::GetDefaultFontColor
  0006c	50		 push	 eax
  0006d	8b 4d f8	 mov	 ecx, DWORD PTR _pControl$[ebp]
  00070	e8 00 00 00 00	 call	 ?SetTextColor@CLabelUI@DuiLib@@QAEXK@Z ; DuiLib::CLabelUI::SetTextColor
$LN2@OnItemUnHo:

; 43   : 		CControlUI* pIco = m_pManager->FindSubControlByName(pMenuItem, L"ico");

  00075	68 00 00 00 00	 push	 OFFSET ??_C@_17LIFMABKH@?$AAi?$AAc?$AAo@
  0007a	8b 45 08	 mov	 eax, DWORD PTR _pMenuItem$[ebp]
  0007d	50		 push	 eax
  0007e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00081	8b 49 5c	 mov	 ecx, DWORD PTR [ecx+92]
  00084	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  00089	89 45 f4	 mov	 DWORD PTR _pIco$[ebp], eax

; 44   : 		CControlUI* pIcoHover = m_pManager->FindSubControlByName(pMenuItem, L"ico_hover");

  0008c	68 00 00 00 00	 push	 OFFSET ??_C@_1BE@BBGJNCJN@?$AAi?$AAc?$AAo?$AA_?$AAh?$AAo?$AAv?$AAe?$AAr@
  00091	8b 55 08	 mov	 edx, DWORD PTR _pMenuItem$[ebp]
  00094	52		 push	 edx
  00095	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00098	8b 48 5c	 mov	 ecx, DWORD PTR [eax+92]
  0009b	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  000a0	89 45 f0	 mov	 DWORD PTR _pIcoHover$[ebp], eax

; 45   : 		if (pIco&&pIcoHover)

  000a3	83 7d f4 00	 cmp	 DWORD PTR _pIco$[ebp], 0
  000a7	74 3c		 je	 SHORT $LN3@OnItemUnHo
  000a9	83 7d f0 00	 cmp	 DWORD PTR _pIcoHover$[ebp], 0
  000ad	74 36		 je	 SHORT $LN3@OnItemUnHo

; 46   : 		{
; 47   : 			pIco->SetVisible(true);

  000af	8b f4		 mov	 esi, esp
  000b1	6a 01		 push	 1
  000b3	8b 4d f4	 mov	 ecx, DWORD PTR _pIco$[ebp]
  000b6	8b 11		 mov	 edx, DWORD PTR [ecx]
  000b8	8b 4d f4	 mov	 ecx, DWORD PTR _pIco$[ebp]
  000bb	8b 82 04 01 00
	00		 mov	 eax, DWORD PTR [edx+260]
  000c1	ff d0		 call	 eax
  000c3	3b f4		 cmp	 esi, esp
  000c5	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 48   : 			pIcoHover->SetVisible(false);

  000ca	8b f4		 mov	 esi, esp
  000cc	6a 00		 push	 0
  000ce	8b 4d f0	 mov	 ecx, DWORD PTR _pIcoHover$[ebp]
  000d1	8b 11		 mov	 edx, DWORD PTR [ecx]
  000d3	8b 4d f0	 mov	 ecx, DWORD PTR _pIcoHover$[ebp]
  000d6	8b 82 04 01 00
	00		 mov	 eax, DWORD PTR [edx+260]
  000dc	ff d0		 call	 eax
  000de	3b f4		 cmp	 esi, esp
  000e0	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN3@OnItemUnHo:

; 49   : 		}
; 50   : 
; 51   : 
; 52   : 		CControlUI* pExtend = m_pManager->FindSubControlByName(pMenuItem, L"extend");

  000e5	68 00 00 00 00	 push	 OFFSET ??_C@_1O@OFGPBFDF@?$AAe?$AAx?$AAt?$AAe?$AAn?$AAd@
  000ea	8b 4d 08	 mov	 ecx, DWORD PTR _pMenuItem$[ebp]
  000ed	51		 push	 ecx
  000ee	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  000f1	8b 4a 5c	 mov	 ecx, DWORD PTR [edx+92]
  000f4	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  000f9	89 45 ec	 mov	 DWORD PTR _pExtend$[ebp], eax

; 53   : 		CControlUI* pExtend_Hover = m_pManager->FindSubControlByName(pMenuItem, L"extend_hover");

  000fc	68 00 00 00 00	 push	 OFFSET ??_C@_1BK@IPHLOGEN@?$AAe?$AAx?$AAt?$AAe?$AAn?$AAd?$AA_?$AAh?$AAo?$AAv?$AAe?$AAr@
  00101	8b 45 08	 mov	 eax, DWORD PTR _pMenuItem$[ebp]
  00104	50		 push	 eax
  00105	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00108	8b 49 5c	 mov	 ecx, DWORD PTR [ecx+92]
  0010b	e8 00 00 00 00	 call	 ?FindSubControlByName@CPaintManagerUI@DuiLib@@QBEPAVCControlUI@2@PAV32@PB_W@Z ; DuiLib::CPaintManagerUI::FindSubControlByName
  00110	89 45 e8	 mov	 DWORD PTR _pExtend_Hover$[ebp], eax

; 54   : 
; 55   : 		if (pExtend&&pExtend_Hover)

  00113	83 7d ec 00	 cmp	 DWORD PTR _pExtend$[ebp], 0
  00117	74 3c		 je	 SHORT $LN1@OnItemUnHo
  00119	83 7d e8 00	 cmp	 DWORD PTR _pExtend_Hover$[ebp], 0
  0011d	74 36		 je	 SHORT $LN1@OnItemUnHo

; 56   : 		{
; 57   : 			pExtend->SetVisible(true);

  0011f	8b f4		 mov	 esi, esp
  00121	6a 01		 push	 1
  00123	8b 55 ec	 mov	 edx, DWORD PTR _pExtend$[ebp]
  00126	8b 02		 mov	 eax, DWORD PTR [edx]
  00128	8b 4d ec	 mov	 ecx, DWORD PTR _pExtend$[ebp]
  0012b	8b 90 04 01 00
	00		 mov	 edx, DWORD PTR [eax+260]
  00131	ff d2		 call	 edx
  00133	3b f4		 cmp	 esi, esp
  00135	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 58   : 			pExtend_Hover->SetVisible(false);

  0013a	8b f4		 mov	 esi, esp
  0013c	6a 00		 push	 0
  0013e	8b 45 e8	 mov	 eax, DWORD PTR _pExtend_Hover$[ebp]
  00141	8b 10		 mov	 edx, DWORD PTR [eax]
  00143	8b 4d e8	 mov	 ecx, DWORD PTR _pExtend_Hover$[ebp]
  00146	8b 82 04 01 00
	00		 mov	 eax, DWORD PTR [edx+260]
  0014c	ff d0		 call	 eax
  0014e	3b f4		 cmp	 esi, esp
  00150	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@OnItemUnHo:

; 59   : 		}
; 60   : 
; 61   : 
; 62   : 	}

  00155	5e		 pop	 esi
  00156	83 c4 18	 add	 esp, 24			; 00000018H
  00159	3b ec		 cmp	 ebp, esp
  0015b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00160	8b e5		 mov	 esp, ebp
  00162	5d		 pop	 ebp
  00163	c2 04 00	 ret	 4
?OnItemUnHot@CUserIcoMenuUIEx@@UAEXPAVCMenuElementUI@DuiLib@@@Z ENDP ; CUserIcoMenuUIEx::OnItemUnHot
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\control\userico\usericomenu.h
;	COMDAT ?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z
_TEXT	SEGMENT
tv82 = -28						; size = 4
$T2 = -24						; size = 4
$T3 = -20						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_pstrClass$ = 8						; size = 4
?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z PROC ; CUserIcoMenu::CreateControl, COMDAT
; _this$ = ecx

; 72   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 10	 sub	 esp, 16			; 00000010H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00023	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00026	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00029	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0002c	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 73   : 		if (_tcscmp(pstrClass, L"MenuEx") == 0)

  0002f	68 00 00 00 00	 push	 OFFSET ??_C@_1O@LCHGKCBB@?$AAM?$AAe?$AAn?$AAu?$AAE?$AAx@
  00034	8b 45 08	 mov	 eax, DWORD PTR _pstrClass$[ebp]
  00037	50		 push	 eax
  00038	e8 00 00 00 00	 call	 _wcscmp
  0003d	83 c4 08	 add	 esp, 8
  00040	85 c0		 test	 eax, eax
  00042	75 43		 jne	 SHORT $LN2@CreateCont

; 74   : 		{
; 75   : 			return new CUserIcoMenuUIEx;

  00044	68 08 12 00 00	 push	 4616			; 00001208H
  00049	e8 00 00 00 00	 call	 ??2@YAPAXI@Z		; operator new
  0004e	83 c4 04	 add	 esp, 4
  00051	89 45 e8	 mov	 DWORD PTR $T2[ebp], eax
  00054	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0005b	83 7d e8 00	 cmp	 DWORD PTR $T2[ebp], 0
  0005f	74 0d		 je	 SHORT $LN4@CreateCont
  00061	8b 4d e8	 mov	 ecx, DWORD PTR $T2[ebp]
  00064	e8 00 00 00 00	 call	 ??0CUserIcoMenuUIEx@@QAE@XZ
  00069	89 45 e4	 mov	 DWORD PTR tv82[ebp], eax
  0006c	eb 07		 jmp	 SHORT $LN5@CreateCont
$LN4@CreateCont:
  0006e	c7 45 e4 00 00
	00 00		 mov	 DWORD PTR tv82[ebp], 0
$LN5@CreateCont:
  00075	8b 4d e4	 mov	 ecx, DWORD PTR tv82[ebp]
  00078	89 4d ec	 mov	 DWORD PTR $T3[ebp], ecx
  0007b	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00082	8b 45 ec	 mov	 eax, DWORD PTR $T3[ebp]
  00085	eb 0c		 jmp	 SHORT $LN1@CreateCont
$LN2@CreateCont:

; 76   : 		}
; 77   : 		return CDuiMenu::CreateControl(pstrClass);

  00087	8b 55 08	 mov	 edx, DWORD PTR _pstrClass$[ebp]
  0008a	52		 push	 edx
  0008b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0008e	e8 00 00 00 00	 call	 ?CreateControl@CDuiMenu@DuiLib@@UAEPAVCControlUI@2@PB_W@Z ; DuiLib::CDuiMenu::CreateControl
$LN1@CreateCont:

; 78   : 	}

  00093	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00096	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0009d	83 c4 1c	 add	 esp, 28			; 0000001cH
  000a0	3b ec		 cmp	 ebp, esp
  000a2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000a7	8b e5		 mov	 esp, ebp
  000a9	5d		 pop	 ebp
  000aa	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z$0:
  00000	68 08 12 00 00	 push	 4616			; 00001208H
  00005	8b 45 e8	 mov	 eax, DWORD PTR $T2[ebp]
  00008	50		 push	 eax
  00009	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  0000e	83 c4 08	 add	 esp, 8
  00011	c3		 ret	 0
__ehhandler$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z:
  00012	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z
  00017	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?CreateControl@CUserIcoMenu@@UAEPAVCControlUI@DuiLib@@PB_W@Z ENDP ; CUserIcoMenu::CreateControl
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??0CUserIcoMenuUIEx@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0CUserIcoMenuUIEx@@QAE@XZ PROC			; CUserIcoMenuUIEx::CUserIcoMenuUIEx, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0CUserIcoMenuUIEx@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0CMenuUI@DuiLib@@QAE@XZ ; DuiLib::CMenuUI::CMenuUI
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CUserIcoMenuUIEx@@6BCControlUI@DuiLib@@@
  0003b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003e	c7 81 00 08 00
	00 00 00 00 00	 mov	 DWORD PTR [ecx+2048], OFFSET ??_7CUserIcoMenuUIEx@@6BIContainerUI@DuiLib@@@
  00048	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0004b	c7 82 60 08 00
	00 00 00 00 00	 mov	 DWORD PTR [edx+2144], OFFSET ??_7CUserIcoMenuUIEx@@6BCListUI@DuiLib@@@
  00055	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00058	c7 80 e8 11 00
	00 00 00 00 00	 mov	 DWORD PTR [eax+4584], OFFSET ??_7CUserIcoMenuUIEx@@6BINotifyUI@DuiLib@@@
  00062	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00069	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0006c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0006f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00076	83 c4 10	 add	 esp, 16			; 00000010H
  00079	3b ec		 cmp	 ebp, esp
  0007b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00080	8b e5		 mov	 esp, ebp
  00082	5d		 pop	 ebp
  00083	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0CUserIcoMenuUIEx@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1CMenuUI@DuiLib@@UAE@XZ ; DuiLib::CMenuUI::~CMenuUI
__ehhandler$??0CUserIcoMenuUIEx@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0CUserIcoMenuUIEx@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0CUserIcoMenuUIEx@@QAE@XZ ENDP			; CUserIcoMenuUIEx::CUserIcoMenuUIEx
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_GCUserIcoMenuUIEx@@UAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___flags$ = 8						; size = 4
??_GCUserIcoMenuUIEx@@UAEPAXI@Z PROC			; CUserIcoMenuUIEx::`scalar deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??1CUserIcoMenuUIEx@@UAE@XZ
  00016	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  00019	83 e0 01	 and	 eax, 1
  0001c	74 11		 je	 SHORT $LN2@scalar
  0001e	68 08 12 00 00	 push	 4616			; 00001208H
  00023	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	51		 push	 ecx
  00027	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  0002c	83 c4 08	 add	 esp, 8
$LN2@scalar:
  0002f	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00032	83 c4 04	 add	 esp, 4
  00035	3b ec		 cmp	 ebp, esp
  00037	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003c	8b e5		 mov	 esp, ebp
  0003e	5d		 pop	 ebp
  0003f	c2 04 00	 ret	 4
??_GCUserIcoMenuUIEx@@UAEPAXI@Z ENDP			; CUserIcoMenuUIEx::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1CUserIcoMenuUIEx@@UAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1CUserIcoMenuUIEx@@UAE@XZ PROC			; CUserIcoMenuUIEx::~CUserIcoMenuUIEx, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1CUserIcoMenuUIEx@@UAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0002a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	e8 00 00 00 00	 call	 ??1CMenuUI@DuiLib@@UAE@XZ ; DuiLib::CMenuUI::~CMenuUI
  00039	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003c	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00043	83 c4 10	 add	 esp, 16			; 00000010H
  00046	3b ec		 cmp	 ebp, esp
  00048	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004d	8b e5		 mov	 esp, ebp
  0004f	5d		 pop	 ebp
  00050	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1CUserIcoMenuUIEx@@UAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1CMenuUI@DuiLib@@UAE@XZ ; DuiLib::CMenuUI::~CMenuUI
__ehhandler$??1CUserIcoMenuUIEx@@UAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1CUserIcoMenuUIEx@@UAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1CUserIcoMenuUIEx@@UAE@XZ ENDP			; CUserIcoMenuUIEx::~CUserIcoMenuUIEx
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\control\userico\usericomenu.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1CUserIcoMenu@@QAE@XZ PROC				; CUserIcoMenu::~CUserIcoMenu
; _this$ = ecx

; 10   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1CUserIcoMenu@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00026	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CUserIcoMenu@@6B@
  0002c	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 11   : 
; 12   : }

  00033	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0003a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003d	e8 00 00 00 00	 call	 ??1CDuiMenu@DuiLib@@QAE@XZ ; DuiLib::CDuiMenu::~CDuiMenu
  00042	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00045	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0004c	83 c4 10	 add	 esp, 16			; 00000010H
  0004f	3b ec		 cmp	 ebp, esp
  00051	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00056	8b e5		 mov	 esp, ebp
  00058	5d		 pop	 ebp
  00059	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??1CUserIcoMenu@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1CDuiMenu@DuiLib@@QAE@XZ ; DuiLib::CDuiMenu::~CDuiMenu
__ehhandler$??1CUserIcoMenu@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1CUserIcoMenu@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1CUserIcoMenu@@QAE@XZ ENDP				; CUserIcoMenu::~CUserIcoMenu
END
