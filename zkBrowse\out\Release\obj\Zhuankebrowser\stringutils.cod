; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
_BSS	ENDS
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
$SG4294445760 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294445761 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294445762 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294445763 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294445752 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294445753 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445754 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445755 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445756 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294445757 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445758 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294445759 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445744 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294445745 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294445746 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294445747 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294445748 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294445749 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294445750 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294445751 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445736 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445737 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445738 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445739 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294445740 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445741 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294445742 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294445743 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294445728 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445729 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445730 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294445731 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445732 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445733 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445734 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445735 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445720 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445721 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445722 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445723 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445724 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445725 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445726 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445727 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445712 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294445713 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294445714 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294445715 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445716 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445717 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445718 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445719 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445704 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294445705 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445706 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445707 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445708 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445709 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294445710 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445711 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445696 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294445697 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445698 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445699 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294445700 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294445701 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294445702 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445703 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445688 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294445689 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294445690 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294445691 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445692 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445693 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294445694 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445695 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445680 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445681 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445682 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445683 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445684 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445685 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445686 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445687 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445672 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294445673 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294445674 DB 00H, 00H
	ORG $+2
$SG4294445675 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294445676 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294445677 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294445678 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445679 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445664 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294445665 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294445666 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294445667 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294445668 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294445669 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294445670 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294445671 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294445656 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294445657 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294445658 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294445659 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294445660 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294445661 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294445662 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294445663 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294445648 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294445649 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294445650 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294445651 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294445652 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294445653 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294445654 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294445655 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294445640 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294445641 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294445642 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294445643 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294445644 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294445645 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294445646 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294445647 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294445632 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294445633 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445634 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294445635 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294445636 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294445637 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294445638 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294445639 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294445624 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445625 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294445626 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294445627 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294445628 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294445629 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294445630 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294445631 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294445616 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294445617 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445618 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294445619 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294445620 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294445621 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294445622 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294445623 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294445608 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445609 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445610 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445611 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445612 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445613 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445614 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445615 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445600 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445601 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445602 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445603 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445604 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294445605 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294445606 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294445607 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294445592 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294445593 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294445594 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294445595 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294445596 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294445597 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294445598 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294445599 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294445584 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294445585 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445586 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294445587 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445588 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294445589 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294445590 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294445591 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294445576 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294445577 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294445578 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294445579 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294445580 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294445581 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445582 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445583 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294445568 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294445569 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294445570 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294445571 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294445572 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445573 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294445574 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445575 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294445560 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294445561 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294445562 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294445563 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294445564 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445565 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294445566 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294445567 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294445552 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294445553 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294445554 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294445555 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294445556 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445557 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445558 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294445559 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294445544 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294445545 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445546 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445547 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294445548 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294445549 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294445550 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445551 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445536 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294445537 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445538 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294445539 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294445540 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294445541 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445542 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294445543 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294445528 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294445529 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294445530 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294445531 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294445532 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294445533 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294445534 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294445535 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294445520 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294445521 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294445522 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294445523 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294445524 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294445525 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294445526 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294445527 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294445512 DB 00H, 00H
	ORG $+2
$SG4294445513 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445514 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445515 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294445516 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294445517 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294445518 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294445519 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294445504 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294445505 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294445506 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294445507 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294445508 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294445509 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445510 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294445511 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294445496 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294445497 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294445498 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294445499 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294445500 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294445501 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294445502 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294445503 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294445488 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294445489 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445490 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445491 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445492 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294445493 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294445494 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294445495 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294445480 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294445481 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445482 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294445483 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445484 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445485 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445486 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294445487 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294445472 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294445473 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294445474 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294445475 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294445476 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445477 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294445478 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445479 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294445464 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294445465 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294445466 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445467 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294445468 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445469 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445470 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294445471 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294445456 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294445457 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294445458 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294445459 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294445460 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294445461 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294445462 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294445463 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294445448 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294445449 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294445450 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294445451 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294445452 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445453 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294445454 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294445455 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294445440 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294445441 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294445442 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294445443 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294445444 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294445445 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294445446 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294445447 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294445432 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445433 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294445434 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294445435 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294445436 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294445437 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294445438 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294445439 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294445424 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445425 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294445426 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294445427 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294445428 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445429 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294445430 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445431 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294445416 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294445417 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294445418 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294445419 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294445420 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294445421 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294445422 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445423 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294445408 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445409 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294445410 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294445411 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445412 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445413 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445414 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294445415 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445400 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294445401 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294445402 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294445403 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294445404 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445405 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294445406 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294445407 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445392 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294445393 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445394 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294445395 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294445396 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445397 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294445398 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445399 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294445384 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294445385 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445386 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445387 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445388 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294445389 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445390 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294445391 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445376 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294445377 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294445378 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294445379 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445380 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294445381 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294445382 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294445383 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445368 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445369 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445370 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294445371 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445372 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445373 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294445374 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445375 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294445360 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294445361 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294445362 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445363 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294445364 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294445365 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294445366 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294445367 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294445352 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445353 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294445354 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445355 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445356 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445357 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294445358 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445359 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294445344 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294445345 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294445346 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294445347 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445348 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445349 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445350 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294445351 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294445336 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294445337 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445338 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294445339 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294445340 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294445341 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445342 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445343 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294445328 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445329 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445330 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445331 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445332 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445333 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445334 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294445335 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445320 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294445321 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445322 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445323 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445324 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445325 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445326 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294445327 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294445312 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445313 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294445314 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445315 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294445316 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294445317 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294445318 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294445319 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294445304 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294445305 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294445306 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445307 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294445308 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294445309 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294445310 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294445311 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445296 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294445297 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294445298 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294445299 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294445300 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294445301 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294445302 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294445303 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294445288 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294445289 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294445290 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294445291 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445292 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445293 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294445294 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294445295 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294445285 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294445286 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294445287 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294445248 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294445249 DB 'B', 00H, 00H, 00H
$SG4294445250 DB 'D', 00H, 00H, 00H
$SG4294445251 DB 'M', 00H, 00H, 00H
$SG4294445252 DB 'S', 00H, 00H, 00H
$SG4294445240 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294445241 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445242 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294445243 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294445244 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294445245 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445246 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445247 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294445233 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294445234 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445235 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294445236 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294445237 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294445238 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445239 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294445200 DB 00H, 00H
	ORG $+2
$SG4294445201 DB 00H, 00H
	ORG $+2
$SG4294445202 DB ':', 00H, 00H, 00H
$SG4294445109 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294444395 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294444396 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
PUBLIC	??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z ; std::operator+<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	??A?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@I@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::operator[]
PUBLIC	??A?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@I@Z ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::operator[]
PUBLIC	?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z ; join
PUBLIC	?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z ; split
PUBLIC	?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z ; split
PUBLIC	?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z ; replace2
PUBLIC	?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z ; replace2
PUBLIC	?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z ; str_split
PUBLIC	?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z ; join
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z$1
	DD	00H
	DD	FLAT:__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z$0
__ehfuncinfo$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z$1
	DD	00H
	DD	FLAT:__unwindfunclet$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z$2
__ehfuncinfo$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z DD 019930522H
	DD	04H
	DD	FLAT:__unwindtable$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$3
	DD	00H
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$1
	DD	01H
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$2
__ehfuncinfo$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z DD 019930522H
	DD	04H
	DD	FLAT:__unwindtable$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$3
	DD	00H
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$1
	DD	01H
	DD	FLAT:__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$2
__ehfuncinfo$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z$1
	DD	00H
	DD	FLAT:__unwindfunclet$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z$0
__ehfuncinfo$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z$1
	DD	00H
	DD	FLAT:__unwindfunclet$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z$0
__ehfuncinfo$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z DD 019930522H
	DD	05H
	DD	FLAT:__unwindtable$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$2
	DD	00H
	DD	FLAT:__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$1
	DD	01H
	DD	FLAT:__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$3
	DD	01H
	DD	FLAT:__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$4
__ehfuncinfo$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z$1
	DD	00H
	DD	FLAT:__unwindfunclet$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z$2
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
_TEXT	SEGMENT
tv136 = -84						; size = 4
tv140 = -80						; size = 4
$T2 = -76						; size = 4
$T3 = -72						; size = 24
_i$4 = -48						; size = 4
_sret$ = -40						; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_vRaw$ = 12						; size = 4
_sSep$ = 16						; size = 4
?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z PROC ; join

; 148  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 48	 sub	 esp, 72			; 00000048H
  0001b	57		 push	 edi
  0001c	8d 7d ac	 lea	 edi, DWORD PTR [ebp-84]
  0001f	b9 12 00 00 00	 mov	 ecx, 18			; 00000012H
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 b4 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 149  : 	std::string sret;

  00032	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00035	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0003a	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 150  : 	if (!vRaw.size()) return sret;

  00041	8b 4d 0c	 mov	 ecx, DWORD PTR _vRaw$[ebp]
  00044	e8 00 00 00 00	 call	 ?size@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEIXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::size
  00049	85 c0		 test	 eax, eax
  0004b	75 29		 jne	 SHORT $LN5@join
  0004d	8d 45 d8	 lea	 eax, DWORD PTR _sret$[ebp]
  00050	50		 push	 eax
  00051	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00054	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00059	8b 4d b4	 mov	 ecx, DWORD PTR $T2[ebp]
  0005c	83 c9 01	 or	 ecx, 1
  0005f	89 4d b4	 mov	 DWORD PTR $T2[ebp], ecx
  00062	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00066	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00069	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  0006e	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  00071	e9 92 00 00 00	 jmp	 $LN1@join
$LN5@join:

; 151  : 	for (size_t i = 0; i<vRaw.size(); ++i)

  00076	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR _i$4[ebp], 0
  0007d	eb 09		 jmp	 SHORT $LN4@join
$LN2@join:
  0007f	8b 55 d0	 mov	 edx, DWORD PTR _i$4[ebp]
  00082	83 c2 01	 add	 edx, 1
  00085	89 55 d0	 mov	 DWORD PTR _i$4[ebp], edx
$LN4@join:
  00088	8b 4d 0c	 mov	 ecx, DWORD PTR _vRaw$[ebp]
  0008b	e8 00 00 00 00	 call	 ?size@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEIXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::size
  00090	39 45 d0	 cmp	 DWORD PTR _i$4[ebp], eax
  00093	73 44		 jae	 SHORT $LN3@join

; 152  : 	{
; 153  : 		sret += sSep + vRaw[i];

  00095	8b 45 d0	 mov	 eax, DWORD PTR _i$4[ebp]
  00098	50		 push	 eax
  00099	8b 4d 0c	 mov	 ecx, DWORD PTR _vRaw$[ebp]
  0009c	e8 00 00 00 00	 call	 ??A?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@I@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::operator[]
  000a1	50		 push	 eax
  000a2	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  000a5	51		 push	 ecx
  000a6	8d 55 b8	 lea	 edx, DWORD PTR $T3[ebp]
  000a9	52		 push	 edx
  000aa	e8 00 00 00 00	 call	 ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z ; std::operator+<char,std::char_traits<char>,std::allocator<char> >
  000af	83 c4 0c	 add	 esp, 12			; 0000000cH
  000b2	89 45 b0	 mov	 DWORD PTR tv140[ebp], eax
  000b5	8b 45 b0	 mov	 eax, DWORD PTR tv140[ebp]
  000b8	89 45 ac	 mov	 DWORD PTR tv136[ebp], eax
  000bb	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  000bf	8b 4d ac	 mov	 ecx, DWORD PTR tv136[ebp]
  000c2	51		 push	 ecx
  000c3	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  000c6	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=
  000cb	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  000cf	8d 4d b8	 lea	 ecx, DWORD PTR $T3[ebp]
  000d2	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

; 154  : 	}

  000d7	eb a6		 jmp	 SHORT $LN2@join
$LN3@join:

; 155  : 	return sret.substr(sSep.length());

  000d9	6a ff		 push	 -1
  000db	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  000de	e8 00 00 00 00	 call	 ?length@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::length
  000e3	50		 push	 eax
  000e4	8b 55 08	 mov	 edx, DWORD PTR ___$ReturnUdt$[ebp]
  000e7	52		 push	 edx
  000e8	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  000eb	e8 00 00 00 00	 call	 ?substr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV12@II@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::substr
  000f0	8b 45 b4	 mov	 eax, DWORD PTR $T2[ebp]
  000f3	83 c8 01	 or	 eax, 1
  000f6	89 45 b4	 mov	 DWORD PTR $T2[ebp], eax
  000f9	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000fd	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00100	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00105	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
$LN1@join:

; 156  : }

  00108	52		 push	 edx
  00109	8b cd		 mov	 ecx, ebp
  0010b	50		 push	 eax
  0010c	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN14@join
  00112	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00117	58		 pop	 eax
  00118	5a		 pop	 edx
  00119	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0011c	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00123	5f		 pop	 edi
  00124	83 c4 54	 add	 esp, 84			; 00000054H
  00127	3b ec		 cmp	 ebp, esp
  00129	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0012e	8b e5		 mov	 esp, ebp
  00130	5d		 pop	 ebp
  00131	c3		 ret	 0
  00132	66 90		 npad	 2
$LN14@join:
  00134	01 00 00 00	 DD	 1
  00138	00 00 00 00	 DD	 $LN13@join
$LN13@join:
  0013c	d8 ff ff ff	 DD	 -40			; ffffffd8H
  00140	18 00 00 00	 DD	 24			; 00000018H
  00144	00 00 00 00	 DD	 $LN11@join
$LN11@join:
  00148	73		 DB	 115			; 00000073H
  00149	72		 DB	 114			; 00000072H
  0014a	65		 DB	 101			; 00000065H
  0014b	74		 DB	 116			; 00000074H
  0014c	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z$1:
  00008	8b 45 b4	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN9@join
  00014	83 65 b4 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN9@join:
  00020	c3		 ret	 0
__unwindfunclet$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z$2:
  00021	8d 4d b8	 lea	 ecx, DWORD PTR $T3[ebp]
  00024	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__ehhandler$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z:
  00029	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z
  0002e	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?join@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@2@ABV12@@Z ENDP ; join
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
_TEXT	SEGMENT
tv160 = -148						; size = 4
tv166 = -144						; size = 4
tv158 = -140						; size = 4
tv165 = -136						; size = 4
tv151 = -132						; size = 4
tv65 = -128						; size = 4
$T2 = -124						; size = 4
$T3 = -120						; size = 24
$T4 = -96						; size = 24
$T5 = -72						; size = 24
_nlen$ = -48						; size = 4
_s_lst$ = -40						; size = 12
_icount$ = -24						; size = 4
_ipos$ = -20						; size = 4
_istart$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_sRaw$ = 12						; size = 4
_sSep$ = 16						; size = 4
_nMax$ = 20						; size = 4
?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z PROC ; str_split

; 128  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	81 ec 88 00 00
	00		 sub	 esp, 136		; 00000088H
  0001e	57		 push	 edi
  0001f	8d bd 6c ff ff
	ff		 lea	 edi, DWORD PTR [ebp-148]
  00025	b9 22 00 00 00	 mov	 ecx, 34			; 00000022H
  0002a	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002f	f3 ab		 rep stosd
  00031	c7 45 84 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 129  : 	using namespace std;
; 130  : 	size_t istart = 0, ipos = 0; int icount = 0;

  00038	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR _istart$[ebp], 0
  0003f	c7 45 ec 00 00
	00 00		 mov	 DWORD PTR _ipos$[ebp], 0
  00046	c7 45 e8 00 00
	00 00		 mov	 DWORD PTR _icount$[ebp], 0

; 131  : 	vector<string> s_lst;

  0004d	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00050	e8 00 00 00 00	 call	 ??0?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  00055	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 132  : 	size_t nlen = sRaw.length();

  0005c	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  0005f	e8 00 00 00 00	 call	 ?length@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::length
  00064	89 45 d0	 mov	 DWORD PTR _nlen$[ebp], eax
$LN2@str_split:

; 133  : 	while ((ipos = sRaw.find(sSep, istart)) != std::string::npos)

  00067	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  0006a	50		 push	 eax
  0006b	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  0006e	51		 push	 ecx
  0006f	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  00072	e8 00 00 00 00	 call	 ?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIABV12@I@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find
  00077	89 45 ec	 mov	 DWORD PTR _ipos$[ebp], eax
  0007a	83 7d ec ff	 cmp	 DWORD PTR _ipos$[ebp], -1
  0007e	0f 84 d8 00 00
	00		 je	 $LN3@str_split

; 134  : 	{
; 135  : 		icount++;

  00084	8b 55 e8	 mov	 edx, DWORD PTR _icount$[ebp]
  00087	83 c2 01	 add	 edx, 1
  0008a	89 55 e8	 mov	 DWORD PTR _icount$[ebp], edx

; 136  : 		if (nMax>0 && icount >= nMax) {

  0008d	83 7d 14 00	 cmp	 DWORD PTR _nMax$[ebp], 0
  00091	76 6e		 jbe	 SHORT $LN4@str_split
  00093	8b 45 e8	 mov	 eax, DWORD PTR _icount$[ebp]
  00096	3b 45 14	 cmp	 eax, DWORD PTR _nMax$[ebp]
  00099	72 66		 jb	 SHORT $LN4@str_split

; 137  : 			s_lst.push_back(sRaw.substr(istart));

  0009b	6a ff		 push	 -1
  0009d	8b 4d f0	 mov	 ecx, DWORD PTR _istart$[ebp]
  000a0	51		 push	 ecx
  000a1	8d 55 b8	 lea	 edx, DWORD PTR $T5[ebp]
  000a4	52		 push	 edx
  000a5	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  000a8	e8 00 00 00 00	 call	 ?substr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV12@II@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::substr
  000ad	89 45 80	 mov	 DWORD PTR tv65[ebp], eax
  000b0	8b 45 80	 mov	 eax, DWORD PTR tv65[ebp]
  000b3	89 85 7c ff ff
	ff		 mov	 DWORD PTR tv151[ebp], eax
  000b9	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  000bd	8b 8d 7c ff ff
	ff		 mov	 ecx, DWORD PTR tv151[ebp]
  000c3	51		 push	 ecx
  000c4	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  000c7	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEX$$QAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::push_back
  000cc	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  000d0	8d 4d b8	 lea	 ecx, DWORD PTR $T5[ebp]
  000d3	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

; 138  : 			return s_lst;

  000d8	8d 55 d8	 lea	 edx, DWORD PTR _s_lst$[ebp]
  000db	52		 push	 edx
  000dc	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  000df	e8 00 00 00 00	 call	 ??0?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@$$QAV01@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  000e4	8b 45 84	 mov	 eax, DWORD PTR $T2[ebp]
  000e7	83 c8 01	 or	 eax, 1
  000ea	89 45 84	 mov	 DWORD PTR $T2[ebp], eax
  000ed	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000f1	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  000f4	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  000f9	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  000fc	e9 ca 00 00 00	 jmp	 $LN1@str_split
$LN4@str_split:

; 139  : 		}
; 140  : 		s_lst.push_back(sRaw.substr(istart, ipos - istart));

  00101	8b 4d ec	 mov	 ecx, DWORD PTR _ipos$[ebp]
  00104	2b 4d f0	 sub	 ecx, DWORD PTR _istart$[ebp]
  00107	51		 push	 ecx
  00108	8b 55 f0	 mov	 edx, DWORD PTR _istart$[ebp]
  0010b	52		 push	 edx
  0010c	8d 45 a0	 lea	 eax, DWORD PTR $T4[ebp]
  0010f	50		 push	 eax
  00110	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  00113	e8 00 00 00 00	 call	 ?substr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV12@II@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::substr
  00118	89 85 78 ff ff
	ff		 mov	 DWORD PTR tv165[ebp], eax
  0011e	8b 8d 78 ff ff
	ff		 mov	 ecx, DWORD PTR tv165[ebp]
  00124	89 8d 74 ff ff
	ff		 mov	 DWORD PTR tv158[ebp], ecx
  0012a	c6 45 fc 03	 mov	 BYTE PTR __$EHRec$[ebp+8], 3
  0012e	8b 95 74 ff ff
	ff		 mov	 edx, DWORD PTR tv158[ebp]
  00134	52		 push	 edx
  00135	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00138	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEX$$QAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::push_back
  0013d	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00141	8d 4d a0	 lea	 ecx, DWORD PTR $T4[ebp]
  00144	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

; 141  : 		istart = ipos + sSep.length();

  00149	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  0014c	e8 00 00 00 00	 call	 ?length@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::length
  00151	03 45 ec	 add	 eax, DWORD PTR _ipos$[ebp]
  00154	89 45 f0	 mov	 DWORD PTR _istart$[ebp], eax

; 142  : 	}

  00157	e9 0b ff ff ff	 jmp	 $LN2@str_split
$LN3@str_split:

; 143  : 	if (istart<nlen) s_lst.push_back(sRaw.substr(istart));

  0015c	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  0015f	3b 45 d0	 cmp	 eax, DWORD PTR _nlen$[ebp]
  00162	73 43		 jae	 SHORT $LN5@str_split
  00164	6a ff		 push	 -1
  00166	8b 4d f0	 mov	 ecx, DWORD PTR _istart$[ebp]
  00169	51		 push	 ecx
  0016a	8d 55 88	 lea	 edx, DWORD PTR $T3[ebp]
  0016d	52		 push	 edx
  0016e	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  00171	e8 00 00 00 00	 call	 ?substr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV12@II@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::substr
  00176	89 85 70 ff ff
	ff		 mov	 DWORD PTR tv166[ebp], eax
  0017c	8b 85 70 ff ff
	ff		 mov	 eax, DWORD PTR tv166[ebp]
  00182	89 85 6c ff ff
	ff		 mov	 DWORD PTR tv160[ebp], eax
  00188	c6 45 fc 04	 mov	 BYTE PTR __$EHRec$[ebp+8], 4
  0018c	8b 8d 6c ff ff
	ff		 mov	 ecx, DWORD PTR tv160[ebp]
  00192	51		 push	 ecx
  00193	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00196	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEX$$QAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::push_back
  0019b	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  0019f	8d 4d 88	 lea	 ecx, DWORD PTR $T3[ebp]
  001a2	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN5@str_split:

; 144  : 	return s_lst;

  001a7	8d 55 d8	 lea	 edx, DWORD PTR _s_lst$[ebp]
  001aa	52		 push	 edx
  001ab	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  001ae	e8 00 00 00 00	 call	 ??0?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@$$QAV01@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  001b3	8b 45 84	 mov	 eax, DWORD PTR $T2[ebp]
  001b6	83 c8 01	 or	 eax, 1
  001b9	89 45 84	 mov	 DWORD PTR $T2[ebp], eax
  001bc	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  001c0	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  001c3	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  001c8	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
$LN1@str_split:

; 145  : }

  001cb	52		 push	 edx
  001cc	8b cd		 mov	 ecx, ebp
  001ce	50		 push	 eax
  001cf	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN16@str_split
  001d5	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  001da	58		 pop	 eax
  001db	5a		 pop	 edx
  001dc	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  001df	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  001e6	5f		 pop	 edi
  001e7	81 c4 94 00 00
	00		 add	 esp, 148		; 00000094H
  001ed	3b ec		 cmp	 ebp, esp
  001ef	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001f4	8b e5		 mov	 esp, ebp
  001f6	5d		 pop	 ebp
  001f7	c3		 ret	 0
$LN16@str_split:
  001f8	01 00 00 00	 DD	 1
  001fc	00 00 00 00	 DD	 $LN15@str_split
$LN15@str_split:
  00200	d8 ff ff ff	 DD	 -40			; ffffffd8H
  00204	0c 00 00 00	 DD	 12			; 0000000cH
  00208	00 00 00 00	 DD	 $LN13@str_split
$LN13@str_split:
  0020c	73		 DB	 115			; 00000073H
  0020d	5f		 DB	 95			; 0000005fH
  0020e	6c		 DB	 108			; 0000006cH
  0020f	73		 DB	 115			; 00000073H
  00210	74		 DB	 116			; 00000074H
  00211	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$1:
  00008	8d 4d b8	 lea	 ecx, DWORD PTR $T5[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$2:
  00010	8b 45 84	 mov	 eax, DWORD PTR $T2[ebp]
  00013	83 e0 01	 and	 eax, 1
  00016	0f 84 0c 00 00
	00		 je	 $LN10@str_split
  0001c	83 65 84 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00020	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00023	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
$LN10@str_split:
  00028	c3		 ret	 0
__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$3:
  00029	8d 4d a0	 lea	 ecx, DWORD PTR $T4[ebp]
  0002c	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z$4:
  00031	8d 4d 88	 lea	 ecx, DWORD PTR $T3[ebp]
  00034	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__ehhandler$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z:
  00039	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z
  0003e	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?str_split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0I@Z ENDP ; str_split
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
_TEXT	SEGMENT
$T2 = -52						; size = 4
_sRet$ = -44						; size = 24
_pos$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_sRaw$ = 12						; size = 4
_sFind$ = 16						; size = 4
_sReplace$ = 20						; size = 4
?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z PROC ; replace2

; 28   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 28	 sub	 esp, 40			; 00000028H
  0001b	57		 push	 edi
  0001c	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  0001f	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 cc 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 29   :   size_t pos = 0;

  00032	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR _pos$[ebp], 0

; 30   :   std::string sRet=sRaw;

  00039	8b 45 0c	 mov	 eax, DWORD PTR _sRaw$[ebp]
  0003c	50		 push	 eax
  0003d	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00040	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00045	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
$LN2@replace2:

; 31   :   while((pos = sRet.find(sFind, pos)) != std::string ::npos)

  0004c	8b 4d f0	 mov	 ecx, DWORD PTR _pos$[ebp]
  0004f	51		 push	 ecx
  00050	8b 55 10	 mov	 edx, DWORD PTR _sFind$[ebp]
  00053	52		 push	 edx
  00054	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00057	e8 00 00 00 00	 call	 ?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIABV12@I@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find
  0005c	89 45 f0	 mov	 DWORD PTR _pos$[ebp], eax
  0005f	83 7d f0 ff	 cmp	 DWORD PTR _pos$[ebp], -1
  00063	74 29		 je	 SHORT $LN3@replace2

; 32   :   {
; 33   :      sRet.replace(pos, sFind.length(), sReplace);

  00065	8b 45 14	 mov	 eax, DWORD PTR _sReplace$[ebp]
  00068	50		 push	 eax
  00069	8b 4d 10	 mov	 ecx, DWORD PTR _sFind$[ebp]
  0006c	e8 00 00 00 00	 call	 ?length@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::length
  00071	50		 push	 eax
  00072	8b 4d f0	 mov	 ecx, DWORD PTR _pos$[ebp]
  00075	51		 push	 ecx
  00076	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00079	e8 00 00 00 00	 call	 ?replace@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV12@IIABV12@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::replace

; 34   :      pos += sReplace.length();

  0007e	8b 4d 14	 mov	 ecx, DWORD PTR _sReplace$[ebp]
  00081	e8 00 00 00 00	 call	 ?length@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::length
  00086	03 45 f0	 add	 eax, DWORD PTR _pos$[ebp]
  00089	89 45 f0	 mov	 DWORD PTR _pos$[ebp], eax

; 35   :   }

  0008c	eb be		 jmp	 SHORT $LN2@replace2
$LN3@replace2:

; 36   :   return sRet;

  0008e	8d 55 d4	 lea	 edx, DWORD PTR _sRet$[ebp]
  00091	52		 push	 edx
  00092	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00095	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0009a	8b 45 cc	 mov	 eax, DWORD PTR $T2[ebp]
  0009d	83 c8 01	 or	 eax, 1
  000a0	89 45 cc	 mov	 DWORD PTR $T2[ebp], eax
  000a3	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000a7	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  000aa	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  000af	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 37   :  }

  000b2	52		 push	 edx
  000b3	8b cd		 mov	 ecx, ebp
  000b5	50		 push	 eax
  000b6	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@replace2
  000bc	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000c1	58		 pop	 eax
  000c2	5a		 pop	 edx
  000c3	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000c6	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000cd	5f		 pop	 edi
  000ce	83 c4 34	 add	 esp, 52			; 00000034H
  000d1	3b ec		 cmp	 ebp, esp
  000d3	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000d8	8b e5		 mov	 esp, ebp
  000da	5d		 pop	 ebp
  000db	c3		 ret	 0
$LN11@replace2:
  000dc	01 00 00 00	 DD	 1
  000e0	00 00 00 00	 DD	 $LN10@replace2
$LN10@replace2:
  000e4	d4 ff ff ff	 DD	 -44			; ffffffd4H
  000e8	18 00 00 00	 DD	 24			; 00000018H
  000ec	00 00 00 00	 DD	 $LN8@replace2
$LN8@replace2:
  000f0	73		 DB	 115			; 00000073H
  000f1	52		 DB	 82			; 00000052H
  000f2	65		 DB	 101			; 00000065H
  000f3	74		 DB	 116			; 00000074H
  000f4	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z$0:
  00000	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z$1:
  00008	8b 45 cc	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN7@replace2
  00014	83 65 cc fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN7@replace2:
  00020	c3		 ret	 0
__ehhandler$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?replace2@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV12@00@Z ENDP ; replace2
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
_TEXT	SEGMENT
$T2 = -52						; size = 4
_sRet$ = -44						; size = 24
_pos$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_sRaw$ = 12						; size = 4
_sFind$ = 16						; size = 4
_sReplace$ = 20						; size = 4
?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z PROC ; replace2

; 40   :  {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 28	 sub	 esp, 40			; 00000028H
  0001b	57		 push	 edi
  0001c	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  0001f	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 cc 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 41   : 	 size_t pos = 0;

  00032	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR _pos$[ebp], 0

; 42   : 	 std::wstring sRet=sRaw;

  00039	8b 45 0c	 mov	 eax, DWORD PTR _sRaw$[ebp]
  0003c	50		 push	 eax
  0003d	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00040	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@ABV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00045	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
$LN2@replace2:

; 43   : 	 while((pos = sRet.find(sFind, pos)) != std::wstring ::npos)

  0004c	8b 4d f0	 mov	 ecx, DWORD PTR _pos$[ebp]
  0004f	51		 push	 ecx
  00050	8b 55 10	 mov	 edx, DWORD PTR _sFind$[ebp]
  00053	52		 push	 edx
  00054	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00057	e8 00 00 00 00	 call	 ?find@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIABV12@I@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::find
  0005c	89 45 f0	 mov	 DWORD PTR _pos$[ebp], eax
  0005f	83 7d f0 ff	 cmp	 DWORD PTR _pos$[ebp], -1
  00063	74 29		 je	 SHORT $LN3@replace2

; 44   : 	 {
; 45   : 		 sRet.replace(pos, sFind.length(), sReplace);

  00065	8b 45 14	 mov	 eax, DWORD PTR _sReplace$[ebp]
  00068	50		 push	 eax
  00069	8b 4d 10	 mov	 ecx, DWORD PTR _sFind$[ebp]
  0006c	e8 00 00 00 00	 call	 ?length@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::length
  00071	50		 push	 eax
  00072	8b 4d f0	 mov	 ecx, DWORD PTR _pos$[ebp]
  00075	51		 push	 ecx
  00076	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00079	e8 00 00 00 00	 call	 ?replace@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV12@IIABV12@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::replace

; 46   : 		 pos += sReplace.length();

  0007e	8b 4d 14	 mov	 ecx, DWORD PTR _sReplace$[ebp]
  00081	e8 00 00 00 00	 call	 ?length@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::length
  00086	03 45 f0	 add	 eax, DWORD PTR _pos$[ebp]
  00089	89 45 f0	 mov	 DWORD PTR _pos$[ebp], eax

; 47   : 	 }

  0008c	eb be		 jmp	 SHORT $LN2@replace2
$LN3@replace2:

; 48   : 	 return sRet;

  0008e	8d 55 d4	 lea	 edx, DWORD PTR _sRet$[ebp]
  00091	52		 push	 edx
  00092	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00095	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0009a	8b 45 cc	 mov	 eax, DWORD PTR $T2[ebp]
  0009d	83 c8 01	 or	 eax, 1
  000a0	89 45 cc	 mov	 DWORD PTR $T2[ebp], eax
  000a3	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000a7	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  000aa	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000af	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 49   :  }

  000b2	52		 push	 edx
  000b3	8b cd		 mov	 ecx, ebp
  000b5	50		 push	 eax
  000b6	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@replace2
  000bc	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000c1	58		 pop	 eax
  000c2	5a		 pop	 edx
  000c3	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000c6	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000cd	5f		 pop	 edi
  000ce	83 c4 34	 add	 esp, 52			; 00000034H
  000d1	3b ec		 cmp	 ebp, esp
  000d3	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000d8	8b e5		 mov	 esp, ebp
  000da	5d		 pop	 ebp
  000db	c3		 ret	 0
$LN11@replace2:
  000dc	01 00 00 00	 DD	 1
  000e0	00 00 00 00	 DD	 $LN10@replace2
$LN10@replace2:
  000e4	d4 ff ff ff	 DD	 -44			; ffffffd4H
  000e8	18 00 00 00	 DD	 24			; 00000018H
  000ec	00 00 00 00	 DD	 $LN8@replace2
$LN8@replace2:
  000f0	73		 DB	 115			; 00000073H
  000f1	52		 DB	 82			; 00000052H
  000f2	65		 DB	 101			; 00000065H
  000f3	74		 DB	 116			; 00000074H
  000f4	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z$0:
  00000	8d 4d d4	 lea	 ecx, DWORD PTR _sRet$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z$1:
  00008	8b 45 cc	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN7@replace2
  00014	83 65 cc fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
$LN7@replace2:
  00020	c3		 ret	 0
__ehhandler$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?replace2@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV12@00@Z ENDP ; replace2
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
_TEXT	SEGMENT
tv137 = -112						; size = 4
tv143 = -108						; size = 4
tv135 = -104						; size = 4
tv65 = -100						; size = 4
$T2 = -96						; size = 4
$T3 = -92						; size = 24
$T4 = -68						; size = 24
_s_lst$ = -40						; size = 12
_icount$ = -24						; size = 4
_ipos$ = -20						; size = 4
_istart$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_sRaw$ = 12						; size = 4
_sSep$ = 16						; size = 4
_nMax$ = 20						; size = 4
?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z PROC ; split

; 79   :  {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 64	 sub	 esp, 100		; 00000064H
  0001b	57		 push	 edi
  0001c	8d 7d 90	 lea	 edi, DWORD PTR [ebp-112]
  0001f	b9 19 00 00 00	 mov	 ecx, 25			; 00000019H
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 a0 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 80   : 
; 81   : 	 size_t istart=0,ipos=0;int icount=0;

  00032	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR _istart$[ebp], 0
  00039	c7 45 ec 00 00
	00 00		 mov	 DWORD PTR _ipos$[ebp], 0
  00040	c7 45 e8 00 00
	00 00		 mov	 DWORD PTR _icount$[ebp], 0

; 82   : 	 vector<string> s_lst;

  00047	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  0004a	e8 00 00 00 00	 call	 ??0?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  0004f	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
$LN2@split:

; 83   : 
; 84   : 	 while((ipos=sRaw.find(sSep,istart))!=string::npos)

  00056	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  00059	50		 push	 eax
  0005a	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  0005d	51		 push	 ecx
  0005e	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  00061	e8 00 00 00 00	 call	 ?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIABV12@I@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find
  00066	89 45 ec	 mov	 DWORD PTR _ipos$[ebp], eax
  00069	83 7d ec ff	 cmp	 DWORD PTR _ipos$[ebp], -1
  0006d	74 65		 je	 SHORT $LN3@split

; 85   : 	 {
; 86   : 		 s_lst.push_back(sRaw.substr(istart,ipos-istart));	

  0006f	8b 55 ec	 mov	 edx, DWORD PTR _ipos$[ebp]
  00072	2b 55 f0	 sub	 edx, DWORD PTR _istart$[ebp]
  00075	52		 push	 edx
  00076	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  00079	50		 push	 eax
  0007a	8d 4d bc	 lea	 ecx, DWORD PTR $T4[ebp]
  0007d	51		 push	 ecx
  0007e	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  00081	e8 00 00 00 00	 call	 ?substr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV12@II@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::substr
  00086	89 45 9c	 mov	 DWORD PTR tv65[ebp], eax
  00089	8b 55 9c	 mov	 edx, DWORD PTR tv65[ebp]
  0008c	89 55 98	 mov	 DWORD PTR tv135[ebp], edx
  0008f	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  00093	8b 45 98	 mov	 eax, DWORD PTR tv135[ebp]
  00096	50		 push	 eax
  00097	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  0009a	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEX$$QAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::push_back
  0009f	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  000a3	8d 4d bc	 lea	 ecx, DWORD PTR $T4[ebp]
  000a6	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

; 87   : 		 istart=ipos+sSep.length();

  000ab	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  000ae	e8 00 00 00 00	 call	 ?length@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::length
  000b3	03 45 ec	 add	 eax, DWORD PTR _ipos$[ebp]
  000b6	89 45 f0	 mov	 DWORD PTR _istart$[ebp], eax

; 88   : 		 icount++;

  000b9	8b 4d e8	 mov	 ecx, DWORD PTR _icount$[ebp]
  000bc	83 c1 01	 add	 ecx, 1
  000bf	89 4d e8	 mov	 DWORD PTR _icount$[ebp], ecx

; 89   : 		 if(nMax>0 && icount>=nMax) break;

  000c2	83 7d 14 00	 cmp	 DWORD PTR _nMax$[ebp], 0
  000c6	7e 0a		 jle	 SHORT $LN4@split
  000c8	8b 55 e8	 mov	 edx, DWORD PTR _icount$[ebp]
  000cb	3b 55 14	 cmp	 edx, DWORD PTR _nMax$[ebp]
  000ce	7c 02		 jl	 SHORT $LN4@split
  000d0	eb 02		 jmp	 SHORT $LN3@split
$LN4@split:

; 90   : 	 }

  000d2	eb 82		 jmp	 SHORT $LN2@split
$LN3@split:

; 91   : 
; 92   : 	 s_lst.push_back( sRaw.substr(istart));	

  000d4	6a ff		 push	 -1
  000d6	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  000d9	50		 push	 eax
  000da	8d 4d a4	 lea	 ecx, DWORD PTR $T3[ebp]
  000dd	51		 push	 ecx
  000de	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  000e1	e8 00 00 00 00	 call	 ?substr@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV12@II@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::substr
  000e6	89 45 94	 mov	 DWORD PTR tv143[ebp], eax
  000e9	8b 55 94	 mov	 edx, DWORD PTR tv143[ebp]
  000ec	89 55 90	 mov	 DWORD PTR tv137[ebp], edx
  000ef	c6 45 fc 03	 mov	 BYTE PTR __$EHRec$[ebp+8], 3
  000f3	8b 45 90	 mov	 eax, DWORD PTR tv137[ebp]
  000f6	50		 push	 eax
  000f7	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  000fa	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEX$$QAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::push_back
  000ff	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00103	8d 4d a4	 lea	 ecx, DWORD PTR $T3[ebp]
  00106	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

; 93   : 
; 94   : 	 return s_lst;

  0010b	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  0010e	51		 push	 ecx
  0010f	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00112	e8 00 00 00 00	 call	 ??0?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@$$QAV01@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  00117	8b 55 a0	 mov	 edx, DWORD PTR $T2[ebp]
  0011a	83 ca 01	 or	 edx, 1
  0011d	89 55 a0	 mov	 DWORD PTR $T2[ebp], edx
  00120	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00124	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00127	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  0012c	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 95   :  }

  0012f	52		 push	 edx
  00130	8b cd		 mov	 ecx, ebp
  00132	50		 push	 eax
  00133	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN14@split
  00139	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0013e	58		 pop	 eax
  0013f	5a		 pop	 edx
  00140	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00143	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0014a	5f		 pop	 edi
  0014b	83 c4 70	 add	 esp, 112		; 00000070H
  0014e	3b ec		 cmp	 ebp, esp
  00150	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00155	8b e5		 mov	 esp, ebp
  00157	5d		 pop	 ebp
  00158	c3		 ret	 0
  00159	0f 1f 00	 npad	 3
$LN14@split:
  0015c	01 00 00 00	 DD	 1
  00160	00 00 00 00	 DD	 $LN13@split
$LN13@split:
  00164	d8 ff ff ff	 DD	 -40			; ffffffd8H
  00168	0c 00 00 00	 DD	 12			; 0000000cH
  0016c	00 00 00 00	 DD	 $LN11@split
$LN11@split:
  00170	73		 DB	 115			; 00000073H
  00171	5f		 DB	 95			; 0000005fH
  00172	6c		 DB	 108			; 0000006cH
  00173	73		 DB	 115			; 00000073H
  00174	74		 DB	 116			; 00000074H
  00175	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$1:
  00008	8d 4d bc	 lea	 ecx, DWORD PTR $T4[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$2:
  00010	8d 4d a4	 lea	 ecx, DWORD PTR $T3[ebp]
  00013	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z$3:
  00018	8b 45 a0	 mov	 eax, DWORD PTR $T2[ebp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	0f 84 0c 00 00
	00		 je	 $LN10@split
  00024	83 65 a0 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00028	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0002b	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
$LN10@split:
  00030	c3		 ret	 0
__ehhandler$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z:
  00031	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z
  00036	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?split@@YA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@ABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@0H@Z ENDP ; split
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
_TEXT	SEGMENT
tv137 = -112						; size = 4
tv143 = -108						; size = 4
tv135 = -104						; size = 4
tv65 = -100						; size = 4
$T2 = -96						; size = 4
$T3 = -92						; size = 24
$T4 = -68						; size = 24
_s_lst$ = -40						; size = 12
_icount$ = -24						; size = 4
_ipos$ = -20						; size = 4
_istart$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_sRaw$ = 12						; size = 4
_sSep$ = 16						; size = 4
_nMax$ = 20						; size = 4
?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z PROC ; split

; 98   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 64	 sub	 esp, 100		; 00000064H
  0001b	57		 push	 edi
  0001c	8d 7d 90	 lea	 edi, DWORD PTR [ebp-112]
  0001f	b9 19 00 00 00	 mov	 ecx, 25			; 00000019H
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 a0 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 99   : 	size_t istart=0,ipos=0;int icount=0;

  00032	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR _istart$[ebp], 0
  00039	c7 45 ec 00 00
	00 00		 mov	 DWORD PTR _ipos$[ebp], 0
  00040	c7 45 e8 00 00
	00 00		 mov	 DWORD PTR _icount$[ebp], 0

; 100  : 	vector<wstring> s_lst;

  00047	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  0004a	e8 00 00 00 00	 call	 ??0?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  0004f	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
$LN2@split:

; 101  : 
; 102  : 	while((ipos=sRaw.find(sSep,istart))!=wstring::npos)

  00056	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  00059	50		 push	 eax
  0005a	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  0005d	51		 push	 ecx
  0005e	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  00061	e8 00 00 00 00	 call	 ?find@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIABV12@I@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::find
  00066	89 45 ec	 mov	 DWORD PTR _ipos$[ebp], eax
  00069	83 7d ec ff	 cmp	 DWORD PTR _ipos$[ebp], -1
  0006d	74 65		 je	 SHORT $LN3@split

; 103  : 	{
; 104  : 		s_lst.push_back(sRaw.substr(istart,ipos-istart));	

  0006f	8b 55 ec	 mov	 edx, DWORD PTR _ipos$[ebp]
  00072	2b 55 f0	 sub	 edx, DWORD PTR _istart$[ebp]
  00075	52		 push	 edx
  00076	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  00079	50		 push	 eax
  0007a	8d 4d bc	 lea	 ecx, DWORD PTR $T4[ebp]
  0007d	51		 push	 ecx
  0007e	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  00081	e8 00 00 00 00	 call	 ?substr@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBE?AV12@II@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::substr
  00086	89 45 9c	 mov	 DWORD PTR tv65[ebp], eax
  00089	8b 55 9c	 mov	 edx, DWORD PTR tv65[ebp]
  0008c	89 55 98	 mov	 DWORD PTR tv135[ebp], edx
  0008f	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  00093	8b 45 98	 mov	 eax, DWORD PTR tv135[ebp]
  00096	50		 push	 eax
  00097	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  0009a	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAEX$$QAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@@Z ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::push_back
  0009f	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  000a3	8d 4d bc	 lea	 ecx, DWORD PTR $T4[ebp]
  000a6	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 105  : 		istart=ipos+sSep.length();

  000ab	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  000ae	e8 00 00 00 00	 call	 ?length@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::length
  000b3	03 45 ec	 add	 eax, DWORD PTR _ipos$[ebp]
  000b6	89 45 f0	 mov	 DWORD PTR _istart$[ebp], eax

; 106  : 		icount++;

  000b9	8b 4d e8	 mov	 ecx, DWORD PTR _icount$[ebp]
  000bc	83 c1 01	 add	 ecx, 1
  000bf	89 4d e8	 mov	 DWORD PTR _icount$[ebp], ecx

; 107  : 		if(nMax>0 && icount>=nMax) break;

  000c2	83 7d 14 00	 cmp	 DWORD PTR _nMax$[ebp], 0
  000c6	7e 0a		 jle	 SHORT $LN4@split
  000c8	8b 55 e8	 mov	 edx, DWORD PTR _icount$[ebp]
  000cb	3b 55 14	 cmp	 edx, DWORD PTR _nMax$[ebp]
  000ce	7c 02		 jl	 SHORT $LN4@split
  000d0	eb 02		 jmp	 SHORT $LN3@split
$LN4@split:

; 108  : 	}

  000d2	eb 82		 jmp	 SHORT $LN2@split
$LN3@split:

; 109  : 
; 110  : 	s_lst.push_back( sRaw.substr(istart));	

  000d4	6a ff		 push	 -1
  000d6	8b 45 f0	 mov	 eax, DWORD PTR _istart$[ebp]
  000d9	50		 push	 eax
  000da	8d 4d a4	 lea	 ecx, DWORD PTR $T3[ebp]
  000dd	51		 push	 ecx
  000de	8b 4d 0c	 mov	 ecx, DWORD PTR _sRaw$[ebp]
  000e1	e8 00 00 00 00	 call	 ?substr@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBE?AV12@II@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::substr
  000e6	89 45 94	 mov	 DWORD PTR tv143[ebp], eax
  000e9	8b 55 94	 mov	 edx, DWORD PTR tv143[ebp]
  000ec	89 55 90	 mov	 DWORD PTR tv137[ebp], edx
  000ef	c6 45 fc 03	 mov	 BYTE PTR __$EHRec$[ebp+8], 3
  000f3	8b 45 90	 mov	 eax, DWORD PTR tv137[ebp]
  000f6	50		 push	 eax
  000f7	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  000fa	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAEX$$QAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@@Z ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::push_back
  000ff	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00103	8d 4d a4	 lea	 ecx, DWORD PTR $T3[ebp]
  00106	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 111  : 
; 112  : 	return s_lst;

  0010b	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  0010e	51		 push	 ecx
  0010f	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00112	e8 00 00 00 00	 call	 ??0?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@$$QAV01@@Z ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  00117	8b 55 a0	 mov	 edx, DWORD PTR $T2[ebp]
  0011a	83 ca 01	 or	 edx, 1
  0011d	89 55 a0	 mov	 DWORD PTR $T2[ebp], edx
  00120	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00124	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00127	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  0012c	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 113  : }

  0012f	52		 push	 edx
  00130	8b cd		 mov	 ecx, ebp
  00132	50		 push	 eax
  00133	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN14@split
  00139	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0013e	58		 pop	 eax
  0013f	5a		 pop	 edx
  00140	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00143	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0014a	5f		 pop	 edi
  0014b	83 c4 70	 add	 esp, 112		; 00000070H
  0014e	3b ec		 cmp	 ebp, esp
  00150	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00155	8b e5		 mov	 esp, ebp
  00157	5d		 pop	 ebp
  00158	c3		 ret	 0
  00159	0f 1f 00	 npad	 3
$LN14@split:
  0015c	01 00 00 00	 DD	 1
  00160	00 00 00 00	 DD	 $LN13@split
$LN13@split:
  00164	d8 ff ff ff	 DD	 -40			; ffffffd8H
  00168	0c 00 00 00	 DD	 12			; 0000000cH
  0016c	00 00 00 00	 DD	 $LN11@split
$LN11@split:
  00170	73		 DB	 115			; 00000073H
  00171	5f		 DB	 95			; 0000005fH
  00172	6c		 DB	 108			; 0000006cH
  00173	73		 DB	 115			; 00000073H
  00174	74		 DB	 116			; 00000074H
  00175	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _s_lst$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$1:
  00008	8d 4d bc	 lea	 ecx, DWORD PTR $T4[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$2:
  00010	8d 4d a4	 lea	 ecx, DWORD PTR $T3[ebp]
  00013	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z$3:
  00018	8b 45 a0	 mov	 eax, DWORD PTR $T2[ebp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	0f 84 0c 00 00
	00		 je	 $LN10@split
  00024	83 65 a0 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00028	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0002b	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
$LN10@split:
  00030	c3		 ret	 0
__ehhandler$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z:
  00031	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z
  00036	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?split@@YA?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@0H@Z ENDP ; split
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\stringutils.cpp
_TEXT	SEGMENT
tv136 = -84						; size = 4
tv140 = -80						; size = 4
$T2 = -76						; size = 4
$T3 = -72						; size = 24
_i$4 = -48						; size = 4
_sret$ = -40						; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_vRaw$ = 12						; size = 4
_sSep$ = 16						; size = 4
?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z PROC ; join

; 116  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 48	 sub	 esp, 72			; 00000048H
  0001b	57		 push	 edi
  0001c	8d 7d ac	 lea	 edi, DWORD PTR [ebp-84]
  0001f	b9 12 00 00 00	 mov	 ecx, 18			; 00000012H
  00024	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00029	f3 ab		 rep stosd
  0002b	c7 45 b4 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 117  : 	using namespace std;
; 118  : 	wstring sret;

  00032	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00035	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0003a	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 119  : 	if(!vRaw.size()) return sret;

  00041	8b 4d 0c	 mov	 ecx, DWORD PTR _vRaw$[ebp]
  00044	e8 00 00 00 00	 call	 ?size@?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEIXZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::size
  00049	85 c0		 test	 eax, eax
  0004b	75 29		 jne	 SHORT $LN5@join
  0004d	8d 45 d8	 lea	 eax, DWORD PTR _sret$[ebp]
  00050	50		 push	 eax
  00051	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00054	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00059	8b 4d b4	 mov	 ecx, DWORD PTR $T2[ebp]
  0005c	83 c9 01	 or	 ecx, 1
  0005f	89 4d b4	 mov	 DWORD PTR $T2[ebp], ecx
  00062	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00066	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00069	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0006e	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  00071	e9 92 00 00 00	 jmp	 $LN1@join
$LN5@join:

; 120  : 	for(size_t i=0;i<vRaw.size();++i)

  00076	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR _i$4[ebp], 0
  0007d	eb 09		 jmp	 SHORT $LN4@join
$LN2@join:
  0007f	8b 55 d0	 mov	 edx, DWORD PTR _i$4[ebp]
  00082	83 c2 01	 add	 edx, 1
  00085	89 55 d0	 mov	 DWORD PTR _i$4[ebp], edx
$LN4@join:
  00088	8b 4d 0c	 mov	 ecx, DWORD PTR _vRaw$[ebp]
  0008b	e8 00 00 00 00	 call	 ?size@?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEIXZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::size
  00090	39 45 d0	 cmp	 DWORD PTR _i$4[ebp], eax
  00093	73 44		 jae	 SHORT $LN3@join

; 121  : 	{
; 122  : 		sret+=sSep+vRaw[i];

  00095	8b 45 d0	 mov	 eax, DWORD PTR _i$4[ebp]
  00098	50		 push	 eax
  00099	8b 4d 0c	 mov	 ecx, DWORD PTR _vRaw$[ebp]
  0009c	e8 00 00 00 00	 call	 ??A?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@I@Z ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::operator[]
  000a1	50		 push	 eax
  000a2	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  000a5	51		 push	 ecx
  000a6	8d 55 b8	 lea	 edx, DWORD PTR $T3[ebp]
  000a9	52		 push	 edx
  000aa	e8 00 00 00 00	 call	 ??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@ABV10@0@Z ; std::operator+<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000af	83 c4 0c	 add	 esp, 12			; 0000000cH
  000b2	89 45 b0	 mov	 DWORD PTR tv140[ebp], eax
  000b5	8b 45 b0	 mov	 eax, DWORD PTR tv140[ebp]
  000b8	89 45 ac	 mov	 DWORD PTR tv136[ebp], eax
  000bb	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  000bf	8b 4d ac	 mov	 ecx, DWORD PTR tv136[ebp]
  000c2	51		 push	 ecx
  000c3	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  000c6	e8 00 00 00 00	 call	 ??Y?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator+=
  000cb	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  000cf	8d 4d b8	 lea	 ecx, DWORD PTR $T3[ebp]
  000d2	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 123  : 	}

  000d7	eb a6		 jmp	 SHORT $LN2@join
$LN3@join:

; 124  : 	return sret.substr(sSep.length());

  000d9	6a ff		 push	 -1
  000db	8b 4d 10	 mov	 ecx, DWORD PTR _sSep$[ebp]
  000de	e8 00 00 00 00	 call	 ?length@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::length
  000e3	50		 push	 eax
  000e4	8b 55 08	 mov	 edx, DWORD PTR ___$ReturnUdt$[ebp]
  000e7	52		 push	 edx
  000e8	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  000eb	e8 00 00 00 00	 call	 ?substr@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBE?AV12@II@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::substr
  000f0	8b 45 b4	 mov	 eax, DWORD PTR $T2[ebp]
  000f3	83 c8 01	 or	 eax, 1
  000f6	89 45 b4	 mov	 DWORD PTR $T2[ebp], eax
  000f9	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000fd	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00100	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00105	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
$LN1@join:

; 125  : }

  00108	52		 push	 edx
  00109	8b cd		 mov	 ecx, ebp
  0010b	50		 push	 eax
  0010c	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN14@join
  00112	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00117	58		 pop	 eax
  00118	5a		 pop	 edx
  00119	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0011c	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00123	5f		 pop	 edi
  00124	83 c4 54	 add	 esp, 84			; 00000054H
  00127	3b ec		 cmp	 ebp, esp
  00129	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0012e	8b e5		 mov	 esp, ebp
  00130	5d		 pop	 ebp
  00131	c3		 ret	 0
  00132	66 90		 npad	 2
$LN14@join:
  00134	01 00 00 00	 DD	 1
  00138	00 00 00 00	 DD	 $LN13@join
$LN13@join:
  0013c	d8 ff ff ff	 DD	 -40			; ffffffd8H
  00140	18 00 00 00	 DD	 24			; 00000018H
  00144	00 00 00 00	 DD	 $LN11@join
$LN11@join:
  00148	73		 DB	 115			; 00000073H
  00149	72		 DB	 114			; 00000072H
  0014a	65		 DB	 101			; 00000065H
  0014b	74		 DB	 116			; 00000074H
  0014c	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _sret$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z$1:
  00008	8b 45 b4	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN9@join
  00014	83 65 b4 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
$LN9@join:
  00020	c3		 ret	 0
__unwindfunclet$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z$2:
  00021	8d 4d b8	 lea	 ecx, DWORD PTR $T3[ebp]
  00024	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__ehhandler$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z:
  00029	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z
  0002e	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?join@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@ABV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@2@ABV12@@Z ENDP ; join
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??A?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@I@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pos$ = 8						; size = 4
??A?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@I@Z PROC ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::operator[], COMDAT
; _this$ = ecx

; 1740 : 		{	// subscript nonmutable sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1741 :  #if _ITERATOR_DEBUG_LEVEL != 0
; 1742 : 		_STL_VERIFY(_Pos < size(), "vector subscript out of range");
; 1743 :  #endif /* _ITERATOR_DEBUG_LEVEL != 0 */
; 1744 : 
; 1745 : 		return (this->_Myfirst()[_Pos]);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@@std@@QBEABQAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > > >::_Myfirst
  00016	6b 4d 08 18	 imul	 ecx, DWORD PTR __Pos$[ebp], 24
  0001a	03 08		 add	 ecx, DWORD PTR [eax]
  0001c	8b c1		 mov	 eax, ecx

; 1746 : 		}

  0001e	83 c4 04	 add	 esp, 4
  00021	3b ec		 cmp	 ebp, esp
  00023	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00028	8b e5		 mov	 esp, ebp
  0002a	5d		 pop	 ebp
  0002b	c2 04 00	 ret	 4
??A?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@I@Z ENDP ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::operator[]
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\vector
;	COMDAT ??A?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@I@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pos$ = 8						; size = 4
??A?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@I@Z PROC ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::operator[], COMDAT
; _this$ = ecx

; 1740 : 		{	// subscript nonmutable sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1741 :  #if _ITERATOR_DEBUG_LEVEL != 0
; 1742 : 		_STL_VERIFY(_Pos < size(), "vector subscript out of range");
; 1743 :  #endif /* _ITERATOR_DEBUG_LEVEL != 0 */
; 1744 : 
; 1745 : 		return (this->_Myfirst()[_Pos]);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Myfirst@?$_Vector_alloc@U?$_Vec_base_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@std@@QBEABQAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ ; std::_Vector_alloc<std::_Vec_base_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > >::_Myfirst
  00016	6b 4d 08 18	 imul	 ecx, DWORD PTR __Pos$[ebp], 24
  0001a	03 08		 add	 ecx, DWORD PTR [eax]
  0001c	8b c1		 mov	 eax, ecx

; 1746 : 		}

  0001e	83 c4 04	 add	 esp, 4
  00021	3b ec		 cmp	 ebp, esp
  00023	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00028	8b e5		 mov	 esp, ebp
  0002a	5d		 pop	 ebp
  0002b	c2 04 00	 ret	 4
??A?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@I@Z ENDP ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::operator[]
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstring
;	COMDAT ??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z
_TEXT	SEGMENT
$T2 = -48						; size = 4
__Ans$ = -40						; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
__Left$ = 12						; size = 4
__Right$ = 16						; size = 4
??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z PROC ; std::operator+<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 4049 : 	{	// return string + string

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 24	 sub	 esp, 36			; 00000024H
  0001b	56		 push	 esi
  0001c	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00021	89 45 d0	 mov	 DWORD PTR [ebp-48], eax
  00024	89 45 d4	 mov	 DWORD PTR [ebp-44], eax
  00027	89 45 d8	 mov	 DWORD PTR [ebp-40], eax
  0002a	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0002d	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00030	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00033	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00036	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00039	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0003c	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 4050 : 	basic_string<_Elem, _Traits, _Alloc> _Ans;

  00043	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00046	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0004b	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 4051 : 	_Ans.reserve(_Left.size() + _Right.size());

  00052	8b 4d 0c	 mov	 ecx, DWORD PTR __Left$[ebp]
  00055	e8 00 00 00 00	 call	 ?size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::size
  0005a	8b f0		 mov	 esi, eax
  0005c	8b 4d 10	 mov	 ecx, DWORD PTR __Right$[ebp]
  0005f	e8 00 00 00 00	 call	 ?size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEIXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::size
  00064	03 f0		 add	 esi, eax
  00066	56		 push	 esi
  00067	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  0006a	e8 00 00 00 00	 call	 ?reserve@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXI@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::reserve

; 4052 : 	_Ans += _Left;

  0006f	8b 45 0c	 mov	 eax, DWORD PTR __Left$[ebp]
  00072	50		 push	 eax
  00073	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00076	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=

; 4053 : 	_Ans += _Right;

  0007b	8b 4d 10	 mov	 ecx, DWORD PTR __Right$[ebp]
  0007e	51		 push	 ecx
  0007f	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00082	e8 00 00 00 00	 call	 ??Y?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator+=

; 4054 : 	return (_Ans);

  00087	8d 55 d8	 lea	 edx, DWORD PTR __Ans$[ebp]
  0008a	52		 push	 edx
  0008b	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0008e	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00093	8b 45 d0	 mov	 eax, DWORD PTR $T2[ebp]
  00096	83 c8 01	 or	 eax, 1
  00099	89 45 d0	 mov	 DWORD PTR $T2[ebp], eax
  0009c	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000a0	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  000a3	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  000a8	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 4055 : 	}

  000ab	52		 push	 edx
  000ac	8b cd		 mov	 ecx, ebp
  000ae	50		 push	 eax
  000af	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN9@operator
  000b5	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000ba	58		 pop	 eax
  000bb	5a		 pop	 edx
  000bc	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000bf	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000c6	5e		 pop	 esi
  000c7	83 c4 30	 add	 esp, 48			; 00000030H
  000ca	3b ec		 cmp	 ebp, esp
  000cc	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000d1	8b e5		 mov	 esp, ebp
  000d3	5d		 pop	 ebp
  000d4	c3		 ret	 0
  000d5	0f 1f 00	 npad	 3
$LN9@operator:
  000d8	01 00 00 00	 DD	 1
  000dc	00 00 00 00	 DD	 $LN8@operator
$LN8@operator:
  000e0	d8 ff ff ff	 DD	 -40			; ffffffd8H
  000e4	18 00 00 00	 DD	 24			; 00000018H
  000e8	00 00 00 00	 DD	 $LN6@operator
$LN6@operator:
  000ec	5f		 DB	 95			; 0000005fH
  000ed	41		 DB	 65			; 00000041H
  000ee	6e		 DB	 110			; 0000006eH
  000ef	73		 DB	 115			; 00000073H
  000f0	00		 DB	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR __Ans$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z$1:
  00008	8b 45 d0	 mov	 eax, DWORD PTR $T2[ebp]
  0000b	83 e0 01	 and	 eax, 1
  0000e	0f 84 0c 00 00
	00		 je	 $LN5@operator
  00014	83 65 d0 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00018	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0001b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN5@operator:
  00020	c3		 ret	 0
__ehhandler$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z:
  00021	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z
  00026	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@ABV10@0@Z ENDP ; std::operator+<char,std::char_traits<char>,std::allocator<char> >
END
