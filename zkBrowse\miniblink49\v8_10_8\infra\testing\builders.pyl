# Copyright 2018 The V8 project authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
#
# Please keep builder names, builder configs and test definitions sorted.
# Builder names should be sorted alphabetically. Builder configs should have
# keys sorted in the alphabetical order except 'tests' key, which should always
# come last. Test definitions must have keys in the following order, but omit
# optional fields:
#  * name (required)
#  * suffix
#  * variant
#  * test_args
#  * shards
#  * swarming_dimensions
#  * swarming_task_attrs
#
# Please also format test definitions as a single line with ', ' separating
# fields, e.g.
#
#   {'name': 'v8testing', 'variant': 'extra', 'shards': 2}
#
# After formatting test definitions this way, please sort them alphabetically by
# test name. For all variants of the test with the same name, the
# least-qualified test (no variant, no test args) should come first. You may
# also deviate from the alphabetical order if necessary and group tests
# differently, but in this case please add a comment before each group and
# continue to sort tests using the rules above within each group.

{
  ##############################################################################
  ### luci.v8.try
  ##############################################################################
  # Android
  'v8_android_arm64_n5x_rel': {
    'swarming_dimensions' : {
      'device_os': 'MMB29Q',
      'device_type': 'bullhead',
      'os': 'Android',
    },
    'tests': [
      {'name': 'mozilla', 'variant': 'default'},
      {'name': 'test262', 'variant': 'default', 'shards': 10},
      {'name': 'v8testing', 'variant': 'default', 'shards': 4},
    ],
  },
  ##############################################################################
  # Fuchsia
  'v8_fuchsia_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'fuchsia-unittests'},
    ],
  },
  'V8 Fuchsia': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'fuchsia-unittests'},
    ],
  },
  ##############################################################################
  # Linux32
  'v8_linux_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'test262', 'variant': 'extra', 'shards': 5},
      {'name': 'v8testing', 'shards': 4},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 4},
      # Noavx.
      {
        'name': 'mozilla',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'noavx',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx'],
        'shards': 4
      },
      # Nosse3.
      {
        'name': 'v8testing',
        'suffix': 'nosse3',
        'test_args': [
          '--extra-flags',
          '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
        'shards': 4,
      },
    ],
  },
  'v8_linux_gc_stress_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit', 'variant': 'slow_path', 'test_args': ['--gc-stress'], 'shards': 2},
      {'name': 'd8testing', 'test_args': ['--gc-stress'], 'shards': 5},
    ],
  },
  'v8_linux_nodcheck_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'test262', 'shards': 2},
      {'name': 'test262', 'variant': 'extra', 'shards': 2},
      {'name': 'v8testing', 'shards': 2},
      {'name': 'v8testing', 'variant': 'extra'},
      {
        'name': 'v8testing',
        'suffix': 'nosse3',
        'test_args': [
          '--extra-flags',
          '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
        'shards': 2,
      },
    ],
  },
  'v8_linux_noi18n_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mozilla', 'variant': 'default'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing', 'variant': 'default', 'shards': 2},
    ],
  },
  'v8_linux_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'gcmole'},
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'optimize_for_size'},
      {'name': 'test262', 'shards': 4},
      {'name': 'test262', 'variant': 'extra', 'shards': 6},
      {'name': 'v8testing', 'shards': 4},
      {'name': 'v8testing', 'suffix': 'isolates', 'test_args': ['--isolates'], 'shards': 4},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 4},
      {
        'name': 'v8testing',
        'suffix': 'nosse3',
        'test_args': [
          '--extra-flags',
          '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
        'shards': 4,
      },
    ],
  },
  'v8_linux_optional_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      # Code serializer.
      {'name': 'benchmarks', 'variant': 'code_serializer', 'shards': 1},
      {'name': 'd8testing', 'variant': 'code_serializer', 'shards': 1},
      {'name': 'mozilla', 'variant': 'code_serializer', 'shards': 1},
      {'name': 'test262', 'variant': 'code_serializer', 'shards': 1},
      # No SSE3.
      {
        'name': 'mozilla',
        'suffix': 'nosse3',
        'test_args': [
          '--extra-flags',
          '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
      },
      {
        'name': 'test262',
        'suffix': 'nosse3',
        'variant': 'default',
        'test_args': [
          '--extra-flags',
          '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
      },
      {
        'name': 'v8testing',
        'suffix': 'nosse3',
        'test_args': [
          '--extra-flags',
          '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
        'shards': 3,
      },
      # No SSSE3.
      {
        'name': 'mozilla',
        'suffix': 'nossse3',
        'test_args': [
          '--extra-flags',
          '--noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
      },
      {
        'name': 'test262',
        'suffix': 'nossse3',
        'variant': 'default',
        'test_args': [
          '--extra-flags',
          '--noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
      },
      {
        'name': 'v8testing',
        'suffix': 'nossse3',
        'test_args': [
          '--extra-flags',
          '--noenable-ssse3 --noenable-sse4-1 --noenable-avx',
        ],
        'shards': 3,
      },
      # No SSE4.
      {
        'name': 'mozilla',
        'suffix': 'nosse4',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx'],
      },
      {
        'name': 'test262',
        'suffix': 'nosse4',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx'],
      },
      {
        'name': 'v8testing',
        'suffix': 'nosse4',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx'],
        'shards': 3,
      },
      # Noavx.
      {
        'name': 'mozilla',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'noavx',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx'],
        'shards': 2
      },
    ],
  },
  'v8_linux_verify_csa_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  ##############################################################################
  # Linux32 with arm simulators
  'v8_linux_arm_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access', 'shards': 3},
      {'name': 'mozilla', 'shards': 3},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 10},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 10},
    ],
  },
  'v8_linux_arm_lite_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'variant': 'default', 'shards': 4},
    ],
  },
  'v8_linux_arm_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access', 'shards': 2},
      {'name': 'mozilla', 'shards': 2},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 10},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 10},
    ],
  },
  ##############################################################################
  # Linux64
  'v8_linux64_asan_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'test262', 'shards': 12},
      {'name': 'v8testing', 'shards': 5},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 5},
      {'name': 'v8testing', 'variant': 'slow_path'},
    ],
  },
  'v8_linux64_cfi_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'mozilla'},
      {'name': 'optimize_for_size'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_cppgc_non_default_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks', 'shards': 2},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'mjsunit_sp_frame_access', 'shards': 2},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'test262', 'variant': 'extra', 'shards': 9},
      {'name': 'v8testing', 'shards': 5},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 5},
      {'name': 'v8testing', 'variant': 'minor_mc'},
      {'name': 'v8testing', 'variant': 'no_lfa'},
      {'name': 'v8testing', 'variant': 'stress_instruction_scheduling'},
      {'name': 'v8testing', 'variant': 'stress_concurrent_allocation'},
      {'name': 'v8testing', 'variant': 'stress_concurrent_inlining'},
      # Maglev -- move to extra once more architectures are supported.
      {'name': 'mjsunit', 'variant': 'maglev'},
    ],
  },
  'v8_linux64_dict_tracking_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_disable_runtime_call_stats_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'v8_linux64_external_code_space_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_fuzzilli_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    # TODO(almuthanna): Add a new test config for the fuzzilli suite.
    'tests': [],
  },
  'v8_linux64_fyi_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      # Infra staging.
      {'name': 'v8testing', 'variant': 'infra_staging', 'shards': 2},
      # Stress sampling.
      {'name': 'mjsunit', 'variant': 'stress_sampling'},
      {'name': 'webkit', 'variant': 'stress_sampling'},
      # Stress snapshot.
      {'name': 'mjsunit', 'variant': 'stress_snapshot'},
      # Maglev.
      {'name': 'mjsunit', 'variant': 'maglev'},
      # Stress maglev.
      {'name': 'mjsunit', 'variant': 'stress_maglev'},
      # Experimental regexp engine.
      {'name': 'mjsunit', 'variant': 'experimental_regexp'},
      # Wasm write protect code space.
      {'name': 'mjsunit', 'variant': 'wasm_write_protect_code'},
    ],
  },
  'v8_linux64_gc_stress_custom_snapshot_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'mjsunit',
        'test_args': ['--gc-stress', '--no-harness'],
        'shards': 3,
      },
    ],
  },
  'v8_linux64_gc_stress_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'd8testing', 'test_args': ['--gc-stress'], 'shards': 5},
      {
        'name': 'mjsunit',
        'variant': 'slow_path',
        'test_args': ['--gc-stress'],
        'shards': 2
      },
      {
        'name': 'mjsunit',
        'variant': 'maglev',
        'test_args': ['--gc-stress'],
        'shards': 2
      },
    ],
  },
  'v8_linux64_gcc_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-20.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'v8_linux64_gcov_coverage': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'v8_linux64_heap_sandbox_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 4},
    ],
  },
  'v8_linux64_minor_mc_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'variant': 'minor_mc'},
      {'name': 'benchmarks', 'variant': 'minor_mc'},
      {'name': 'mozilla', 'variant': 'minor_mc'},
      {'name': 'test262', 'variant': 'minor_mc', 'shards': 2},
      {'name': 'mjsunit', 'variant': 'minor_mc'},
    ],
  },
  'v8_linux64_msan_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 5},
    ],
  },
  'v8_linux64_nodcheck_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'assert_types'},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'check-bytecode-baseline'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'assert_types'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'perf_integration'},
      {'name': 'test262', 'shards': 2},
      {'name': 'test262', 'variant': 'assert_types', 'shards': 2},
      {'name': 'test262', 'variant': 'extra', 'shards': 2},
      {'name': 'v8testing', 'shards': 2},
      {'name': 'v8testing', 'variant': 'assert_types'},
      {'name': 'v8testing', 'variant': 'extra'},
      {'name': 'v8testing', 'variant': 'no_lfa'},
      {'name': 'v8testing', 'variant': 'stress_instruction_scheduling'},
      # Maglev -- move to extra once more architectures are supported.
      {'name': 'mjsunit', 'variant': 'maglev'},
    ],
  },
  'v8_linux64_perfetto_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_no_pointer_compression_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_no_sandbox_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 5},
    ],
  },
  'v8_linux64_single_generation_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      # TODO(machenbach): Add benchmarks.
      # TODO(machenbach): Add mozilla tests.
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'optimize_for_size'},
      {'name': 'test262', 'shards': 4},
      {'name': 'test262', 'variant': 'extra', 'shards': 3},
      {'name': 'v8initializers'},
      {'name': 'v8testing', 'shards': 2},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 2},
      {'name': 'v8testing', 'variant': 'minor_mc'},
      {'name': 'v8testing', 'variant': 'no_lfa'},
      {'name': 'v8testing', 'variant': 'slow_path'},
      {'name': 'v8testing', 'variant': 'stress_instruction_scheduling'},
      # Maglev -- move to extra once more architectures are supported.
      {'name': 'mjsunit', 'variant': 'maglev'},
    ],
  },
  'v8_linux64_predictable_rel': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'd8testing'},
      {'name': 'mozilla'},
    ],
  },
  'v8_linux64_no_sandbox_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'v8_linux64_tsan_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks', 'shards': 2},
      {'name': 'mozilla', 'shards': 2},
      {'name': 'test262', 'variant': 'default', 'shards': 5},
      {'name': 'v8testing', 'shards': 6},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 5},
      {'name': 'v8testing', 'variant': 'slow_path', 'shards': 2},
      {'name': 'v8testing', 'variant': 'stress_concurrent_allocation', 'shards': 2},
    ],
  },
  'v8_linux64_tsan_no_cm_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'v8testing',
        'shards': 6,
      },
      {
        'name': 'v8testing',
        'suffix': 'future',
        'test_args': ['--extra-flags=--future'],
        'shards': 6,
      },
      {'name': 'v8testing', 'variant': 'stress_concurrent_inlining', 'shards': 2},
    ],
  },
  'v8_linux64_tsan_isolates_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'test_args': ['--isolates'], 'shards': 7},
    ],
  },
  'v8_linux64_ubsan_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'v8_linux64_verify_csa_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  ##############################################################################
  # Linux64 with arm64 simulators
  'v8_linux_arm64_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access', 'shards': 2},
      {'name': 'mozilla', 'shards': 4},
      {'name': 'test262', 'variant': 'default', 'shards': 4},
      {'name': 'v8testing', 'shards': 14},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 14},
    ],
  },
  'v8_linux_arm64_gc_stress_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'd8testing', 'test_args': ['--gc-stress'], 'shards': 12},
    ],
  },
  'v8_linux_arm64_sim_heap_sandbox_dbg': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 14},
    ],
  },
  'v8_linux_arm64_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access', 'shards': 4},
      {'name': 'mozilla', 'shards': 4},
      {'name': 'test262', 'variant': 'default', 'shards': 4},
      {'name': 'v8testing', 'shards': 14},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 14},
    ],
  },
  'v8_linux_arm64_cfi_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'test262', 'variant': 'default', 'shards': 3},
      {'name': 'v8testing', 'shards': 4},
    ],
  },
  'v8_linux64_arm64_no_pointer_compression_rel': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'variant': 'default', 'shards': 4},
    ],
  },
  ##############################################################################
  # Linux64 with Loongson simulators
  'v8_linux64_loong64_rel': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  ##############################################################################
  # Linux with RISC-V simulators
  'v8_linux_riscv32_rel': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_linux64_riscv64_rel': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  ##############################################################################
  # Odroids with native arm
  'v8_odroid_arm_rel': {
    'swarming_dimensions' : {
      'cores': '8',
      'cpu': 'armv7l-32-ODROID-XU4',
      'os': 'Ubuntu',
    },
    'swarming_task_attrs': {
      # Use same prio as CI due to limited resources.
      'priority': 25,
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'optimize_for_size'},
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  ##############################################################################
  # Win32
  'v8_win_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Windows-7-SP1',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'v8_win_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Windows-7-SP1',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  ##############################################################################
  # Win64
  'v8_win64_asan_rel': {
    'swarming_dimensions' : {
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 5},
    ],
  },
  'v8_win64_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 4},
      {'name': 'v8testing', 'shards': 3},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 2},
    ],
  },
  'v8_win64_msvc_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'v8_win64_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 2},
      {'name': 'v8testing', 'variant': 'extra'},
    ],
  },
  ##############################################################################
  # Mac64
  'v8_mac64_asan_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 8},
    ],
  },
  'v8_mac64_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 4},
      {'name': 'v8testing', 'shards': 6},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 6},
    ],
  },
  'v8_mac64_gc_stress_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'd8testing', 'test_args': ['--gc-stress'], 'shards': 6},
    ],
  },
  'v8_mac64_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 3},
      {'name': 'v8testing', 'shards': 3},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 3},
    ],
  },
  'v8_mac_arm64_rel': {
    'swarming_dimensions' : {
      'cpu': 'arm64',
      'os': 'Mac-11',
      'pool': 'chromium.tests',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'v8_mac_arm64_dbg': {
    'swarming_dimensions' : {
      'cpu': 'arm64',
      'os': 'Mac-11',
      'pool': 'chromium.tests',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'v8_mac_arm64_full_dbg': {
    'swarming_dimensions' : {
      'cpu': 'arm64',
      'os': 'Mac-11',
      'pool': 'chromium.tests',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'v8_mac_arm64_no_pointer_compression_dbg': {
    'swarming_dimensions' : {
      'cpu': 'arm64',
      'os': 'Mac-11',
      'pool': 'chromium.tests',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'v8_mac_arm64_sim_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 8},
      {'name': 'v8testing', 'variant': 'future', 'shards': 2},
    ],
  },
  'v8_mac_arm64_sim_dbg': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 8},
      {'name': 'v8testing', 'variant': 'future', 'shards': 2},
    ],
  },
  'v8_mac_arm64_sim_nodcheck_rel': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 8},
      {'name': 'v8testing', 'variant': 'future', 'shards': 2},
    ],
  },
  ##############################################################################
  ### luci.v8.ci
  ##############################################################################
  # Main.
  'V8 Fuzzer': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'jsfunfuzz'},
    ],
  },
  'V8 Linux': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'gcmole'},
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'optimize_for_size'},
      {'name': 'test262', 'shards': 2},
      {'name': 'test262', 'variant': 'extra', 'shards': 2},
      {'name': 'v8initializers'},
      {'name': 'v8testing'},
      {'name': 'v8testing', 'suffix': 'isolates', 'test_args': ['--isolates']},
      {'name': 'v8testing', 'variant': 'extra'},
      # Nosse3.
      {
        'name': 'mozilla',
        'suffix': 'nosse3',
        'test_args': ['--extra-flags', '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'nosse3',
        'test_args': ['--extra-flags', '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      # Nossse3.
      {
        'name': 'mozilla',
        'suffix': 'nossse3',
        'test_args': ['--extra-flags', '--noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'nossse3',
        'test_args': ['--extra-flags', '--noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      # Nosse4.
      {
        'name': 'mozilla',
        'suffix': 'nosse4',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'nosse4',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx']
      },
      # Noavx.
      {
        'name': 'mozilla',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'noavx',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx'],
        'shards': 2
      },
    ],
  },
  'V8 Linux - arm64 - sim - CFI': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'test262', 'variant': 'default', 'shards': 3},
      {'name': 'v8testing', 'shards': 4},
    ],
  },
  'V8 Linux - arm64 - sim - MSAN': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'test262', 'variant': 'default', 'shards': 3},
      {'name': 'v8testing', 'shards': 4},
    ],
  },
  'V8 Linux - debug': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'code_serializer', 'shards': 1},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'd8testing', 'variant': 'code_serializer', 'shards': 1},
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'code_serializer', 'shards': 1},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'optimize_for_size'},
      {'name': 'test262', 'shards': 6},
      {'name': 'test262', 'variant': 'code_serializer', 'shards': 2},
      {'name': 'test262', 'variant': 'extra', 'shards': 5},
      {'name': 'v8testing', 'shards': 3},
      {
        'name': 'v8testing',
        'suffix': 'isolates',
        'test_args': ['--isolates'],
        'shards': 4
      },
      {'name': 'v8testing', 'variant': 'extra', 'shards': 2},
      # Nosse3.
      {
        'name': 'mozilla',
        'suffix': 'nosse3',
        'test_args': ['--extra-flags', '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'nosse3',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'nosse3',
        'test_args': ['--extra-flags', '--noenable-sse3 --noenable-ssse3 --noenable-sse4-1 --noenable-avx'],
        'shards': 3
      },
      # Nossse3.
      {
        'name': 'mozilla',
        'suffix': 'nossse3',
        'test_args': ['--extra-flags', '--noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'nossse3',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-ssse3 --noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'nossse3',
        'test_args': ['--extra-flags', '--noenable-ssse3 --noenable-sse4-1 --noenable-avx'],
        'shards': 3
      },
      # Nosse4.
      {
        'name': 'mozilla',
        'suffix': 'nosse4',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'nosse4',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'nosse4',
        'test_args': ['--extra-flags', '--noenable-sse4-1 --noenable-avx'],
        'shards': 3
      },
      # Noavx.
      {
        'name': 'mozilla',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'noavx',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx'],
        'shards': 2
      },
    ],
  },
  'V8 Linux - full debug': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'variant': 'default', 'shards': 6},
    ],
  },
  'V8 Linux - gc stress': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'd8testing',
        'test_args': ['--gc-stress'],
        'shards': 5,
      },
      {
        'name': 'mjsunit',
        'variant': 'slow_path',
        'test_args': ['--gc-stress'],
        'shards': 2,
      },
    ],
  },
  'V8 Linux - noi18n - debug': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mozilla', 'variant': 'default'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing', 'variant': 'default'},
    ],
  },
  'V8 Linux64 - predictable': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'd8testing'},
      {'name': 'mozilla'},
    ],
  },
  'V8 Linux - shared': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux - verify csa': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'assert_types'},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'check-bytecode-baseline'},
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'assert_types'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'optimize_for_size'},
      {'name': 'perf_integration'},
      {'name': 'test262', 'shards': 2},
      {'name': 'test262', 'variant': 'assert_types'},
      {'name': 'test262', 'variant': 'extra', 'shards': 2},
      {'name': 'v8initializers'},
      {'name': 'v8testing'},
      {'name': 'v8testing', 'variant': 'assert_types'},
      {'name': 'v8testing', 'variant': 'extra'},
      {'name': 'v8testing', 'variant': 'minor_mc'},
      {'name': 'v8testing', 'variant': 'no_lfa'},
      {'name': 'v8testing', 'variant': 'stress_instruction_scheduling'},
      # Maglev -- move to extra once more architectures are supported.
      {'name': 'mjsunit', 'variant': 'maglev'},
      # Noavx.
      {
        'name': 'mozilla',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'noavx',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
    ],
  },
  'V8 Linux64 - cfi': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'mozilla'},
      {'name': 'optimize_for_size'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'V8 Linux64 - custom snapshot - debug': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit', 'test_args': ['--no-harness']},
    ],
  },
  'V8 Linux64 - debug': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks'},
      {'name': 'benchmarks', 'variant': 'extra'},
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'mozilla', 'variant': 'extra'},
      {'name': 'optimize_for_size'},
      {'name': 'test262', 'shards': 7},
      {'name': 'test262', 'variant': 'extra', 'shards': 5},
      {'name': 'v8testing', 'shards': 2},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 2},
      {'name': 'v8testing', 'variant': 'minor_mc'},
      {'name': 'v8testing', 'variant': 'no_lfa'},
      {'name': 'v8testing', 'variant': 'slow_path'},
      {'name': 'v8testing', 'variant': 'stress_instruction_scheduling'},
      {'name': 'v8testing', 'variant': 'stress_concurrent_allocation'},
      {'name': 'v8testing', 'variant': 'stress_concurrent_inlining'},
      # Maglev -- move to extra once more architectures are supported.
      {'name': 'mjsunit', 'variant': 'maglev'},
      # Noavx.
      {
        'name': 'mozilla',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'test262',
        'suffix': 'noavx',
        'variant': 'default',
        'test_args': ['--extra-flags', '--noenable-avx']
      },
      {
        'name': 'v8testing',
        'suffix': 'noavx',
        'test_args': ['--extra-flags', '--noenable-avx'],
        'shards': 2
      },
    ],
  },
  'V8 Linux64 - minor mc - debug': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'variant': 'minor_mc'},
      {'name': 'benchmarks', 'variant': 'minor_mc'},
      {'name': 'mozilla', 'variant': 'minor_mc'},
      {'name': 'test262', 'variant': 'minor_mc', 'shards': 2},
      {'name': 'mjsunit', 'variant': 'minor_mc'},
    ],
  },
  'V8 Linux64 - disable runtime call stats': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64 - debug - fyi': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      # Infra staging.
      {'name': 'v8testing', 'variant': 'infra_staging', 'shards': 2},
      # Stress sampling.
      {'name': 'mjsunit', 'variant': 'stress_sampling'},
      {'name': 'webkit', 'variant': 'stress_sampling'},
      # Stress snapshot.
      {'name': 'mjsunit', 'variant': 'stress_snapshot'},
      # Maglev.
      {'name': 'mjsunit', 'variant': 'maglev'},
      # Stress maglev.
      {'name': 'mjsunit', 'variant': 'stress_maglev'},
      # Experimental regexp engine.
      {'name': 'mjsunit', 'variant': 'experimental_regexp'},
      # Wasm write protect code space.
      {'name': 'mjsunit', 'variant': 'wasm_write_protect_code'},
    ],
  },
  'V8 Linux64 - cppgc-non-default - debug': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux64 - debug - perfetto': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'V8 Linux64 - debug - single generation': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux64 - external code space - debug': {
    'swarming_dimensions' : {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux64 - fyi': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      # Infra staging.
      {'name': 'v8testing', 'variant': 'infra_staging'},
      # Stress sampling.
      {'name': 'mjsunit', 'variant': 'stress_sampling'},
      {'name': 'webkit', 'variant': 'stress_sampling'},
      # Stress snapshot.
      {'name': 'mjsunit', 'variant': 'stress_snapshot'},
      # Maglev.
      {'name': 'mjsunit', 'variant': 'maglev'},
      # Stress maglev.
      {'name': 'mjsunit', 'variant': 'stress_maglev'},
      # Experimental regexp engine.
      {'name': 'mjsunit', 'variant': 'experimental_regexp'},
      # Wasm write protect code space.
      {'name': 'mjsunit', 'variant': 'wasm_write_protect_code'},
    ],
  },
  'V8 Linux64 gcc': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-20.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64 - gc stress': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'd8testing',
        'test_args': ['--gc-stress'],
        'shards': 5,
      },
      {
        'name': 'mjsunit',
        'variant': 'slow_path',
        'test_args': ['--gc-stress'],
        'shards': 2,
      },
      {
        'name': 'mjsunit',
        'variant': 'maglev',
        'test_args': ['--gc-stress'],
        'shards': 2
      },
    ],
  },
  'V8 Linux64 - gcov coverage': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64 - heap sandbox - debug': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'V8 Linux64 - internal snapshot': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64 - no pointer compression': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'V8 Linux64 - no sandbox': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64 - no sandbox - debug': {
    'swarming_dimensions': {
      'cpu': 'x86-64-avx2',
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
    ],
  },
  'V8 Linux64 - shared': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64 - verify csa': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Linux64 ASAN': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'test262', 'shards': 7},
      {'name': 'v8testing', 'shards': 2},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 2},
      {'name': 'v8testing', 'variant': 'slow_path', 'shards': 1},
    ],
  },
  'V8 Linux64 GC Stress - custom snapshot': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'mjsunit',
        'test_args': ['--gc-stress', '--no-harness'],
        'shards': 3,
      },
    ],
  },
  'V8 Linux64 TSAN': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'benchmarks', 'shards': 2},
      {'name': 'mozilla', 'shards': 2},
      {'name': 'test262', 'variant': 'default', 'shards': 5},
      {'name': 'v8testing', 'shards': 6},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 5},
      {'name': 'v8testing', 'variant': 'slow_path', 'shards': 2},
      {'name': 'v8testing', 'variant': 'stress_concurrent_allocation', 'shards': 2},
    ],
  },
  'V8 Linux64 TSAN - stress-incremental-marking': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {
        'name': 'benchmarks',
        'test_args': ['--extra-flags=--stress-incremental-marking'],
        'shards': 2,
      },
      {
        'name': 'mozilla',
        'test_args': ['--extra-flags=--stress-incremental-marking'],
        'shards': 2,
      },
      {
        'name': 'test262',
        'variant': 'default',
        'test_args': ['--extra-flags=--stress-incremental-marking'],
        'shards': 5,
      },
      {
        'name': 'v8testing',
        'test_args': ['--extra-flags=--stress-incremental-marking'],
        'shards': 6,
      },
    ],
  },
  'V8 Linux64 TSAN - isolates': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'test_args': ['--isolates'], 'shards': 7},
    ],
  },
  'V8 Linux64 TSAN - no-concurrent-marking': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'v8testing',
        'shards': 6,
      },
      {
        'name': 'v8testing',
        'suffix': 'future',
        'test_args': ['--extra-flags=--future'],
        'shards': 6,
      },
      {'name': 'v8testing', 'variant': 'stress_concurrent_inlining', 'shards': 2},
    ],
  },
  'V8 Linux64 UBSan': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing'},
      {'name': 'v8testing', 'variant': 'extra'},
    ],
  },
  'V8 Mac64': {
    'swarming_dimensions': {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 3},
      {'name': 'v8testing', 'shards': 3},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 3},
    ],
  },
  'V8 Mac64 - debug': {
    'swarming_dimensions': {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 4},
      {'name': 'v8testing', 'shards': 6},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 6},
    ],
  },
  'V8 Mac64 ASAN': {
    'swarming_dimensions': {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 10},
    ],
  },
  'V8 Mac64 GC Stress': {
    'swarming_dimensions': {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'tests': [
      {'name': 'd8testing', 'test_args': ['--gc-stress'], 'shards': 6},
    ],
  },
  'V8 Mac - arm64 - release': {
    'swarming_dimensions' : {
      'os': 'Mac-11',
      'cpu': 'arm64',
      'pool': 'chromium.tests',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing'},
      {'name': 'v8testing', 'variant': 'extra'},
    ],
  },
  'V8 Mac - arm64 - debug': {
    'swarming_dimensions' : {
      'os': 'Mac-11',
      'cpu': 'arm64',
      'pool': 'chromium.tests',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 2},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 2},
    ],
  },
  'V8 Mac - arm64 - no pointer compression debug': {
    'swarming_dimensions' : {
      'cpu': 'arm64',
      'os': 'Mac-11',
      'pool': 'chromium.tests',
    },
    'tests': [
      {'name': 'v8testing'},
    ],
  },
  'V8 Mac - arm64 - sim - debug': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 7200,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 10},
      {'name': 'v8testing', 'variant': 'future', 'shards': 4},
    ],
  },
  'V8 Mac - arm64 - sim - release': {
    'swarming_dimensions' : {
      'cpu': 'x86-64',
      'os': 'Mac-10.15',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 8},
      {'name': 'v8testing', 'variant': 'future', 'shards': 2},
    ],
  },
  'V8 Win32': {
    'swarming_dimensions': {
      'cpu': 'x86-64',
      'os': 'Windows-7-SP1',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing'},
    ],
  },
  'V8 Win32 - debug': {
    'swarming_dimensions': {
      'cpu': 'x86-64',
      'os': 'Windows-7-SP1',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing', 'shards': 5},
    ],
  },
  'V8 Win64': {
    'swarming_dimensions': {
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing'},
      {'name': 'v8testing', 'variant': 'extra'},
    ],
  },
  'V8 Win64 - debug': {
    'swarming_dimensions': {
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 4},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 3},
    ],
  },
  'V8 Win64 - msvc': {
    'swarming_dimensions': {
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing'},
    ],
  },
  'V8 Win64 ASAN': {
    'swarming_dimensions': {
      'os': 'Windows-10-19042',
    },
    'tests': [
      {'name': 'v8testing', 'shards': 5},
    ],
  },
  ##############################################################################
  # Ports.
  'V8 Android Arm64 - N5X': {
    'swarming_dimensions': {
      'device_os': 'MMB29Q',
      'device_type': 'bullhead',
      'os': 'Android',
    },
    'tests': [
      {'name': 'mozilla', 'variant': 'default'},
      {'name': 'test262', 'variant': 'default', 'shards': 10},
      {'name': 'v8testing', 'variant': 'default', 'shards': 4},
    ],
  },
  'V8 Arm': {
    'swarming_dimensions': {
      'cores': '8',
      'cpu': 'armv7l-32-ODROID-XU4',
      'os': 'Ubuntu',
    },
    'swarming_task_attrs': {
      'expiration': 21600,
      'hard_timeout': 5400,
    },
    'tests': [
      # Odroid.
      {
        'name': 'benchmarks',
        'suffix': 'ODROID',
        # Less parallelism to prevent OOMs in benchmarks.
        'test_args': ['-j2'],
      },
      {
        'name': 'optimize_for_size',
        'suffix': 'ODROID',
      },
      {
        'name': 'v8testing',
        'suffix': 'ODROID',
        'shards': 2,
      },
    ],
  },
  'V8 Arm - debug': {
    'swarming_dimensions': {
      'cores': '8',
      'cpu': 'armv7l-32-ODROID-XU4',
      'os': 'Ubuntu',
    },
    'swarming_task_attrs': {
      'expiration': 21600,
      'hard_timeout': 3600,
    },
    'tests': [
      # Odroid.
      {
        'name': 'optimize_for_size',
        'suffix': 'ODROID',
        'variant': 'default',
        'test_args': ['--extra-flags=--verify-heap-skip-remembered-set'],
        'shards': 2,
      },
      {
        'name': 'v8testing',
        'suffix': 'ODROID',
        'variant': 'default',
        'test_args': ['--extra-flags=--verify-heap-skip-remembered-set'],
        'shards': 3,
      },
    ],
  },
  'V8 Arm GC Stress': {
    'swarming_dimensions': {
      'cores': '8',
      'cpu': 'armv7l-32-ODROID-XU4',
      'os': 'Ubuntu',
    },
    'swarming_task_attrs': {
      'expiration': 21600,
      'hard_timeout': 7200,
    },
    'tests': [
      {
        'name': 'd8testing',
        'suffix': 'ODROID',
        'variant': 'default',
        'test_args': ['--gc-stress', '--extra-flags=--verify-heap-skip-remembered-set'],
        'shards': 3,
      },
    ],
  },
  'V8 Linux - arm - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 6},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 3},
      # Armv8-a.
      {
        'name': 'mozilla',
        'suffix': 'armv8-a',
        'test_args': ['--extra-flags', '--enable-armv8']
      },
      {
        'name': 'test262',
        'suffix': 'armv8-a',
        'variant': 'default',
        'test_args': ['--extra-flags', '--enable-armv8']
      },
      {
        'name': 'v8testing',
        'suffix': 'armv8-a',
        'test_args': ['--extra-flags', '--enable-armv8'],
        'shards': 4
      },
      # Novfp3.
      {
        'name': 'mozilla',
        'suffix': 'novfp3',
        'test_args': ['--novfp3'],
      },
      {
        'name': 'test262',
        'suffix': 'novfp3',
        'variant': 'default',
        'test_args': ['--novfp3'],
      },
      {
        'name': 'v8testing',
        'suffix': 'novfp3',
        'test_args': ['--novfp3'],
        'shards': 6
      },
    ],
  },
  'V8 Linux - arm - sim - debug': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access', 'shards': 3},
      {'name': 'mozilla', 'shards': 3},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 10},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 10},
      # Armv8-a.
      {
        'name': 'mozilla',
        'suffix': 'armv8-a',
        'test_args': ['--extra-flags', '--enable-armv8'],
        'shards': 5,
      },
      {
        'name': 'test262',
        'suffix': 'armv8-a',
        'variant': 'default',
        'test_args': ['--extra-flags', '--enable-armv8'],
        'shards': 5,
      },
      {
        'name': 'v8testing',
        'suffix': 'armv8-a',
        'test_args': ['--extra-flags', '--enable-armv8'],
        'shards': 10
      },
      # Novfp3.
      {
        'name': 'mozilla',
        'suffix': 'novfp3',
        'variant': 'default',
        'test_args': ['--novfp3']
      },
      {
        'name': 'test262',
        'suffix': 'novfp3',
        'variant': 'default',
        'test_args': ['--novfp3'],
        'shards': 2
      },
      {
        'name': 'v8testing',
        'suffix': 'novfp3',
        'variant': 'default',
        'test_args': ['--novfp3'],
        'shards': 3
      },
    ],
  },
  'V8 Linux - arm - sim - lite': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'variant': 'default', 'shards': 2},
    ],
  },
  'V8 Linux - arm - sim - lite - debug': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'v8testing', 'variant': 'default', 'shards': 4},
    ],
  },
  'V8 Linux - arm64 - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla'},
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing', 'shards': 3},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 2},
    ],
  },
  'V8 Linux - arm64 - sim - debug': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    # TODO(machenbach): Remove longer timeout when this builder scales better.
    'swarming_task_attrs': {
      'hard_timeout': 3600,
    },
    'tests': [
      {'name': 'mjsunit_sp_frame_access'},
      {'name': 'mozilla', 'shards': 2},
      {'name': 'test262', 'variant': 'default', 'shards': 2},
      {'name': 'v8testing', 'shards': 12},
      {'name': 'v8testing', 'variant': 'extra', 'shards': 14},
    ],
  },
  'V8 Linux - arm64 - sim - gc stress': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 7200,
      'priority': 35,
    },
    'tests': [
      {
        'name': 'd8testing',
        'test_args': ['--gc-stress', '--extra-flags=--verify-heap-skip-remembered-set'],
        'shards': 7
      },
    ],
  },
  'V8 Linux64 - arm64 - sim - heap sandbox - debug': {
    'swarming_dimensions' : {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 7200,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 14},
    ],
  },
  'V8 Linux - loong64 - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux - mips64el - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'test262', 'variant': 'default'},
      {'name': 'v8testing', 'shards': 4},
    ],
  },
  'V8 Linux - ppc64 - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux - riscv32 - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux - riscv64 - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux - s390x - sim': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 3},
    ],
  },
  'V8 Linux64 - arm64 - sim - no pointer compression': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 14400,
      'hard_timeout': 3600,
      'priority': 35,
    },
    'tests': [
      {'name': 'v8testing', 'shards': 7},
    ],
  },
  ##############################################################################
  # Clusterfuzz.
  'V8 NumFuzz': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 13800,
      'hard_timeout': 4200,
      'priority': 35,
    },
    'tests': [
      {
        'name': 'numfuzz',
        'suffix': 'deopt',
        'test_args': ['--total-timeout-sec=2100', '--stress-deopt=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'interrupt',
        'test_args': ['--total-timeout-sec=2100', '--stress-interrupt-budget=1']
      },
    ],
  },
  'V8 NumFuzz - TSAN': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 13800,
      'hard_timeout': 4200,
      'priority': 35,
    },
    'tests': [
      {'name': 'd8testing_random_gc', 'shards': 2},
      {
        'name': 'numfuzz',
        'suffix': 'marking',
        'test_args': ['--total-timeout-sec=2100', '--stress-marking=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'delay',
        'test_args': ['--total-timeout-sec=2100', '--stress-delay-tasks=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'interrupt',
        'test_args': ['--total-timeout-sec=2100', '--stress-interrupt-budget=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'threads',
        'test_args': ['--total-timeout-sec=2100', '--stress-thread-pool-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'stack',
        'test_args': ['--total-timeout-sec=2100', '--stress-stack-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'combined',
        'test_args': [
          '--total-timeout-sec=2100',
          '--stress-delay-tasks=4',
          '--stress-deopt=2',
          '--stress-compaction=2',
          '--stress-gc=4',
          '--stress-marking=4',
          '--stress-scavenge=4',
          '--stress-thread-pool-size=2',
          '--stress-stack-size=1',
          '--stress-interrupt-budget=1',
        ],
        'shards': 4
      },
      {
        'name': 'numfuzz',
        'suffix': 'scavenge',
        'test_args': ['--total-timeout-sec=2100', '--stress-scavenge=1']
      },
    ],
  },
  'V8 NumFuzz - debug': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'swarming_task_attrs': {
      'expiration': 13800,
      'hard_timeout': 4200,
      'priority': 35,
    },
    'tests': [
      {'name': 'd8testing_random_gc'},
      {
        'name': 'numfuzz',
        'suffix': 'marking',
        'test_args': ['--total-timeout-sec=2100', '--stress-marking=1'],
        'shards': 2
      },
      {
        'name': 'numfuzz',
        'suffix': 'delay',
        'test_args': ['--total-timeout-sec=2100', '--stress-delay-tasks=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'interrupt',
        'test_args': ['--total-timeout-sec=2100', '--stress-interrupt-budget=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'threads',
        'test_args': ['--total-timeout-sec=2100', '--stress-thread-pool-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'stack',
        'test_args': ['--total-timeout-sec=2100', '--stress-stack-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'combined',
        'test_args': [
          '--total-timeout-sec=2100',
          '--stress-delay-tasks=4',
          '--stress-deopt=2',
          '--stress-compaction=2',
          '--stress-gc=4',
          '--stress-marking=4',
          '--stress-scavenge=4',
          '--stress-thread-pool-size=2',
          '--stress-stack-size=1',
          '--stress-interrupt-budget=1',
        ],
        'shards': 3
      },
      {
        'name': 'numfuzz',
        'suffix': 'scavenge',
        'test_args': ['--total-timeout-sec=2100', '--stress-scavenge=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'deopt',
        'test_args': ['--total-timeout-sec=2100', '--stress-deopt=1'],
        'shards': 2
      },
    ],
  },
  'v8_numfuzz_rel': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'numfuzz',
        'suffix': 'deopt',
        'test_args': ['--total-timeout-sec=900', '--stress-deopt=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'interrupt',
        'test_args': ['--total-timeout-sec=900', '--stress-interrupt-budget=1']
      },
    ],
  },
  'v8_numfuzz_tsan_rel': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'numfuzz',
        'suffix': 'marking',
        'test_args': ['--total-timeout-sec=900', '--stress-marking=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'delay',
        'test_args': ['--total-timeout-sec=900', '--stress-delay-tasks=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'interrupt',
        'test_args': ['--total-timeout-sec=900', '--stress-interrupt-budget=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'threads',
        'test_args': ['--total-timeout-sec=900', '--stress-thread-pool-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'stack',
        'test_args': ['--total-timeout-sec=900', '--stress-stack-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'combined',
        'test_args': [
          '--total-timeout-sec=900',
          '--stress-delay-tasks=4',
          '--stress-deopt=2',
          '--stress-compaction=2',
          '--stress-gc=4',
          '--stress-marking=4',
          '--stress-scavenge=4',
          '--stress-thread-pool-size=2',
          '--stress-stack-size=1',
          '--stress-interrupt-budget=1',
        ],
      },
      {
        'name': 'numfuzz',
        'suffix': 'scavenge',
        'test_args': ['--total-timeout-sec=900', '--stress-scavenge=1']
      },
    ],
  },
  'v8_numfuzz_dbg': {
    'swarming_dimensions': {
      'os': 'Ubuntu-18.04',
    },
    'tests': [
      {
        'name': 'numfuzz',
        'suffix': 'marking',
        'test_args': ['--total-timeout-sec=900', '--stress-marking=1'],
      },
      {
        'name': 'numfuzz',
        'suffix': 'delay',
        'test_args': ['--total-timeout-sec=900', '--stress-delay-tasks=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'interrupt',
        'test_args': ['--total-timeout-sec=900', '--stress-interrupt-budget=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'threads',
        'test_args': ['--total-timeout-sec=900', '--stress-thread-pool-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'stack',
        'test_args': ['--total-timeout-sec=900', '--stress-stack-size=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'combined',
        'test_args': [
          '--total-timeout-sec=900',
          '--stress-delay-tasks=4',
          '--stress-deopt=2',
          '--stress-compaction=2',
          '--stress-gc=4',
          '--stress-marking=4',
          '--stress-scavenge=4',
          '--stress-thread-pool-size=2',
          '--stress-stack-size=1',
          '--stress-interrupt-budget=1',
        ],
      },
      {
        'name': 'numfuzz',
        'suffix': 'scavenge',
        'test_args': ['--total-timeout-sec=900', '--stress-scavenge=1']
      },
      {
        'name': 'numfuzz',
        'suffix': 'deopt',
        'test_args': ['--total-timeout-sec=900', '--stress-deopt=1'],
      },
    ],
  },
}
