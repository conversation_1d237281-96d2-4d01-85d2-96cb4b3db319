﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  WCDataObject.cpp
  Electron.cpp
  ApiApp.cpp
  ApiBrowserView.cpp
  ApiBrowserWindow.cpp
  ApiDialag.cpp
  ApiDownloadItem.cpp
  ApiElectron.cpp
  ApiProtocol.cpp
  ApiSession.cpp
  ApiWebContents.cpp
  ApiWebRequest.cpp
  NodeThread.cpp
  ThreadCallWrap.cpp
  ApiNativeImage.cpp
  ApiV8Util.cpp
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧 (编译源文件 ..\..\content\ui\WCDataObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\compiler_specific.h(30): warning C4005: “MSVC_PUSH_DISABLE_WARNING”: 宏重定义 (编译源文件 ..\..\electron\Electron.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\electron\cef\include\base\cef_macros.h(54): note: 参见“MSVC_PUSH_DISABLE_WARNING”的前一个定义 (编译源文件 ..\..\electron\Electron.cpp)
  RemoteCallbackFreer.cpp
  Archive.cpp
  WebviewPlugin.cpp
  WebviewPluginImpl.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\electron\browser\api\apidownloaditem.cpp(291): warning C4506: 内联函数“node::Environment *node::Environment::GetCurrent(v8::Local<v8::Context>)”没有定义
  ApiContextBridge.cpp
c:\program files (x86)\microsoft sdks\windows\v7.1a\include\shlobj.h(1151): warning C4091: “typedef ”: 没有声明变量时忽略“tagGPFIDL_FLAGS”的左侧 (编译源文件 ..\..\electron\browser\api\ApiDialag.cpp)
  ApiRendererIpc.cpp
  ApiWebFrame.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\electron\common\asar\Archive.cpp)
    正在创建库 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\electron.lib 和对象 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\electron.exp
  electron.vcxproj -> E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\electron.exe
