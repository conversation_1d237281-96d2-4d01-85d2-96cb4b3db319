// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef V8_BUILTINS_TORQUE_CSA_HEADER_INCLUDES_H_
#define V8_BUILTINS_TORQUE_CSA_HEADER_INCLUDES_H_

// This file is included by Torque-generated CSA headers and contains
// includes necessary for these headers.

#include "src/builtins/builtins-promise.h"
#include "src/builtins/builtins-proxy-gen.h"
#include "src/codegen/code-stub-assembler.h"
#include "src/compiler/code-assembler.h"
#include "src/utils/utils.h"
#include "torque-generated/csa-types.h"

#endif  // V8_BUILTINS_TORQUE_CSA_HEADER_INCLUDES_H_
