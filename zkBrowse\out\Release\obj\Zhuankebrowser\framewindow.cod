; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\framewindow.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
_BSS	ENDS
?piecewise_construct@std@@3Upiecewise_construct_t@1@B	ORG $+1 ; std::piecewise_construct
	ORG $+2
$SG4294477649 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294477642 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
$SG4294477648 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294477650 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294477640 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477641 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477643 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294477644 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477645 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294477646 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477647 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294477632 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294477633 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294477634 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294477635 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294477636 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294477637 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294477638 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477639 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294477624 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477625 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477626 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294477627 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477628 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294477629 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294477630 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294477631 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294477616 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477617 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294477618 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477619 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477620 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477621 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477622 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477623 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477608 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477609 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477610 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477611 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477612 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477613 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477614 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477615 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477600 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294477601 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294477602 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477603 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477604 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477605 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477606 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477607 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477592 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477593 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477594 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477595 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477596 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294477597 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477598 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477599 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294477584 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477585 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477586 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294477587 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294477588 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294477589 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477590 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477591 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294477576 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294477577 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294477578 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477579 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477580 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294477581 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477582 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477583 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294477568 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477569 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477570 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477571 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477572 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477573 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477574 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477575 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294477560 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294477561 DB 00H, 00H
	ORG $+2
$SG4294477562 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294477563 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294477564 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294477565 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477566 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477567 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477552 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294477553 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294477554 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294477555 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294477556 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294477557 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294477558 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294477559 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294477544 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294477545 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294477546 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294477547 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294477548 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294477549 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294477550 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294477551 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294477536 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294477537 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294477538 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294477539 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294477540 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294477541 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294477542 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294477543 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294477528 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294477529 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294477530 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294477531 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294477532 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294477533 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294477534 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294477535 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294477520 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477521 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294477522 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294477523 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294477524 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294477525 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294477526 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294477527 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294477512 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294477513 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294477514 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294477515 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294477516 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294477517 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294477518 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294477519 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294477504 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477505 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294477506 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294477507 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294477508 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294477509 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294477510 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294477511 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477496 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477497 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477498 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477499 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477500 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477501 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477502 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477503 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294477488 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477489 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477490 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477491 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294477492 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294477493 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294477494 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477495 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294477480 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294477481 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294477482 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294477483 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294477484 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294477485 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294477486 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294477487 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477472 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477473 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294477474 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477475 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294477476 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294477477 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294477478 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294477479 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294477464 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294477465 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294477466 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294477467 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294477468 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477469 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477470 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294477471 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294477456 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294477457 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294477458 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294477459 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477460 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294477461 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477462 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294477463 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294477448 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294477449 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294477450 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294477451 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477452 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294477453 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294477454 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294477455 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294477440 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294477441 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294477442 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294477443 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477444 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477445 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294477446 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294477447 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294477432 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477433 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477434 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294477435 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294477436 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294477437 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477438 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477439 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294477424 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477425 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294477426 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294477427 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294477428 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477429 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294477430 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294477431 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294477416 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294477417 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294477418 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294477419 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294477420 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294477421 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294477422 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294477423 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294477408 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294477409 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294477410 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294477411 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294477412 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294477413 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294477414 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294477415 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294477400 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477401 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477402 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294477403 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294477404 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294477405 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294477406 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294477407 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294477392 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294477393 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294477394 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294477395 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294477396 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477397 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294477398 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294477399 DB 00H, 00H
	ORG $+2
$SG4294477384 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294477385 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294477386 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294477387 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294477388 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294477389 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294477390 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294477391 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294477376 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477377 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477378 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477379 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294477380 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294477381 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294477382 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294477383 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294477368 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477369 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294477370 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477371 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477372 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477373 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294477374 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294477375 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294477360 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294477361 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294477362 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294477363 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477364 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294477365 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477366 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294477367 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294477352 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294477353 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477354 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294477355 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477356 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477357 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294477358 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294477359 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294477344 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294477345 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294477346 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294477347 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294477348 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294477349 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294477350 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294477351 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294477336 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294477337 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294477338 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294477339 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477340 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294477341 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294477342 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294477343 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294477328 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294477329 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294477330 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294477331 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294477332 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294477333 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294477334 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294477335 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294477320 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294477321 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294477322 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294477323 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294477324 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294477325 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294477326 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294477327 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294477312 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294477313 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294477314 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294477315 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477316 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294477317 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477318 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294477319 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477304 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294477305 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294477306 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294477307 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294477308 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294477309 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477310 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294477311 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477296 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294477297 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294477298 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477299 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477300 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477301 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294477302 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477303 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294477288 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294477289 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294477290 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294477291 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477292 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294477293 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294477294 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477295 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477280 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477281 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294477282 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294477283 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477284 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294477285 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477286 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294477287 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294477272 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477273 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477274 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477275 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294477276 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477277 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294477278 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477279 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294477264 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294477265 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294477266 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477267 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294477268 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294477269 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294477270 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477271 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294477256 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477257 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294477258 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477259 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477260 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294477261 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477262 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294477263 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294477248 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294477249 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477250 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294477251 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294477252 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294477253 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294477254 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294477255 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477240 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294477241 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477242 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477243 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477244 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294477245 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477246 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294477247 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294477232 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294477233 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294477234 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477235 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477236 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477237 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294477238 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294477239 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477224 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477225 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294477226 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294477227 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294477228 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477229 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477230 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294477231 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294477216 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477217 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477218 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477219 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477220 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477221 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294477222 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477223 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294477208 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477209 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477210 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477211 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477212 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477213 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294477214 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294477215 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477200 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294477201 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477202 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294477203 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294477204 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294477205 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294477206 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294477207 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294477192 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294477193 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477194 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294477195 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294477196 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294477197 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477198 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477199 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294477184 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294477185 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294477186 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294477187 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294477188 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294477189 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294477190 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294477191 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294477176 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294477177 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294477178 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477179 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477180 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294477181 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294477182 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294477183 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294477172 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294477173 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294477174 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294477175 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294477136 DB 'B', 00H, 00H, 00H
$SG4294477137 DB 'D', 00H, 00H, 00H
$SG4294477138 DB 'M', 00H, 00H, 00H
$SG4294477139 DB 'S', 00H, 00H, 00H
$SG4294477128 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477129 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294477130 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294477131 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294477132 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477133 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477134 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294477135 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294477120 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294477121 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477122 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294477123 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294477124 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294477125 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477126 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294477127 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294477088 DB 00H, 00H
	ORG $+2
$SG4294477089 DB ':', 00H, 00H, 00H
$SG4294477087 DB 00H, 00H
	ORG $+2
$SG4294476996 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294476282 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294476283 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
PUBLIC	??$forward@AAH@std@@YAAAHAAH@Z			; std::forward<int &>
PUBLIC	??$forward@J@std@@YA$$QAJAAJ@Z			; std::forward<long>
PUBLIC	?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABU?$less@I@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first
PUBLIC	?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABU?$less@I@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp
PUBLIC	?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second
PUBLIC	?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second
PUBLIC	?_Compare@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBE_NABI0@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Compare
PUBLIC	?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data
PUBLIC	??$_Kfn@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@SAABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@@Z ; std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0>::_Kfn<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >
PUBLIC	??$_Lbound@I@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@ABI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Lbound<unsigned int>
PUBLIC	??$addressof@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@YAPAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@0@AAV10@@Z ; std::addressof<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
PUBLIC	??R?$_Func_class@JPAUHWND__@@IIJAAH@std@@QBEJPAUHWND__@@IIJAAH@Z ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::operator()
PUBLIC	?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::WindowProc
PUBLIC	??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>
PUBLIC	?_Kfn@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Kfn
PUBLIC	??0?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
PUBLIC	?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second
PUBLIC	?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAU?$less@I@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first
PUBLIC	?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second
PUBLIC	?_Getimpl@?$_Func_class@JPAUHWND__@@IIJAAH@std@@ABEPAV?$_Func_base@JPAUHWND__@@IIJAAH@2@XZ ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Getimpl
PUBLIC	?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@QAEJIIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::DefWindowProcW
PUBLIC	?lower_bound@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::lower_bound
PUBLIC	?_Key@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Key
PUBLIC	?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAU?$less@I@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp
PUBLIC	?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data
PUBLIC	??0?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
PUBLIC	??D?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*
PUBLIC	??8?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator==
PUBLIC	?_Empty@?$_Func_class@JPAUHWND__@@IIJAAH@std@@IBE_NXZ ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Empty
PUBLIC	??B?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@QBE_NXZ ; std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>::operator bool
PUBLIC	?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::end
PUBLIC	?find@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::find
PUBLIC	??D?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEAAU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*
PUBLIC	??9?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator!=
PUBLIC	?WindowProc@CFrameWindow@@SGJPAUHWND__@@IIJ@Z	; CFrameWindow::WindowProc
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\framewindow.cpp
_TEXT	SEGMENT
$T1 = -36						; size = 4
_lret$2 = -32						; size = 4
_bHandled$3 = -24					; size = 4
_iter$4 = -12						; size = 4
_pThis$ = -4						; size = 4
_hWnd$ = 8						; size = 4
_uMsg$ = 12						; size = 4
_wParam$ = 16						; size = 4
_lParam$ = 20						; size = 4
?WindowProc@CFrameWindow@@SGJPAUHWND__@@IIJ@Z PROC	; CFrameWindow::WindowProc

; 5    : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 24	 sub	 esp, 36			; 00000024H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0000e	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00011	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00014	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00017	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0001a	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0001d	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00020	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00023	89 45 fc	 mov	 DWORD PTR [ebp-4], eax

; 6    : 	CFrameWindow* pThis = (CFrameWindow*)hWnd;

  00026	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  00029	89 45 fc	 mov	 DWORD PTR _pThis$[ebp], eax

; 7    : 	if (pThis)

  0002c	83 7d fc 00	 cmp	 DWORD PTR _pThis$[ebp], 0
  00030	0f 84 8b 00 00
	00		 je	 $LN2@WindowProc

; 8    : 	{
; 9    : 		auto iter = pThis->m_fnMsgHandler_map.find(uMsg);

  00036	8d 4d 0c	 lea	 ecx, DWORD PTR _uMsg$[ebp]
  00039	51		 push	 ecx
  0003a	8d 55 f4	 lea	 edx, DWORD PTR _iter$4[ebp]
  0003d	52		 push	 edx
  0003e	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00041	83 c1 2c	 add	 ecx, 44			; 0000002cH
  00044	e8 00 00 00 00	 call	 ?find@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::find

; 10   : 		if (iter != pThis->m_fnMsgHandler_map.end())

  00049	8d 45 dc	 lea	 eax, DWORD PTR $T1[ebp]
  0004c	50		 push	 eax
  0004d	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00050	83 c1 2c	 add	 ecx, 44			; 0000002cH
  00053	e8 00 00 00 00	 call	 ?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::end
  00058	50		 push	 eax
  00059	8d 4d f4	 lea	 ecx, DWORD PTR _iter$4[ebp]
  0005c	e8 00 00 00 00	 call	 ??9?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator!=
  00061	0f b6 c8	 movzx	 ecx, al
  00064	85 c9		 test	 ecx, ecx
  00066	74 57		 je	 SHORT $LN4@WindowProc

; 11   : 		{
; 12   : 			if (!!(*iter).second)

  00068	8d 4d f4	 lea	 ecx, DWORD PTR _iter$4[ebp]
  0006b	e8 00 00 00 00	 call	 ??D?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEAAU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*
  00070	83 c0 08	 add	 eax, 8
  00073	8b c8		 mov	 ecx, eax
  00075	e8 00 00 00 00	 call	 ??B?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@QBE_NXZ ; std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>::operator bool
  0007a	0f b6 d0	 movzx	 edx, al
  0007d	85 d2		 test	 edx, edx
  0007f	74 3e		 je	 SHORT $LN4@WindowProc

; 13   : 			{
; 14   : 				BOOL bHandled = TRUE;

  00081	c7 45 e8 01 00
	00 00		 mov	 DWORD PTR _bHandled$3[ebp], 1

; 15   : 				LRESULT lret = (*iter).second(pThis->m_hWnd, uMsg, wParam, lParam, bHandled);

  00088	8d 45 e8	 lea	 eax, DWORD PTR _bHandled$3[ebp]
  0008b	50		 push	 eax
  0008c	8b 4d 14	 mov	 ecx, DWORD PTR _lParam$[ebp]
  0008f	51		 push	 ecx
  00090	8b 55 10	 mov	 edx, DWORD PTR _wParam$[ebp]
  00093	52		 push	 edx
  00094	8b 45 0c	 mov	 eax, DWORD PTR _uMsg$[ebp]
  00097	50		 push	 eax
  00098	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0009b	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0009e	52		 push	 edx
  0009f	8d 4d f4	 lea	 ecx, DWORD PTR _iter$4[ebp]
  000a2	e8 00 00 00 00	 call	 ??D?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEAAU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*
  000a7	83 c0 08	 add	 eax, 8
  000aa	8b c8		 mov	 ecx, eax
  000ac	e8 00 00 00 00	 call	 ??R?$_Func_class@JPAUHWND__@@IIJAAH@std@@QBEJPAUHWND__@@IIJAAH@Z ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::operator()
  000b1	89 45 e0	 mov	 DWORD PTR _lret$2[ebp], eax

; 16   : 				if (bHandled) return lret;

  000b4	83 7d e8 00	 cmp	 DWORD PTR _bHandled$3[ebp], 0
  000b8	74 05		 je	 SHORT $LN4@WindowProc
  000ba	8b 45 e0	 mov	 eax, DWORD PTR _lret$2[ebp]
  000bd	eb 1b		 jmp	 SHORT $LN1@WindowProc
$LN4@WindowProc:

; 17   : 			}
; 18   : 		}
; 19   : 
; 20   : 	}
; 21   : 	else {

  000bf	eb 04		 jmp	 SHORT $LN3@WindowProc
$LN2@WindowProc:

; 22   : 		return 0;

  000c1	33 c0		 xor	 eax, eax
  000c3	eb 15		 jmp	 SHORT $LN1@WindowProc
$LN3@WindowProc:

; 23   : 	}
; 24   : 	return __super::WindowProc(hWnd, uMsg, wParam, lParam);

  000c5	8b 45 14	 mov	 eax, DWORD PTR _lParam$[ebp]
  000c8	50		 push	 eax
  000c9	8b 4d 10	 mov	 ecx, DWORD PTR _wParam$[ebp]
  000cc	51		 push	 ecx
  000cd	8b 55 0c	 mov	 edx, DWORD PTR _uMsg$[ebp]
  000d0	52		 push	 edx
  000d1	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  000d4	50		 push	 eax
  000d5	e8 00 00 00 00	 call	 ?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::WindowProc
$LN1@WindowProc:

; 25   : }

  000da	52		 push	 edx
  000db	8b cd		 mov	 ecx, ebp
  000dd	50		 push	 eax
  000de	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@WindowProc
  000e4	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000e9	58		 pop	 eax
  000ea	5a		 pop	 edx
  000eb	83 c4 24	 add	 esp, 36			; 00000024H
  000ee	3b ec		 cmp	 ebp, esp
  000f0	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000f5	8b e5		 mov	 esp, ebp
  000f7	5d		 pop	 ebp
  000f8	c2 10 00	 ret	 16			; 00000010H
  000fb	90		 npad	 1
$LN11@WindowProc:
  000fc	02 00 00 00	 DD	 2
  00100	00 00 00 00	 DD	 $LN10@WindowProc
$LN10@WindowProc:
  00104	f4 ff ff ff	 DD	 -12			; fffffff4H
  00108	04 00 00 00	 DD	 4
  0010c	00 00 00 00	 DD	 $LN8@WindowProc
  00110	e8 ff ff ff	 DD	 -24			; ffffffe8H
  00114	04 00 00 00	 DD	 4
  00118	00 00 00 00	 DD	 $LN9@WindowProc
$LN9@WindowProc:
  0011c	62		 DB	 98			; 00000062H
  0011d	48		 DB	 72			; 00000048H
  0011e	61		 DB	 97			; 00000061H
  0011f	6e		 DB	 110			; 0000006eH
  00120	64		 DB	 100			; 00000064H
  00121	6c		 DB	 108			; 0000006cH
  00122	65		 DB	 101			; 00000065H
  00123	64		 DB	 100			; 00000064H
  00124	00		 DB	 0
$LN8@WindowProc:
  00125	69		 DB	 105			; 00000069H
  00126	74		 DB	 116			; 00000074H
  00127	65		 DB	 101			; 00000065H
  00128	72		 DB	 114			; 00000072H
  00129	00		 DB	 0
?WindowProc@CFrameWindow@@SGJPAUHWND__@@IIJ@Z ENDP	; CFrameWindow::WindowProc
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??9?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z
_TEXT	SEGMENT
tv69 = -8						; size = 4
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
??9?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z PROC ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator!=, COMDAT
; _this$ = ecx

; 282  : 		{	// test for iterator inequality

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 283  : 		return (!(*this == _Right));

  00017	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  0001a	50		 push	 eax
  0001b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001e	e8 00 00 00 00	 call	 ??8?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator==
  00023	0f b6 c8	 movzx	 ecx, al
  00026	85 c9		 test	 ecx, ecx
  00028	75 09		 jne	 SHORT $LN3@operator
  0002a	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv69[ebp], 1
  00031	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  00033	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv69[ebp], 0
$LN4@operator:
  0003a	8a 45 f8	 mov	 al, BYTE PTR tv69[ebp]

; 284  : 		}

  0003d	83 c4 08	 add	 esp, 8
  00040	3b ec		 cmp	 ebp, esp
  00042	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00047	8b e5		 mov	 esp, ebp
  00049	5d		 pop	 ebp
  0004a	c2 04 00	 ret	 4
??9?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z ENDP ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator!=
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??D?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEAAU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??D?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEAAU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ PROC ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*, COMDAT
; _this$ = ecx

; 331  : 		{	// return designated value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 332  : 		return ((reference)**(_Mybase *)this);

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??D?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*

; 333  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
??D?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEAAU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ ENDP ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?find@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z
_TEXT	SEGMENT
tv138 = -36						; size = 4
$T1 = -32						; size = 4
$T2 = -28						; size = 4
$T3 = -24						; size = 4
$T4 = -20						; size = 4
__Where$ = -12						; size = 4
_this$ = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
__Keyval$ = 12						; size = 4
?find@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::find, COMDAT
; _this$ = ecx

; 1423 : 		{	// find an element in mutable sequence that matches _Keyval

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 24	 sub	 esp, 36			; 00000024H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0000e	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00011	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00014	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00017	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0001a	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0001d	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00020	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00023	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  00026	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1424 : 		iterator _Where = lower_bound(_Keyval);

  00029	8b 45 0c	 mov	 eax, DWORD PTR __Keyval$[ebp]
  0002c	50		 push	 eax
  0002d	8d 4d f4	 lea	 ecx, DWORD PTR __Where$[ebp]
  00030	51		 push	 ecx
  00031	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	e8 00 00 00 00	 call	 ?lower_bound@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::lower_bound

; 1425 : 		return (_Where == end()

  00039	8d 55 e8	 lea	 edx, DWORD PTR $T3[ebp]
  0003c	52		 push	 edx
  0003d	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00040	e8 00 00 00 00	 call	 ?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::end
  00045	50		 push	 eax
  00046	8d 4d f4	 lea	 ecx, DWORD PTR __Where$[ebp]
  00049	e8 00 00 00 00	 call	 ??8?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator==
  0004e	0f b6 c0	 movzx	 eax, al
  00051	85 c0		 test	 eax, eax
  00053	75 35		 jne	 SHORT $LN3@find
  00055	8b 4d f4	 mov	 ecx, DWORD PTR __Where$[ebp]
  00058	51		 push	 ecx
  00059	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0005c	e8 00 00 00 00	 call	 ?_Key@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Key
  00061	50		 push	 eax
  00062	8b 55 0c	 mov	 edx, DWORD PTR __Keyval$[ebp]
  00065	52		 push	 edx
  00066	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00069	e8 00 00 00 00	 call	 ?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAU?$less@I@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp
  0006e	8b c8		 mov	 ecx, eax
  00070	e8 00 00 00 00	 call	 ??R?$less@I@std@@QBE_NABI0@Z ; std::less<unsigned int>::operator()
  00075	0f b6 c0	 movzx	 eax, al
  00078	85 c0		 test	 eax, eax
  0007a	75 0e		 jne	 SHORT $LN3@find
  0007c	8b 4d f4	 mov	 ecx, DWORD PTR __Where$[ebp]
  0007f	89 4d e0	 mov	 DWORD PTR $T1[ebp], ecx
  00082	8d 55 e0	 lea	 edx, DWORD PTR $T1[ebp]
  00085	89 55 dc	 mov	 DWORD PTR tv138[ebp], edx
  00088	eb 0f		 jmp	 SHORT $LN4@find
$LN3@find:
  0008a	8d 45 e4	 lea	 eax, DWORD PTR $T2[ebp]
  0008d	50		 push	 eax
  0008e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00091	e8 00 00 00 00	 call	 ?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::end
  00096	89 45 dc	 mov	 DWORD PTR tv138[ebp], eax
$LN4@find:
  00099	8b 4d dc	 mov	 ecx, DWORD PTR tv138[ebp]
  0009c	89 4d ec	 mov	 DWORD PTR $T4[ebp], ecx
  0009f	8b 55 ec	 mov	 edx, DWORD PTR $T4[ebp]
  000a2	8b 02		 mov	 eax, DWORD PTR [edx]
  000a4	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  000a7	89 01		 mov	 DWORD PTR [ecx], eax
  000a9	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1426 : 			|| _DEBUG_LT_PRED(this->_Getcomp(),
; 1427 : 				_Keyval, this->_Key(_Where._Ptr))
; 1428 : 					? end() : _Where);
; 1429 : 		}

  000ac	52		 push	 edx
  000ad	8b cd		 mov	 ecx, ebp
  000af	50		 push	 eax
  000b0	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN7@find
  000b6	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000bb	58		 pop	 eax
  000bc	5a		 pop	 edx
  000bd	83 c4 24	 add	 esp, 36			; 00000024H
  000c0	3b ec		 cmp	 ebp, esp
  000c2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000c7	8b e5		 mov	 esp, ebp
  000c9	5d		 pop	 ebp
  000ca	c2 08 00	 ret	 8
  000cd	0f 1f 00	 npad	 3
$LN7@find:
  000d0	01 00 00 00	 DD	 1
  000d4	00 00 00 00	 DD	 $LN6@find
$LN6@find:
  000d8	f4 ff ff ff	 DD	 -12			; fffffff4H
  000dc	04 00 00 00	 DD	 4
  000e0	00 00 00 00	 DD	 $LN5@find
$LN5@find:
  000e4	5f		 DB	 95			; 0000005fH
  000e5	57		 DB	 87			; 00000057H
  000e6	68		 DB	 104			; 00000068H
  000e7	65		 DB	 101			; 00000065H
  000e8	72		 DB	 114			; 00000072H
  000e9	65		 DB	 101			; 00000065H
  000ea	00		 DB	 0
?find@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::find
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
__My_data$ = -20					; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::end, COMDAT
; _this$ = ecx

; 1197 : 		{	// return iterator for end of mutable sequence

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 08	 sub	 esp, 8
  0001b	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00022	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00029	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1198 : 		auto& _My_data = this->_Get_data();

  0002c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002f	e8 00 00 00 00	 call	 ?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data
  00034	89 45 ec	 mov	 DWORD PTR __My_data$[ebp], eax

; 1199 : 		return (iterator(_My_data._Myhead, _STD addressof(_My_data)));

  00037	8b 45 ec	 mov	 eax, DWORD PTR __My_data$[ebp]
  0003a	50		 push	 eax
  0003b	e8 00 00 00 00	 call	 ??$addressof@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@YAPAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@0@AAV10@@Z ; std::addressof<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
  00040	83 c4 04	 add	 esp, 4
  00043	50		 push	 eax
  00044	8b 4d ec	 mov	 ecx, DWORD PTR __My_data$[ebp]
  00047	8b 11		 mov	 edx, DWORD PTR [ecx]
  00049	52		 push	 edx
  0004a	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0004d	e8 00 00 00 00	 call	 ??0?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
  00052	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1200 : 		}

  00055	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00058	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0005f	83 c4 14	 add	 esp, 20			; 00000014H
  00062	3b ec		 cmp	 ebp, esp
  00064	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00069	8b e5		 mov	 esp, ebp
  0006b	5d		 pop	 ebp
  0006c	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?end@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@XZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::end
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ??B?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@QBE_NXZ
_TEXT	SEGMENT
tv72 = -8						; size = 4
_this$ = -4						; size = 4
??B?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@QBE_NXZ PROC ; std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>::operator bool, COMDAT
; _this$ = ecx

; 1597 : 		{	// test if wrapper holds function object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1598 : 		return (!this->_Empty());

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?_Empty@?$_Func_class@JPAUHWND__@@IIJAAH@std@@IBE_NXZ ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Empty
  0001f	0f b6 c0	 movzx	 eax, al
  00022	85 c0		 test	 eax, eax
  00024	75 09		 jne	 SHORT $LN3@operator
  00026	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv72[ebp], 1
  0002d	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  0002f	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv72[ebp], 0
$LN4@operator:
  00036	8a 45 f8	 mov	 al, BYTE PTR tv72[ebp]

; 1599 : 		}

  00039	83 c4 08	 add	 esp, 8
  0003c	3b ec		 cmp	 ebp, esp
  0003e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00043	8b e5		 mov	 esp, ebp
  00045	5d		 pop	 ebp
  00046	c3		 ret	 0
??B?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@QBE_NXZ ENDP ; std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>::operator bool
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ?_Empty@?$_Func_class@JPAUHWND__@@IIJAAH@std@@IBE_NXZ
_TEXT	SEGMENT
tv67 = -8						; size = 4
_this$ = -4						; size = 4
?_Empty@?$_Func_class@JPAUHWND__@@IIJAAH@std@@IBE_NXZ PROC ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Empty, COMDAT
; _this$ = ecx

; 1282 : 		{	// return true if no stored object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1283 : 		return (_Getimpl() == nullptr);

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?_Getimpl@?$_Func_class@JPAUHWND__@@IIJAAH@std@@ABEPAV?$_Func_base@JPAUHWND__@@IIJAAH@2@XZ ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Getimpl
  0001f	85 c0		 test	 eax, eax
  00021	75 09		 jne	 SHORT $LN3@Empty
  00023	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv67[ebp], 1
  0002a	eb 07		 jmp	 SHORT $LN4@Empty
$LN3@Empty:
  0002c	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv67[ebp], 0
$LN4@Empty:
  00033	8a 45 f8	 mov	 al, BYTE PTR tv67[ebp]

; 1284 : 		}

  00036	83 c4 08	 add	 esp, 8
  00039	3b ec		 cmp	 ebp, esp
  0003b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00040	8b e5		 mov	 esp, ebp
  00042	5d		 pop	 ebp
  00043	c3		 ret	 0
?_Empty@?$_Func_class@JPAUHWND__@@IIJAAH@std@@IBE_NXZ ENDP ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Empty
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??8?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z
_TEXT	SEGMENT
tv75 = -8						; size = 4
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
??8?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z PROC ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator==, COMDAT
; _this$ = ecx

; 273  : 		{	// test for iterator equality

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 274  :  #if _ITERATOR_DEBUG_LEVEL != 0
; 275  : 		_STL_VERIFY(this->_Getcont() == _Right._Getcont(), "map/set iterators incompatible");
; 276  :  #endif /* _ITERATOR_DEBUG_LEVEL != 0 */
; 277  : 
; 278  : 		return (this->_Ptr == _Right._Ptr);

  00017	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001a	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  0001d	8b 10		 mov	 edx, DWORD PTR [eax]
  0001f	3b 11		 cmp	 edx, DWORD PTR [ecx]
  00021	75 09		 jne	 SHORT $LN3@operator
  00023	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv75[ebp], 1
  0002a	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  0002c	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv75[ebp], 0
$LN4@operator:
  00033	8a 45 f8	 mov	 al, BYTE PTR tv75[ebp]

; 279  : 		}

  00036	8b e5		 mov	 esp, ebp
  00038	5d		 pop	 ebp
  00039	c2 04 00	 ret	 4
??8?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBE_NABV01@@Z ENDP ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator==
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??D?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??D?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ PROC ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*, COMDAT
; _this$ = ecx

; 218  : 		{	// return designated value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 219  :  #if _ITERATOR_DEBUG_LEVEL != 0
; 220  : 		const auto _Mycont = static_cast<const _Mytree *>(this->_Getcont());
; 221  : 		_STL_ASSERT(_Mycont, "cannot dereference value-initialized map/set iterator");
; 222  : 		_STL_VERIFY(this->_Ptr != _Mycont->_Myhead, "cannot dereference end map/set iterator");
; 223  :  #endif /* _ITERATOR_DEBUG_LEVEL != 0 */
; 224  : 
; 225  : 		return (this->_Ptr->_Myval);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 00		 mov	 eax, DWORD PTR [eax]
  00013	83 c0 10	 add	 eax, 16			; 00000010H

; 226  : 		}

  00016	8b e5		 mov	 esp, ebp
  00018	5d		 pop	 ebp
  00019	c3		 ret	 0
??D?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QBEABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@XZ ENDP ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::operator*
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??0?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
__Plist$ = 12						; size = 4
??0?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z PROC ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >, COMDAT
; _this$ = ecx

; 327  : 		{	// construct with node pointer _Pnode

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 326  : 		: _Mybase(_Pnode, _Plist)

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Plist$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ??0?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >

; 328  : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 08 00	 ret	 8
??0?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ENDP ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ PROC ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data, COMDAT
; _this$ = ecx

; 1014 : 		{	// return reference to _Tree_val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1015 : 		return (_Mypair._Get_second()._Get_second());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second
  00016	8b c8		 mov	 ecx, eax
  00018	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second

; 1016 : 		}

  0001d	83 c4 04	 add	 esp, 4
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	8b e5		 mov	 esp, ebp
  00029	5d		 pop	 ebp
  0002a	c3		 ret	 0
?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ENDP ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAU?$less@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAU?$less@I@2@XZ PROC ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp, COMDAT
; _this$ = ecx

; 994  : 		{	// return reference to ordering predicate

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 995  : 		return (_Mypair._Get_first());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAU?$less@I@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first

; 996  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAU?$less@I@2@XZ ENDP ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?_Key@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@2@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
?_Key@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@2@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Key, COMDAT
; _this$ = ecx

; 2122 : 		{	// return reference to key in node

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 2123 : 		return (this->_Kfn(_Pnode->_Myval));

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Pnode$[ebp]
  00011	83 c0 10	 add	 eax, 16			; 00000010H
  00014	50		 push	 eax
  00015	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00018	e8 00 00 00 00	 call	 ?_Kfn@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Kfn

; 2124 : 		}

  0001d	83 c4 04	 add	 esp, 4
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	8b e5		 mov	 esp, ebp
  00029	5d		 pop	 ebp
  0002a	c2 04 00	 ret	 4
?_Key@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@2@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Key
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?lower_bound@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___$ReturnUdt$ = 8					; size = 4
__Keyval$ = 12						; size = 4
?lower_bound@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::lower_bound, COMDAT
; _this$ = ecx

; 1480 : 		{	// find leftmost node not less than _Keyval in mutable tree

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1481 : 		return (iterator(_Lbound(_Keyval), _STD addressof(this->_Get_data())));

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data
  00016	50		 push	 eax
  00017	e8 00 00 00 00	 call	 ??$addressof@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@YAPAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@0@AAV10@@Z ; std::addressof<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
  0001c	83 c4 04	 add	 esp, 4
  0001f	50		 push	 eax
  00020	8b 45 0c	 mov	 eax, DWORD PTR __Keyval$[ebp]
  00023	50		 push	 eax
  00024	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00027	e8 00 00 00 00	 call	 ??$_Lbound@I@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@ABI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Lbound<unsigned int>
  0002c	50		 push	 eax
  0002d	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00030	e8 00 00 00 00	 call	 ??0?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ; std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
  00035	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1482 : 		}

  00038	83 c4 04	 add	 esp, 4
  0003b	3b ec		 cmp	 ebp, esp
  0003d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00042	8b e5		 mov	 esp, ebp
  00044	5d		 pop	 ebp
  00045	c2 08 00	 ret	 8
?lower_bound@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QAE?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@2@ABI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::lower_bound
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@QAEJIIJ@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_uMsg$ = 8						; size = 4
_wParam$ = 12						; size = 4
_lParam$ = 16						; size = 4
?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@QAEJIIJ@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::DefWindowProcW, COMDAT
; _this$ = ecx

; 3494 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 3495 : #ifdef STRICT
; 3496 : 		return ::CallWindowProc(m_pfnSuperWindowProc, this->m_hWnd, uMsg, wParam, lParam);

  0000f	8b f4		 mov	 esi, esp
  00011	8b 45 10	 mov	 eax, DWORD PTR _lParam$[ebp]
  00014	50		 push	 eax
  00015	8b 4d 0c	 mov	 ecx, DWORD PTR _wParam$[ebp]
  00018	51		 push	 ecx
  00019	8b 55 08	 mov	 edx, DWORD PTR _uMsg$[ebp]
  0001c	52		 push	 edx
  0001d	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00020	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  00023	51		 push	 ecx
  00024	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00027	8b 42 20	 mov	 eax, DWORD PTR [edx+32]
  0002a	50		 push	 eax
  0002b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__CallWindowProcW@20
  00031	3b f4		 cmp	 esi, esp
  00033	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 3497 : #else
; 3498 : 		return ::CallWindowProc((FARPROC)m_pfnSuperWindowProc, this->m_hWnd, uMsg, wParam, lParam);
; 3499 : #endif
; 3500 : 	}

  00038	5e		 pop	 esi
  00039	83 c4 04	 add	 esp, 4
  0003c	3b ec		 cmp	 ebp, esp
  0003e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00043	8b e5		 mov	 esp, ebp
  00045	5d		 pop	 ebp
  00046	c2 0c 00	 ret	 12			; 0000000cH
?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@QAEJIIJ@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::DefWindowProcW
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ?_Getimpl@?$_Func_class@JPAUHWND__@@IIJAAH@std@@ABEPAV?$_Func_base@JPAUHWND__@@IIJAAH@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getimpl@?$_Func_class@JPAUHWND__@@IIJAAH@std@@ABEPAV?$_Func_base@JPAUHWND__@@IIJAAH@2@XZ PROC ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Getimpl, COMDAT
; _this$ = ecx

; 1441 : 		{	// get pointer to object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1442 : 		return (_Mystorage._Ptrs[_Small_object_num_ptrs - 1]);

  0000e	b8 04 00 00 00	 mov	 eax, 4
  00013	6b c8 09	 imul	 ecx, eax, 9
  00016	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00019	8b 04 0a	 mov	 eax, DWORD PTR [edx+ecx]

; 1443 : 		}

  0001c	8b e5		 mov	 esp, ebp
  0001e	5d		 pop	 ebp
  0001f	c3		 ret	 0
?_Getimpl@?$_Func_class@JPAUHWND__@@IIJAAH@std@@ABEPAV?$_Func_base@JPAUHWND__@@IIJAAH@2@XZ ENDP ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Getimpl
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ PROC ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second, COMDAT
; _this$ = ecx

; 301  : 		{	// return reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 302  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 303  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ ENDP ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAU?$less@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAU?$less@I@2@XZ PROC ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first, COMDAT
; _this$ = ecx

; 291  : 		{	// return reference to first

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 292  : 		return (*this);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 293  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QAEAAU?$less@I@2@XZ ENDP ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second, COMDAT
; _this$ = ecx

; 301  : 		{	// return reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 302  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 303  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QAEAAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??0?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
__Plist$ = 12						; size = 4
??0?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z PROC ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >, COMDAT
; _this$ = ecx

; 214  : 		{	// construct with node pointer _Pnode

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 213  : 		: _Mybase(_Pnode, _Plist)

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Plist$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>

; 215  : 		}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 08 00	 ret	 8
??0?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ENDP ; std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?_Kfn@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@2@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Val$ = 8						; size = 4
?_Kfn@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@2@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Kfn, COMDAT
; _this$ = ecx

; 2117 : 		{	// get key from value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 2118 : 		return (_Traits::_Kfn(_Val));

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]
  00011	50		 push	 eax
  00012	e8 00 00 00 00	 call	 ??$_Kfn@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@SAABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@@Z ; std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0>::_Kfn<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >
  00017	83 c4 04	 add	 esp, 4

; 2119 : 		}

  0001a	83 c4 04	 add	 esp, 4
  0001d	3b ec		 cmp	 ebp, esp
  0001f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00024	8b e5		 mov	 esp, ebp
  00026	5d		 pop	 ebp
  00027	c2 04 00	 ret	 4
?_Kfn@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@2@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Kfn
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Pnode$ = 8						; size = 4
__Plist$ = 12						; size = 4
??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z PROC ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>, COMDAT
; _this$ = ecx

; 43   : 		{	// construct with node pointer _Pnode

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 42   : 		: _Ptr(_Pnode)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  00014	89 08		 mov	 DWORD PTR [eax], ecx

; 44   : 		this->_Adopt(_Plist);

  00016	8b 55 0c	 mov	 edx, DWORD PTR __Plist$[ebp]
  00019	52		 push	 edx
  0001a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001d	e8 00 00 00 00	 call	 ?_Adopt@_Iterator_base0@std@@QAEXPBX@Z ; std::_Iterator_base0::_Adopt

; 45   : 		}

  00022	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00025	83 c4 04	 add	 esp, 4
  00028	3b ec		 cmp	 ebp, esp
  0002a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002f	8b e5		 mov	 esp, ebp
  00031	5d		 pop	 ebp
  00032	c2 08 00	 ret	 8
??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QAE@PAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@PBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@1@@Z ENDP ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,std::_Iterator_base0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z
_TEXT	SEGMENT
_hWndThis$1 = -76					; size = 4
_pfnWndProc$2 = -72					; size = 4
_bRet$ = -68						; size = 4
_lRes$ = -60						; size = 4
_pOldMsg$ = -52						; size = 4
_msg$ = -44						; size = 36
_pThis$ = -4						; size = 4
_hWnd$ = 8						; size = 4
_uMsg$ = 12						; size = 4
_wParam$ = 16						; size = 4
_lParam$ = 20						; size = 4
?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::WindowProc, COMDAT

; 3547 : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 4c	 sub	 esp, 76			; 0000004cH
  00006	56		 push	 esi
  00007	57		 push	 edi
  00008	8d 7d b4	 lea	 edi, DWORD PTR [ebp-76]
  0000b	b9 13 00 00 00	 mov	 ecx, 19			; 00000013H
  00010	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00015	f3 ab		 rep stosd

; 3548 : 	CWindowImplBaseT< TBase, TWinTraits >* pThis = (CWindowImplBaseT< TBase, TWinTraits >*)hWnd;

  00017	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  0001a	89 45 fc	 mov	 DWORD PTR _pThis$[ebp], eax

; 3549 : 	// set a ptr to this message and save the old value
; 3550 : 	_ATL_MSG msg(pThis->m_hWnd, uMsg, wParam, lParam);

  0001d	6a 01		 push	 1
  0001f	8b 4d 14	 mov	 ecx, DWORD PTR _lParam$[ebp]
  00022	51		 push	 ecx
  00023	8b 55 10	 mov	 edx, DWORD PTR _wParam$[ebp]
  00026	52		 push	 edx
  00027	8b 45 0c	 mov	 eax, DWORD PTR _uMsg$[ebp]
  0002a	50		 push	 eax
  0002b	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0002e	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  00031	52		 push	 edx
  00032	8d 4d d4	 lea	 ecx, DWORD PTR _msg$[ebp]
  00035	e8 00 00 00 00	 call	 ??0_ATL_MSG@ATL@@QAE@PAUHWND__@@IIJH@Z ; ATL::_ATL_MSG::_ATL_MSG

; 3551 : 	const _ATL_MSG* pOldMsg = pThis->m_pCurrentMsg;

  0003a	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  0003d	8b 48 18	 mov	 ecx, DWORD PTR [eax+24]
  00040	89 4d cc	 mov	 DWORD PTR _pOldMsg$[ebp], ecx

; 3552 : 	pThis->m_pCurrentMsg = &msg;

  00043	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  00046	8d 45 d4	 lea	 eax, DWORD PTR _msg$[ebp]
  00049	89 42 18	 mov	 DWORD PTR [edx+24], eax

; 3553 : 	// pass to the message map to process
; 3554 : 	LRESULT lRes = 0;

  0004c	c7 45 c4 00 00
	00 00		 mov	 DWORD PTR _lRes$[ebp], 0

; 3555 : 	BOOL bRet = pThis->ProcessWindowMessage(pThis->m_hWnd, uMsg, wParam, lParam, lRes, 0);

  00053	8b f4		 mov	 esi, esp
  00055	6a 00		 push	 0
  00057	8d 4d c4	 lea	 ecx, DWORD PTR _lRes$[ebp]
  0005a	51		 push	 ecx
  0005b	8b 55 14	 mov	 edx, DWORD PTR _lParam$[ebp]
  0005e	52		 push	 edx
  0005f	8b 45 10	 mov	 eax, DWORD PTR _wParam$[ebp]
  00062	50		 push	 eax
  00063	8b 4d 0c	 mov	 ecx, DWORD PTR _uMsg$[ebp]
  00066	51		 push	 ecx
  00067	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  0006a	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  0006d	50		 push	 eax
  0006e	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00071	8b 11		 mov	 edx, DWORD PTR [ecx]
  00073	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00076	8b 02		 mov	 eax, DWORD PTR [edx]
  00078	ff d0		 call	 eax
  0007a	3b f4		 cmp	 esi, esp
  0007c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00081	89 45 bc	 mov	 DWORD PTR _bRet$[ebp], eax

; 3556 : 	// restore saved value for the current message
; 3557 : 	ATLASSERT(pThis->m_pCurrentMsg == &msg);
; 3558 : 
; 3559 : 	// do the default processing if message was not handled
; 3560 : 	if(!bRet)

  00084	83 7d bc 00	 cmp	 DWORD PTR _bRet$[ebp], 0
  00088	0f 85 98 00 00
	00		 jne	 $LN2@WindowProc

; 3561 : 	{
; 3562 : 		if(uMsg != WM_NCDESTROY)

  0008e	81 7d 0c 82 00
	00 00		 cmp	 DWORD PTR _uMsg$[ebp], 130 ; 00000082H
  00095	74 19		 je	 SHORT $LN3@WindowProc

; 3563 : 			lRes = pThis->DefWindowProc(uMsg, wParam, lParam);

  00097	8b 4d 14	 mov	 ecx, DWORD PTR _lParam$[ebp]
  0009a	51		 push	 ecx
  0009b	8b 55 10	 mov	 edx, DWORD PTR _wParam$[ebp]
  0009e	52		 push	 edx
  0009f	8b 45 0c	 mov	 eax, DWORD PTR _uMsg$[ebp]
  000a2	50		 push	 eax
  000a3	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  000a6	e8 00 00 00 00	 call	 ?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@QAEJIIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::DefWindowProcW
  000ab	89 45 c4	 mov	 DWORD PTR _lRes$[ebp], eax

; 3564 : 		else

  000ae	eb 76		 jmp	 SHORT $LN2@WindowProc
$LN3@WindowProc:

; 3565 : 		{
; 3566 : 			// unsubclass, if needed
; 3567 : 			LONG_PTR pfnWndProc = ::GetWindowLongPtr(pThis->m_hWnd, GWLP_WNDPROC);

  000b0	6a fc		 push	 -4			; fffffffcH
  000b2	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  000b5	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  000b8	52		 push	 edx
  000b9	e8 00 00 00 00	 call	 ?GetWindowLongPtrW@@YAJPAUHWND__@@H@Z ; GetWindowLongPtrW
  000be	83 c4 08	 add	 esp, 8
  000c1	89 45 b8	 mov	 DWORD PTR _pfnWndProc$2[ebp], eax

; 3568 : 			lRes = pThis->DefWindowProc(uMsg, wParam, lParam);

  000c4	8b 45 14	 mov	 eax, DWORD PTR _lParam$[ebp]
  000c7	50		 push	 eax
  000c8	8b 4d 10	 mov	 ecx, DWORD PTR _wParam$[ebp]
  000cb	51		 push	 ecx
  000cc	8b 55 0c	 mov	 edx, DWORD PTR _uMsg$[ebp]
  000cf	52		 push	 edx
  000d0	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  000d3	e8 00 00 00 00	 call	 ?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@QAEJIIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::DefWindowProcW
  000d8	89 45 c4	 mov	 DWORD PTR _lRes$[ebp], eax

; 3569 : 			if(pThis->m_pfnSuperWindowProc != ::DefWindowProc && ::GetWindowLongPtr(pThis->m_hWnd, GWLP_WNDPROC) == pfnWndProc)

  000db	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  000de	8b 48 20	 mov	 ecx, DWORD PTR [eax+32]
  000e1	3b 0d 00 00 00
	00		 cmp	 ecx, DWORD PTR __imp__DefWindowProcW@16
  000e7	74 2e		 je	 SHORT $LN5@WindowProc
  000e9	6a fc		 push	 -4			; fffffffcH
  000eb	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  000ee	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  000f1	50		 push	 eax
  000f2	e8 00 00 00 00	 call	 ?GetWindowLongPtrW@@YAJPAUHWND__@@H@Z ; GetWindowLongPtrW
  000f7	83 c4 08	 add	 esp, 8
  000fa	3b 45 b8	 cmp	 eax, DWORD PTR _pfnWndProc$2[ebp]
  000fd	75 18		 jne	 SHORT $LN5@WindowProc

; 3570 : 				::SetWindowLongPtr(pThis->m_hWnd, GWLP_WNDPROC, (LONG_PTR)pThis->m_pfnSuperWindowProc);

  000ff	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00102	8b 51 20	 mov	 edx, DWORD PTR [ecx+32]
  00105	52		 push	 edx
  00106	6a fc		 push	 -4			; fffffffcH
  00108	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  0010b	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  0010e	51		 push	 ecx
  0010f	e8 00 00 00 00	 call	 ?SetWindowLongPtrW@@YAJPAUHWND__@@HJ@Z ; SetWindowLongPtrW
  00114	83 c4 0c	 add	 esp, 12			; 0000000cH
$LN5@WindowProc:

; 3571 : 			// mark window as destroyed
; 3572 : 			pThis->m_dwState |= CWindowImplRoot<TBase>::WINSTATE_DESTROYED;

  00117	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  0011a	8b 42 1c	 mov	 eax, DWORD PTR [edx+28]
  0011d	83 c8 01	 or	 eax, 1
  00120	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00123	89 41 1c	 mov	 DWORD PTR [ecx+28], eax
$LN2@WindowProc:

; 3573 : 		}
; 3574 : 	}
; 3575 : 	if((pThis->m_dwState & CWindowImplRoot<TBase>::WINSTATE_DESTROYED) && pOldMsg== NULL)

  00126	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  00129	8b 42 1c	 mov	 eax, DWORD PTR [edx+28]
  0012c	83 e0 01	 and	 eax, 1
  0012f	74 4d		 je	 SHORT $LN6@WindowProc
  00131	83 7d cc 00	 cmp	 DWORD PTR _pOldMsg$[ebp], 0
  00135	75 47		 jne	 SHORT $LN6@WindowProc

; 3576 : 	{
; 3577 : 		// clear out window handle
; 3578 : 		HWND hWndThis = pThis->m_hWnd;

  00137	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0013a	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0013d	89 55 b4	 mov	 DWORD PTR _hWndThis$1[ebp], edx

; 3579 : 		pThis->m_hWnd = NULL;

  00140	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  00143	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [eax+4], 0

; 3580 : 		pThis->m_dwState &= ~CWindowImplRoot<TBase>::WINSTATE_DESTROYED;

  0014a	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0014d	8b 51 1c	 mov	 edx, DWORD PTR [ecx+28]
  00150	83 e2 fe	 and	 edx, -2			; fffffffeH
  00153	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  00156	89 50 1c	 mov	 DWORD PTR [eax+28], edx

; 3581 : 		// clean up after window is destroyed
; 3582 : 		pThis->m_pCurrentMsg = pOldMsg;

  00159	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0015c	8b 55 cc	 mov	 edx, DWORD PTR _pOldMsg$[ebp]
  0015f	89 51 18	 mov	 DWORD PTR [ecx+24], edx

; 3583 : 		pThis->OnFinalMessage(hWndThis);

  00162	8b f4		 mov	 esi, esp
  00164	8b 45 b4	 mov	 eax, DWORD PTR _hWndThis$1[ebp]
  00167	50		 push	 eax
  00168	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0016b	8b 11		 mov	 edx, DWORD PTR [ecx]
  0016d	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00170	8b 42 0c	 mov	 eax, DWORD PTR [edx+12]
  00173	ff d0		 call	 eax
  00175	3b f4		 cmp	 esi, esp
  00177	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 3584 : 	}else {

  0017c	eb 09		 jmp	 SHORT $LN7@WindowProc
$LN6@WindowProc:

; 3585 : 		pThis->m_pCurrentMsg = pOldMsg;

  0017e	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00181	8b 55 cc	 mov	 edx, DWORD PTR _pOldMsg$[ebp]
  00184	89 51 18	 mov	 DWORD PTR [ecx+24], edx
$LN7@WindowProc:

; 3586 : 	}
; 3587 : 	return lRes;

  00187	8b 45 c4	 mov	 eax, DWORD PTR _lRes$[ebp]

; 3588 : }

  0018a	52		 push	 edx
  0018b	8b cd		 mov	 ecx, ebp
  0018d	50		 push	 eax
  0018e	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN12@WindowProc
  00194	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00199	58		 pop	 eax
  0019a	5a		 pop	 edx
  0019b	5f		 pop	 edi
  0019c	5e		 pop	 esi
  0019d	83 c4 4c	 add	 esp, 76			; 0000004cH
  001a0	3b ec		 cmp	 ebp, esp
  001a2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001a7	8b e5		 mov	 esp, ebp
  001a9	5d		 pop	 ebp
  001aa	c2 10 00	 ret	 16			; 00000010H
  001ad	0f 1f 00	 npad	 3
$LN12@WindowProc:
  001b0	02 00 00 00	 DD	 2
  001b4	00 00 00 00	 DD	 $LN11@WindowProc
$LN11@WindowProc:
  001b8	d4 ff ff ff	 DD	 -44			; ffffffd4H
  001bc	24 00 00 00	 DD	 36			; 00000024H
  001c0	00 00 00 00	 DD	 $LN9@WindowProc
  001c4	c4 ff ff ff	 DD	 -60			; ffffffc4H
  001c8	04 00 00 00	 DD	 4
  001cc	00 00 00 00	 DD	 $LN10@WindowProc
$LN10@WindowProc:
  001d0	6c		 DB	 108			; 0000006cH
  001d1	52		 DB	 82			; 00000052H
  001d2	65		 DB	 101			; 00000065H
  001d3	73		 DB	 115			; 00000073H
  001d4	00		 DB	 0
$LN9@WindowProc:
  001d5	6d		 DB	 109			; 0000006dH
  001d6	73		 DB	 115			; 00000073H
  001d7	67		 DB	 103			; 00000067H
  001d8	00		 DB	 0
?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0GMPAAAA@$0EABAA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<114229248,262400> >::WindowProc
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??R?$_Func_class@JPAUHWND__@@IIJAAH@std@@QBEJPAUHWND__@@IIJAAH@Z
_TEXT	SEGMENT
__Impl$ = -8						; size = 4
_this$ = -4						; size = 4
_<_Args_0>$ = 8						; size = 4
_<_Args_1>$ = 12					; size = 4
_<_Args_2>$ = 16					; size = 4
_<_Args_3>$ = 20					; size = 4
_<_Args_4>$ = 24					; size = 4
??R?$_Func_class@JPAUHWND__@@IIJAAH@std@@QBEJPAUHWND__@@IIJAAH@Z PROC ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::operator(), COMDAT
; _this$ = ecx
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  00018	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001b	e8 00 00 00 00	 call	 ?_Empty@?$_Func_class@JPAUHWND__@@IIJAAH@std@@IBE_NXZ ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Empty
  00020	0f b6 c0	 movzx	 eax, al
  00023	85 c0		 test	 eax, eax
  00025	74 05		 je	 SHORT $LN2@operator
  00027	e8 00 00 00 00	 call	 ?_Xbad_function_call@std@@YAXXZ ; std::_Xbad_function_call
$LN2@operator:
  0002c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002f	e8 00 00 00 00	 call	 ?_Getimpl@?$_Func_class@JPAUHWND__@@IIJAAH@std@@ABEPAV?$_Func_base@JPAUHWND__@@IIJAAH@2@XZ ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::_Getimpl
  00034	89 45 f8	 mov	 DWORD PTR __Impl$[ebp], eax
  00037	8b 4d 18	 mov	 ecx, DWORD PTR _<_Args_4>$[ebp]
  0003a	51		 push	 ecx
  0003b	e8 00 00 00 00	 call	 ??$forward@AAH@std@@YAAAHAAH@Z ; std::forward<int &>
  00040	83 c4 04	 add	 esp, 4
  00043	8b f4		 mov	 esi, esp
  00045	50		 push	 eax
  00046	8d 55 14	 lea	 edx, DWORD PTR _<_Args_3>$[ebp]
  00049	52		 push	 edx
  0004a	e8 00 00 00 00	 call	 ??$forward@J@std@@YA$$QAJAAJ@Z ; std::forward<long>
  0004f	83 c4 04	 add	 esp, 4
  00052	50		 push	 eax
  00053	8d 45 10	 lea	 eax, DWORD PTR _<_Args_2>$[ebp]
  00056	50		 push	 eax
  00057	e8 00 00 00 00	 call	 ??$forward@I@std@@YA$$QAIAAI@Z ; std::forward<unsigned int>
  0005c	83 c4 04	 add	 esp, 4
  0005f	50		 push	 eax
  00060	8d 4d 0c	 lea	 ecx, DWORD PTR _<_Args_1>$[ebp]
  00063	51		 push	 ecx
  00064	e8 00 00 00 00	 call	 ??$forward@I@std@@YA$$QAIAAI@Z ; std::forward<unsigned int>
  00069	83 c4 04	 add	 esp, 4
  0006c	50		 push	 eax
  0006d	8d 55 08	 lea	 edx, DWORD PTR _<_Args_0>$[ebp]
  00070	52		 push	 edx
  00071	e8 00 00 00 00	 call	 ??$forward@PAUHWND__@@@std@@YA$$QAPAUHWND__@@AAPAU1@@Z ; std::forward<HWND__ *>
  00076	83 c4 04	 add	 esp, 4
  00079	50		 push	 eax
  0007a	8b 45 f8	 mov	 eax, DWORD PTR __Impl$[ebp]
  0007d	8b 10		 mov	 edx, DWORD PTR [eax]
  0007f	8b 4d f8	 mov	 ecx, DWORD PTR __Impl$[ebp]
  00082	8b 42 08	 mov	 eax, DWORD PTR [edx+8]
  00085	ff d0		 call	 eax
  00087	3b f4		 cmp	 esi, esp
  00089	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN3@operator:
  0008e	5e		 pop	 esi
  0008f	83 c4 08	 add	 esp, 8
  00092	3b ec		 cmp	 ebp, esp
  00094	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00099	8b e5		 mov	 esp, ebp
  0009b	5d		 pop	 ebp
  0009c	c2 14 00	 ret	 20			; 00000014H
??R?$_Func_class@JPAUHWND__@@IIJAAH@std@@QBEJPAUHWND__@@IIJAAH@Z ENDP ; std::_Func_class<long,HWND__ *,unsigned int,unsigned int,long,int &>::operator()
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xstddef
;	COMDAT ??$addressof@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@YAPAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@0@AAV10@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
??$addressof@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@YAPAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@0@AAV10@@Z PROC ; std::addressof<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >, COMDAT

; 329  : 	{	// return address of _Val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 330  : 	return (__builtin_addressof(_Val));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]

; 331  : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$addressof@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@std@@@std@@YAPAV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@0@AAV10@@Z ENDP ; std::addressof<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > > >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ??$_Lbound@I@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@ABI@Z
_TEXT	SEGMENT
__Pnode$ = -12						; size = 4
__Wherenode$ = -8					; size = 4
_this$ = -4						; size = 4
__Keyval$ = 8						; size = 4
??$_Lbound@I@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@ABI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Lbound<unsigned int>, COMDAT
; _this$ = ecx

; 2044 : 		{	// find leftmost node not less than _Keyval

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 2045 : 		_Nodeptr _Wherenode = this->_Get_data()._Myhead;	// end() if search fails

  0001e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00021	e8 00 00 00 00	 call	 ?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data
  00026	8b 00		 mov	 eax, DWORD PTR [eax]
  00028	89 45 f8	 mov	 DWORD PTR __Wherenode$[ebp], eax

; 2046 : 		_Nodeptr _Pnode = _Wherenode->_Parent;

  0002b	8b 4d f8	 mov	 ecx, DWORD PTR __Wherenode$[ebp]
  0002e	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  00031	89 55 f4	 mov	 DWORD PTR __Pnode$[ebp], edx
$LN2@Lbound:

; 2047 : 
; 2048 : 		while (!_Pnode->_Isnil)

  00034	8b 45 f4	 mov	 eax, DWORD PTR __Pnode$[ebp]
  00037	0f be 48 0d	 movsx	 ecx, BYTE PTR [eax+13]
  0003b	85 c9		 test	 ecx, ecx
  0003d	75 3b		 jne	 SHORT $LN3@Lbound

; 2049 : 			{
; 2050 : 			if (_Compare(this->_Key(_Pnode), _Keyval))

  0003f	8b 55 08	 mov	 edx, DWORD PTR __Keyval$[ebp]
  00042	52		 push	 edx
  00043	8b 45 f4	 mov	 eax, DWORD PTR __Pnode$[ebp]
  00046	50		 push	 eax
  00047	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0004a	e8 00 00 00 00	 call	 ?_Key@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEABIPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Key
  0004f	50		 push	 eax
  00050	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00053	e8 00 00 00 00	 call	 ?_Compare@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBE_NABI0@Z ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Compare
  00058	0f b6 c8	 movzx	 ecx, al
  0005b	85 c9		 test	 ecx, ecx
  0005d	74 0b		 je	 SHORT $LN4@Lbound

; 2051 : 				{
; 2052 : 				_Pnode = _Pnode->_Right;	// descend right subtree

  0005f	8b 55 f4	 mov	 edx, DWORD PTR __Pnode$[ebp]
  00062	8b 42 08	 mov	 eax, DWORD PTR [edx+8]
  00065	89 45 f4	 mov	 DWORD PTR __Pnode$[ebp], eax

; 2053 : 				}
; 2054 : 			else

  00068	eb 0e		 jmp	 SHORT $LN5@Lbound
$LN4@Lbound:

; 2055 : 				{	// _Pnode not less than _Keyval, remember it
; 2056 : 				_Wherenode = _Pnode;

  0006a	8b 4d f4	 mov	 ecx, DWORD PTR __Pnode$[ebp]
  0006d	89 4d f8	 mov	 DWORD PTR __Wherenode$[ebp], ecx

; 2057 : 				_Pnode = _Pnode->_Left;	// descend left subtree

  00070	8b 55 f4	 mov	 edx, DWORD PTR __Pnode$[ebp]
  00073	8b 02		 mov	 eax, DWORD PTR [edx]
  00075	89 45 f4	 mov	 DWORD PTR __Pnode$[ebp], eax
$LN5@Lbound:

; 2058 : 				}
; 2059 : 			}

  00078	eb ba		 jmp	 SHORT $LN2@Lbound
$LN3@Lbound:

; 2060 : 
; 2061 : 		return (_Wherenode);	// return best remembered candidate

  0007a	8b 45 f8	 mov	 eax, DWORD PTR __Wherenode$[ebp]

; 2062 : 		}

  0007d	83 c4 0c	 add	 esp, 12			; 0000000cH
  00080	3b ec		 cmp	 ebp, esp
  00082	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00087	8b e5		 mov	 esp, ebp
  00089	5d		 pop	 ebp
  0008a	c2 04 00	 ret	 4
??$_Lbound@I@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBEPAU?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@1@ABI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Lbound<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\map
;	COMDAT ??$_Kfn@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@SAABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@@Z
_TEXT	SEGMENT
__Val$ = 8						; size = 4
??$_Kfn@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@SAABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@@Z PROC ; std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0>::_Kfn<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >, COMDAT

; 70   : 		{	// extract key from element value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 71   : 		return (_Val.first);

  00003	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]

; 72   : 		}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$_Kfn@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@SAABIABU?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@1@@Z ENDP ; std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0>::_Kfn<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ PROC ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data, COMDAT
; _this$ = ecx

; 1019 : 		{	// return const reference to _Tree_val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1020 : 		return (_Mypair._Get_second()._Get_second());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second
  00016	8b c8		 mov	 ecx, eax
  00018	e8 00 00 00 00	 call	 ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second

; 1021 : 		}

  0001d	83 c4 04	 add	 esp, 4
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	8b e5		 mov	 esp, ebp
  00029	5d		 pop	 ebp
  0002a	c3		 ret	 0
?_Get_data@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ENDP ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Get_data
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?_Compare@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBE_NABI0@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Left$ = 8						; size = 4
__Right$ = 12						; size = 4
?_Compare@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBE_NABI0@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Compare, COMDAT
; _this$ = ecx

; 2031 : 		{	// compare key_type to key_type, with debug checks

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 2032 : 		return (_DEBUG_LT_PRED(this->_Getcomp(), _Left, _Right));

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Right$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR __Left$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABU?$less@I@2@XZ ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp
  0001e	8b c8		 mov	 ecx, eax
  00020	e8 00 00 00 00	 call	 ??R?$less@I@std@@QBE_NABI0@Z ; std::less<unsigned int>::operator()

; 2033 : 		}

  00025	83 c4 04	 add	 esp, 4
  00028	3b ec		 cmp	 ebp, esp
  0002a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002f	8b e5		 mov	 esp, ebp
  00031	5d		 pop	 ebp
  00032	c2 08 00	 ret	 8
?_Compare@?$_Tree@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@IBE_NABI0@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Compare
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ PROC ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second, COMDAT
; _this$ = ecx

; 306  : 		{	// return const reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 307  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 308  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABV?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@XZ ENDP ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ PROC ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second, COMDAT
; _this$ = ecx

; 306  : 		{	// return const reference to second

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 307  : 		return (_Myval2);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 308  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_second@?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@std@@QBEABV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@XZ ENDP ; std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>::_Get_second
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xtree
;	COMDAT ?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABU?$less@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABU?$less@I@2@XZ PROC ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp, COMDAT
; _this$ = ecx

; 999  : 		{	// return const reference to ordering predicate

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1000 : 		return (_Mypair._Get_first());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABU?$less@I@2@XZ ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first

; 1001 : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
?_Getcomp@?$_Tree_comp_alloc@V?$_Tmap_traits@IV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@U?$less@I@2@V?$allocator@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@2@$0A@@std@@@std@@QBEABU?$less@I@2@XZ ENDP ; std::_Tree_comp_alloc<std::_Tmap_traits<unsigned int,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > >,0> >::_Getcomp
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xutility
;	COMDAT ?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABU?$less@I@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABU?$less@I@2@XZ PROC ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first, COMDAT
; _this$ = ecx

; 296  : 		{	// return const reference to first

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 297  : 		return (*this);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 298  : 		}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?_Get_first@?$_Compressed_pair@U?$less@I@std@@V?$_Compressed_pair@V?$allocator@U?$_Tree_node@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@PAX@std@@@std@@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$function@$$A6AJPAUHWND__@@IIJAAH@Z@std@@@std@@@std@@@2@$00@2@$00@std@@QBEABU?$less@I@2@XZ ENDP ; std::_Compressed_pair<std::less<unsigned int>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> >,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,std::function<long __cdecl(HWND__ *,unsigned int,unsigned int,long,int &)> > > >,1>,1>::_Get_first
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$forward@J@std@@YA$$QAJAAJ@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$forward@J@std@@YA$$QAJAAJ@Z PROC			; std::forward<long>, COMDAT

; 1573 : 	{	// forward an lvalue as either an lvalue or an rvalue

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1574 : 	return (static_cast<_Ty&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1575 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$forward@J@std@@YA$$QAJAAJ@Z ENDP			; std::forward<long>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$forward@AAH@std@@YAAAHAAH@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$forward@AAH@std@@YAAAHAAH@Z PROC			; std::forward<int &>, COMDAT

; 1573 : 	{	// forward an lvalue as either an lvalue or an rvalue

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1574 : 	return (static_cast<_Ty&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1575 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$forward@AAH@std@@YAAAHAAH@Z ENDP			; std::forward<int &>
_TEXT	ENDS
END
