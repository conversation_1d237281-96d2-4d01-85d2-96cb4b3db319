# Copyright 2019 the V8 project authors. All rights reserved.
# Copyright 2016 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/compiler/compiler.gni")

template("split_static_library") {
  assert(defined(invoker.split_count),
         "Must define split_count for split_static_library")

  # In many conditions the number of inputs will be 1 (because the
  # count will be conditional on platform or configuration) and for
  # some build configurations it's unnecessary to split libraries
  # since the tooling will never create files of a problematic size.
  if (invoker.split_count == 1 || use_lld) {
    static_library(target_name) {
      forward_variables_from(invoker, "*")
    }
  } else {
    group_name = target_name

    generated_static_libraries = []
    current_library_index = 0
    foreach(current_sources, split_list(invoker.sources, invoker.split_count)) {
      current_name = "${target_name}_$current_library_index"
      assert(
          current_sources != [],
          "Your values for splitting a static library generate one that has no sources.")
      generated_static_libraries += [ ":$current_name" ]

      static_library(current_name) {
        # Generated static library shard gets everything but sources (which
        # we're redefining) and visibility (which is set to be the group
        # below).
        forward_variables_from(invoker,
                               "*",
                               [
                                 "check_includes",
                                 "sources",
                                 "visibility",
                               ])
        sources = current_sources
        visibility = [ ":$group_name" ]

        # When splitting a target's sources up into a series of static
        # libraries, those targets will naturally include headers from each
        # other arbitrarily. We could theoretically generate a web of
        # dependencies and allow_circular_includes_from between all pairs of
        # targets, but that's very cumbersome. Typical usage in Chrome is that
        # only official Windows builds use split static libraries due to the
        # Visual Studio size limits, and this means we'll still get header
        # checking coverage for the other configurations.
        check_includes = false

        # Uniquify the output name if one is specified.
        if (defined(invoker.output_name)) {
          output_name = "${invoker.output_name}_$current_library_index"
        }
      }

      current_library_index = current_library_index + 1
    }

    group(group_name) {
      public_deps = generated_static_libraries
      forward_variables_from(invoker,
                             [
                               "testonly",
                               "visibility",
                             ])
    }
  }
}

set_defaults("split_static_library") {
  configs = default_compiler_configs
}
