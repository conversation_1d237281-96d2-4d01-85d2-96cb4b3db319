// Copyright 2020 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

function __f_1( __v_9) {
  return arguments;
}

var __v_48 = [];
var __v_49 = __f_1();
var __v_50 = 3;
Object.preventExtensions(__v_49);
function __f_7(__v_52, __v_53, __v_54) {
  __v_52[__v_53] =
  __v_54;
}
__v_48.__proto__ = __v_49;
for (var __v_51 = 0; __v_51 < __v_50; __v_51++) {
  __f_7(__v_48, __v_51);
}
