// Copyright 2021 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-typed-array-gen.h'

namespace typed_array {
const kBuiltinNameFindLast: constexpr string =
    '%TypedArray%.prototype.findLast';

// https://tc39.es/proposal-array-find-from-last/index.html#sec-%typedarray%.prototype.findlast
transitioning macro FindLastAllElements(implicit context: Context)(
    attachedArrayAndLength: typed_array::AttachedJSTypedArrayAndLength,
    predicate: Callable, thisArg: JSAny): JSAny {
  let witness =
      typed_array::NewAttachedJSTypedArrayWitness(attachedArrayAndLength.array);
  // 5. Let k be len - 1.
  // 6. Repeat, while k ≥ 0
  for (let k: uintptr = attachedArrayAndLength.length; k-- > 0;) {
    // 6a. Let Pk be ! ToString(𝔽(k)).
    // There is no need to cast ToString to load elements.

    // 6b. Let kValue be ! Get(O, Pk).
    // kValue must be undefined when the buffer was detached.
    let value: JSAny;
    try {
      witness.RecheckIndex(k) otherwise goto IsDetachedOrOutOfBounds;
      value = witness.Load(k);
    } label IsDetachedOrOutOfBounds deferred {
      value = Undefined;
    }

    // 6c. Let testResult be ! ToBoolean(? Call(predicate, thisArg, « kValue,
    // 𝔽(k), O »)).
    // TODO(v8:4153): Consider versioning this loop for Smi and non-Smi
    // indices to optimize Convert<Number>(k) for the most common case.
    const result = Call(
        context, predicate, thisArg, value, Convert<Number>(k),
        witness.GetStable());
    // 6d. If testResult is true, return kValue.
    if (ToBoolean(result)) {
      return value;
    }

    // 6e. Set k to k - 1. (done by the loop).
  }

  // 7. Return undefined.
  return Undefined;
}

// https://tc39.es/proposal-array-find-from-last/index.html#sec-%typedarray%.prototype.findlast
transitioning javascript builtin
TypedArrayPrototypeFindLast(
    js-implicit context: NativeContext, receiver: JSAny)(...arguments): JSAny {
  // arguments[0] = predicate
  // arguments[1] = thisArg
  try {
    // 1. Let O be the this value.
    // 2. Perform ? ValidateTypedArray(O).
    // 3. Let len be IntegerIndexedObjectLength(O).
    const array: JSTypedArray = Cast<JSTypedArray>(receiver)
        otherwise NotTypedArray;
    const attachedArrayAndLength = EnsureAttachedAndReadLength(array)
        otherwise IsDetachedOrOutOfBounds;
    // 4. If IsCallable(predicate) is false, throw a TypeError exception.
    const predicate = Cast<Callable>(arguments[0]) otherwise NotCallable;
    const thisArg = arguments[1];
    return FindLastAllElements(attachedArrayAndLength, predicate, thisArg);
  } label NotCallable deferred {
    ThrowTypeError(MessageTemplate::kCalledNonCallable, arguments[0]);
  } label NotTypedArray deferred {
    ThrowTypeError(MessageTemplate::kNotTypedArray, kBuiltinNameFindLast);
  } label IsDetachedOrOutOfBounds deferred {
    ThrowTypeError(MessageTemplate::kDetachedOperation, kBuiltinNameFindLast);
  }
}
}
