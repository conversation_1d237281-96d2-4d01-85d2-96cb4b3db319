﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Microsoft.CppBuild.targets(1216,5): warning MSB8012: TargetPath(E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\miniblink.dll) 与 Linker 的 OutputFile 属性值(E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\out\Debug\miniblink_d.dll)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Microsoft.CppBuild.targets(1218,5): warning MSB8012: TargetName(miniblink) 与 Linker 的 OutputFile 属性值(miniblink_d)不匹配。这可能导致项目生成不正确。若要更正此问题，请确保 $(OutDir)、$(TargetName) 和 $(TargetExt) 属性值与 %(Link.OutputFile) 中指定的值匹配。
harfbuzz.lib(hb-blob.obj) : warning LNK4075: 忽略“/EDITANDCONTINUE”(由于“/INCREMENTAL:NO”规范)
    正在创建库 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\node.lib 和对象 E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\node.exp
node.exp : warning LNK4070: .EXP 中的 /OUT:miniblink_4975_x32.dll 指令与输出文件名“E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\miniblink_d.dll”不同；忽略指令
  miniblink.vcxproj -> E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\miniblink.dll
