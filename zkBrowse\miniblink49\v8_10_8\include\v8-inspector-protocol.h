// Copyright 2016 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef V8_V8_INSPECTOR_PROTOCOL_H_
#define V8_V8_INSPECTOR_PROTOCOL_H_

#include "inspector/Debugger.h"  // NOLINT(build/include_directory)
#include "inspector/Runtime.h"   // NOLINT(build/include_directory)
#include "inspector/Schema.h"    // NOLINT(build/include_directory)
#include "v8-inspector.h"        // NOLINT(build/include_directory)

#endif  // V8_V8_INSPECTOR_PROTOCOL_H_
