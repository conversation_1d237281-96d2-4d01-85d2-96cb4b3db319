﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  WebFrameClientImpl.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\node\nodeblink.h(14): warning C4005: “USING_V8_SHARED”: 宏重定义
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\config.cpp: note: 参见“USING_V8_SHARED”的前一个定义
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\content\browser\webframeclientimpl.cpp(828): error C2712: 无法在要求对象展开的函数中使用 __try
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\content\browser\webframeclientimpl.cpp(862): error C2712: 无法在要求对象展开的函数中使用 __try
