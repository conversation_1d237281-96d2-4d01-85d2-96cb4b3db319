﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  thread_local_storage.cc
  thread_local_storage_win.cc
  OrigChromeMgr.cpp
  ffmpeg_common.cc
  ffmpeg_demuxer.cc
  ffmpeg_video_decoder.cc
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_video_decoder.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_demuxer.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\content\OrigChromeMgr.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\ffmpeg\ffmpeg_common.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(282): warning C4244: “参数”: 从“double”转换到“int64”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(295): warning C4996: 'av_init_packet': 被声明为已否决
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\ffmpeg\libavcodec\packet.h(655): note: 参见“av_init_packet”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(60): warning C4244: “参数”: 从“double”转换到“int64”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1042): warning C4244: “return”: 从“int64_t”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1049): warning C4244: “+=”: 从“int64_t”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1066): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1067): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1068): warning C4244: “return”: 从“double”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1551): warning C4996: 'av_init_packet': 被声明为已否决
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\ffmpeg\libavcodec\packet.h(655): note: 参见“av_init_packet”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1620): warning C4996: 'av_init_packet': 被声明为已否决
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\ffmpeg\libavcodec\packet.h(655): note: 参见“av_init_packet”的声明
  orig_chrome.vcxproj -> E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Release\orig_chrome.lib
