; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\childweb\webviewmanager.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
_BSS	ENDS
$SG4294626971 DB 00H
$SG4294628784 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294628778 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628779 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294628776 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628777 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294628782 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294628783 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294628780 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628781 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294628770 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294628771 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294628768 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294628769 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294628774 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628775 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628772 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628773 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294628762 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294628763 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294628760 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294628761 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628766 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294628767 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294628764 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294628765 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294628754 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628755 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628752 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628753 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628758 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628759 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628756 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628757 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628746 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628747 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628744 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628745 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628750 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628751 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294628748 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628749 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628738 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628739 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628736 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628737 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628742 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628743 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628740 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628741 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628730 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294628731 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628728 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628729 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628734 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294628735 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294628732 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628733 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294628722 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294628723 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628720 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294628721 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294628726 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628727 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628724 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628725 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294628714 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294628715 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628712 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628713 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628718 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628719 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628716 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628717 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294628706 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628707 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628704 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628705 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628710 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294628711 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294628708 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628709 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294628698 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294628699 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628696 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294628697 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294628702 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628703 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628700 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628701 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628690 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294628691 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294628688 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294628689 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294628694 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294628695 DB 00H, 00H
	ORG $+2
$SG4294628692 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294628693 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294628682 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294628683 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294628680 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294628681 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294628686 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294628687 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294628684 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294628685 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294628674 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294628675 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294628672 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294628673 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294628678 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294628679 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294628676 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294628677 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294628666 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294628667 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294628664 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294628665 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294628670 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294628671 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294628668 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294628669 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294628658 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294628659 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294628656 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294628657 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294628662 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294628663 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294628660 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294628661 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294628650 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294628651 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294628648 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294628649 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294628654 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628655 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294628652 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294628653 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294628642 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294628643 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294628640 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294628641 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294628646 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294628647 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294628644 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294628645 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628634 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628635 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628632 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628633 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628638 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628639 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294628636 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628637 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294628626 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294628627 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294628624 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628625 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294628630 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628631 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628628 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628629 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294628618 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294628619 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294628616 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294628617 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294628622 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628623 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628620 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294628621 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628610 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294628611 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294628608 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628609 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294628614 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294628615 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294628612 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294628613 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294628602 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628603 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628600 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294628601 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294628606 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628607 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294628604 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294628605 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294628594 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294628595 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628592 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294628593 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628598 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294628599 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294628596 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294628597 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294628586 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294628587 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294628584 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294628585 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628590 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294628591 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294628588 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294628589 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294628578 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628579 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294628576 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294628577 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628582 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294628583 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294628580 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294628581 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294628570 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294628571 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628568 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294628569 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294628574 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294628575 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294628572 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628573 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294628562 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628563 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294628560 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294628561 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294628566 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628567 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628564 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294628565 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294628554 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294628555 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294628552 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294628553 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294628558 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628559 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294628556 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294628557 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294628546 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294628547 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294628544 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294628545 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294628550 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294628551 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294628548 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294628549 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294628538 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294628539 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294628536 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294628537 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294628542 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294628543 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294628540 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294628541 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294628530 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628531 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294628528 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294628529 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294628534 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628535 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628532 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294628533 DB 00H, 00H
	ORG $+2
$SG4294628522 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294628523 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294628520 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294628521 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294628526 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294628527 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294628524 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294628525 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294628514 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294628515 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294628512 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628513 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294628518 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294628519 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294628516 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294628517 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294628506 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628507 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294628504 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628505 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628510 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628511 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628508 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294628509 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294628498 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294628499 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628496 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294628497 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628502 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628503 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294628500 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294628501 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294628490 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628491 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294628488 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294628489 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628494 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294628495 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294628492 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294628493 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294628482 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294628483 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294628480 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294628481 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294628486 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294628487 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628484 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294628485 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294628474 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294628475 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294628472 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294628473 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628478 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294628479 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294628476 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294628477 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294628466 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294628467 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294628464 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294628465 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294628470 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294628471 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294628468 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294628469 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294628458 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294628459 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294628456 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294628457 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294628462 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294628463 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294628460 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294628461 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294628450 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294628451 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628448 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294628449 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628454 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294628455 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294628452 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294628453 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628442 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294628443 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628440 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294628441 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294628446 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294628447 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294628444 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294628445 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628434 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628435 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294628432 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628433 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628438 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294628439 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294628436 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628437 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294628426 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294628427 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294628424 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294628425 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628430 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294628431 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294628428 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628429 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628418 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294628419 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628416 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294628417 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628422 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294628423 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294628420 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294628421 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294628410 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628411 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294628408 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628409 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294628414 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628415 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294628412 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628413 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294628402 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294628403 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294628400 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628401 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294628406 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628407 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628404 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628405 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294628394 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294628395 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628392 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628393 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628398 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294628399 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294628396 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294628397 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294628386 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294628387 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294628384 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294628385 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294628390 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628391 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294628388 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294628389 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628378 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294628379 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628376 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628377 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628382 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294628383 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628380 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294628381 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294628370 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628371 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294628368 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628369 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628374 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294628375 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628372 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294628373 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628362 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628363 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628360 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294628361 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294628366 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294628367 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294628364 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294628365 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294628354 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628355 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294628352 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628353 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628358 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628359 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294628356 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628357 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294628346 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628347 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294628344 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628345 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628350 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628351 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628348 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294628349 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628338 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294628339 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294628336 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294628337 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294628342 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628343 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628340 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294628341 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294628330 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294628331 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628328 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294628329 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294628334 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294628335 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628332 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628333 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294628322 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294628323 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294628320 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294628321 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628326 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294628327 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294628324 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294628325 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294628314 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294628315 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294628312 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628313 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628318 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294628319 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294628316 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294628317 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294628306 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294628307 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294628310 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294628311 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294628308 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294628309 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294628272 DB 'M', 00H, 00H, 00H
$SG4294628273 DB 'S', 00H, 00H, 00H
$SG4294628266 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628267 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628264 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294628265 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294628270 DB 'B', 00H, 00H, 00H
$SG4294628271 DB 'D', 00H, 00H, 00H
$SG4294628268 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294628269 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294628258 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294628259 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628256 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294628257 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294628262 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628263 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294628260 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628261 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294628254 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294628255 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294628222 DB 00H, 00H
	ORG $+2
$SG4294628223 DB ':', 00H, 00H, 00H
$SG4294628221 DB 00H, 00H
	ORG $+2
$SG4294628130 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294627416 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294627417 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294626970 DB '\', 00H, 'z', 00H, 'h', 00H, 'u', 00H, 'a', 00H, 'n', 00H
	DB	'k', 00H, 'L', 00H, 'o', 00H, 'g', 00H, '.', 00H, 't', 00H, 'x'
	DB	00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294626968 DB '%', 00H, '0', 00H, '2', 00H, 'd', 00H, '-', 00H, '%', 00H
	DB	'0', 00H, '2', 00H, 'd', 00H, ' ', 00H, '%', 00H, '0', 00H, '2'
	DB	00H, 'd', 00H, ':', 00H, '%', 00H, '0', 00H, '2', 00H, 'd', 00H
	DB	':', 00H, '%', 00H, '0', 00H, '2', 00H, 'd', 00H, ' ', 00H, '%'
	DB	00H, 's', 00H, 0aH, 00H, 00H, 00H
	ORG $+2
$SG4294626969 DB 'C', 00H
	ORG $+2
$SG4294626963 DB 'CloseIEWebrowser', 00H
	ORG $+3
$SG4294626966 DB 00H, 00H
	ORG $+2
$SG4294626967 DB 0aH, 00H, 00H, 00H
$SG4294626964 DB 'AddIEWebrowser', 00H
	ORG $+1
$SG4294626965 DB 'i', 00H, 'e', 00H, 'p', 00H, 'r', 00H, 'o', 00H, 'x', 00H
	DB	'y', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H, 00H, 00H
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
PUBLIC	?Init@CWebViewTool@@QAEHXZ			; CWebViewTool::Init
PUBLIC	?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z	; CWebViewTool::AddWebView
PUBLIC	??1CWebViewTool@@QAE@XZ				; CWebViewTool::~CWebViewTool
PUBLIC	??0CWebViewTool@@QAE@XZ				; CWebViewTool::CWebViewTool
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
__ehfuncinfo$?Init@CWebViewTool@@QAEHXZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?Init@CWebViewTool@@QAEHXZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?Init@CWebViewTool@@QAEHXZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?Init@CWebViewTool@@QAEHXZ$0
__ehfuncinfo$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z$0
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\childweb\webviewmanager.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0CWebViewTool@@QAE@XZ PROC				; CWebViewTool::CWebViewTool
; _this$ = ecx

; 6    : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 5    : CWebViewTool::CWebViewTool(void) : m_pAddIEWebrowser(NULL),m_pCloseIEWebrowser(NULL)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0
  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	c7 41 04 00 00
	00 00		 mov	 DWORD PTR [ecx+4], 0

; 7    : 	m_pBrowserThread = NULL;

  00021	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00024	c7 42 08 00 00
	00 00		 mov	 DWORD PTR [edx+8], 0

; 8    : }

  0002b	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002e	8b e5		 mov	 esp, ebp
  00030	5d		 pop	 ebp
  00031	c3		 ret	 0
??0CWebViewTool@@QAE@XZ ENDP				; CWebViewTool::CWebViewTool
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\childweb\webviewmanager.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
??1CWebViewTool@@QAE@XZ PROC				; CWebViewTool::~CWebViewTool
; _this$ = ecx

; 11   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 12   : }

  0000e	8b e5		 mov	 esp, ebp
  00010	5d		 pop	 ebp
  00011	c3		 ret	 0
??1CWebViewTool@@QAE@XZ ENDP				; CWebViewTool::~CWebViewTool
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\childweb\webviewmanager.cpp
_TEXT	SEGMENT
tv78 = -52						; size = 4
$T2 = -48						; size = 4
$T3 = -44						; size = 16
$T4 = -28						; size = 4
$T5 = -24						; size = 4
_pTemp$6 = -20						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_sJson$ = 8						; size = 4
_hWndMgr$ = 12						; size = 4
?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z PROC	; CWebViewTool::AddWebView
; _this$ = ecx

; 31   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 28	 sub	 esp, 40			; 00000028H
  0001b	56		 push	 esi
  0001c	57		 push	 edi
  0001d	51		 push	 ecx
  0001e	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  00021	b9 0a 00 00 00	 mov	 ecx, 10			; 0000000aH
  00026	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002b	f3 ab		 rep stosd
  0002d	59		 pop	 ecx
  0002e	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 32   : 	if(!m_pBrowserThread)

  00031	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00034	83 78 08 00	 cmp	 DWORD PTR [eax+8], 0
  00038	0f 85 a4 00 00
	00		 jne	 $LN2@AddWebView

; 33   : 	{
; 34   : 		//MessageBox(NULL, NULL, NULL, NULL);
; 35   : 		m_pBrowserThread = new BrowserThread();

  0003e	6a 4c		 push	 76			; 0000004cH
  00040	e8 00 00 00 00	 call	 ??2@YAPAXI@Z		; operator new
  00045	83 c4 04	 add	 esp, 4
  00048	89 45 e4	 mov	 DWORD PTR $T4[ebp], eax
  0004b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00052	83 7d e4 00	 cmp	 DWORD PTR $T4[ebp], 0
  00056	74 0d		 je	 SHORT $LN5@AddWebView
  00058	8b 4d e4	 mov	 ecx, DWORD PTR $T4[ebp]
  0005b	e8 00 00 00 00	 call	 ??0BrowserThread@@QAE@XZ ; BrowserThread::BrowserThread
  00060	89 45 cc	 mov	 DWORD PTR tv78[ebp], eax
  00063	eb 07		 jmp	 SHORT $LN6@AddWebView
$LN5@AddWebView:
  00065	c7 45 cc 00 00
	00 00		 mov	 DWORD PTR tv78[ebp], 0
$LN6@AddWebView:
  0006c	8b 4d cc	 mov	 ecx, DWORD PTR tv78[ebp]
  0006f	89 4d e8	 mov	 DWORD PTR $T5[ebp], ecx
  00072	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00079	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0007c	8b 45 e8	 mov	 eax, DWORD PTR $T5[ebp]
  0007f	89 42 08	 mov	 DWORD PTR [edx+8], eax

; 36   : 		m_pBrowserThread->m_jsonParam = sJson;

  00082	8b 4d 08	 mov	 ecx, DWORD PTR _sJson$[ebp]
  00085	51		 push	 ecx
  00086	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00089	8b 4a 08	 mov	 ecx, DWORD PTR [edx+8]
  0008c	83 c1 28	 add	 ecx, 40			; 00000028H
  0008f	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=

; 37   : 		m_pBrowserThread->m_hWndManger = hWndMgr;

  00094	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00097	8b 48 08	 mov	 ecx, DWORD PTR [eax+8]
  0009a	8b 55 0c	 mov	 edx, DWORD PTR _hWndMgr$[ebp]
  0009d	89 51 20	 mov	 DWORD PTR [ecx+32], edx

; 38   : 		m_pBrowserThread->m_rcIE = CRect(1, 100, 1000, 1000);

  000a0	68 e8 03 00 00	 push	 1000			; 000003e8H
  000a5	68 e8 03 00 00	 push	 1000			; 000003e8H
  000aa	6a 64		 push	 100			; 00000064H
  000ac	6a 01		 push	 1
  000ae	8d 4d d4	 lea	 ecx, DWORD PTR $T3[ebp]
  000b1	e8 00 00 00 00	 call	 ??0CRect@@QAE@HHHH@Z	; CRect::CRect
  000b6	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000b9	8b 51 08	 mov	 edx, DWORD PTR [ecx+8]
  000bc	83 c2 0c	 add	 edx, 12			; 0000000cH
  000bf	8b 08		 mov	 ecx, DWORD PTR [eax]
  000c1	89 0a		 mov	 DWORD PTR [edx], ecx
  000c3	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  000c6	89 4a 04	 mov	 DWORD PTR [edx+4], ecx
  000c9	8b 48 08	 mov	 ecx, DWORD PTR [eax+8]
  000cc	89 4a 08	 mov	 DWORD PTR [edx+8], ecx
  000cf	8b 40 0c	 mov	 eax, DWORD PTR [eax+12]
  000d2	89 42 0c	 mov	 DWORD PTR [edx+12], eax

; 39   : 		m_pBrowserThread->Start_Thread();

  000d5	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000d8	8b 49 08	 mov	 ecx, DWORD PTR [ecx+8]
  000db	e8 00 00 00 00	 call	 ?Start_Thread@CBaseThread@@QAEXXZ ; CBaseThread::Start_Thread

; 40   : 	}
; 41   : 	else

  000e0	eb 55		 jmp	 SHORT $LN1@AddWebView
$LN2@AddWebView:

; 42   : 	{
; 43   : //#ifndef MulitProcess
; 44   : //		m_pBrowserThread->m_jsonParam = sJson;
; 45   : 		char* pTemp = new char[strlen(sJson) + 1];

  000e2	8b 55 08	 mov	 edx, DWORD PTR _sJson$[ebp]
  000e5	52		 push	 edx
  000e6	e8 00 00 00 00	 call	 _strlen
  000eb	83 c4 04	 add	 esp, 4
  000ee	83 c0 01	 add	 eax, 1
  000f1	50		 push	 eax
  000f2	e8 00 00 00 00	 call	 ??_U@YAPAXI@Z		; operator new[]
  000f7	83 c4 04	 add	 esp, 4
  000fa	89 45 d0	 mov	 DWORD PTR $T2[ebp], eax
  000fd	8b 45 d0	 mov	 eax, DWORD PTR $T2[ebp]
  00100	89 45 ec	 mov	 DWORD PTR _pTemp$6[ebp], eax

; 46   : 		strcpy(pTemp, sJson);

  00103	8b 4d 08	 mov	 ecx, DWORD PTR _sJson$[ebp]
  00106	51		 push	 ecx
  00107	8b 55 ec	 mov	 edx, DWORD PTR _pTemp$6[ebp]
  0010a	52		 push	 edx
  0010b	e8 00 00 00 00	 call	 _strcpy
  00110	83 c4 08	 add	 esp, 8

; 47   : 		PostThreadMessage(m_pBrowserThread->m_nTheadID, WM_NEW_CHILDVIEW,(WPARAM) pTemp, 0);

  00113	8b f4		 mov	 esi, esp
  00115	6a 00		 push	 0
  00117	8b 45 ec	 mov	 eax, DWORD PTR _pTemp$6[ebp]
  0011a	50		 push	 eax
  0011b	68 d7 0b 00 00	 push	 3031			; 00000bd7H
  00120	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00123	8b 51 08	 mov	 edx, DWORD PTR [ecx+8]
  00126	8b 42 08	 mov	 eax, DWORD PTR [edx+8]
  00129	50		 push	 eax
  0012a	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__PostThreadMessageW@16
  00130	3b f4		 cmp	 esi, esp
  00132	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@AddWebView:

; 48   : // 		if(m_pAddIEWebrowser)
; 49   : // 			m_pAddIEWebrowser(sJson,hWndMgr);
; 50   : //#else
; 51   : // 	BrowserThread* pBrowserThread = new BrowserThread();
; 52   : // 	pBrowserThread->m_jsonParam = sJson;
; 53   : // 	pBrowserThread->m_hWndManger = hWndMgr;
; 54   : // 	pBrowserThread->m_rcIE = CRect(1, 100, 1000, 1000);
; 55   : // 	pBrowserThread->Start_Thread();
; 56   : ////#endif	
; 57   : 	}
; 58   : 
; 59   : }

  00137	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0013a	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00141	5f		 pop	 edi
  00142	5e		 pop	 esi
  00143	83 c4 34	 add	 esp, 52			; 00000034H
  00146	3b ec		 cmp	 ebp, esp
  00148	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0014d	8b e5		 mov	 esp, ebp
  0014f	5d		 pop	 ebp
  00150	c2 08 00	 ret	 8
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z$0:
  00000	6a 4c		 push	 76			; 0000004cH
  00002	8b 45 e4	 mov	 eax, DWORD PTR $T4[ebp]
  00005	50		 push	 eax
  00006	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  0000b	83 c4 08	 add	 esp, 8
  0000e	c3		 ret	 0
__ehhandler$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z:
  0000f	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z
  00014	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?AddWebView@CWebViewTool@@QAEXPBDPAUHWND__@@@Z ENDP	; CWebViewTool::AddWebView
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\childweb\webviewmanager.cpp
_TEXT	SEGMENT
$T2 = -60						; size = 4
$T3 = -56						; size = 4
_hIEDll$ = -52						; size = 4
_sDll$ = -44						; size = 24
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
?Init@CWebViewTool@@QAEHXZ PROC				; CWebViewTool::Init
; _this$ = ecx

; 15   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?Init@CWebViewTool@@QAEHXZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 30	 sub	 esp, 48			; 00000030H
  0001b	56		 push	 esi
  0001c	57		 push	 edi
  0001d	51		 push	 ecx
  0001e	8d 7d c4	 lea	 edi, DWORD PTR [ebp-60]
  00021	b9 0c 00 00 00	 mov	 ecx, 12			; 0000000cH
  00026	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002b	f3 ab		 rep stosd
  0002d	59		 pop	 ecx
  0002e	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 16   : 	std::wstring sDll = L"";

  00031	68 00 00 00 00	 push	 OFFSET $SG4294626966
  00036	8d 4d d4	 lea	 ecx, DWORD PTR _sDll$[ebp]
  00039	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@QB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0003e	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 17   : 	get_app_path_w(sDll);

  00045	6a 00		 push	 0
  00047	8d 45 d4	 lea	 eax, DWORD PTR _sDll$[ebp]
  0004a	50		 push	 eax
  0004b	e8 00 00 00 00	 call	 ?get_app_path_w@@YA_NAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@PAUHINSTANCE__@@@Z ; get_app_path_w
  00050	83 c4 08	 add	 esp, 8

; 18   : 	sDll += L"ieproxy.dll";

  00053	68 00 00 00 00	 push	 OFFSET $SG4294626965
  00058	8d 4d d4	 lea	 ecx, DWORD PTR _sDll$[ebp]
  0005b	e8 00 00 00 00	 call	 ??Y?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV01@QB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator+=

; 19   : 	HMODULE hIEDll = LoadLibraryW(sDll.c_str());

  00060	8d 4d d4	 lea	 ecx, DWORD PTR _sDll$[ebp]
  00063	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEPB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  00068	8b f4		 mov	 esi, esp
  0006a	50		 push	 eax
  0006b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__LoadLibraryW@4
  00071	3b f4		 cmp	 esi, esp
  00073	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00078	89 45 cc	 mov	 DWORD PTR _hIEDll$[ebp], eax

; 20   : 	if (!hIEDll)

  0007b	83 7d cc 00	 cmp	 DWORD PTR _hIEDll$[ebp], 0
  0007f	75 1b		 jne	 SHORT $LN2@Init

; 21   : 		return FALSE;

  00081	c7 45 c8 00 00
	00 00		 mov	 DWORD PTR $T3[ebp], 0
  00088	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0008f	8d 4d d4	 lea	 ecx, DWORD PTR _sDll$[ebp]
  00092	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00097	8b 45 c8	 mov	 eax, DWORD PTR $T3[ebp]
  0009a	eb 54		 jmp	 SHORT $LN1@Init
$LN2@Init:

; 22   : 
; 23   : 	m_pAddIEWebrowser = (pAddIEWebrowser)GetProcAddress(hIEDll,"AddIEWebrowser");

  0009c	8b f4		 mov	 esi, esp
  0009e	68 00 00 00 00	 push	 OFFSET $SG4294626964
  000a3	8b 4d cc	 mov	 ecx, DWORD PTR _hIEDll$[ebp]
  000a6	51		 push	 ecx
  000a7	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetProcAddress@8
  000ad	3b f4		 cmp	 esi, esp
  000af	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b4	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  000b7	89 02		 mov	 DWORD PTR [edx], eax

; 24   : 	m_pCloseIEWebrowser = (pCloseIEWebrowser)GetProcAddress(hIEDll,"CloseIEWebrowser");

  000b9	8b f4		 mov	 esi, esp
  000bb	68 00 00 00 00	 push	 OFFSET $SG4294626963
  000c0	8b 45 cc	 mov	 eax, DWORD PTR _hIEDll$[ebp]
  000c3	50		 push	 eax
  000c4	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetProcAddress@8
  000ca	3b f4		 cmp	 esi, esp
  000cc	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000d1	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000d4	89 41 04	 mov	 DWORD PTR [ecx+4], eax

; 25   : 
; 26   : 
; 27   : 	return TRUE;

  000d7	c7 45 c4 01 00
	00 00		 mov	 DWORD PTR $T2[ebp], 1
  000de	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000e5	8d 4d d4	 lea	 ecx, DWORD PTR _sDll$[ebp]
  000e8	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000ed	8b 45 c4	 mov	 eax, DWORD PTR $T2[ebp]
$LN1@Init:

; 28   : }

  000f0	52		 push	 edx
  000f1	8b cd		 mov	 ecx, ebp
  000f3	50		 push	 eax
  000f4	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN8@Init
  000fa	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000ff	58		 pop	 eax
  00100	5a		 pop	 edx
  00101	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00104	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0010b	5f		 pop	 edi
  0010c	5e		 pop	 esi
  0010d	83 c4 3c	 add	 esp, 60			; 0000003cH
  00110	3b ec		 cmp	 ebp, esp
  00112	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00117	8b e5		 mov	 esp, ebp
  00119	5d		 pop	 ebp
  0011a	c3		 ret	 0
  0011b	90		 npad	 1
$LN8@Init:
  0011c	01 00 00 00	 DD	 1
  00120	00 00 00 00	 DD	 $LN7@Init
$LN7@Init:
  00124	d4 ff ff ff	 DD	 -44			; ffffffd4H
  00128	18 00 00 00	 DD	 24			; 00000018H
  0012c	00 00 00 00	 DD	 $LN5@Init
$LN5@Init:
  00130	73		 DB	 115			; 00000073H
  00131	44		 DB	 68			; 00000044H
  00132	6c		 DB	 108			; 0000006cH
  00133	6c		 DB	 108			; 0000006cH
  00134	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?Init@CWebViewTool@@QAEHXZ$0:
  00000	8d 4d d4	 lea	 ecx, DWORD PTR _sDll$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__ehhandler$?Init@CWebViewTool@@QAEHXZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?Init@CWebViewTool@@QAEHXZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?Init@CWebViewTool@@QAEHXZ ENDP				; CWebViewTool::Init
END
