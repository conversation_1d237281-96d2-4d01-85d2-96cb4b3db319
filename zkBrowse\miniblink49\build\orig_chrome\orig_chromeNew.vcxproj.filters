﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="src">
      <UniqueIdentifier>{eecfdd9c-b73d-4330-844c-fced1bbd0822}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc">
      <UniqueIdentifier>{f52980dd-ed90-4614-b1cc-4562865028cf}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\animation">
      <UniqueIdentifier>{3b7dbd82-20ee-4856-a48e-d5e597bcefc7}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\base">
      <UniqueIdentifier>{f0219319-770e-4c0d-990f-98bd83158a55}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\blink">
      <UniqueIdentifier>{3c240769-5cd3-4374-b701-c9503142df73}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\debug">
      <UniqueIdentifier>{b8819da1-a6fb-442a-90f7-f34ffedfceab}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\input">
      <UniqueIdentifier>{3f539901-b57c-4341-b599-8efa07b9bf96}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\layers">
      <UniqueIdentifier>{e1ccfdb8-6735-466b-b085-d11614f78624}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\output">
      <UniqueIdentifier>{b7e52fce-7d5c-46af-8e84-fd93619c73b7}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\playback">
      <UniqueIdentifier>{59da220e-4401-4370-baeb-ce22937959f5}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\proto">
      <UniqueIdentifier>{89380994-d870-4530-a64b-b953712bd5c3}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\quads">
      <UniqueIdentifier>{ec966564-4373-4d17-9c2b-a98999d565aa}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\raster">
      <UniqueIdentifier>{00a1c5de-0030-4bea-b5fe-fe98851ba079}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\resources">
      <UniqueIdentifier>{1450f0f1-1a1a-42a3-a0d7-e468f008743c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\scheduler">
      <UniqueIdentifier>{a7d0db59-cb6b-4a77-9a9f-ac85d44be958}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\surfaces">
      <UniqueIdentifier>{f3cdb6cf-6603-41e6-a0d0-ae7161237028}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\tiles">
      <UniqueIdentifier>{20385a46-b7a8-4502-9527-65b5298901f0}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\cc\trees">
      <UniqueIdentifier>{fc063cdf-aac8-4e64-9c8e-074ddfed40cc}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base">
      <UniqueIdentifier>{8a798948-2ea6-4495-b17c-01326c515681}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\containers">
      <UniqueIdentifier>{ccc33350-bd22-434e-800b-afa470de0bb6}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\debug">
      <UniqueIdentifier>{42523d66-e077-4a5d-b652-d365cfcef405}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\files">
      <UniqueIdentifier>{2c0b026d-6a9f-420f-b36c-6804f83f7073}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\memory">
      <UniqueIdentifier>{c147ebd5-b71c-450a-9c86-e1b1466a2620}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\message_loop">
      <UniqueIdentifier>{8909ed9b-ab07-4c37-887f-7b88fd9fd012}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\numerics">
      <UniqueIdentifier>{1fd68c21-247a-4832-ab8a-453f4a8e6ec4}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\process">
      <UniqueIdentifier>{2af5e9fd-bce1-471c-9305-6bbb8495cec0}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\strings">
      <UniqueIdentifier>{fb0af51c-692b-4a56-807f-175257564b94}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\synchronization">
      <UniqueIdentifier>{739d5978-468f-4f02-a54d-b4e2f3ffc044}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\threading">
      <UniqueIdentifier>{63df4669-fe6c-48b0-bdbf-0aba46f00fb3}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\time">
      <UniqueIdentifier>{39260a96-2051-4543-90f5-856e5b84436a}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\timer">
      <UniqueIdentifier>{c6cb0d43-23a7-43ef-8f79-820d4bf88b7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\win">
      <UniqueIdentifier>{dde058cf-ab4f-44d8-8c63-75db5bb09298}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\content">
      <UniqueIdentifier>{0acadd60-230c-434e-a34d-83e0520091f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\content\compositor">
      <UniqueIdentifier>{2d8e8b8b-e079-43c4-9d6a-d1abf0662151}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\trace_event">
      <UniqueIdentifier>{ed3ee61f-594d-4fdc-a268-819633be70d1}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ui">
      <UniqueIdentifier>{74d12bc7-214d-49d4-87fa-cc33639c89b1}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ui\gfx">
      <UniqueIdentifier>{682601ec-9b36-485c-858d-0a462947c43c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ui\gfx\geometry">
      <UniqueIdentifier>{eed3478d-add4-4658-94f2-e65b9fc6713c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\profiler">
      <UniqueIdentifier>{03b9abad-9843-4d4c-aefa-3eb7829ebbfe}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ui\event">
      <UniqueIdentifier>{4cfd1831-326a-4cef-b32f-41de87990e09}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu">
      <UniqueIdentifier>{f51bd566-0527-439e-a609-b9eafd1c50f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\command_buffer">
      <UniqueIdentifier>{a958f746-35d2-49ec-93f4-484fa630aa4d}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\command_buffer\common">
      <UniqueIdentifier>{ed3cfe33-ecfb-450c-adcb-40dc085ed060}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ui\gfx\animation">
      <UniqueIdentifier>{9e37b910-be90-4608-a420-9b8c3fb801fc}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\command_buffer\client">
      <UniqueIdentifier>{49ed8bed-e7cc-41c5-9460-24c550d5cfa6}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\GLES2">
      <UniqueIdentifier>{d9a90e73-e25a-4db6-952c-f7310123fac2}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\ui\gl">
      <UniqueIdentifier>{032f8f53-4853-4acd-a85c-15c0def7dea3}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\command_buffer\service">
      <UniqueIdentifier>{ee163b2d-12ad-4d19-9a8b-6065f17415db}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\config">
      <UniqueIdentifier>{4b3e244a-0c6b-4e7f-89f0-603acb7e8682}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\content\gpu">
      <UniqueIdentifier>{f80bf4c0-f87a-4b1a-8354-34608a0f3fc6}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\blink">
      <UniqueIdentifier>{0f59a559-0045-49da-81fc-7c8f69c13b8e}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\gpu\skia_bindings">
      <UniqueIdentifier>{a318033f-3160-4685-99dd-6b04a595dd7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media">
      <UniqueIdentifier>{c4b2485b-99bf-41f2-bf06-5dfe3d445e7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\base">
      <UniqueIdentifier>{ce87eeab-573b-401b-b094-c323f4d6772d}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\base\simd">
      <UniqueIdentifier>{3d0a5355-4110-430c-8b1c-854c526ec0af}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\base\win">
      <UniqueIdentifier>{a725ab48-5101-49b5-97d6-59209433882d}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\audio">
      <UniqueIdentifier>{1529088a-9498-4999-802f-c12824894ad8}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\audio\sounds">
      <UniqueIdentifier>{88ba6139-45db-4570-8d1d-1d420ef72c95}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\audio\win">
      <UniqueIdentifier>{f81b0ab7-7f4b-4e88-9436-378fb5d6893c}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\blink">
      <UniqueIdentifier>{eef5f353-c404-404e-bb7d-e2a54ac5e3cf}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\ffmpeg">
      <UniqueIdentifier>{975453e3-e43c-4b53-ad5f-be400c152c40}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\filters">
      <UniqueIdentifier>{6b3f94e9-4774-4348-a827-b5b50ba62c7f}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\formats">
      <UniqueIdentifier>{423e0d8a-d238-433e-8d39-1941a3bba460}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\formats\common">
      <UniqueIdentifier>{48d646c0-ad03-4ec3-93cc-299d263428ce}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\formats\mp2t">
      <UniqueIdentifier>{e46684f7-edf1-4b9b-848a-44a6f8212bdd}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\formats\mp4">
      <UniqueIdentifier>{b31f8ccd-36fe-4229-8ad0-3aeee34bb07f}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\formats\mpeg">
      <UniqueIdentifier>{4161e699-3e42-4e76-8a23-0a5f9c1b6ac1}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\formats\webm">
      <UniqueIdentifier>{026e4caf-b5bb-4e63-a534-e8656438b4d5}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\renderers">
      <UniqueIdentifier>{fa21f207-756b-46e4-8cef-599726ee97f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\video">
      <UniqueIdentifier>{0dd1c3cd-0fd1-44be-8205-a2d917b016f2}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\media\cdm">
      <UniqueIdentifier>{2cc66960-6d9b-42e6-93df-a5299ff09ab0}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\content\meida">
      <UniqueIdentifier>{63b35a82-1193-4ff2-8128-e8546ed80404}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\third_party">
      <UniqueIdentifier>{139dc0a4-ac6b-433f-a82f-a989133ea542}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\base\third_party\nspr">
      <UniqueIdentifier>{ee77a4e5-2bde-42f5-adee-f64af15d253e}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\orig_chrome\cc\animation\transform_operation.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\transform_operations.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_cc.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_curve.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_events.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_host.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_id_provider.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_player.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_registrar.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\animation_timeline.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\element_animations.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\keyframed_animation_curve.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\layer_animation_controller.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\scroll_offset_animation_curve.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\scrollbar_animation_controller.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\scrollbar_animation_controller_linear_fade.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\scrollbar_animation_controller_thinning.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\animation\timing_function.cc">
      <Filter>src\cc\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\delayed_unique_notifier.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\invalidation_region.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\list_container_helper.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\math_util.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\region_base_cc.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\rolling_time_delta_history.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\rtree.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\simple_enclosed_region.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\switches_base.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\tiling_data.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\base\unique_notifier.cc">
      <Filter>src\cc\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_transform_animation_curve_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_transform_operations_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\scrollbar_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_animation_curve_common.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_animation_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_compositor_animation_player_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_compositor_animation_timeline_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_compositor_support_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_content_layer_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_display_item_list_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_external_bitmap_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_external_texture_layer_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_filter_animation_curve_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_filter_operations_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_float_animation_curve_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_image_layer_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_layer_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_layer_impl_fixed_bounds.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_nine_patch_layer_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_scroll_offset_animation_curve_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_scrollbar_layer_impl.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\blink\web_to_cc_animation_delegate_adapter.cc">
      <Filter>src\cc\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\frame_rate_counter.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\frame_timing_request.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\frame_timing_tracker.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\frame_viewer_instrumentation.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\invalidation_benchmark.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\lap_timer.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\layer_tree_debug_state.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\micro_benchmark.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\micro_benchmark_controller.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\micro_benchmark_controller_impl.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\micro_benchmark_impl.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\paint_time_counter.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\picture_debug_util.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\picture_record_benchmark.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\rasterize_and_record_benchmark.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\rasterize_and_record_benchmark_impl.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\rendering_stats.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\rendering_stats_instrumentation.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\traced_display_item_list.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\traced_value.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\unittest_only_benchmark.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\unittest_only_benchmark_impl.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\benchmark_instrumentation.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\debug_colors.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\debug\debug_rect_history.cc">
      <Filter>src\cc\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\input\scroll_elasticity_helper.cc">
      <Filter>src\cc\input</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\input\scroll_state.cc">
      <Filter>src\cc\input</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\input\top_controls_manager.cc">
      <Filter>src\cc\input</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\input\input_handler.cc">
      <Filter>src\cc\input</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\input\layer_selection_bound.cc">
      <Filter>src\cc\input</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\input\page_scale_animation.cc">
      <Filter>src\cc\input</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\viewport.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\delegated_frame_provider.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\delegated_frame_resource_collection.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\delegated_renderer_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\delegated_renderer_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\draw_properties.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\heads_up_display_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\heads_up_display_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\io_surface_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\io_surface_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\layer_position_constraint.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\layer_utils.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\nine_patch_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\nine_patch_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\painted_scrollbar_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\painted_scrollbar_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\picture_image_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\picture_image_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\picture_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\picture_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\render_surface_draw_properties.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\render_surface_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\scrollbar_layer_impl_base.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\solid_color_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\solid_color_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\solid_color_scrollbar_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\solid_color_scrollbar_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\surface_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\surface_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\texture_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\texture_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\ui_resource_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\ui_resource_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\begin_frame_args.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\bsp_tree.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\bsp_walk_action.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\compositor_frame.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\compositor_frame_ack.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\compositor_frame_metadata.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\context_provider.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\copy_output_request.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\copy_output_result.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\delegated_frame_data.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\delegating_renderer.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\direct_renderer.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\dynamic_geometry_binding.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\filter_operation.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\filter_operations.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\geometry_binding.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\gl_frame_data.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\gl_renderer_draw_cache.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\latency_info_swap_promise.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\layer_quad.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\managed_memory_policy.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\output_surface.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\overlay_candidate.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\overlay_processor.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\overlay_strategy_all_or_nothing.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\overlay_strategy_common.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\overlay_strategy_sandwich.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\overlay_strategy_single_on_top.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\overlay_strategy_underlay.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\program_binding.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\render_surface_filters.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\renderer.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\renderer_capabilities.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\renderer_settings.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\shader.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\software_frame_data.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\software_output_device.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\software_renderer.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\static_geometry_binding.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\texture_mailbox_deleter.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\viewport_selection_bound.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\raster_source_helper.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\transform_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\clip_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\clip_path_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\compositing_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\discardable_image_map.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\display_item_list.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\display_item_list_bounds_calculator.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\display_item_list_settings.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\display_list_raster_source.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\display_list_recording_source.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\drawing_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\filter_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\float_clip_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\largest_display_item.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\playback\picture.cc">
      <Filter>src\cc\playback</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\tile_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\yuv_video_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\content_draw_quad_base.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\debug_border_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\draw_polygon.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\io_surface_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\largest_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\picture_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\render_pass.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\render_pass_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\render_pass_id.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\shared_quad_state.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\solid_color_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\stream_video_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\surface_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\quads\texture_draw_quad.cc">
      <Filter>src\cc\quads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\tile_task_worker_pool.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\zero_copy_tile_task_worker_pool.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\bitmap_tile_task_worker_pool.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\gpu_rasterizer.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\gpu_tile_task_worker_pool.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\one_copy_tile_task_worker_pool.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\raster_buffer.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\scoped_gpu_raster.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\task_graph_runner.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\texture_compressor.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\texture_compressor_etc1.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\texture_compressor_etc1_sse.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\raster\tile_task_runner.cc">
      <Filter>src\cc\raster</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\ui_resource_bitmap.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\ui_resource_request.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\memory_history.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\resource_format.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\resource_pool.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\resource_provider.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\scoped_resource.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\scoped_ui_resource.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\shared_bitmap.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\single_release_callback.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\single_release_callback_impl.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\texture_mailbox.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\transferable_resource.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\scheduler\scheduler_state_machine.cc">
      <Filter>src\cc\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\scheduler\begin_frame_source.cc">
      <Filter>src\cc\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\scheduler\begin_frame_tracker.cc">
      <Filter>src\cc\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\scheduler\compositor_timing_history.cc">
      <Filter>src\cc\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\scheduler\delay_based_time_source.cc">
      <Filter>src\cc\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\scheduler\scheduler.cc">
      <Filter>src\cc\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\scheduler\scheduler_settings.cc">
      <Filter>src\cc\scheduler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface_manager.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface_resource_holder.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\display.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\display_scheduler.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\onscreen_display_client.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface_aggregator.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface_display_output_surface.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface_factory.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface_hittest.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\surfaces\surface_id_allocator.cc">
      <Filter>src\cc\surfaces</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\tiling_set_raster_queue_all.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\tiling_set_raster_queue_required.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\eviction_tile_priority_queue.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\image_decode_controller.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\picture_layer_tiling.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\picture_layer_tiling_set.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\prioritized_tile.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\raster_tile_priority_queue.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\raster_tile_priority_queue_all.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\raster_tile_priority_queue_required.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\tile.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\tile_draw_info.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\tile_manager.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\tile_priority.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\tiles\tiling_set_eviction_queue.cc">
      <Filter>src\cc\tiles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\task_runner_provider.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\thread_proxy.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\threaded_channel.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\tree_synchronizer.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\blocking_task_runner.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\damage_tracker.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\draw_property_utils.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\latency_info_swap_promise_monitor.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\layer_tree_host.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\layer_tree_host_common.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\layer_tree_host_impl.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\layer_tree_impl.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\layer_tree_settings.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\occlusion.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\occlusion_tracker.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\property_tree.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\property_tree_builder.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\proxy.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\proxy_common.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\single_thread_proxy.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\trees\swap_promise_monitor.cc">
      <Filter>src\cc\trees</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\task_annotator.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\alias.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\asan_invalid_access.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\debugger.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\debugger_win.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\dump_without_crashing.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\gdi_debug_util_win.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\profiler.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\debug\stack_trace.cc">
      <Filter>src\base\debug</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\aligned_memory.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\discardable_memory.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\discardable_memory_allocator.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\discardable_shared_memory.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\memory_pressure_listener.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\memory_pressure_monitor.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\memory_pressure_monitor_win.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\ref_counted.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\ref_counted_memory.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\shared_memory_handle_win.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\shared_memory_win.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\singleton.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\memory\weak_ptr.cc">
      <Filter>src\base\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\message_loop\incoming_task_queue.cc">
      <Filter>src\base\message_loop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\message_loop\message_loop.cc">
      <Filter>src\base\message_loop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\message_loop\message_loop_task_runner.cc">
      <Filter>src\base\message_loop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\message_loop\message_pump.cc">
      <Filter>src\base\message_loop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\message_loop\message_pump_default.cc">
      <Filter>src\base\message_loop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\message_loop\message_pump_win.cc">
      <Filter>src\base\message_loop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\utf_string_conversions.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\nullable_string16.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\pattern_base.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\safe_sprintf.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\string_piece.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\string_split.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\string_util.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\string_util_constants.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\stringprintf.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\sys_string_conversions_win.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\utf_offset_string_conversions.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\synchronization\waitable_event_win.cc">
      <Filter>src\base\synchronization</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\synchronization\cancellation_flag.cc">
      <Filter>src\base\synchronization</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\synchronization\condition_variable_win.cc">
      <Filter>src\base\synchronization</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\synchronization\lock.cc">
      <Filter>src\base\synchronization</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\synchronization\lock_impl_win.cc">
      <Filter>src\base\synchronization</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\synchronization\waitable_event_watcher_win.cc">
      <Filter>src\base\synchronization</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread_checker_impl.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread_collision_warner.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread_id_name_manager.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread_local_storage.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread_local_storage_win.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread_local_win.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread_restrictions.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\watchdog.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\worker_pool.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\worker_pool_win.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\non_thread_safe_impl.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\platform_thread_win.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\post_task_and_reply_impl.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\sequenced_task_runner_handle.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\sequenced_worker_pool.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\simple_thread.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\threading\thread.cc">
      <Filter>src\base\threading</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\time\default_tick_clock.cc">
      <Filter>src\base\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\time\tick_clock.cc">
      <Filter>src\base\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\time\clock_base.cc">
      <Filter>src\base\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\time\default_clock.cc">
      <Filter>src\base\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\timer\hi_res_timer_manager_win.cc">
      <Filter>src\base\timer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\timer\mock_timer.cc">
      <Filter>src\base\timer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\timer\timer_base.cc">
      <Filter>src\base\timer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\timer\elapsed_timer.cc">
      <Filter>src\base\timer</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\wrapped_window_proc.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\enum_variant.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\iat_patch_function.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\iunknown_impl.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\message_window.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\object_watcher.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\registry.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\resource_util.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\scoped_bstr.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\scoped_process_information.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\win_util.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\windows_version.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\barrier_closure.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\base_paths.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\base_switches.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\big_endian.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\bind_helpers.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\build_time.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\callback_helpers.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\callback_internal.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\check_example.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\cpu.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\deferred_sequenced_task_runner.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\hash.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\lazy_instance.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\location_base.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\logging_win.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\native_library_win.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\path_service.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\pending_task.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\pickle.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\rand_util.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\rand_util_win.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\run_loop.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\scoped_native_library.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\sequence_checker_impl.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\sequenced_task_runner.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\supports_user_data.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\task_runner.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\thread_task_runner_handle.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\value_conversions.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\RasterWorkerPool.cpp">
      <Filter>src\content</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\RenderWidgetCompositor.cpp">
      <Filter>src\content</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\WebSharedBitmapManager.cpp">
      <Filter>src\content</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\OrigChromeMgr.cpp">
      <Filter>src\content</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\LayerTreeWrap.cpp">
      <Filter>src\content</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\logging_base.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\skia_util.cc">
      <Filter>src\ui\gfx</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\transform.cc">
      <Filter>src\ui\gfx</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\transform_util.cc">
      <Filter>src\ui\gfx</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\cubic_bezier.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\dip_util.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\insets.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\insets_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\matrix3_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\point.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\point_conversions.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\point_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\point3_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\quad_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\rect_conversions.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\rect_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\rect_gfx.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\scroll_offset.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\size.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\size_conversions.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\size_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\vector2d.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\vector2d_conversions.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\vector2d_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\vector3d_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\geometry\box_f.cc">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\at_exit.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\utf_string_conversion_utils.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\profiler\test_support_library.cc">
      <Filter>src\base\profiler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\animation\tween.cc">
      <Filter>src\ui\gfx\animation</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\display_gfx.cc">
      <Filter>src\ui\gfx</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\buffer_format_util.cc">
      <Filter>src\ui\gfx</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\switches.cc">
      <Filter>src\ui\gfx</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\process\process_handle.cc">
      <Filter>src\base\process</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\process\process_handle_win.cc">
      <Filter>src\base\process</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\events\Latency_info_empty.cc">
      <Filter>src\ui\event</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\win\scoped_handle.cc">
      <Filter>src\base\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\trace_event\trace_empty.cc">
      <Filter>src\base\trace_event</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\tracking_info.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\tracked_objects.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\profiler\tracked_time.cc">
      <Filter>src\base\profiler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\profiler\alternate_timer.cc">
      <Filter>src\base\profiler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\buffer_tracker.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\client_context_state.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\cmd_buffer_helper.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\fenced_allocator.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gl_in_process_context.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_c_lib.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_cmd_helper.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_implementation.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_interface_stub.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_lib.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_trace_implementation.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gpu_memory_buffer_manager.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\mapped_memory.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\program_info_manager.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\query_tracker.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\ring_buffer.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\share_group.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\transfer_buffer.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\vertex_array_object_manager.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\angle_platform_impl.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\egl_util.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_egl.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_gl.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_context.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_context_egl.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_context_stub.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_context_stub_with_extensions.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_context_win.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_egl_api_implementation.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_enums.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_fence.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_fence_apple.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_fence_arb.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_fence_egl.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_fence_nv.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_gl_api_implementation.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_image_egl.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_image_memory.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_image_ref_counted_memory.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_image_shared_memory.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_image_stub.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_implementation.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_implementation_win.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_share_group.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_state_restorer.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_surface.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_surface_egl.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_surface_overlay.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_surface_stub.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_surface_win.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_switches.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_version_info.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gpu_switching_manager.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gpu_timing.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\scoped_api.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\scoped_binders.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\scoped_make_current.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\sync_control_vsync_provider.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\trace_util.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\vsync_provider_win.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_bindings.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\error_state.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\feature_info.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\framebuffer_completeness_cache.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\framebuffer_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gl_context_virtual.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gl_state_restorer_impl.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_clear_framebuffer.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_copy_texture_chromium.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_decoder.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_validation.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_control_service.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_scheduler.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_state_tracer.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_tracer.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\id_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\image_factory.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\image_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\in_process_command_buffer.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\logger.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\mailbox_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\mailbox_manager_impl.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\mailbox_manager_sync.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\path_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\program_cache.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\program_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\query_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\renderbuffer_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\shader_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\shader_translator.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\shader_translator_cache.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\sync_point_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\texture_definition.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\texture_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\transfer_buffer_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\valuebuffer_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\vertex_array_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\vertex_attrib_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_delegate.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_egl.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_idle.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_share_group.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_stub.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_sync.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_win.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\buffer_manager.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\cmd_parser.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\command_buffer_service.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\common_decoder.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\context_group.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\context_state.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\value_state.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\buffer.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\capabilities.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\cmd_buffer_common.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\debug_marker_manager.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_format.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_utils.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\id_allocator.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\mailbox.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\common\mailbox_holder.cc">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\City.cpp">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\output\gl_renderer.cc">
      <Filter>src\cc\output</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\compositor\SoftwareOutputSurface.cpp">
      <Filter>src\content\compositor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\gpu\ContextProviderCommandBuffer.cpp">
      <Filter>src\content\gpu</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\gpu\GrcontextForWebgraphicscontext3d.cpp">
      <Filter>src\content\gpu</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\compositor\GpuOutputSurface.cpp">
      <Filter>src\content\compositor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\blink\webgraphicscontext3d_in_process_command_buffer_impl.cc">
      <Filter>src\gpu\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\blink\webgraphicscontext3d_impl.cc">
      <Filter>src\gpu\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\files\file_path.cc">
      <Filter>src\base\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\base\files\file_path_constants.cc">
      <Filter>src\base\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gfx\gpu_memory_buffer.cc">
      <Filter>src\ui\gfx</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\ui\gl\gl_surface_osmesa.cc">
      <Filter>src\ui\gl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\skia_bindings\gl_bindings_skia_cmd_buffer.cc">
      <Filter>src\gpu\skia_bindings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\sys_info.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\gpu\CommandBufferMetrics.cpp">
      <Filter>src\content\gpu</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\sys_info_win.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_service_switches.cc">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\command_buffer\client\gpu_client_switches.cc">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\files\file_util_win_small.cc">
      <Filter>src\base\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\gpu\config\gpu_config_switches.cc">
      <Filter>src\gpu\config</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\gpu\ChildGpuMemoryBufferManager.cpp">
      <Filter>src\content\gpu</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\compositor\EmptyOutputSurface.cpp">
      <Filter>src\content\compositor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\compositor\SoftwareOutputDevice.cpp">
      <Filter>src\content\compositor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_buffer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_buffer_converter.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_buffer_queue.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_bus.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_converter.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_decoder.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_decoder_config.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_discard_helper.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_fifo.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_hardware_config.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_hash.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_pull_fifo.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_renderer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_renderer_mixer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_renderer_mixer_input.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_shifter.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_splicer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_timestamp_helper.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_video_metadata_extractor.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\bit_reader.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\bit_reader_core.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\bitstream_buffer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\byte_queue.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\cdm_callback_promise.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\cdm_context.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\cdm_factory.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\cdm_initialized_promise.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\cdm_key_information.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\cdm_promise.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\cdm_promise_adapter.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\channel_layout.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\channel_mixer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\channel_mixing_matrix.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\container_names.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\data_buffer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\data_source.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\decoder_buffer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\decoder_buffer_queue.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\decrypt_config.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\decryptor.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\demuxer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\demuxer_stream.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\demuxer_stream_provider.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\djb2.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\key_system_info.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\key_systems.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\key_systems_support_uma.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_client.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_file_checker.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_keys.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_log.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_permission.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_resources.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_switches.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\media_util.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\mime_util.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\moving_average.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\multi_channel_resampler.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\null_video_sink.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\pipeline.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\player_tracker.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\ranges.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\renderer_factory.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\sample_format.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\seekable_buffer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\serial_runner.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\sinc_resampler.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\stream_parser.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\stream_parser_buffer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\text_cue.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\text_ranges.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\text_renderer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\text_track_config.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\time_delta_interpolator.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\vector_math.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_capture_types.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_capturer_source.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_decoder.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_decoder_config.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_frame.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_frame_metadata.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_frame_pool.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_renderer.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_types.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\video_util.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\wall_clock_time_source.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\yuv_convert.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\audio_block_fifo.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\simd\convert_rgb_to_yuv_sse2.cc">
      <Filter>src\media\base\simd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\simd\convert_rgb_to_yuv_ssse3.cc">
      <Filter>src\media\base\simd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\simd\convert_yuv_to_rgb_c.cc">
      <Filter>src\media\base\simd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\simd\convert_yuv_to_rgb_x86.cc">
      <Filter>src\media\base\simd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\simd\filter_yuv_c.cc">
      <Filter>src\media\base\simd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\simd\filter_yuv_sse2.cc">
      <Filter>src\media\base\simd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\simd\convert_rgb_to_yuv_c.cc">
      <Filter>src\media\base\simd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\sounds\sounds_manager.cc">
      <Filter>src\media\audio\sounds</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\sounds\wav_audio_handler.cc">
      <Filter>src\media\audio\sounds</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\sounds\audio_stream_handler.cc">
      <Filter>src\media\audio\sounds</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\audio_low_latency_output_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\audio_manager_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\avrt_wrapper_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\core_audio_util_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\device_enumeration_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\wavein_input_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\waveout_output_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\win\audio_device_listener_win.cc">
      <Filter>src\media\audio\win</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\sample_rates.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\scoped_task_runner_observer.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\simple_sources.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\virtual_audio_input_stream.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\virtual_audio_output_stream.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_device_name.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_device_thread.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_input_controller.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_input_device.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_input_ipc.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_manager.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_manager_base.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_controller.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_device.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_dispatcher.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_dispatcher_impl.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_ipc.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_proxy.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_resampler.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_output_stream_sink.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_parameters.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\audio_power_monitor.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\clockless_audio_sink.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\fake_audio_input_stream.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\fake_audio_log_factory.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\fake_audio_manager.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\fake_audio_output_stream.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\fake_audio_worker.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\null_audio_sink.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\buffered_data_source.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\buffered_data_source_host_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\buffered_resource_loader.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\cache_util.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\cdm_result_promise_helper.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\cdm_session_adapter.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\encrypted_media_player_support.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\key_system_config_selector.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\multibuffer.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\new_session_cdm_result_promise.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\texttrack_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\video_frame_compositor.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\webaudiosourceprovider_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\webencryptedmediaclient_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\webinbandtexttrack_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\webmediaplayer_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\webmediaplayer_params.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\webmediaplayer_util.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\webmediasource_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\websourcebuffer_impl.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\blink\active_loader.cc">
      <Filter>src\media\blink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\ffmpeg\ffmpeg_common.cc">
      <Filter>src\media\ffmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\audio_file_reader.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\audio_renderer_algorithm.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\blocking_url_protocol.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\chunk_demuxer.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\decoder_selector.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\decoder_stream.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\decoder_stream_traits.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\decrypting_audio_decoder.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\decrypting_demuxer_stream.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\decrypting_video_decoder.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\default_media_permission.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ffmpeg_audio_decoder.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ffmpeg_demuxer.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ffmpeg_glue.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ffmpeg_h264_to_annex_b_bitstream_converter.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ffmpeg_video_decoder.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\file_data_source.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\frame_processor.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\gpu_video_decoder.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\h264_bit_reader.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\h264_parser.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\h264_to_annex_b_bitstream_converter.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\in_memory_url_protocol.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ivf_parser.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\jpeg_parser.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\source_buffer_platform.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\source_buffer_range.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\source_buffer_stream.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\stream_parser_factory.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\video_cadence_estimator.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\video_renderer_algorithm.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\vp8_bool_decoder.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\vp8_parser.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\vp9_parser.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\vp9_raw_bits_reader.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\wsola_internals.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\audio_clock.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\common\offset_byte_queue.cc">
      <Filter>src\media\formats\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\es_adapter_video.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\es_parser.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\es_parser_adts.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\es_parser_h264.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\es_parser_mpeg1audio.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\mp2t_stream_parser.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\timestamp_unroller.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\ts_packet.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\ts_section_pat.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\ts_section_pes.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\ts_section_pmt.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp2t\ts_section_psi.cc">
      <Filter>src\media\formats\mp2t</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\avc.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\bitstream_converter.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\box_definitions.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\box_reader.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\cenc.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\es_descriptor.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\mp4_stream_parser.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\sample_to_group_iterator.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\track_run_iterator.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\aac.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mpeg\adts_stream_parser.cc">
      <Filter>src\media\formats\mpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mpeg\mpeg_audio_stream_parser_base.cc">
      <Filter>src\media\formats\mpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mpeg\mpeg1_audio_stream_parser.cc">
      <Filter>src\media\formats\mpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mpeg\adts_constants.cc">
      <Filter>src\media\formats\mpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_cluster_parser.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_constants.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_content_encodings.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_content_encodings_client.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_crypto_helpers.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_info_parser.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_parser.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_stream_parser.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_tracks_parser.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_video_client.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_webvtt_parser.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\webm\webm_audio_client.cc">
      <Filter>src\media\formats\webm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\renderers\renderer_impl.cc">
      <Filter>src\media\renderers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\renderers\skcanvas_video_renderer.cc">
      <Filter>src\media\renderers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\renderers\video_renderer_impl.cc">
      <Filter>src\media\renderers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\renderers\audio_renderer_impl.cc">
      <Filter>src\media\renderers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\renderers\default_renderer_factory.cc">
      <Filter>src\media\renderers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\video\h264_poc.cc">
      <Filter>src\media\video</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\video\jpeg_decode_accelerator.cc">
      <Filter>src\media\video</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\video\video_decode_accelerator.cc">
      <Filter>src\media\video</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\video\video_encode_accelerator.cc">
      <Filter>src\media\video</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\video\fake_video_encode_accelerator.cc">
      <Filter>src\media\video</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\base\renderer_media.cc">
      <Filter>src\media\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\video\picture_video.cc">
      <Filter>src\media\video</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\audio\point_audio.cc">
      <Filter>src\media\audio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\video_layer_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\video_layer.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\latin1_string_conversions.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\guid_win.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\guid.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\strings\string_number_conversions.cc">
      <Filter>src\base\strings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\cdm\key_system_names.cc">
      <Filter>src\media\cdm</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\resources\video_resource_updater.cc">
      <Filter>src\cc\resources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\cc\layers\video_frame_provider_client_impl.cc">
      <Filter>src\cc\layers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\formats\mp4\hevc.cc">
      <Filter>src\media\formats\mp4</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\h265_parser.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\media\filters\ffmpeg_h265_to_annex_b_bitstream_converter.cc">
      <Filter>src\media\filters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\media\audio_renderer_mixer_manager.cc">
      <Filter>src\content\meida</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\media\audio_device_factory.cc">
      <Filter>src\content\meida</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\sync_socket_win.cc">
      <Filter>src\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\process\process_win.cc">
      <Filter>src\base\process</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\base\third_party\nspr\prtime.cc">
      <Filter>src\base\third_party\nspr</Filter>
    </ClCompile>
    <ClCompile Include="..\..\orig_chrome\content\media\renderer_webaudiodevice_impl.cc">
      <Filter>src\content\meida</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\orig_chrome\cc\animation\transform_operation.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\transform_operations.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_curve.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_delegate.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_events.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_host.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_id_provider.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_player.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_registrar.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\animation_timeline.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\element_animations.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\keyframed_animation_curve.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\layer_animation_controller.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\layer_animation_event_observer.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\layer_animation_value_observer.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\layer_animation_value_provider.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\scroll_offset_animation_curve.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\scrollbar_animation_controller.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\scrollbar_animation_controller_linear_fade.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\scrollbar_animation_controller_thinning.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\animation\timing_function.h">
      <Filter>src\cc\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\delayed_unique_notifier.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\histograms.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\invalidation_region.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\list_container.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\list_container_helper.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\math_util.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\random_access_list_container.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\region.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\resource_id.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\rolling_time_delta_history.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\rtree.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\scoped_ptr_algorithm.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\scoped_ptr_deque.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\scoped_ptr_vector.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\sidecar_list_container.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\simple_enclosed_region.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\switches.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\synced_property.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\tiling_data.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\time_util.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\unique_notifier.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\cc_export.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\base\completion_event.h">
      <Filter>src\cc\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_to_cc_animation_delegate_adapter.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_transform_animation_curve_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_transform_operations_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\context_provider_web_context.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\scrollbar_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_animation_curve_common.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_animation_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_blend_mode.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_compositor_animation_player_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_compositor_animation_timeline_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_compositor_support_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_content_layer_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_display_item_list_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_external_bitmap_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_external_texture_layer_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_filter_animation_curve_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_filter_operations_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_float_animation_curve_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_image_layer_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_layer_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_layer_impl_fixed_bounds.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_nine_patch_layer_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_scroll_offset_animation_curve_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\blink\web_scrollbar_layer_impl.h">
      <Filter>src\cc\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\devtools_instrumentation.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\frame_rate_counter.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\frame_timing_request.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\frame_timing_tracker.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\frame_viewer_instrumentation.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\invalidation_benchmark.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\lap_timer.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\layer_tree_debug_state.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\micro_benchmark.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\micro_benchmark_controller.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\micro_benchmark_controller_impl.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\micro_benchmark_impl.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\paint_time_counter.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\picture_debug_util.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\picture_record_benchmark.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\rasterize_and_record_benchmark.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\rasterize_and_record_benchmark_impl.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\rendering_stats.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\rendering_stats_instrumentation.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\ring_buffer.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\traced_display_item_list.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\traced_picture.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\traced_value.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\unittest_only_benchmark.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\unittest_only_benchmark_impl.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\benchmark_instrumentation.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\debug_colors.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\debug\debug_rect_history.h">
      <Filter>src\cc\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\page_scale_animation.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\scroll_elasticity_helper.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\scroll_state.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\scrollbar.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\selection.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\selection_bound_type.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\top_controls_manager.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\top_controls_manager_client.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\top_controls_state.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\input_handler.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\layer_scroll_offset_delegate.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\input\layer_selection_bound.h">
      <Filter>src\cc\input</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\video_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\video_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\viewport.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\append_quads_data.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\content_layer_client.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\delegated_frame_provider.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\delegated_frame_resource_collection.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\delegated_renderer_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\delegated_renderer_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\draw_properties.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\heads_up_display_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\heads_up_display_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\io_surface_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\io_surface_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\layer_client.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\layer_iterator.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\layer_lists.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\layer_position_constraint.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\layer_utils.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\nine_patch_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\nine_patch_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\paint_properties.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\painted_scrollbar_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\painted_scrollbar_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\picture_image_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\picture_image_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\picture_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\picture_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\render_pass_sink.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\render_surface.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\render_surface_draw_properties.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\render_surface_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\scroll_blocks_on.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\scrollbar_layer_impl_base.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\scrollbar_layer_interface.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\scrollbar_theme_painter.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\solid_color_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\solid_color_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\solid_color_scrollbar_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\solid_color_scrollbar_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\surface_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\surface_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\texture_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\texture_layer_client.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\texture_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\ui_resource_layer.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\ui_resource_layer_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\video_frame_provider.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\layers\video_frame_provider_client_impl.h">
      <Filter>src\cc\layers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\viewport_selection_bound.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\begin_frame_args.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\bsp_compare_result.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\bsp_tree.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\bsp_walk_action.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\compositor_frame.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\compositor_frame_ack.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\compositor_frame_metadata.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\context_provider.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\copy_output_request.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\copy_output_result.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\delegated_frame_data.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\delegating_renderer.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\direct_renderer.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\dynamic_geometry_binding.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\filter_operation.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\filter_operations.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\geometry_binding.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\gl_frame_data.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\gl_renderer.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\gl_renderer_draw_cache.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\latency_info_swap_promise.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\layer_quad.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\managed_memory_policy.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\output_surface.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\output_surface_client.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_candidate.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_candidate_validator.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_processor.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_strategy_all_or_nothing.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_strategy_common.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_strategy_sandwich.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_strategy_single_on_top.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\overlay_strategy_underlay.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\program_binding.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\render_surface_filters.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\renderer.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\renderer_capabilities.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\renderer_settings.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\shader.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\software_frame_data.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\software_output_device.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\software_renderer.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\static_geometry_binding.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\swap_promise.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\output\texture_mailbox_deleter.h">
      <Filter>src\cc\output</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\raster_source.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\raster_source_helper.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\recording_source.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\transform_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\clip_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\clip_path_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\compositing_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\discardable_image_map.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\display_item_list.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\display_item_list_bounds_calculator.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\display_item_list_settings.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\display_item_proto_factory.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\display_list_raster_source.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\display_list_recording_source.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\draw_image.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\drawing_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\filter_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\float_clip_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\largest_display_item.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\picture.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\picture_pile.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\picture_pile_impl.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\pixel_ref_map.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\playback\position_image.h">
      <Filter>src\cc\playback</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\proto\gfx_conversions.h">
      <Filter>src\cc\proto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\proto\skia_conversions.h">
      <Filter>src\cc\proto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\proto\cc_proto_export.h">
      <Filter>src\cc\proto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\texture_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\tile_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\yuv_video_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\checkerboard_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\content_draw_quad_base.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\debug_border_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\draw_polygon.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\io_surface_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\largest_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\picture_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\render_pass.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\render_pass_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\render_pass_id.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\shared_quad_state.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\solid_color_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\stream_video_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\quads\surface_draw_quad.h">
      <Filter>src\cc\quads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\tile_task_worker_pool.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\zero_copy_tile_task_worker_pool.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\bitmap_tile_task_worker_pool.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\gpu_rasterizer.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\gpu_tile_task_worker_pool.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\one_copy_tile_task_worker_pool.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\pixel_buffer_tile_task_worker_pool.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\raster_buffer.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\scoped_gpu_raster.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\task_graph_runner.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\texture_compressor.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\texture_compressor_etc1.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\texture_compressor_etc1_sse.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\raster\tile_task_runner.h">
      <Filter>src\cc\raster</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\ui_resource_bitmap.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\ui_resource_client.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\ui_resource_request.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\video_resource_updater.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\memory_history.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\platform_color.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\release_callback.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\release_callback_impl.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\resource.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\resource_format.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\resource_pool.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\resource_provider.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\resource_util.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\return_callback.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\returned_resource.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\scoped_resource.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\scoped_ui_resource.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\shared_bitmap.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\shared_bitmap_manager.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\single_release_callback.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\single_release_callback_impl.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\texture_mailbox.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\resources\transferable_resource.h">
      <Filter>src\cc\resources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\scheduler_settings.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\scheduler_state_machine.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\video_frame_controller.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\begin_frame_source.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\begin_frame_tracker.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\commit_earlyout_reason.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\compositor_timing_history.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\delay_based_time_source.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\draw_result.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\scheduler\scheduler.h">
      <Filter>src\cc\scheduler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_manager.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_resource_holder.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_sequence.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surfaces_export.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\display.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\display_client.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\display_scheduler.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\onscreen_display_client.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_aggregator.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_aggregator_test_helpers.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_damage_observer.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_display_output_surface.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_factory.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_factory_client.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_hittest.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_id.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\surfaces\surface_id_allocator.h">
      <Filter>src\cc\surfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\tiling_set_eviction_queue.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\tiling_set_raster_queue_all.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\tiling_set_raster_queue_required.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\eviction_tile_priority_queue.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\image_decode_controller.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\picture_layer_tiling.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\picture_layer_tiling_set.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\prioritized_tile.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\raster_tile_priority_queue.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\raster_tile_priority_queue_all.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\raster_tile_priority_queue_required.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\tile.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\tile_draw_info.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\tile_manager.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\tiles\tile_priority.h">
      <Filter>src\cc\tiles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\swap_promise_monitor.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\task_runner_provider.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\thread_proxy.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\threaded_channel.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\tree_synchronizer.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\blocking_task_runner.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\channel_impl.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\channel_main.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\damage_tracker.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\draw_property_utils.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\latency_info_swap_promise_monitor.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\layer_tree_host.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\layer_tree_host_client.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\layer_tree_host_common.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\layer_tree_host_impl.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\layer_tree_host_single_thread_client.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\layer_tree_impl.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\layer_tree_settings.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\mutator_host_client.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\occlusion.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\occlusion_tracker.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\property_tree.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\property_tree_builder.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\proxy.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\proxy_common.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\proxy_impl.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\proxy_main.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\scoped_abort_remaining_swap_promises.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\cc\trees\single_thread_proxy.h">
      <Filter>src\cc\trees</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\stack_container.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\adapters.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\hash_tables.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\linked_list.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\mru_cache.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\scoped_ptr_hash_map.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\scoped_ptr_map.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\containers\small_map.h">
      <Filter>src\base\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\task_annotator.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\alias.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\asan_invalid_access.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\crash_logging.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\debugger.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\dump_without_crashing.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\gdi_debug_util_win.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\leak_annotations.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\leak_tracker.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\proc_maps_linux.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\profiler.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\debug\stack_trace.h">
      <Filter>src\base\debug</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\weak_ptr.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\aligned_memory.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\discardable_memory.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\discardable_memory_allocator.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\discardable_shared_memory.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\linked_ptr.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\manual_constructor.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\memory_pressure_listener.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\memory_pressure_monitor.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\memory_pressure_monitor_chromeos.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\memory_pressure_monitor_mac.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\memory_pressure_monitor_win.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\raw_scoped_refptr_mismatch_checker.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\ref_counted.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\ref_counted_delete_on_message_loop.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\ref_counted_memory.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\scoped_policy.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\scoped_ptr.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\scoped_vector.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\shared_memory.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\shared_memory_handle.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\singleton.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\memory\singleton_objc.h">
      <Filter>src\base\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_pump_win.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\timer_slack.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\incoming_task_queue.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_loop.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_loop_task_runner.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_pump.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_pump_default.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_pump_dispatcher.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_pump_glib.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_pump_io_ios.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\message_loop\message_pump_libevent.h">
      <Filter>src\base\message_loop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\numerics\safe_math.h">
      <Filter>src\base\numerics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\numerics\safe_math_impl.h">
      <Filter>src\base\numerics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\numerics\safe_conversions.h">
      <Filter>src\base\numerics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\numerics\safe_conversions_impl.h">
      <Filter>src\base\numerics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\utf_string_conversion_utils.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\utf_string_conversions.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\latin1_string_conversions.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\nullable_string16.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\pattern.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\safe_sprintf.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\string_number_conversions.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\string_piece.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\string_split.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\string_tokenizer.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\string_util.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\string_util_win.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\string16.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\stringize_macros.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\stringprintf.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\sys_string_conversions.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\strings\utf_offset_string_conversions.h">
      <Filter>src\base\strings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\synchronization\cancellation_flag.h">
      <Filter>src\base\synchronization</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\synchronization\condition_variable.h">
      <Filter>src\base\synchronization</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\synchronization\lock.h">
      <Filter>src\base\synchronization</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\synchronization\lock_impl.h">
      <Filter>src\base\synchronization</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\synchronization\spin_wait.h">
      <Filter>src\base\synchronization</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\synchronization\waitable_event.h">
      <Filter>src\base\synchronization</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\synchronization\waitable_event_watcher.h">
      <Filter>src\base\synchronization</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread_checker.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread_checker_impl.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread_collision_warner.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread_id_name_manager.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread_local.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread_local_storage.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\thread_restrictions.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\watchdog.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\worker_pool.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\non_thread_safe.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\non_thread_safe_impl.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\platform_thread.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\post_task_and_reply_impl.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\sequenced_task_runner_handle.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\sequenced_worker_pool.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\threading\simple_thread.h">
      <Filter>src\base\threading</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\time\default_tick_clock.h">
      <Filter>src\base\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\time\tick_clock.h">
      <Filter>src\base\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\time\time.h">
      <Filter>src\base\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\time\clock.h">
      <Filter>src\base\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\time\default_clock.h">
      <Filter>src\base\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\timer\elapsed_timer.h">
      <Filter>src\base\timer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\timer\hi_res_timer_manager.h">
      <Filter>src\base\timer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\timer\mock_timer.h">
      <Filter>src\base\timer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\timer\timer.h">
      <Filter>src\base\timer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\windows_version.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\wrapped_window_proc.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\enum_variant.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\event_trace_consumer.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\event_trace_controller.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\event_trace_provider.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\i18n.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\iat_patch_function.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\iunknown_impl.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\message_window.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\metro.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\object_watcher.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\pe_image.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\process_startup_helper.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\registry.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\resource_util.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_bstr.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_co_mem.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_com_initializer.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_comptr.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_gdi_object.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_handle.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_hdc.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_hglobal.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_process_information.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_propvariant.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_select_object.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\scoped_variant.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\shortcut.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\startup_information.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\win\win_util.h">
      <Filter>src\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\atomic_sequence_num.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\atomicops.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\atomicops_internals_atomicword_compat.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\atomicops_internals_portable.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\atomicops_internals_x86_msvc.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\auto_reset.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\barrier_closure.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\base_export.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\base_paths.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\base_paths_win.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\base_switches.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\base64.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\base64url.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\basictypes.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\big_endian.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\bind.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\bind_helpers.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\bind_internal.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\bind_internal_win.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\bits.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\build_time.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\callback.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\callback_forward.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\callback_helpers.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\callback_internal.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\callback_list.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\cancelable_callback.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\command_line.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\compiler_specific.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\cpu.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\critical_closure.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\deferred_sequenced_task_runner.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\environment.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\event_types.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\feature_list.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\file_path.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\file_version_info.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\file_version_info_win.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\format_macros.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\gtest_prod_util.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\guid.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\hash.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\id_map.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\lazy_instance.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\location.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\logging.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\logging_win.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\macros.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\md5.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\move.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\native_library.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\observer_list.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\observer_list_threadsafe.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\path_service.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\pending_task.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\pickle.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\port.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\rand_util.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\run_loop.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\scoped_clear_errno.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\scoped_generic.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\scoped_native_library.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\scoped_observer.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sequence_checker.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sequence_checker_impl.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sequenced_task_runner.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sequenced_task_runner_helpers.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sha1.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\single_thread_task_runner.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\stl_util.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\supports_user_data.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sync_socket.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sys_byteorder.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\sys_info.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\task_runner.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\task_runner_util.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\template_util.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\thread_task_runner_handle.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\tracked_objects.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\tracking_info.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\tuple.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\value_conversions.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\values.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\version.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\vlog.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\atomic_ref_count.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\RasterWorkerPool.h">
      <Filter>src\content</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\RenderWidgetCompositor.h">
      <Filter>src\content</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\WebSharedBitmapManager.h">
      <Filter>src\content</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\OrigChromeMgr.h">
      <Filter>src\content</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\LayerTreeWrap.h">
      <Filter>src\content</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\skia_util.h">
      <Filter>src\ui\gfx</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\transform.h">
      <Filter>src\ui\gfx</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\transform_util.h">
      <Filter>src\ui\gfx</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\cubic_bezier.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\dip_util.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\insets.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\insets_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\matrix3_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\point.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\point_conversions.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\point_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\point3_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\quad_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\rect.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\rect_conversions.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\rect_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\safe_integer_conversions.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\scroll_offset.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\size.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\size_conversions.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\size_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\vector2d.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\vector2d_conversions.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\vector2d_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\vector3d_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\geometry\box_f.h">
      <Filter>src\ui\gfx\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\at_exit.h">
      <Filter>src\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\profiler\native_stack_sampler.h">
      <Filter>src\base\profiler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\profiler\scoped_profile.h">
      <Filter>src\base\profiler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\profiler\scoped_tracker.h">
      <Filter>src\base\profiler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\profiler\stack_sampling_profiler.h">
      <Filter>src\base\profiler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\profiler\tracked_time.h">
      <Filter>src\base\profiler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\profiler\win32_stack_frame_unwinder.h">
      <Filter>src\base\profiler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\profiler\alternate_timer.h">
      <Filter>src\base\profiler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\events\latency_info.h">
      <Filter>src\ui\event</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\animation\tween.h">
      <Filter>src\ui\gfx\animation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\display.h">
      <Filter>src\ui\gfx</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\buffer_format_util.h">
      <Filter>src\ui\gfx</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\switches.h">
      <Filter>src\ui\gfx</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\process\process_handle.h">
      <Filter>src\base\process</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\process\process.h">
      <Filter>src\base\process</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\WebPageOcBridge.h">
      <Filter>src\content</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\vertex_array_object_manager.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\buffer_tracker.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\client_context_state.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\client_context_state_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\client_context_state_impl_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\cmd_buffer_helper.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\context_support.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\fenced_allocator.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gl_in_process_context.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gl_in_process_context_export.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_c_lib_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_c_lib_export.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_cmd_helper.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_cmd_helper_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_impl_export.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_implementation.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_implementation_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_implementation_impl_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_interface.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_interface_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_interface_stub.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_interface_stub_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_interface_stub_impl_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_lib.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_trace_implementation.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_trace_implementation_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gles2_trace_implementation_impl_autogen.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gpu_control.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gpu_memory_buffer_factory.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gpu_memory_buffer_manager.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gpu_memory_buffer_tracker.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\gpu_switches.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\mapped_memory.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\program_info_manager.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\query_tracker.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\ref_counted.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\ring_buffer.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\share_group.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\client\transfer_buffer.h">
      <Filter>src\gpu\command_buffer\client</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\GLES2\gl2chromium_autogen.h">
      <Filter>src\gpu\GLES2</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\GLES2\gl2extchromium.h">
      <Filter>src\gpu\GLES2</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\GLES2\gl2chromium.h">
      <Filter>src\gpu\GLES2</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\egl_util.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\angle_platform_impl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_api_autogen_egl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_api_autogen_gl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_egl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_gl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_glx.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_mock.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_osmesa.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_bindings_autogen_wgl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context_cgl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context_egl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context_glx.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context_osmesa.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context_stub.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context_stub_with_extensions.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_context_wgl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_egl_api_implementation.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_enums.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_enums_implementation_autogen.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_export.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_fence.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_fence_apple.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_fence_arb.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_fence_egl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_fence_nv.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_gl_api_implementation.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_glx_api_implementation.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_helper.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_egl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_io_surface.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_memory.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_ozone_native_pixmap.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_ref_counted_memory.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_shared_memory.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_stub.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_image_surface_texture.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_implementation.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_mock_autogen_gl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_osmesa_api_implementation.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_share_group.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_state_restorer.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_surface.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_surface_egl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_surface_glx.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_surface_osmesa.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_surface_overlay.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_surface_stub.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_surface_wgl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_switches.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_version_info.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gl_wgl_api_implementation.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gpu_preference.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gpu_switching_manager.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gpu_switching_observer.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gpu_timing.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\gpu_timing_fake.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\scoped_api.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\scoped_binders.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\scoped_cgl.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\scoped_make_current.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\sync_control_vsync_provider.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\trace_util.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gl\vsync_provider_win.h">
      <Filter>src\ui\gl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\context_state_impl_autogen.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\error_state.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\feature_info.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\framebuffer_completeness_cache.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\framebuffer_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gl_context_virtual.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gl_state_restorer_impl.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gl_utils.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_clear_framebuffer.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_copy_texture_chromium.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_decoder.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_decoder_autogen.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_validation.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_validation_autogen.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gles2_cmd_validation_implementation_autogen.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_control_service.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_memory_buffer_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_scheduler.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_state_tracer.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_switches.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\gpu_tracer.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\id_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\image_factory.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\image_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\in_process_command_buffer.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\logger.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\mailbox_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\mailbox_manager_impl.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\mailbox_manager_sync.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\mailbox_synchronizer.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\memory_program_cache.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\memory_tracking.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\path_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\program_cache.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\program_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\query_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\renderbuffer_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\shader_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\shader_translator.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\shader_translator_cache.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\stream_texture_manager_in_process_android.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\sync_point_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\test_helper.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\texture_definition.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\texture_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\transfer_buffer_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\valuebuffer_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\vertex_array_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\vertex_attrib_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_delegate.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_egl.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_idle.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_share_group.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_stub.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\async_pixel_transfer_manager_sync.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\buffer_manager.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\cmd_buffer_engine.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\cmd_parser.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\command_buffer_service.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\common_decoder.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\context_group.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\context_state.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\context_state_autogen.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\mailbox_holder.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\thread_local.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\time.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\trace_event.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\value_state.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\bitfield_helpers.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\buffer.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\capabilities.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\cmd_buffer_common.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\command_buffer.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\command_buffer_shared.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\constants.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\debug_marker_manager.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_format.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_format_autogen.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_ids.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_ids_autogen.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_utils.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_utils_autogen.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_cmd_utils_implementation_autogen.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gles2_utils_export.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\gpu_memory_allocation.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\id_allocator.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\common\mailbox.h">
      <Filter>src\gpu\command_buffer\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\command_buffer\service\City.h">
      <Filter>src\gpu\command_buffer\service</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\compositor\SoftwareOutputSurface.h">
      <Filter>src\content\compositor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\compositor\SoftwareOutputDevice.h">
      <Filter>src\content\compositor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\compositor\GpuOutputSurface.h">
      <Filter>src\content\compositor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\ContextProviderCommandBuffer.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\GrcontextForWebgraphicscontext3d.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\CommandBufferMetrics.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\GpuChannelMgr.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\CommandBufferClientImpl.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\CommandBufferServiceStub.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\ImageTransportSurface.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\WgContext3dCmdBufImpl.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\blink\webgraphicscontext3d_impl.h">
      <Filter>src\gpu\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\blink\webgraphicscontext3d_in_process_command_buffer_impl.h">
      <Filter>src\gpu\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\blink\gpu_blink_export.h">
      <Filter>src\gpu\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\files\file_path.h">
      <Filter>src\base\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\ui\gfx\gpu_memory_buffer.h">
      <Filter>src\ui\gfx</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\gpu\skia_bindings\gl_bindings_skia_cmd_buffer.h">
      <Filter>src\gpu\skia_bindings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\files\file_util.h">
      <Filter>src\base\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\base\files\file.h">
      <Filter>src\base\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\gpu\ChildGpuMemoryBufferManager.h">
      <Filter>src\content\gpu</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\compositor\EmptyOutputSurface.h">
      <Filter>src\content\compositor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_buffer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_buffer_converter.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_buffer_queue.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_bus.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_capturer_source.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_converter.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_decoder.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_decoder_config.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_discard_helper.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_fifo.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_hardware_config.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_hash.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_pull_fifo.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_renderer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_renderer_mixer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_renderer_mixer_input.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_renderer_sink.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_shifter.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_splicer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_timestamp_helper.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_video_metadata_extractor.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\bind_to_current_loop.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\bit_reader.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\bit_reader_core.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\bitstream_buffer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\buffering_state.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\byte_queue.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_callback_promise.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_config.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_context.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_factory.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_initialized_promise.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_key_information.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_promise.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\cdm_promise_adapter.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\channel_layout.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\channel_mixer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\channel_mixing_matrix.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\container_names.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\data_buffer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\data_source.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\decoder_buffer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\decoder_buffer_queue.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\decrypt_config.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\decryptor.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\demuxer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\demuxer_stream.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\demuxer_stream_provider.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\djb2.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\eme_constants.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\key_system_info.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\key_systems.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\key_systems_support_uma.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\keyboard_event_counter.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\limits.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_client.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_export.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_file_checker.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_keys.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_log.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_log_event.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_permission.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_resources.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_switches.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\media_util.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\mime_util.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\moving_average.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\multi_channel_resampler.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\null_video_sink.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\output_device.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\pipeline.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\pipeline_status.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\player_tracker.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\ranges.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\renderer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\renderer_factory.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\sample_format.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\seekable_buffer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\serial_runner.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\sinc_resampler.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\stream_parser.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\stream_parser_buffer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\text_cue.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\text_ranges.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\text_renderer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\text_track.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\text_track_config.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\time_delta_interpolator.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\time_source.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\timestamp_constants.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\user_input_monitor.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\vector_math.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\vector_math_testing.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_capture_types.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_capturer_source.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_codecs.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_decoder.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_decoder_config.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_frame.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_frame_metadata.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_frame_pool.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_renderer.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_renderer_sink.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_rotation.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_types.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\video_util.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\wall_clock_time_source.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\yuv_convert.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\audio_block_fifo.h">
      <Filter>src\media\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\simd\convert_rgb_to_yuv_ssse3.h">
      <Filter>src\media\base\simd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\simd\convert_yuv_to_rgb.h">
      <Filter>src\media\base\simd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\simd\filter_yuv.h">
      <Filter>src\media\base\simd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\simd\convert_rgb_to_yuv.h">
      <Filter>src\media\base\simd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\base\win\mf_initializer.h">
      <Filter>src\media\base\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\sounds\audio_stream_handler.h">
      <Filter>src\media\audio\sounds</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\sounds\sounds_manager.h">
      <Filter>src\media\audio\sounds</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\sounds\wav_audio_handler.h">
      <Filter>src\media\audio\sounds</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\audio_device_listener_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\audio_low_latency_input_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\audio_low_latency_output_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\audio_manager_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\avrt_wrapper_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\core_audio_util_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\device_enumeration_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\wavein_input_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\win\waveout_output_win.h">
      <Filter>src\media\audio\win</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\sample_rates.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\scoped_task_runner_observer.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\simple_sources.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\virtual_audio_input_stream.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\virtual_audio_output_stream.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\agc_audio_stream.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_device_name.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_device_thread.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_input_controller.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_input_device.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_input_ipc.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_input_writer.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_io.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_logging.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_manager.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_manager_base.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_manager_factory.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_controller.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_device.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_dispatcher.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_dispatcher_impl.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_ipc.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_proxy.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_resampler.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_output_stream_sink.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_parameters.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_power_monitor.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\audio_source_diverter.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\clockless_audio_sink.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\fake_audio_input_stream.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\fake_audio_log_factory.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\fake_audio_manager.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\fake_audio_output_stream.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\fake_audio_worker.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\null_audio_sink.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\audio\point.h">
      <Filter>src\media\audio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\buffered_data_source.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\buffered_data_source_host_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\buffered_resource_loader.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\cache_util.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\cdm_result_promise.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\cdm_result_promise_helper.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\cdm_session_adapter.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\encrypted_media_player_support.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\interval_map.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\key_system_config_selector.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\lru.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\media_blink_export.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\multibuffer.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\new_session_cdm_result_promise.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\texttrack_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\video_frame_compositor.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webaudiosourceprovider_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webcontentdecryptionmodule_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webcontentdecryptionmoduleaccess_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webcontentdecryptionmodulesession_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webencryptedmediaclient_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webinbandtexttrack_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webmediaplayer_delegate.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webmediaplayer_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webmediaplayer_params.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webmediaplayer_util.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\webmediasource_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\websourcebuffer_impl.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\blink\active_loader.h">
      <Filter>src\media\blink</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\ffmpeg\ffmpeg_common.h">
      <Filter>src\media\ffmpeg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\ffmpeg\ffmpeg_deleters.h">
      <Filter>src\media\ffmpeg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\audio_clock.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\audio_file_reader.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\audio_renderer_algorithm.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\blocking_url_protocol.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\chunk_demuxer.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\context_3d.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\decoder_selector.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\decoder_stream.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\decoder_stream_traits.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\decrypting_audio_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\decrypting_demuxer_stream.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\decrypting_video_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\default_media_permission.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_audio_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_bitstream_converter.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_demuxer.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_glue.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_h264_to_annex_b_bitstream_converter.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_video_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\file_data_source.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\frame_processor.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\gpu_video_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\h264_bit_reader.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\h264_parser.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\h264_to_annex_b_bitstream_converter.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\in_memory_url_protocol.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ivf_parser.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\jpeg_parser.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\opus_audio_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\opus_constants.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\source_buffer_platform.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\source_buffer_range.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\source_buffer_stream.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\stream_parser_factory.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\video_cadence_estimator.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\video_renderer_algorithm.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\vp8_bool_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\vp8_parser.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\vp9_parser.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\vp9_raw_bits_reader.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\vpx_video_decoder.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\webvtt_util.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\wsola_internals.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\common\offset_byte_queue.h">
      <Filter>src\media\formats\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\es_adapter_video.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\es_parser.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\es_parser_adts.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\es_parser_h264.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\es_parser_mpeg1audio.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\mp2t_common.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\mp2t_stream_parser.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\timestamp_unroller.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\ts_packet.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\ts_section.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\ts_section_pat.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\ts_section_pes.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\ts_section_pmt.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp2t\ts_section_psi.h">
      <Filter>src\media\formats\mp2t</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\aac.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\avc.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\bitstream_converter.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\box_definitions.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\box_reader.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\cenc.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\es_descriptor.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\fourccs.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\mp4_stream_parser.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\rcheck.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\sample_to_group_iterator.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\track_run_iterator.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mpeg\adts_constants.h">
      <Filter>src\media\formats\mpeg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mpeg\adts_stream_parser.h">
      <Filter>src\media\formats\mpeg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mpeg\mpeg_audio_stream_parser_base.h">
      <Filter>src\media\formats\mpeg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mpeg\mpeg1_audio_stream_parser.h">
      <Filter>src\media\formats\mpeg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_cluster_parser.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_constants.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_content_encodings.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_content_encodings_client.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_crypto_helpers.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_info_parser.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_parser.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_stream_parser.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_tracks_parser.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_video_client.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_webvtt_parser.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\webm\webm_audio_client.h">
      <Filter>src\media\formats\webm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\renderers\default_renderer_factory.h">
      <Filter>src\media\renderers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\renderers\gpu_video_accelerator_factories.h">
      <Filter>src\media\renderers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\renderers\renderer_impl.h">
      <Filter>src\media\renderers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\renderers\skcanvas_video_renderer.h">
      <Filter>src\media\renderers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\renderers\video_renderer_impl.h">
      <Filter>src\media\renderers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\renderers\audio_renderer_impl.h">
      <Filter>src\media\renderers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\video\gpu_memory_buffer_video_frame_pool.h">
      <Filter>src\media\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\video\h264_poc.h">
      <Filter>src\media\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\video\jpeg_decode_accelerator.h">
      <Filter>src\media\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\video\picture.h">
      <Filter>src\media\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\video\video_decode_accelerator.h">
      <Filter>src\media\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\video\video_encode_accelerator.h">
      <Filter>src\media\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\video\fake_video_encode_accelerator.h">
      <Filter>src\media\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\cdm\key_system_names.h">
      <Filter>src\media\cdm</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\formats\mp4\hevc.h">
      <Filter>src\media\formats\mp4</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\h265_parser.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\media\filters\ffmpeg_h265_to_annex_b_bitstream_converter.h">
      <Filter>src\media\filters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\media\audio_renderer_mixer_manager.h">
      <Filter>src\content\meida</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\media\audio_device_factory.h">
      <Filter>src\content\meida</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\base\third_party\nspr\prtime.h">
      <Filter>src\base\third_party\nspr</Filter>
    </ClInclude>
    <ClInclude Include="..\..\orig_chrome\content\media\renderer_webaudiodevice_impl.h">
      <Filter>src\content\meida</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\orig_chrome\cc\base\DEPS">
      <Filter>src\cc\base</Filter>
    </None>
    <None Include="..\..\orig_chrome\cc\debug\OWNERS">
      <Filter>src\cc\debug</Filter>
    </None>
    <None Include="..\..\orig_chrome\cc\surfaces\BUILD.gn">
      <Filter>src\cc\surfaces</Filter>
    </None>
    <None Include="..\..\orig_chrome\cc\surfaces\OWNERS">
      <Filter>src\cc\surfaces</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\debug\BUILD.gn">
      <Filter>src\base\debug</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\debug\OWNERS">
      <Filter>src\base\debug</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\memory\BUILD.gn">
      <Filter>src\base\memory</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\memory\OWNERS">
      <Filter>src\base\memory</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\strings\OWNERS">
      <Filter>src\base\strings</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\threading\OWNERS">
      <Filter>src\base\threading</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\win\OWNERS">
      <Filter>src\base\win</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\base.isolate">
      <Filter>src\base</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\BUILD.gn">
      <Filter>src\base</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\DEPS">
      <Filter>src\base</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\OWNERS">
      <Filter>src\base</Filter>
    </None>
    <None Include="..\..\orig_chrome\base\PRESUBMIT.py">
      <Filter>src\base</Filter>
    </None>
    <None Include="..\..\orig_chrome\ui\gfx\geometry\OWNERS">
      <Filter>src\ui\gfx\geometry</Filter>
    </None>
    <None Include="..\..\orig_chrome\ui\gfx\geometry\BUILD.gn">
      <Filter>src\ui\gfx\geometry</Filter>
    </None>
    <None Include="..\..\orig_chrome\gpu\command_buffer\client\BUILD.gn">
      <Filter>src\gpu\command_buffer\client</Filter>
    </None>
    <None Include="..\..\orig_chrome\ui\gl\OWNERS">
      <Filter>src\ui\gl</Filter>
    </None>
    <None Include="..\..\orig_chrome\gpu\command_buffer\service\BUILD.gn">
      <Filter>src\gpu\command_buffer\service</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\convert_rgb_to_yuv_ssse3.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\convert_rgb_to_yuv_ssse3.inc">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\convert_yuv_to_rgb_mmx.inc">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\convert_yuv_to_rgb_sse.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\convert_yuva_to_argb_mmx.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\convert_yuva_to_argb_mmx.inc">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\empty_register_state_mmx.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\linear_scale_yuv_to_rgb_mmx.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\linear_scale_yuv_to_rgb_mmx.inc">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\linear_scale_yuv_to_rgb_sse.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\media_export.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\scale_yuv_to_rgb_mmx.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\scale_yuv_to_rgb_mmx.inc">
      <Filter>src\media\base\simd</Filter>
    </None>
    <None Include="..\..\orig_chrome\media\base\simd\scale_yuv_to_rgb_sse.asm">
      <Filter>src\media\base\simd</Filter>
    </None>
  </ItemGroup>
</Project>