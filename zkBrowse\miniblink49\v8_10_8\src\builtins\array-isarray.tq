// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

namespace runtime {
extern runtime ArrayIsArray(implicit context: Context)(JSAny): JSAny;
}  // namespace runtime

namespace array {
// ES #sec-array.isarray
javascript builtin ArrayIsArray(js-implicit context: NativeContext)(arg: JSAny):
    JSAny {
  // 1. Return ? IsArray(arg).
  typeswitch (arg) {
    case (JSArray): {
      return True;
    }
    case (JSProxy): {
      // TODO(verwaest): Handle proxies in-place
      return runtime::ArrayIsArray(arg);
    }
    case (JSAny): {
      return False;
    }
  }
}
}  // namespace array
