// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-typed-array-gen.h'

namespace typed_array {
const kBuiltinNameReduce: constexpr string = '%TypedArray%.prototype.reduce';

transitioning macro ReduceAllElements(implicit context: Context)(
    attachedArrayAndLength: typed_array::AttachedJSTypedArrayAndLength,
    callbackfn: Callable, initialValue: JSAny|TheHole): JSAny {
  let witness =
      typed_array::NewAttachedJSTypedArrayWitness(attachedArrayAndLength.array);

  let accumulator = initialValue;
  for (let k: uintptr = 0; k < attachedArrayAndLength.length; k++) {
    let value: JSAny;
    try {
      witness.RecheckIndex(k)
          otherwise goto IsDetachedOrOutOfBounds;
      value = witness.Load(k);
    } label IsDetachedOrOutOfBounds deferred {
      value = Undefined;
    }
    typeswitch (accumulator) {
      case (TheHole): {
        accumulator = value;
      }
      case (accumulatorNotHole: JSAny): {
        // TODO(v8:4153): Consider versioning this loop for Smi and non-Smi
        // indices to optimize Convert<Number>(k) for the most common case.
        accumulator = Call(
            context, callbackfn, Undefined, accumulatorNotHole, value,
            Convert<Number>(k), witness.GetStable());
      }
    }
  }
  typeswitch (accumulator) {
    case (TheHole): {
      ThrowTypeError(MessageTemplate::kReduceNoInitial, kBuiltinNameReduce);
    }
    case (accumulator: JSAny): {
      return accumulator;
    }
  }
}

// https://tc39.github.io/ecma262/#sec-%typedarray%.prototype.reduce
transitioning javascript builtin
TypedArrayPrototypeReduce(
    js-implicit context: NativeContext, receiver: JSAny)(...arguments): JSAny {
  // arguments[0] = callback
  // arguments[1] = initialValue.
  try {
    // 1. Let O be the this value.
    // 2. Perform ? ValidateTypedArray(O).
    // 3. Let len be IntegerIndexedObjectLength(O).
    const array: JSTypedArray = Cast<JSTypedArray>(receiver)
        otherwise NotTypedArray;
    const attachedArrayAndLength = EnsureAttachedAndReadLength(array)
        otherwise IsDetachedOrOutOfBounds;
    // 4. If IsCallable(callbackfn) is false, throw a TypeError exception.
    const callbackfn = Cast<Callable>(arguments[0]) otherwise NotCallable;
    const initialValue = arguments.length >= 2 ? arguments[1] : TheHole;
    return ReduceAllElements(attachedArrayAndLength, callbackfn, initialValue);
  } label NotCallable deferred {
    ThrowTypeError(MessageTemplate::kCalledNonCallable, arguments[0]);
  } label NotTypedArray deferred {
    ThrowTypeError(MessageTemplate::kNotTypedArray, kBuiltinNameReduce);
  } label IsDetachedOrOutOfBounds deferred {
    ThrowTypeError(MessageTemplate::kDetachedOperation, kBuiltinNameReduce);
  }
}
}
