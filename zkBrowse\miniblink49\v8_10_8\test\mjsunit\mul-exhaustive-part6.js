// Copyright 2008 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

var x;

// Converts a number to string respecting -0.
function stringify(n) {
  if ((1 / n) === -Infinity) return "-0";
  return String(n);
}

function f(expected, y) {
  function testEval(string, x, y) {
    var mulFunction = Function("x, y", "return " + string);
    return mulFunction(x, y);
  }
  function mulTest(expected, x, y) {
    assertEquals(expected, x * y);
    assertEquals(expected, testEval(stringify(x) + " * y", x, y));
    assertEquals(expected, testEval("x * " + stringify(y), x, y));
    assertEquals(expected, testEval(stringify(x) + " * " + stringify(y), x, y));
  }
  mulTest(expected, x, y);
  mulTest(-expected, -x, y);
  mulTest(-expected, x, -y);
  mulTest(expected, -x, -y);
  if (x === y) return;  // Symmetric cases not necessary.
  mulTest(expected, y, x);
  mulTest(-expected, -y, x);
  mulTest(-expected, y, -x);
  mulTest(expected, -y, -x);
}

x = 8388607;
f(0, 0);
f(8388607, 1);
f(16777214, 2);
f(25165821, 3);
f(33554428, 4);
f(41943035, 5);
f(58720249, 7);
f(67108856, 8);
f(75497463, 9);
f(125829105, 15);
f(134217712, 16);
f(142606319, 17);
f(260046817, 31);
f(268435424, 32);
f(276824031, 33);
f(528482241, 63);
f(536870848, 64);
f(545259455, 65);
f(1065353089, 127);
f(1073741696, 128);
f(1082130303, 129);
f(2139094785, 255);
f(2147483392, 256);
f(2155871999, 257);
f(4286578177, 511);
f(4294966784, 512);
f(4303355391, 513);
f(8581544961, 1023);
f(8589933568, 1024);
f(8598322175, 1025);
f(17171478529, 2047);
f(17179867136, 2048);
f(17188255743, 2049);
f(34351345665, 4095);
f(34359734272, 4096);
f(34368122879, 4097);
f(68711079937, 8191);
f(68719468544, 8192);
f(68727857151, 8193);
f(137430548481, 16383);
f(137438937088, 16384);
f(137447325695, 16385);
f(274869485569, 32767);
f(274877874176, 32768);
f(274886262783, 32769);
f(549747359745, 65535);
f(549755748352, 65536);
f(549764136959, 65537);
f(1099503108097, 131071);
f(1099511496704, 131072);
f(1099519885311, 131073);
f(2199014604801, 262143);
f(2199022993408, 262144);
f(2199031382015, 262145);
f(4398037598209, 524287);
f(4398045986816, 524288);
f(4398054375423, 524289);
f(8796083585025, 1048575);
f(8796091973632, 1048576);
f(8796100362239, 1048577);
f(17592175558657, 2097151);
f(17592183947264, 2097152);
f(17592192335871, 2097153);
f(35184359505921, 4194303);
f(35184367894528, 4194304);
f(35184376283135, 4194305);
f(70368727400449, 8388607);
x = 8388608;
f(0, 0);
f(8388608, 1);
f(16777216, 2);
f(25165824, 3);
f(33554432, 4);
f(41943040, 5);
f(58720256, 7);
f(67108864, 8);
f(75497472, 9);
f(125829120, 15);
f(134217728, 16);
f(142606336, 17);
f(260046848, 31);
f(268435456, 32);
f(276824064, 33);
f(528482304, 63);
f(536870912, 64);
f(545259520, 65);
f(1065353216, 127);
f(1073741824, 128);
f(1082130432, 129);
f(2139095040, 255);
f(2147483648, 256);
f(2155872256, 257);
f(4286578688, 511);
f(4294967296, 512);
f(4303355904, 513);
f(8581545984, 1023);
f(8589934592, 1024);
f(8598323200, 1025);
f(17171480576, 2047);
f(17179869184, 2048);
f(17188257792, 2049);
f(34351349760, 4095);
f(34359738368, 4096);
f(34368126976, 4097);
f(68711088128, 8191);
f(68719476736, 8192);
f(68727865344, 8193);
f(137430564864, 16383);
f(137438953472, 16384);
f(137447342080, 16385);
f(274869518336, 32767);
f(274877906944, 32768);
f(274886295552, 32769);
f(549747425280, 65535);
f(549755813888, 65536);
f(549764202496, 65537);
f(1099503239168, 131071);
f(1099511627776, 131072);
f(1099520016384, 131073);
f(2199014866944, 262143);
f(2199023255552, 262144);
f(2199031644160, 262145);
f(4398038122496, 524287);
f(4398046511104, 524288);
f(4398054899712, 524289);
f(8796084633600, 1048575);
f(8796093022208, 1048576);
f(8796101410816, 1048577);
f(17592177655808, 2097151);
f(17592186044416, 2097152);
f(17592194433024, 2097153);
f(35184363700224, 4194303);
f(35184372088832, 4194304);
f(35184380477440, 4194305);
f(70368735789056, 8388607);
f(70368744177664, 8388608);
x = 8388609;
f(0, 0);
f(8388609, 1);
f(16777218, 2);
f(25165827, 3);
f(33554436, 4);
f(41943045, 5);
f(58720263, 7);
f(67108872, 8);
f(75497481, 9);
f(125829135, 15);
f(134217744, 16);
f(142606353, 17);
f(260046879, 31);
f(268435488, 32);
f(276824097, 33);
f(528482367, 63);
f(536870976, 64);
f(545259585, 65);
f(1065353343, 127);
f(1073741952, 128);
f(1082130561, 129);
f(2139095295, 255);
f(2147483904, 256);
f(2155872513, 257);
f(4286579199, 511);
f(4294967808, 512);
f(4303356417, 513);
f(8581547007, 1023);
f(8589935616, 1024);
f(8598324225, 1025);
f(17171482623, 2047);
f(17179871232, 2048);
f(17188259841, 2049);
f(34351353855, 4095);
f(34359742464, 4096);
f(34368131073, 4097);
f(68711096319, 8191);
f(68719484928, 8192);
f(68727873537, 8193);
f(137430581247, 16383);
f(137438969856, 16384);
f(137447358465, 16385);
f(274869551103, 32767);
f(274877939712, 32768);
f(274886328321, 32769);
f(549747490815, 65535);
f(549755879424, 65536);
f(549764268033, 65537);
f(1099503370239, 131071);
f(1099511758848, 131072);
f(1099520147457, 131073);
f(2199015129087, 262143);
f(2199023517696, 262144);
f(2199031906305, 262145);
f(4398038646783, 524287);
f(4398047035392, 524288);
f(4398055424001, 524289);
f(8796085682175, 1048575);
f(8796094070784, 1048576);
f(8796102459393, 1048577);
f(17592179752959, 2097151);
f(17592188141568, 2097152);
f(17592196530177, 2097153);
f(35184367894527, 4194303);
f(35184376283136, 4194304);
f(35184384671745, 4194305);
f(70368744177663, 8388607);
f(70368752566272, 8388608);
f(70368760954881, 8388609);
x = 16777215;
f(0, 0);
f(16777215, 1);
f(33554430, 2);
f(50331645, 3);
f(67108860, 4);
f(83886075, 5);
f(117440505, 7);
f(134217720, 8);
f(150994935, 9);
f(251658225, 15);
f(268435440, 16);
f(285212655, 17);
f(520093665, 31);
f(536870880, 32);
f(553648095, 33);
f(1056964545, 63);
f(1073741760, 64);
f(1090518975, 65);
f(2130706305, 127);
f(2147483520, 128);
f(2164260735, 129);
f(4278189825, 255);
f(4294967040, 256);
f(4311744255, 257);
f(8573156865, 511);
f(8589934080, 512);
f(8606711295, 513);
f(17163090945, 1023);
f(17179868160, 1024);
f(17196645375, 1025);
f(34342959105, 2047);
f(34359736320, 2048);
f(34376513535, 2049);
f(68702695425, 4095);
f(68719472640, 4096);
f(68736249855, 4097);
f(137422168065, 8191);
f(137438945280, 8192);
f(137455722495, 8193);
f(274861113345, 16383);
f(274877890560, 16384);
f(274894667775, 16385);
f(549739003905, 32767);
f(549755781120, 32768);
f(549772558335, 32769);
f(1099494785025, 65535);
f(1099511562240, 65536);
f(1099528339455, 65537);
f(2199006347265, 131071);
f(2199023124480, 131072);
f(2199039901695, 131073);
f(4398029471745, 262143);
f(4398046248960, 262144);
f(4398063026175, 262145);
f(8796075720705, 524287);
f(8796092497920, 524288);
f(8796109275135, 524289);
f(17592168218625, 1048575);
f(17592184995840, 1048576);
f(17592201773055, 1048577);
f(35184353214465, 2097151);
f(35184369991680, 2097152);
f(35184386768895, 2097153);
f(70368723206145, 4194303);
f(70368739983360, 4194304);
f(70368756760575, 4194305);
f(140737463189505, 8388607);
f(140737479966720, 8388608);
f(140737496743935, 8388609);
f(281474943156225, 16777215);
x = 16777216;
f(0, 0);
f(16777216, 1);
f(33554432, 2);
f(50331648, 3);
f(67108864, 4);
f(83886080, 5);
f(117440512, 7);
f(134217728, 8);
f(150994944, 9);
f(251658240, 15);
f(268435456, 16);
f(285212672, 17);
f(520093696, 31);
f(536870912, 32);
f(553648128, 33);
f(1056964608, 63);
f(1073741824, 64);
f(1090519040, 65);
f(2130706432, 127);
f(2147483648, 128);
f(2164260864, 129);
f(4278190080, 255);
f(4294967296, 256);
f(4311744512, 257);
f(8573157376, 511);
f(8589934592, 512);
f(8606711808, 513);
f(17163091968, 1023);
f(17179869184, 1024);
f(17196646400, 1025);
f(34342961152, 2047);
f(34359738368, 2048);
f(34376515584, 2049);
f(68702699520, 4095);
f(68719476736, 4096);
f(68736253952, 4097);
f(137422176256, 8191);
f(137438953472, 8192);
f(137455730688, 8193);
f(274861129728, 16383);
f(274877906944, 16384);
f(274894684160, 16385);
f(549739036672, 32767);
f(549755813888, 32768);
f(549772591104, 32769);
f(1099494850560, 65535);
f(1099511627776, 65536);
f(1099528404992, 65537);
f(2199006478336, 131071);
f(2199023255552, 131072);
f(2199040032768, 131073);
f(4398029733888, 262143);
f(4398046511104, 262144);
f(4398063288320, 262145);
f(8796076244992, 524287);
f(8796093022208, 524288);
f(8796109799424, 524289);
f(17592169267200, 1048575);
f(17592186044416, 1048576);
f(17592202821632, 1048577);
f(35184355311616, 2097151);
f(35184372088832, 2097152);
f(35184388866048, 2097153);
f(70368727400448, 4194303);
f(70368744177664, 4194304);
f(70368760954880, 4194305);
f(140737471578112, 8388607);
f(140737488355328, 8388608);
f(140737505132544, 8388609);
f(281474959933440, 16777215);
f(281474976710656, 16777216);
x = 16777217;
f(0, 0);
f(16777217, 1);
f(33554434, 2);
f(50331651, 3);
f(67108868, 4);
f(83886085, 5);
f(117440519, 7);
f(134217736, 8);
f(150994953, 9);
f(251658255, 15);
f(268435472, 16);
f(285212689, 17);
f(520093727, 31);
f(536870944, 32);
f(553648161, 33);
f(1056964671, 63);
f(1073741888, 64);
f(1090519105, 65);
f(2130706559, 127);
f(2147483776, 128);
f(2164260993, 129);
f(4278190335, 255);
f(4294967552, 256);
f(4311744769, 257);
f(8573157887, 511);
f(8589935104, 512);
f(8606712321, 513);
f(17163092991, 1023);
f(17179870208, 1024);
f(17196647425, 1025);
f(34342963199, 2047);
f(34359740416, 2048);
f(34376517633, 2049);
f(68702703615, 4095);
f(68719480832, 4096);
f(68736258049, 4097);
f(137422184447, 8191);
f(137438961664, 8192);
f(137455738881, 8193);
f(274861146111, 16383);
f(274877923328, 16384);
f(274894700545, 16385);
f(549739069439, 32767);
f(549755846656, 32768);
f(549772623873, 32769);
f(1099494916095, 65535);
f(1099511693312, 65536);
f(1099528470529, 65537);
f(2199006609407, 131071);
f(2199023386624, 131072);
f(2199040163841, 131073);
f(4398029996031, 262143);
f(4398046773248, 262144);
f(4398063550465, 262145);
f(8796076769279, 524287);
f(8796093546496, 524288);
f(8796110323713, 524289);
f(17592170315775, 1048575);
f(17592187092992, 1048576);
f(17592203870209, 1048577);
f(35184357408767, 2097151);
f(35184374185984, 2097152);
f(35184390963201, 2097153);
f(70368731594751, 4194303);
f(70368748371968, 4194304);
f(70368765149185, 4194305);
f(140737479966719, 8388607);
f(140737496743936, 8388608);
f(140737513521153, 8388609);
f(281474976710655, 16777215);
f(281474993487872, 16777216);
f(281475010265089, 16777217);
x = 33554431;
f(0, 0);
f(33554431, 1);
f(67108862, 2);
f(100663293, 3);
f(134217724, 4);
f(167772155, 5);
f(234881017, 7);
f(268435448, 8);
f(301989879, 9);
f(503316465, 15);
f(536870896, 16);
f(570425327, 17);
f(1040187361, 31);
f(1073741792, 32);
f(1107296223, 33);
f(2113929153, 63);
f(2147483584, 64);
f(2181038015, 65);
f(4261412737, 127);
f(4294967168, 128);
f(4328521599, 129);
f(8556379905, 255);
f(8589934336, 256);
f(8623488767, 257);
f(17146314241, 511);
f(17179868672, 512);
f(17213423103, 513);
f(34326182913, 1023);
f(34359737344, 1024);
f(34393291775, 1025);
f(68685920257, 2047);
f(68719474688, 2048);
f(68753029119, 2049);
f(137405394945, 4095);
f(137438949376, 4096);
f(137472503807, 4097);
f(274844344321, 8191);
f(274877898752, 8192);
f(274911453183, 8193);
f(549722243073, 16383);
f(549755797504, 16384);
f(549789351935, 16385);
f(1099478040577, 32767);
f(1099511595008, 32768);
f(1099545149439, 32769);
f(2198989635585, 65535);
f(2199023190016, 65536);
f(2199056744447, 65537);
f(4398012825601, 131071);
f(4398046380032, 131072);
f(4398079934463, 131073);
f(8796059205633, 262143);
f(8796092760064, 262144);
f(8796126314495, 262145);
f(17592151965697, 524287);
f(17592185520128, 524288);
f(17592219074559, 524289);
f(35184337485825, 1048575);
f(35184371040256, 1048576);
f(35184404594687, 1048577);
f(70368708526081, 2097151);
f(70368742080512, 2097152);
f(70368775634943, 2097153);
f(140737450606593, 4194303);
f(140737484161024, 4194304);
f(140737517715455, 4194305);
f(281474934767617, 8388607);
f(281474968322048, 8388608);
f(281475001876479, 8388609);
f(562949903089665, 16777215);
f(562949936644096, 16777216);
f(562949970198527, 16777217);
f(1125899839733761, 33554431);
