﻿#include "stdafx.h"
#include "TabItem.h"
#include "..\3rd\tween\tween.h"
#include "quicklaunch\QuickPreView.h"

std::map<HWND,std::vector<HWND>>   g_mapPageHWND;

//////////////////////////////////////////////////////////////////////////
//
//
//
//////////////////////////////////////////////////////////////////////////

CControlUIEx::CControlUIEx()
{
    m_pImageInfo = NULL;
    m_pLogoIco = NULL;
    m_showType = 0;
    m_pngcurindex = 0;
}

CControlUIEx::~CControlUIEx()
{

}

void CControlUIEx::LoadImage( LPCTSTR lpImage )
{
    if (m_pImageInfo)
    {
        CRenderEngine::FreeImage(m_pImageInfo);
        m_pImageInfo = NULL;
    }

    m_pImageInfo = CRenderEngine::LoadImage(lpImage);
}

void CControlUIEx::LoadIcon( LPCTSTR lpImage,int nX,int nY )
{

    if (m_pLogoIco)
    {
        DeleteObject(m_pLogoIco);
    }

    m_showType = 0;
    m_pLogoIco = (HICON)::LoadImage(NULL, lpImage,IMAGE_ICON, 0, 0, LR_DEFAULTSIZE | LR_LOADFROMFILE);
}

void CControlUIEx::LoadPngIndex( LPCTSTR lpImage,int nX, int nY )
{

    LoadImage(lpImage);
    m_showType = 1;
    m_pngcount = nX/16;
}

void CControlUIEx::PaintBkImage( HDC hDC )
{
    if (m_showType == 0)
    {
        if (!m_pLogoIco)
            return;

        RECT rz = GetPos();

		RECT rcDest = rz;
		if (m_diBk.rcDestOffset.left != 0 || m_diBk.rcDestOffset.top != 0 ||
			m_diBk.rcDestOffset.right != 0 || m_diBk.rcDestOffset.bottom != 0) {
			rcDest.left = rz.left + m_diBk.rcDestOffset.left;
			rcDest.top = rz.top + m_diBk.rcDestOffset.top;
			rcDest.right = rz.left + m_diBk.rcDestOffset.right;
			if (rcDest.right > rz.right) rcDest.right = rz.right;
			rcDest.bottom = rz.top + m_diBk.rcDestOffset.bottom;
			if (rcDest.bottom > rz.bottom) rcDest.bottom = rz.bottom;
		}

        ::DrawIconEx(hDC, rcDest.left, rcDest.top , m_pLogoIco, 16, 16, 0, NULL,  DI_NORMAL);
    }
    else if (m_showType == 1)
    {
        if(!m_pImageInfo)
            return ;

        RECT rc = GetPos();
        RECT rcBmp ;
        rcBmp.left = m_pngcurindex*16;
        rcBmp.top = 0;
        rcBmp.right = rcBmp.left + 16;
        rcBmp.bottom = rcBmp.top + 16;
        RECT rcCorner = {0,0,0,0};
        CRenderEngine::DrawImage(hDC,m_pImageInfo->hBitmap,rc,rc,rcBmp,rcCorner,true);
    }
    else
    {
        __super::PaintBkImage(hDC);
    }
}

void CControlUIEx::OnTimer( WPARAM wParam,LPARAM lParam )
{
    if (wParam == TIMER_WEB_WAITING)
    {//
        do 
        {
            if (m_pngcurindex < m_pngcount - 1)
                break;

            m_pngcurindex = 0;
            Invalidate();
            return;

        } while (FALSE);

        Invalidate();
        m_pngcurindex++;

    }
}


////////////////////////////////////////////////////////////////////////////
//
//
//
////////////////////////////////////////////////////////////////////////////
CTabItemContainer::CTabItemContainer(TabBase* ptBase) : m_nEvent(type_release)
, m_ptTabBase(ptBase)
{
    m_nRunSymbol = PAGE_NOMAL;
    m_pstuPageInfo = NULL;
    memset(&m_rcMousePressTab,0,sizeof(RECT));
    m_bDragBegin = FALSE;
	m_bDraged = FALSE;
	Dragpt.x = -1;
    m_nMouseStep = 0;
    m_nTabIndex = 0;
    m_MinIndex = -1;
    m_MaxIndex = -1;
    m_nStep = 20;
}

CTabItemContainer::~CTabItemContainer()
{
    if (m_pstuPageInfo)
    {
        delete m_pstuPageInfo;
        m_pstuPageInfo = NULL;
    }

    registr.RemoveAll();
}

void CTabItemContainer::AddNotifWebIcon(){
	registr.Add(this,NotificationStype::NOTIFYSERVICE_WEBLOGO,notification::Source<CTabItemContainer>(this));
}

void CTabItemContainer::SetPageInfo( PAGEVIEWINFO* pageinfo )
{
	ASSERT(pageinfo&&pageinfo->hWndPage);
    m_pstuPageInfo = pageinfo;
}

PAGEVIEWINFO* CTabItemContainer::GetPageInfo() const
{
    return m_pstuPageInfo;
}

HWND CTabItemContainer::GetPagehWnd() const
{
    if (m_pstuPageInfo)
    {
        if (::IsWindow(m_pstuPageInfo->hWndPage))
            return m_pstuPageInfo->hWndPage;	
    }

    return NULL;
}

void CTabItemContainer::GetRBrowser(std::wstring &url, int &size, bool &show)
{
	if (m_pstuPageInfo){
		url = m_pstuPageInfo->rurl;
		size = m_pstuPageInfo->rsize;
		show =  m_pstuPageInfo->rshow;
	}
}

void CTabItemContainer::SetRBrowser(std::wstring &url, int &size, bool &show)
{
	if (m_pstuPageInfo){
		m_pstuPageInfo->rurl = url;
		m_pstuPageInfo->rsize = size;
		m_pstuPageInfo->rshow = show;
	}
}


HWND CTabItemContainer::GetPageManagerhWnd() const
{
    if (m_pstuPageInfo)
    {
        if (::IsWindow(m_pstuPageInfo->hWndPageManager))
            return m_pstuPageInfo->hWndPageManager;	
    }

    return NULL;

}


void CTabItemContainer::SetPageTop()
{
    PAGEVIEWINFO* pPageView = (PAGEVIEWINFO*)m_pstuPageInfo;
    if (!pPageView)
        return;
    m_nRunSymbol = PAGE_TOP;
#ifdef WEB_CHILD_WINDOW
    CWndTopOpt::GetInterface()->SetPageWnd(pPageView->hWndPage);
#endif
}

void CTabItemContainer::SetPageSize()
{
    m_nRunSymbol = PAGE_SIZE;
    PAGEVIEWINFO* pPageView = (PAGEVIEWINFO*)m_pstuPageInfo;
    if (!pPageView)
        return;

#ifdef WEB_CHILD_WINDOW
    CWndTopOpt::GetInterface()->SetPagePos();
#endif
}

void CTabItemContainer::SetPageMove()
{
    m_nRunSymbol = PAGE_MOVE;

    PAGEVIEWINFO* pPageView = (PAGEVIEWINFO*)m_pstuPageInfo;
    if (!pPageView)
        return;

}

void CTabItemContainer::SetGoBackState( BOOL bEnable )
{
    if (m_pstuPageInfo)
    {
        m_pstuPageInfo->bIsGoBack = bEnable;
    }
}

void CTabItemContainer::SetForwardState( BOOL bEnable )
{
    if (m_pstuPageInfo)
    {
        m_pstuPageInfo->bIsGoForward = bEnable;
    }
}
void CTabItemContainer::DblClick( TEventUI& event ){

}

void CTabItemContainer::MouseMove( TEventUI& event )
{

    if (m_nEvent == type_pree)
    {
		if (GetAsyncKeyState(VK_LBUTTON) & 0x8000)
		{
			m_nEvent = type_drage;
			if (!m_bDraged)
				GetCursorPos(&Dragpt);
		}
    }

    if (m_nEvent == type_drage)
    {		
        if (!m_ptTabBase)
            return;

        if(!m_ptTabBase->IsSlide())
            return;

		if (!m_bDraged)
		{
			POINT pt;
			GetCursorPos(&pt);
			if ((pt.x != Dragpt.x) ||
				(pt.y != Dragpt.y))
			{
				//OutputDebugString(_T("m_bDraged true"));
				m_bDraged = TRUE;
			}
		}

		if (!m_bDraged)
			return;

        CDuiRect rc;
        GetDragRect(&rc);
        SetPos(rc);


        int nMaxIndex = m_ptTabBase->GetIndexFromPoint(rc.CenterPoint());

        int nMindex = nMaxIndex;

		//CString sT;
		//sT.Format(_T("MouseMove %d:%d"), m_nTabIndex, nMaxIndex);
		//OutputDebugString(sT);

        int nRCount = GetSlideCount(m_nTabIndex, nMaxIndex);
        int nLCount = GetSlideCount(nMindex, m_nTabIndex);

        int nRIndex = m_nTabIndex;
        int nLIndex = m_nTabIndex;

        SLIDE_ITEM slideitem;

        for(int i = 0; i< nRCount; i++)
        {
            nRIndex++;
            if (nRIndex > m_MaxIndex && m_ptTabBase->IsEnableDrag(nRIndex))
            {
                RECT rcItem;
                CControlUI* pContainer = m_ptTabBase->GetControlFromeIndex(nRIndex);
                rcItem = pContainer->GetPos();

                slideitem.pControl = pContainer;
                slideitem.szTab.cx = rcItem.right - rcItem.left;
                slideitem.szTab.cy = rcItem.bottom - rcItem.top;
                slideitem.index		= nRIndex;
                slideitem.pt.x		= rcItem.left;
                slideitem.pt.y		= rcItem.top;
                slideitem.leftX		= rcItem.left - (rc.right - rc.left);
                slideitem.rightX	= rcItem.left;
                slideitem.bInvalid  = TRUE;
                m_RSlideList.push_back(slideitem);
            }
        }

        for(int i = 0; i< nLCount; i++)
        {
            nLIndex--;
            if (nLIndex < m_MinIndex && m_ptTabBase->IsEnableDrag(nLIndex))
            {
                RECT rcItem;
                CControlUI* pContainer = m_ptTabBase->GetControlFromeIndex(nLIndex);
                rcItem = pContainer->GetPos();

                slideitem.pControl = pContainer;
                slideitem.szTab.cx = rcItem.right - rcItem.left;
                slideitem.szTab.cy = rcItem.bottom - rcItem.top;
                slideitem.index		= nLIndex;
                slideitem.pt.x		= rcItem.left;
                slideitem.pt.y		= rcItem.top;
                slideitem.leftX		= rcItem.left;
                slideitem.rightX	= rcItem.left + (rc.right - rc.left);
                slideitem.bInvalid  = TRUE;
                m_LSlideList.push_back(slideitem);
            }
        }

        if(nMaxIndex > m_MaxIndex) m_MaxIndex = nMaxIndex;
        if(nMindex < m_MinIndex) m_MinIndex = nMindex;


        if (nLCount>0 || nRCount > 0 )
        {
            if (!ThreadWnd::GetInterface()->IsRuning())
            {
                m_bDragBegin = TRUE;
                ThreadWnd::GetInterface()->SetThreadProxy(this);
                ThreadWnd::GetInterface()->Startthread();
            }
        }
    }
}

void CTabItemContainer::MousePress( TEventUI& event )
{

    m_nEvent = type_pree;

    if (!m_ptTabBase)
        return;

    if(!m_ptTabBase->IsSlide())
        return;

    m_LSlideList.clear();
    m_RSlideList.clear();

    m_rcMousePressTab = GetPos();
    GetCursorPos(&m_ptMousePress);
    m_nTabIndex = m_ptTabBase->GetControlIndex(this);
    m_MinIndex = m_nTabIndex;
    m_MaxIndex = m_nTabIndex;
}

void CTabItemContainer::MouseRelese( TEventUI& event )
{
    m_nEvent = type_release;

    if (!m_ptTabBase)
        return;

    if(!m_ptTabBase->IsSlide())
        return;

    ThreadWnd::GetInterface()->Stopthread();

    m_LSlideList.clear();
    m_RSlideList.clear();

    CDuiRect rcItem;

    if (!m_bDragBegin)
    {
        SetPos(m_rcMousePressTab);
        return;
    }


    m_bDragBegin = FALSE;
	m_bDraged = FALSE;
	Dragpt.x = -1;
	//OutputDebugString(_T("m_bDraged FALSE"));
    int nDragItem = m_nTabIndex;
    m_nTabIndex = -1;

    if (nDragItem == -1)
        return;

    GetDragRect(&rcItem);

    int nIndex = m_ptTabBase->GetIndexFromPoint(rcItem.CenterPoint());

    if (nIndex == nDragItem) 
    {
        m_nTabIndex = nIndex;
        return ;
    }

    m_ptTabBase->SetItemIndex(nIndex, this);

}

void CTabItemContainer::MouseLeave()
{
    if (m_nEvent != type_drage)
        m_nEvent = type_leave;
}

void CTabItemContainer::DragedFunction()
{
	TabControlModel* pTabModel = (TabControlModel*)m_ptTabBase;
	if (pTabModel)
		pTabModel->DragedFunction();
}

void CTabItemContainer::AnimationFrame()
{
    if(!m_ptTabBase->IsSlide())
        return;

    Slide();
}

void CTabItemContainer::AnimationEnd()
{
}

void CTabItemContainer::AnimationBegin()
{
}

UINT CTabItemContainer::GetSlideCount(int nDragIndex, int nMaxIndex)
{
    UINT uSlideCount = nMaxIndex - nDragIndex;
    return uSlideCount;
}

void CTabItemContainer::Slide()
{
    if (!m_ptTabBase)
        return;

    CDuiRect rcCurTab;
    GetDragRect(&rcCurTab);

    int nCenterPointIndex = m_ptTabBase->GetIndexFromPoint(rcCurTab.CenterPoint()); //过半的就产生滑动效果

    int nMinIndex = nCenterPointIndex;
    int nMaxIndex = nCenterPointIndex;
    LONG lCount = 0;
    LONG lCount2 = 0;

    //处理拖拽项右边的滑动项
    for (int i = 0; i < (int)m_RSlideList.size(); i++)
    {
        if (nMaxIndex < m_RSlideList[i].index)
        {
            if (m_RSlideList[i].pt.x < m_RSlideList[i].rightX)
                lCount ++;

            m_RSlideList[i].pt.x += m_nStep;

            if (m_RSlideList[i].pt.x > m_RSlideList[i].rightX)
                m_RSlideList[i].pt.x = m_RSlideList[i].rightX;
        }
        else
        {
            if (m_RSlideList[i].pt.x > m_RSlideList[i].leftX)
                lCount ++;

            m_RSlideList[i].pt.x -= m_nStep;

            if (m_RSlideList[i].pt.x < m_RSlideList[i].leftX)
                m_RSlideList[i].pt.x = m_RSlideList[i].leftX;
        }
        if (lCount > 0)
        {
            RECT rcNewTab;
            rcNewTab.left = m_RSlideList[i].pt.x;
            rcNewTab.top = m_RSlideList[i].pt.y;
            rcNewTab.right = rcNewTab.left + m_RSlideList[i].szTab.cx;
            rcNewTab.bottom = rcNewTab.top + m_RSlideList[i].szTab.cy;
            m_RSlideList[i].pControl->SetPos(rcNewTab);
            m_RSlideList[i].bInvalid = FALSE;

        }
    }

    //处理在拖动项左边的滑动项
    for (int i = 0; i < (int)m_LSlideList.size(); i++) 
    {
        if (nMinIndex <= m_LSlideList[i].index)
        {

            if (m_LSlideList[i].pt.x < m_LSlideList[i].rightX)
                lCount2 ++;
            m_LSlideList[i].pt.x += m_nStep;

            if (m_LSlideList[i].pt.x > m_LSlideList[i].rightX)
                m_LSlideList[i].pt.x = m_LSlideList[i].rightX;

        }
        else
        {

            if (m_LSlideList[i].pt.x < m_LSlideList[i].rightX)
                lCount2 ++;

            m_LSlideList[i].pt.x -= m_nStep;

            if (m_LSlideList[i].pt.x < m_LSlideList[i].leftX)
                m_LSlideList[i].pt.x = m_LSlideList[i].leftX;
        }

        if (lCount2 > 0)
        {
            RECT rcNewTab;
            rcNewTab.left = m_LSlideList[i].pt.x;
            rcNewTab.top = m_LSlideList[i].pt.y;
            rcNewTab.right = rcNewTab.left + m_LSlideList[i].szTab.cx;
            rcNewTab.bottom = rcNewTab.top + m_LSlideList[i].szTab.cy;
            m_LSlideList[i].pControl->SetPos(rcNewTab);
            m_LSlideList[i].bInvalid = FALSE;
        }
    }	

}

BOOL CTabItemContainer::GetDragRect( const LPRECT lpRect )
{
    if (!m_ptTabBase)
        return FALSE;

    POINT pt = {0};
    GetCursorPos(&pt);

    m_nMouseStep = pt.x - m_ptMousePress.x;
    int nW = m_rcMousePressTab.right - m_rcMousePressTab.left;

    RECT rc = m_rcMousePressTab;
    rc.left += m_nMouseStep;
    rc.right = rc.left + nW;

    RECT rcParent = {0};
    rcParent = GetParent()->GetPos();
    CControlUI* pAdd = m_ptTabBase->GetAddKeyControl();
    CDuiRect rcAdd;
    if (pAdd)
        rcAdd = pAdd->GetPos();

    if (rcParent.left >= rc.left )//不超出tab显示的容器区域
    {
        rc.left = rcParent.left;
        rc.right = rc.left + nW;
    }

    if ((rcParent.right - rcAdd.GetWidth()) <= rc.right)
    {
        rc.right = rcParent.right - rcAdd.GetWidth();
        rc.left = rc.right - nW;

    }

    ::CopyRect(lpRect,&rc);

    return TRUE;
}

void CTabItemContainer::ResetIcon()
{
	CControlUIEx* pLogo = static_cast<CControlUIEx*>(GetItemAt(2));
	if (pLogo)
		pLogo->SetVisible(false);
	pLogo = static_cast<CControlUIEx*>(GetItemAt(3));
	if (pLogo)
	{
		pLogo->SetVisible();
		pLogo->Invalidate();
	}
}

void CTabItemContainer::Observe( int type,const notification::NotificationSource& sourec )
{
    if (type == NotificationStype::NOTIFYSERVICE_WEBLOGO)
    {
		CControlUIEx* pLogo = static_cast<CControlUIEx*>(GetItemAt(3));
		if (pLogo)
			pLogo->SetVisible(false);
		pLogo = static_cast<CControlUIEx*>(GetItemAt(2));
		if (pLogo)
		{
			PAGEVIEWINFO* pInfo = GetPageInfo();
			if (pInfo)
			{
				pLogo->LoadIcon(pInfo->pPageIcoPath, 16, 16);
				pLogo->SetVisible();
				pLogo->Invalidate();
			}
		}

        m_ptTabBase->GetPaintManagerUI()->KillTimer(this);
    }
}

void CTabItemContainer::DoEvent( DuiLib::TEventUI& event )
{

    if (event.Type ==UIEVENT_TIMER)
    {
        if (event.wParam == TIMER_WEB_WAITING)
        {
            CControlUIEx* pLogo = static_cast<CControlUIEx*>(GetItemAt(3));
            if (!pLogo)
                return;
            
            pLogo->OnTimer(event.wParam,event.lParam);
            
        }
    }

    __super::DoEvent(event);
}

//////////////////////////////////////////////////////////////////////////
//
//
//
//////////////////////////////////////////////////////////////////////////
CTabItem2::CTabItem2(DuiLib::INotifyUI* pListen) : m_pMouseEvent(NULL),    m_nShowType(1),
pListen_(pListen)
{
}

CTabItem3::CTabItem3(DuiLib::INotifyUI* pListen) : CTabItem2(pListen)
{
	bDraged = FALSE;
}


CTabItem2::~CTabItem2( void )
{

}

void CTabItem2::PaintStatusImage( HDC hDC )
{
    if(m_nShowType == 0)
    {
        CButtonUI::PaintStatusImage(hDC);
        return ;
    }

    //主要是为了去掉 父类的 m_uButtonState &= ~UISTATE_PUSHED;
    int nnn = m_uButtonState & UISTATE_PUSHED;
    if( (m_uButtonState & UISTATE_HOT) != 0 && IsSelected() && !m_diSelectedHot.sDrawString.IsEmpty()) {
        if( !DrawImage(hDC, m_diSelectedHot) )
			m_diSelectedHot.sDrawString.Empty();
        else goto Label_ForeImage;
    }
    else if( (m_uButtonState & UISTATE_SELECTED) != 0 || (m_uButtonState & UISTATE_PUSHED) != 0) {
        if( !m_diSelected.sDrawString.IsEmpty() ) {
            if( !DrawImage(hDC, m_diSelected) ) m_diSelected.sDrawString.Empty();
            else goto Label_ForeImage;
        }
        else if(m_dwSelectedBkColor != 0) {
            CRenderEngine::DrawColor(hDC, m_rcPaint, GetAdjustColor(m_dwSelectedBkColor));
            return;
        }	
    }

    CButtonUI::PaintStatusImage(hDC);

Label_ForeImage:
    if( !m_diFore.sDrawString.IsEmpty() ) {
        if( !DrawImage(hDC, m_diFore) ) m_diFore.sDrawString.Empty();
    }
}

void CTabItem2::PaintText( HDC hDC )
{
    if( IsFocused() ) m_uButtonState |= UISTATE_FOCUSED;
    else m_uButtonState &= ~ UISTATE_FOCUSED;
    if( !IsEnabled() ) m_uButtonState |= UISTATE_DISABLED;
    else m_uButtonState &= ~ UISTATE_DISABLED;

    if( m_dwTextColor == 0 ) m_dwTextColor = m_pManager->GetDefaultFontColor();
    if( m_dwDisabledTextColor == 0 ) m_dwDisabledTextColor = m_pManager->GetDefaultDisabledColor();

    if( m_sText.IsEmpty() ) return;
    int nLinks = 0;
    RECT rc = m_rcItem;
    rc.left += m_rcTextPadding.left;
    rc.right -= m_rcTextPadding.right;
    rc.top += m_rcTextPadding.top;
    rc.bottom -= m_rcTextPadding.bottom;

    DWORD clrColor = IsEnabled()?m_dwTextColor:m_dwDisabledTextColor;

    if( ((m_uButtonState & UISTATE_PUSHED) != 0) && (GetPushedTextColor() != 0) )
        clrColor = GetPushedTextColor();
    else if( ((m_uButtonState & UISTATE_HOT) != 0) && (GetHotTextColor() != 0) )
        clrColor = GetHotTextColor();
    else if( ((m_uButtonState & UISTATE_FOCUSED) != 0) && (GetFocusedTextColor() != 0) )
        clrColor = GetFocusedTextColor();

    if( !m_bShowHtml )
    {
        rc.left += 27;
        rc.right -= 20;
        rc.top += 5;
        CRenderEngine::DrawText(hDC, m_pManager, rc, m_sText, clrColor, \
            m_iFont, m_uTextStyle);
    }
}

void CTabItem3::PaintText(HDC hDC)
{
	if (IsFocused()) m_uButtonState |= UISTATE_FOCUSED;
	else m_uButtonState &= ~UISTATE_FOCUSED;
	if (!IsEnabled()) m_uButtonState |= UISTATE_DISABLED;
	else m_uButtonState &= ~UISTATE_DISABLED;

	if (m_dwTextColor == 0) m_dwTextColor = m_pManager->GetDefaultFontColor();
	if (m_dwDisabledTextColor == 0) m_dwDisabledTextColor = m_pManager->GetDefaultDisabledColor();

	if (m_sText.IsEmpty()) return;
	int nLinks = 0;
	RECT rc = m_rcItem;
	rc.left += m_rcTextPadding.left;
	rc.right -= m_rcTextPadding.right;
	rc.top += m_rcTextPadding.top;
	rc.bottom -= m_rcTextPadding.bottom;

	DWORD clrColor = IsEnabled() ? m_dwTextColor : m_dwDisabledTextColor;

	if (((m_uButtonState & UISTATE_PUSHED) != 0) && (GetPushedTextColor() != 0))
		clrColor = GetPushedTextColor();
	else if (((m_uButtonState & UISTATE_HOT) != 0) && (GetHotTextColor() != 0))
		clrColor = GetHotTextColor();
	else if (((m_uButtonState & UISTATE_FOCUSED) != 0) && (GetFocusedTextColor() != 0))
		clrColor = GetFocusedTextColor();

	if (!m_bShowHtml)
	{
		rc.left += 22;
		rc.right -= 4;
		rc.top += 2;
		CRenderEngine::DrawText(hDC, m_pManager, rc, m_sText, clrColor, \
			m_iFont, m_uTextStyle);
	}
}

void CTabItem2::DoEvent( TEventUI& event )
{
    if (m_nShowType == 0)
    {//没有选择模式的  例如书签
        if( event.Type == UIEVENT_BUTTONUP )
        {
            if( (m_uButtonState & UISTATE_CAPTURED) != 0 ) {
                if( ::PtInRect(&m_rcItem, event.ptMouse) ) 
                    Activate();
                m_uButtonState &= ~(UISTATE_PUSHED | UISTATE_CAPTURED);
                Invalidate();
            }
            return;
        }
        COptionUI::DoEvent(event);
        return;
    }

    if (event.Type != UIEVENT_SETCURSOR && event.Type != UIEVENT_BUTTONUP)
    {
        if (event.Type == UIEVENT_BUTTONDOWN || event.Type == UIEVENT_RBUTTONDOWN || event.Type == UIEVENT_DBLCLICK)
        {			
            if (!m_bSelected && m_nShowType == 1)
                Selected(true);

			if (event.Type == UIEVENT_BUTTONDOWN)
			{
				if (m_pMouseEvent)
					m_pMouseEvent->MousePress(event);
			}

			if (event.Type == UIEVENT_DBLCLICK)
			{
				if( !CControlUI::Activate() ) return;

				if (pListen_ != NULL)
				{
					TNotifyUI Msg;
					Msg.pSender = this;
					Msg.sType = DUI_MSGTYPE_ITEMDBCLICK;
					Msg.wParam = 0;
					Msg.lParam = 0;

					pListen_->Notify(Msg);
				}

			}
            return ;
        }
		else if (event.Type == UIEVENT_MOUSEMOVE)
		{
			if (m_pMouseEvent)
				m_pMouseEvent->MouseMove(event);
		}
		else if (event.Type == UIEVENT_MOUSELEAVE)
        {
            if (m_pMouseEvent)
                m_pMouseEvent->MouseLeave();
        }
        COptionUI::DoEvent(event);
    }
    else if (event.Type == UIEVENT_SETCURSOR)
    {
        ::SetCursor(::LoadCursor(NULL, MAKEINTRESOURCE(IDC_ARROW)));
    }
    else if (event.Type == UIEVENT_BUTTONUP)
    {
        if (m_pMouseEvent)
            m_pMouseEvent->MouseRelese(event);

    }


}

void CTabItem3::DoEvent(TEventUI& event)
{
	if (event.Type == UIEVENT_BUTTONUP)
	{
		//CString sStr;
			
		if (!CControlUI::Activate()) return;

		CMouseEvent* pMouseEvent = GetMouseEvent();
		if (pMouseEvent)
		{
			CTabItemContainer* pTab = (CTabItemContainer*)(pMouseEvent);

			//sStr.Format(_T("CTabItem3::DoEvent %d"), pTab->IsDrag());
			//OutputDebugString(sStr);

			if (!pTab->IsDrag())
				PostNotify(iIndex);
			else
			{
				pTab->ResetDrag();
				if(pTab->IsDragBegin())
					bDraged = TRUE;
			}
		}
	}

	CTabItem2::DoEvent(event);

	if (bDraged)
	{
		bDraged = FALSE;

		CMouseEvent* pMouseEvent = GetMouseEvent();
		if (pMouseEvent)
		{
			CTabItemContainer* pTab = (CTabItemContainer*)(pMouseEvent);
			pTab->DragedFunction();
		}
	}	
}

bool CTabItem2::Activate()
{
    if( !PostNotify() ) return false;
    if( !m_sGroupName.IsEmpty() ) Selected(true);
    else Selected(!m_bSelected);

    return true;

}

void CTabItem2::SetContainerPtr(CMouseEvent* pMouseEvent)
{
    if (pMouseEvent)
        m_pMouseEvent = pMouseEvent;
}

bool CTabItem2::PostNotify(WPARAM wParam)
{
    if( !CControlUI::Activate() ) return false;

    if (pListen_ != NULL)
    {
        TNotifyUI Msg;
        Msg.pSender = this;
        Msg.sType = DUI_MSGTYPE_CLICK;
        Msg.wParam = wParam;
        Msg.lParam = 0;

        pListen_->Notify(Msg);
    }

    //if( m_pManager != NULL ) 
    //    m_pManager->SendNotify(this, DUI_MSGTYPE_CLICK);
    return true;

}

bool CTabItem2::PostNotifyDraged(int Index, int Id)
{
	if (!CControlUI::Activate()) return false;

	if (pListen_ != NULL)
	{
		TNotifyUI Msg;
		Msg.pSender = this;
		Msg.sType = DUI_MSGTYPE_DROPDOWN;
		Msg.wParam = Index;
		Msg.lParam = Id;

		pListen_->Notify(Msg);
	}

	//if( m_pManager != NULL ) 
	//    m_pManager->SendNotify(this, DUI_MSGTYPE_CLICK);
	return true;
}

//////////////////////////////////////////////////////////////////////////
//
//
//
//////////////////////////////////////////////////////////////////////////
TabControlModel::TabControlModel()
{
    m_bSlide = TRUE;
    m_pSelectTab = NULL;
    m_pTabPageManger = NULL;
}

TabControlModel::~TabControlModel()
{

}

void TabControlModel::Init(CPaintManagerUI* pMgr, CHorizontalLayoutUI2* pTabMgr)
{
    m_pTabPageManger = (CHorizontalLayoutUI2*)pTabMgr;
    m_pTabPageManger->SetAutoDestroy(false);
    m_pPaintManager = pMgr;

}

int TabControlModel::GetControlIndex( CControlUI* pTimer )
{
    return 	m_pTabPageManger->GetItemIndex(pTimer);

}

CControlUI* TabControlModel::GetControlFromeIndex( int nIndex )
{
    return 	m_pTabPageManger->GetItemAt(nIndex);
}

int TabControlModel::GetIndexFromPoint( POINT pt  )
{
    RECT rcFirstItem;
    GetItemRect(0, &rcFirstItem);

    if (pt.x < rcFirstItem.left) //如果给的坐标点在第一个标签页的左边，返回第一个 标签页的索引
        return 0;

    RECT rcLastItem;
    int nCount = m_pTabPageManger->GetCount() - 1;
    GetItemRect(nCount - 1, &rcLastItem);
    if ( pt.x > rcLastItem.left) //如果给的坐标点在最后一个标签页的左边，返回第一个 标签页的索引
        return nCount -1;

    int nIndex = ItemFromPoint(pt);

    //越界处理
    if (nIndex < 0 && nCount > 0)
        nIndex = 0;

    if (nIndex >= nCount)
        nIndex = nCount - 1;

    return nIndex;

}

BOOL TabControlModel::IsEnableDrag( int nIndex )
{
    if (nIndex < 0 || nIndex >= m_pTabPageManger->GetCount())
        return FALSE;


    return TRUE;
}

CControlUI* TabControlModel::GetFrontControl(int Index)
{
    CControlUI* pControl = NULL;

    if (m_pTabPageManger->GetCount() > 1)
    {
        pControl = m_pTabPageManger->GetItemAt(0);  //不包含加号
    }

    return pControl;
}

CControlUI* TabControlModel::GetAddKeyControl()
{
    CControlUI* pControl = NULL;

    if (m_pTabPageManger->GetCount() > 1)
        pControl = m_pTabPageManger->GetItemAt(m_pTabPageManger->GetCount() - 1);  //不包含加号

    return pControl;

}

BOOL TabControlModel::IsSlide()
{
    return m_bSlide;
}

void TabControlModel::DragedFunction()
{
	int iCount = m_pTabPageManger->GetCount();
	if (iCount <= 1)
		return;
	for (int i = 0; i < (iCount - 1); i++)
	{
		CControlUI* pControl = m_pTabPageManger->GetItemAt(i);
		if (pControl)
		{
			//OutputDebugString(pControl->GetName());
			CTabItemContainer* pCon = (CTabItemContainer*)pControl;
			CTabItem3* pTabItem = (CTabItem3*)pCon->GetItemAt(0);
			if (pTabItem)
			{
				CString sStr;
				sStr.Format(_T("%d:%s"), pTabItem->GetId(), pTabItem->GetText());
				OutputDebugString(sStr);
				if (i != pTabItem->GetId())
				{
					pTabItem->PostNotifyDraged(pTabItem->GetIndex(), i);
					pTabItem->SetId(i);
				}
			}
		}
	}
}

BOOL TabControlModel::GetItemRect(int nIndex, LPRECT lprcItem)
{
    if (!IsEnableDrag(nIndex)) //无效索引返回空矩形
    {
        ::SetRectEmpty(lprcItem);
        return FALSE;
    }

    RECT rcMgr = m_pTabPageManger->GetPos();

    CDuiRect rcFrontTab;
    if (GetFrontControl())
    {
        rcFrontTab = GetFrontControl()->GetPos();
    }

    lprcItem->left	= rcMgr.left + nIndex * (rcFrontTab.GetWidth());
    lprcItem->right	= lprcItem->left + rcFrontTab.GetWidth();


    lprcItem->top		= rcMgr.top;
    lprcItem->bottom	= rcMgr.bottom;
    return TRUE;
}

int TabControlModel::ItemFromPoint(POINT pt)
{
    RECT rcMgr = m_pTabPageManger->GetPos();

    if (pt.x < rcMgr.left || pt.x > rcMgr.right )
        return -1;

    if ((pt.y < rcMgr.top || pt.y > rcMgr.bottom) )
        return -1;


    RECT rcItem;
    int nCount = m_pTabPageManger->GetCount() - 2;
    for (int i = 0; i < nCount; i ++)
    {
        GetItemRect(i, &rcItem);

		//CString sT;
		//sT.Format(_T("%d %d:%d %d"), i, rcItem.left, rcItem.right, pt.x);
		//OutputDebugString(sT);


        if (::PtInRect(&rcItem, pt))
        {
            return i;
        }
    }

    return -1;
}

int TabControlModel::IsTabValid( CControlUI* pControl )
{
    if (!pControl)
        return 0;

    CDuiString str = pControl->GetName();

    if (str == L"itempage")
        return 1;
    else if (str == L"addtab")
        return 2;
    else if (str.IsEmpty())
        return 3;

    return 0;
}

BOOL TabControlModel::SetItemIndex( int iIndex, CControlUI* pControl )
{
    if (!m_pTabPageManger)
        return FALSE;

    m_pTabPageManger->SetItemIndex(pControl,iIndex,true);
    return TRUE;
}

int TabControlModel::GetItemIndex( CControlUI* pControl ) const
{
    return m_pTabPageManger->GetItemIndex(pControl);
}

CControlUI* TabControlModel::GetItemIndex( int nIndex ) const
{
    return m_pTabPageManger->GetItemAt(nIndex);
}

CTabItemContainer* TabControlModel::GetContainerFromWnd(HWND pageWnd)
{
	int nCount = m_pTabPageManger->GetCount();
	for (int i = 0; i < nCount; i++)
	{
		 CTabItemContainer* pTemp = (CTabItemContainer*)m_pTabPageManger->GetItemAt(i);

		 if (pTemp->GetPagehWnd() == pageWnd) {
			 ASSERT(pageWnd);
			 return pTemp;
		 }
	}

	return NULL;
}


void TabControlModel::CloseItem( CTabItemContainer* pTab,int nCloseType/*=closetabtype_item*/ )
{
    if (nCloseType == closetabtype_item)
    {
        m_pTabPageManger->Remove(pTab);
    }
    else if (nCloseType == closetabtype_other)
    {
        m_pSelectTab = pTab;
        CDuiString szName;
        CTabItemContainer* pTemp;
        int nCount = m_pTabPageManger->GetCount();
loop:
        for (int i = 0; i < nCount; i++)
        {
            szName = m_pTabPageManger->GetItemAt(i)->GetName();
            pTemp = (CTabItemContainer*)m_pTabPageManger->GetItemAt(i);
            if (pTab != pTemp && szName != _T("tabadd"))
            {
                m_pTabPageManger->RemoveAt(i);
                nCount = m_pTabPageManger->GetCount();
                goto loop;
            }
        }

        m_pTabPageManger->Invalidate();
    }
    else if (nCloseType == closetabtype_right)
    {
        m_pSelectTab = pTab;
        CDuiString szName;
        CTabItemContainer* pTemp;
        PAGEVIEWINFO*      pTempInfo;
        int nCount = m_pTabPageManger->GetCount();
        BOOL bIsRightItem = FALSE;


        for (int i = 0; i < nCount; )
        {
            szName = m_pTabPageManger->GetItemAt(i)->GetName();
            pTemp = (CTabItemContainer*)m_pTabPageManger->GetItemAt(i);
            if (pTab == pTemp)
            {
                bIsRightItem = TRUE;
                i++;
                continue;
            }

            if (szName != _T("tabadd") && bIsRightItem)
            {
                pTempInfo = pTemp->GetPageInfo();
                ::PostMessage(pTempInfo->hWndPageManager,WM_USER_PAGEVIEWCLOSE,(WPARAM)pTempInfo->hWndPage,0);
                m_pTabPageManger->RemoveAt(i);
                nCount = m_pTabPageManger->GetCount();
            }
            else
                i++;

        }


        m_pTabPageManger->Invalidate();
    }

}

void TabControlModel::SetSelectContainer( CTabItemContainer* pSelectCon )
{
    if (pSelectCon)
    {
        CTabItem2* pTab = (CTabItem2*)pSelectCon->GetItemAt(0);
        if (pTab)
        {
            pTab->Selected(true);
        }
    }
    m_pSelectTab = pSelectCon;
	ATLTRACE(L"\rset item %d\r", pSelectCon);
	m_pTabPageManger->SetSelectChild((CControlUI*)m_pSelectTab);
}

CTabItemContainer* TabControlModel::GetSelectContainer() const
{
    return m_pSelectTab;
}

int TabControlModel::GetItemCount() const
{
    return m_pTabPageManger->GetCount();
}

int TabControlModel::GetChildCount()
{
    return m_pTabPageManger->GetCount();
}

void TabControlModel::AddChildView(CControlUI* pControl)
{
	m_pTabPageManger->Add(pControl);
}

void TabControlModel::AddChildView( CControlUI* pControl, int iIndex )
{
    m_pTabPageManger->AddAt(pControl,iIndex);

}

CPaintManagerUI* TabControlModel::GetPaintManagerUI() const
{
    return m_pPaintManager;
}

void TabControlModel::SetSlide( BOOL bSlide )
{
    m_bSlide = bSlide;
}

BOOL TabControlModel::GetSlide() const
{
    return m_bSlide;
}

RECT TabControlModel::CalcFontWidth( CDuiString& szText )
{

    //Gdiplus::Graphics graphics(m_pPaintManager->GetPaintWindow());
    //GraphicsPath path;
    //FontFamily fontfamily;

    //CDuiString szfontname = L"宋体";
    //int        nfontszie = 16;
    ////if (m_pPaintManager->GetDefaultFontInfo())
    ////{
    ////    szfontname = m_pPaintManager->GetDefaultFontInfo()->sFontName;
    ////    nfontszie = m_pPaintManager->GetDefaultFontInfo()->iSize;
    ////}

    //Font font(szfontname.GetData(),nfontszie,FontStyleBold);
    //font.GetFamily(&fontfamily);

    //StringFormat stringFormat;

    //stringFormat.SetAlignment(StringAlignmentNear);

 
    //PointF startpos;
    //path.AddString(szText.GetData(), szText.GetLength(), &fontfamily,font.GetStyle(),font.GetSize(),startpos,&stringFormat);
    //Rect rcBound;
    //path.GetBounds(&rcBound);
    //RECT rc = {0,0,rcBound.Width,rcBound.Height};
    //return rc;

    RECT rcRect = {0};
    SIZE size;

    if(!m_pPaintManager)
        return rcRect;

    HDC hDC = m_pPaintManager->GetPaintDC();
    TFontInfo* pFontInfo = m_pPaintManager->GetDefaultFontInfo();
    HFONT hOldFont = (HFONT)::SelectObject(hDC,pFontInfo->hFont);
    ::GetTextExtentPoint32(hDC, szText.GetData(), (int)szText.GetLength(), &size);
    ::SelectObject(hDC,hOldFont);

    rcRect.right = rcRect.left + size.cx;
    rcRect.bottom = rcRect.top + size.cy;
    return rcRect;
}

bool TabControlModel::DelChildView( int nIndex )
{
    return m_pTabPageManger->RemoveAt(nIndex);
}
void TabControlModel::DelChildView(DuiLib::CControlUI* pControl)
{
    if(!pControl)
        return;

    m_pTabPageManger->Remove(pControl);
}

void TabControlModel::DelChildViewAll()
{
    //m_pTabPageManger->RemoveAll();
	int nCount = m_pTabPageManger->GetCount();
	if (nCount < 1)
		return;
	for (int i = (nCount - 2); i >=0 ; i--)
		m_pTabPageManger->RemoveAt(i);
}

//////////////////////////////////////////////////////////////////////////
//
//
//
//////////////////////////////////////////////////////////////////////////
CTabControlMgr::CTabControlMgr()
{
    m_pTabModel = NULL;
    g_mapPageHWND.clear();
    m_pListen = NULL;
}

CTabControlMgr::~CTabControlMgr()
{

}

void CTabControlMgr::InitPtr(CPaintManagerUI* pMgr, CHorizontalLayoutUI2* pTabMgr,DuiLib::INotifyUI* pListen  )
{
    assert(pTabMgr);
    assert(pMgr);

    m_pListen = pListen;
    m_pTabModel = new TabControlModel();
    m_pTabModel->Init(pMgr,pTabMgr);
 #define MAXBUFSIZE 20480
	TCHAR MyUrl[MAXBUFSIZE] = { 0 };
	wstring sPath;
	common_space::get_app_path_w(sPath);
	sPath += INIFILENAME;
	GetPrivateProfileString(L"同进程Url", L"OneProcessUrlList", DefaultOneUrl, MyUrl, MAXBUFSIZE, sPath.c_str());
	if (*MyUrl != NULL) {
		CString sList = MyUrl;
		int curPos = 0;
		wstring sUrl = sList.Tokenize(_T(";；"), curPos);
		while (!sUrl.empty())
		{
			m_vecUrlList.push_back(sUrl);
			sUrl = sList.Tokenize(_T(";；"), curPos);
		};
		WritePrivateProfileString(L"同进程Url", L"OneProcessUrlList", MyUrl, sPath.c_str());
	}
}
#include "MainFrame.h"
extern CMainFrame* pMainFrame;
int CTabControlMgr::AddItem(HWND hWndMain, CDuiString szUrl,CDuiString szTabTitle, int ShowType /*= NULL*/
	, std::string sReferer,std::string sJsurl, std::string sCookiePath , std::string sUserAgent)
{
    //if (pMainFrame) {
    //    pMainFrame->SwitchWindow(NULL);
    //}
    int nCount = m_pTabModel->GetChildCount();
    RECT rcLast = {0};
    int nLastWidth=0;

    int nTabWidth = TABITEM_MAX;
    CTabItemContainer* pNewHorLayout = NULL;
    CDuiString szTabWidth;
    szTabWidth.Format(_T("name=\"itemcontainer\" maxwidth=\"%d\" group=\"menubar\""),nTabWidth);

    pNewHorLayout=new CTabItemContainer(m_pTabModel);
    pNewHorLayout->ApplyAttributeList(szTabWidth);

    //在标签HorLayout上增加 OptionUI 和 关闭按钮
    CTabItem2* pTabItem = new CTabItem2(m_pListen);
    CDuiString sText;
    sText.Format(_T("name=\"itempage\" text=\"%s\" float=\"false\" pos=\"0,0,0,0\" maxwidth=\"215\" height=\"25\" menu=\"true\" textcolor=\"#FF000000\" disabledtextcolor=\"#FFA7A6AA\" align=\"center\" normalimage=\"file='tab_bk.png' dest='0,0,215,25' source='0,0,14,24' corner='6,0,6,0'\" hotimage=\"file='tab_bk.png' dest='0,0,215,25' source='28,0,42,24' corner='6,0,6,0'\" pushedimage=\"file='tab_bk.png' dest='0,0,215,25' source='14,0,28,24' corner='6,0,6,0'\" selectedimage=\"file='tab_bk.png' dest='0,0,215,25' source='14,0,28,24' corner='6,0,6,0'\" align=\"left\" group=\"menubar\" "),szTabTitle.GetData());

    pTabItem->SetManager(m_pTabModel->GetPaintManagerUI(),m_pTabModel->GetHorizontalLayoutUI2());
    pTabItem->ApplyAttributeList(sText.GetData());
    pTabItem->SetContainerPtr(pNewHorLayout);
    pNewHorLayout->AddAt(pTabItem,0);

    //关闭
    CButtonUI* pTabCloseBtn = new CButtonUI();
    pTabCloseBtn->SetManager(m_pTabModel->GetPaintManagerUI(),m_pTabModel->GetHorizontalLayoutUI2());
    pTabCloseBtn->ApplyAttributeList(_T("name=\"itemclose\" float=\"true\" pos=\"-20,5,-6,19\" width=\"14\" height=\"14\" normalimage=\"file='tab_close.png' dest='0,0,14,14' source='0,0,14,14'\" hotimage=\"file='tab_close.png' dest='0,0,14,14' source='14,0,28,14'\" pushedimage=\"file='tab_close.png' dest='0,0,14,14' source='28,0,42,14'\" "));
	//pTabCloseBtn->ApplyAttributeList(_T("name=\"itemclose\" float=\"true\" pos=\"196,5,210,19\" width=\"14\" height=\"14\" normalimage=\"file='tab_close.png' dest='0,0,14,14' source='0,0,14,14'\" hotimage=\"file='tab_close.png' dest='0,0,14,14' source='14,0,28,14'\" pushedimage=\"file='tab_close.png' dest='0,0,14,14' source='28,0,42,14'\" "));
	//pTabCloseBtn->ApplyAttributeList(_T("name=\"itemclose\" float=\"-20,5,-6,19\" width=\"14\" height=\"14\" normalimage=\"file='tab_close.png' dest='0,0,14,14' source='0,0,14,14'\" hotimage=\"file='tab_close.png' dest='0,0,14,14' source='14,0,28,14'\" pushedimage=\"file='tab_close.png' dest='0,0,14,14' source='28,0,42,14'\" "));
    pNewHorLayout->AddAt(pTabCloseBtn,1);

    //网站图标
    CControlUIEx* pTabNormal = new CControlUIEx();
    pTabNormal->SetManager(m_pTabModel->GetPaintManagerUI(),m_pTabModel->GetHorizontalLayoutUI2());
    pTabNormal->ApplyAttributeList(_T("name=\"itemnormal\" visible=\"false\" float=\"true\" pos=\"6,4,222,20\" width=\"16\" height=\"16\" bkimage=\"file='itemnormal.png' dest='0,0,16,16' source='0,0,16,16\'\" "));
    //pTabNormal->LoadImage(_T("F:\\浏览器代码\\caiyun\\20130111\\product2\\Debug\\bin\\Skin\\Chrome\\Chrome\\1screen_Chevron.png"));
    pNewHorLayout->AddAt(pTabNormal,2);

    //网站等待图标
    CControlUIEx* pTabWaitting = new CControlUIEx();
    pTabWaitting->SetManager(m_pTabModel->GetPaintManagerUI(),m_pTabModel->GetHorizontalLayoutUI2());
    pTabWaitting->ApplyAttributeList(_T("name=\"itemnormal\" float=\"true\" pos=\"6,4,22,20\" width=\"16\" height=\"16\" bkimage=\"file='itemnormal.png' dest='0,0,16,16' source='0,0,16,16\'\" "));
	std::wstring app_path;
	common_space::get_app_path_w(app_path);
	app_path += L"Skin\\tab_progress.png";
    pTabWaitting->LoadPngIndex(app_path.c_str(),256,16);
    pNewHorLayout->AddAt(pTabWaitting,3);

	//CString sStr;
	//sStr.Format(_T("pTabCloseBtn:%x %x %x"), pTabCloseBtn->GetManager(), pTabNormal->GetManager(), pTabWaitting->GetManager());
	//OutputDebugString(sStr);

    int nIndex = nCount - 1;
    nIndex = nIndex < 0 ? 0 : nIndex;
    m_pTabModel->AddChildView(pNewHorLayout,nIndex);

	if (ShowType == 0) {
		m_pTabModel->SetSelectContainer(pNewHorLayout);
		m_pTabModel->GetPaintManagerUI()->SetTimer(pNewHorLayout, TIMER_WEB_WAITING, TIMER_TIME_WAITING);
		ATLTRACE(L"%d_\r", pNewHorLayout);
		//////////////////////////////////////////////////////////////////////////
		pTabItem->Selected(true);
	}
    //////////////////////////////////////////////////////////////////////////
    //添加页面
//zz     if (g_mapPageHWND.size())
//     {
//         std::map<HWND,std::vector<HWND>>::iterator itr = g_mapPageHWND.begin();
//         for (;itr != g_mapPageHWND.end();itr++)
//         {
//             if ((itr->second).size() < PROCESSOPENPAGEMAXNUM)
//             {
//                 OpenUrl(hWndMain,itr->first,NULL,hWndHide,szUrl,(int)pNewHorLayout,OPENURL_CREATE);
//                 //隐藏
//                 return nIndex;
//             }
//         }
//     }

    //隐藏

    CreateBrowserView(hWndMain,szUrl,(int)pNewHorLayout,sJsurl,sCookiePath,sUserAgent,sReferer,ShowType);

    return nIndex;
}

int CTabControlMgr::AddItem2(int CWidth, HWND hWndMain, CDuiString szTabTitle, LPCTSTR Url, LPCTSTR IconPath)
{
	RECT rcLast = { 0 };
	int nLastWidth = 0;

	//CString sS = szTabTitle;
	int iSize = 4;
	int iWidth = 0;

	int iLen = _tcslen(szTabTitle);
	if (iLen < 4)
		iSize = iLen;
	do
	{
		CString sT = szTabTitle;
		sT = sT.Left(iSize);

		RECT rcText = { 0 };
		CRenderEngine::DrawText(m_pTabModel->GetPaintManagerUI()->GetPaintDC(), m_pTabModel->GetPaintManagerUI(), rcText, sT, RGB(0, 0, 0), -1, DT_CALCRECT | DT_SINGLELINE);
		int iTW = rcText.right - rcText.left;

		if (iTW >= 110)
		{
			iSize--;
			break;
		}

		iWidth = iTW;

		if (iSize >= iLen)
			break;

		iSize++;
	} while (TRUE);

	CString sT = szTabTitle;
	sT = sT.Left(iSize);
	if (iSize < iLen)
	{
		iWidth += 16;
		sT += _T("···");
	}

	//CString sTTT;
	//sTTT.Format(_T("%d:%d %d:%s"), iSize, iLen, iWidth, szTabTitle);
	//OutputDebugString(sTTT);

	int nTabWidth = iWidth + 26;

	if ((CWidth + nTabWidth) > 926)
		return 0;

	CTabItemContainer* pNewHorLayout = NULL;
	CDuiString szTabWidth;
	szTabWidth.Format(_T("name=\"itemcontainer\" minwidth=\"%d\" maxwidth=\"%d\""), nTabWidth, nTabWidth);

	pNewHorLayout = new CTabItemContainer(m_pTabModel);
	pNewHorLayout->ApplyAttributeList(szTabWidth);

	//在标签HorLayout上增加 OptionUI 和 关闭按钮
	CTabItem3* pTabItem = new CTabItem3(m_pListen);
	CDuiString sText;
	//sText.Format(_T("name=\"itemhor\" text=\"%s\" tooltip=\"%s%s\" usertooltip=\"tooltip.xml\" float=\"false\" pos=\"0,0,0,0\" maxwidth=\"180\" height=\"24\" textcolor=\"#FF000000\" disabledtextcolor=\"#FFA7A6AA\" align=\"left\" hotbkcolor=\"#FFC6C6C6\" pushedbkcolor=\"#FFDBDBDB\" "),
	//	sT, szTabTitle, Url);
	sText.Format(_T("name=\"itemhor\" text=\"%s\" float=\"false\" pos=\"0,0,0,0\" height=\"24\" textcolor=\"#FF000000\" disabledtextcolor=\"#FFA7A6AA\" align=\"left\" hotbkcolor=\"#FFC6C6C6\" pushedbkcolor=\"#FFDBDBDB\" tooltip=\"%s%s\""),
		sT,(LPCTSTR)szTabTitle, Url);
	//sText.Format(_T("name=\"itemhor\" text=\"%s\" float=\"false\" pos=\"0,0,0,0\" maxwidth=\"180\" height=\"24\" textcolor=\"#FF000000\" disabledtextcolor=\"#FFA7A6AA\" align=\"left\" hotbkcolor=\"#FFC6C6C6\" pushedbkcolor=\"#FFDBDBDB\" "),
	//	sT);
	//OutputDebugString(sText);
	pTabItem->SetManager(m_pTabModel->GetPaintManagerUI(), m_pTabModel->GetHorizontalLayoutUI2());
	pTabItem->ApplyAttributeList(sText.GetData());

	//sText.Format(_T("11112222%d"),3);
	//pTabItem->SetToolTip(sText);

	pTabItem->SetContainerPtr(pNewHorLayout);
	pNewHorLayout->AddAt(pTabItem, 0);

	CControlUIEx* pTabWaitting = new CControlUIEx();
	pTabWaitting->SetManager(m_pTabModel->GetPaintManagerUI(), m_pTabModel->GetHorizontalLayoutUI2());
	pTabWaitting->ApplyAttributeList(_T("name=\"itemnormal\" float=\"true\" pos=\"4,4,20,20\" width=\"16\" height=\"16\" bkimage=\"file='favicon.png' dest='0,0,16,16' source='0,0,16,16\'\" "));
	if (::PathFileExists(IconPath))
		pTabWaitting->LoadIconW(IconPath, 16, 16);
	else
	{
		std::wstring app_path;
		common_space::get_app_path_w(app_path);
		app_path += L"Skin\\favicon.ico";
		pTabWaitting->LoadIconW(app_path.c_str(), 16, 16);
	}
	pNewHorLayout->AddAt(pTabWaitting, 1);

	int nCount = m_pTabModel->GetChildCount();
	int nIndex = nCount - 1;
	nIndex = nIndex < 0 ? 0 : nIndex;

	//CString sStr;
	//sStr.Format(_T("%d:%s"), nIndex, szTabTitle);
	//OutputDebugString(sStr);

	pTabItem->SetIndex(nIndex);
	m_pTabModel->AddChildView(pNewHorLayout, nIndex);

	//if (ShowType == 0) {
	m_pTabModel->SetSelectContainer(pNewHorLayout);
	//	m_pTabModel->GetPaintManagerUI()->SetTimer(pNewHorLayout, TIMER_WEB_WAITING, TIMER_TIME_WAITING);
	//	ATLTRACE(L"%d_\r", pNewHorLayout);
		//////////////////////////////////////////////////////////////////////////
	//	pTabItem->Selected(true);
	//}

	//CreateBrowserView(hWndMain, szUrl, (int)pNewHorLayout, sJsurl, sCookiePath, sUserAgent, sReferer, ShowType);

	return nTabWidth;
}

extern int ChildProce(LPCTSTR lpCmdLine);
void CTabControlMgr::CreateBrowserView(HWND hWndMain, CDuiString url, int nOpenIndex
	, std::string& sJsurl, std::string& sCookiePath, std::string& sUserAgent, std::string& sReferer,int showType)
{
#ifdef MulitProcess
	BOOL bNewPoce = TRUE;
	if(!url.IsEmpty()){
		for (int i = m_vecUrlList.size() - 1; i >= 0; i--)
		{
			if (url.Find(m_vecUrlList[i].c_str()) >= 0) {
				if (sReferer.empty())
					break;
				wstring sWRef = common_space::str_conv::str2wstr(sReferer);
				if (sWRef.find(m_vecUrlList[i]) != wstring::npos) {
					bNewPoce = FALSE;
				}
				break;
			}
		}
	}
	else
	{
		bNewPoce = FALSE;
	}
#else
	BOOL bNewPoce = FALSE;
#endif
	Json::Value jsroot;
	std::string sUrl = common_space::str_conv::wstr2str(url.GetData());
	jsroot[JSON_KEY_OPENURL] = sUrl;
	jsroot[JSON_KEY_MAINWND] = Json::Value((int)hWndMain);
	jsroot[JSON_KEY_TABINDEX] = Json::Value(nOpenIndex);
	if (!sJsurl.empty())
		jsroot[JSON_KEY_JSURL] = sJsurl;
	else
	{
		jsroot[JSON_KEY_JSURL] = g_scriptUrl;
	}
	jsroot[JSON_KEY_cookiepath] = sCookiePath;
	jsroot[JSON_KEY_ua] = sUserAgent;
	jsroot[JSON_KEY_referer] = sReferer;
	jsroot[JSON_KEY_showtype]= Json::Value(showType);
	std::wstring stemp = common_space::str_conv::str2wstr(jsroot.toStyledString());
	if (!bNewPoce) {
		wstring  app_path = L" ";
		//加密参数
		char* szEncrypt = NULL;
		std::wstring szUrl = _T("");
		decrypt_encrpty_space::decrypt_encrpty_tool_helper::decrpty_encrpty_tool_helper_encrpty(common_space::str_conv::wstr2str(stemp), szEncrypt);
		if (szEncrypt)
		{
			szUrl = common_space::str_conv::str2wstr(szEncrypt);
			delete szEncrypt;
			szEncrypt = NULL;
		}
		app_path += szUrl;
		ChildProce(app_path.c_str());
	}
	else
	{
		//创建进程
		STARTUPINFO    si;
		si.cb = sizeof(STARTUPINFOA);
		si.lpReserved = NULL;
		si.lpDesktop = NULL;
		si.lpTitle = NULL;
		si.dwFlags = 0;
		si.cbReserved2 = 0;
		si.lpReserved2 = NULL;
		LPPROCESS_INFORMATION processInfo = new PROCESS_INFORMATION;
		processInfo->dwProcessId = 0;
		processInfo->dwThreadId = 0;
		processInfo->hProcess = NULL;
		processInfo->hThread = NULL;

		wchar_t wsz_temp[_MAX_PATH] = { 0 };
		::GetModuleFileNameW(NULL, wsz_temp, _MAX_PATH);
		std::wstring app_path;
		app_path.assign(wsz_temp);
		app_path += L" -subwebbrowser";
		app_path += L" ";

		//加密参数
		char* szEncrypt = NULL;
		std::wstring szUrl = _T("");
		decrypt_encrpty_space::decrypt_encrpty_tool_helper::decrpty_encrpty_tool_helper_encrpty(common_space::str_conv::wstr2str(stemp), szEncrypt);
		if (szEncrypt)
		{
			szUrl = common_space::str_conv::str2wstr(szEncrypt);
			delete szEncrypt;
			szEncrypt = NULL;
		}
		app_path += szUrl;
		assert(app_path.size() < 16 * 1024);
		CreateProcessW(NULL, (LPWSTR)app_path.c_str(), NULL, NULL, FALSE, CREATE_NEW_CONSOLE | NORMAL_PRIORITY_CLASS, NULL, NULL, &si, processInfo);
	}
}

void CTabControlMgr::OpenUrl( HWND hWndMain,HWND hWndMangager,HWND hWndPage,HWND hWndPageHide,CDuiString szUrl,int nOpenIndex,openurltype opt )
{	
    Json::Value jsroot;
    std::string sUrl = common_space::str_conv::wstr2str(szUrl.GetData());
    jsroot[JSON_KEY_OPENURL]  = sUrl;
    jsroot[JSON_KEY_MAINWND]  = Json::Value((int)hWndMain);
    jsroot[JSON_KEY_TABINDEX] = Json::Value(nOpenIndex);
    jsroot[JSON_KEY_PAGEWND]  = Json::Value((int) hWndPage);
    jsroot[JSON_KEY_HIDEPAGEWND] = Json::Value((int)hWndPageHide);
    std::string stemp = jsroot.toStyledString();

    //创建普通标签窗口
    COPYDATASTRUCT stCopyData = {0};
    stCopyData.lpData = (VOID*)stemp.c_str();
    stCopyData.cbData = sizeof(char) * (stemp.length() + 1);
    stCopyData.dwData = (ULONG_PTR)hWndMain;
    SendMessage(hWndMangager, WM_COPYDATA, opt, (LPARAM)&stCopyData);
}

void CTabControlMgr::CloseItem( CTabItemContainer* pTab,int nCloseType/*=closetabtype_item*/ )
{
    if(!m_pTabModel)
        return;

    m_pTabModel->CloseItem(pTab,nCloseType);
}

int CTabControlMgr::GetItemCount() const
{
    if(!m_pTabModel)
        return 0;

    return m_pTabModel->GetItemCount();
}

CControlUI* CTabControlMgr::GetItemIndex( int nIndex ) const
{
    if(!m_pTabModel)
        return NULL;

    return m_pTabModel->GetItemIndex(nIndex);
}

int CTabControlMgr::GetItemIndex( CControlUI* pControl ) const
{
    if(!m_pTabModel)
        return 0;

    return m_pTabModel->GetItemIndex(pControl);
}

void CTabControlMgr::SetSelectContainer( CTabItemContainer* pSelectCon )
{
	 if(!m_pTabModel)
        return;

    m_pTabModel->SetSelectContainer(pSelectCon);
}

CTabItemContainer* CTabControlMgr::GetSelectContainer() const
{
	if(!m_pTabModel)
		return NULL;

	return m_pTabModel->GetSelectContainer();
}

CTabItemContainer* CTabControlMgr::GetContainerFromWnd(HWND pageWnd)
{
	if(!m_pTabModel)
		return NULL;

	return m_pTabModel->GetContainerFromWnd(pageWnd);
}

void CTabControlMgr::RemoveAll()
{
	if (m_pTabModel)	
		m_pTabModel->DelChildViewAll();
}

//////////////////////////////////////////////////////////////////////////
//
//
//
//////////////////////////////////////////////////////////////////////////

