// Copyright 2008 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

var x;

// Converts a number to string respecting -0.
function stringify(n) {
  if ((1 / n) === -Infinity) return "-0";
  return String(n);
}

function f(expected, y) {
  function testEval(string, x, y) {
    var mulFunction = Function("x, y", "return " + string);
    return mulFunction(x, y);
  }
  function mulTest(expected, x, y) {
    assertEquals(expected, x * y);
    assertEquals(expected, testEval(stringify(x) + " * y", x, y));
    assertEquals(expected, testEval("x * " + stringify(y), x, y));
    assertEquals(expected, testEval(stringify(x) + " * " + stringify(y), x, y));
  }
  mulTest(expected, x, y);
  mulTest(-expected, -x, y);
  mulTest(-expected, x, -y);
  mulTest(expected, -x, -y);
  if (x === y) return;  // Symmetric cases not necessary.
  mulTest(expected, y, x);
  mulTest(-expected, -y, x);
  mulTest(-expected, y, -x);
  mulTest(expected, -y, -x);
}

x = 262143;
f(0, 0);
f(262143, 1);
f(524286, 2);
f(786429, 3);
f(1048572, 4);
f(1310715, 5);
f(1835001, 7);
f(2097144, 8);
f(2359287, 9);
f(3932145, 15);
f(4194288, 16);
f(4456431, 17);
f(8126433, 31);
f(8388576, 32);
f(8650719, 33);
f(16515009, 63);
f(16777152, 64);
f(17039295, 65);
f(33292161, 127);
f(33554304, 128);
f(33816447, 129);
f(66846465, 255);
f(67108608, 256);
f(67370751, 257);
f(133955073, 511);
f(134217216, 512);
f(134479359, 513);
f(268172289, 1023);
f(268434432, 1024);
f(268696575, 1025);
f(536606721, 2047);
f(536868864, 2048);
f(537131007, 2049);
f(1073475585, 4095);
f(1073737728, 4096);
f(1073999871, 4097);
f(2147213313, 8191);
f(2147475456, 8192);
f(2147737599, 8193);
f(4294688769, 16383);
f(4294950912, 16384);
f(4295213055, 16385);
f(8589639681, 32767);
f(8589901824, 32768);
f(8590163967, 32769);
f(17179541505, 65535);
f(17179803648, 65536);
f(17180065791, 65537);
f(34359345153, 131071);
f(34359607296, 131072);
f(34359869439, 131073);
f(68718952449, 262143);
x = 262144;
f(0, 0);
f(262144, 1);
f(524288, 2);
f(786432, 3);
f(1048576, 4);
f(1310720, 5);
f(1835008, 7);
f(2097152, 8);
f(2359296, 9);
f(3932160, 15);
f(4194304, 16);
f(4456448, 17);
f(8126464, 31);
f(8388608, 32);
f(8650752, 33);
f(16515072, 63);
f(16777216, 64);
f(17039360, 65);
f(33292288, 127);
f(33554432, 128);
f(33816576, 129);
f(66846720, 255);
f(67108864, 256);
f(67371008, 257);
f(133955584, 511);
f(134217728, 512);
f(134479872, 513);
f(268173312, 1023);
f(268435456, 1024);
f(268697600, 1025);
f(536608768, 2047);
f(536870912, 2048);
f(537133056, 2049);
f(1073479680, 4095);
f(1073741824, 4096);
f(1074003968, 4097);
f(2147221504, 8191);
f(2147483648, 8192);
f(2147745792, 8193);
f(4294705152, 16383);
f(4294967296, 16384);
f(4295229440, 16385);
f(8589672448, 32767);
f(8589934592, 32768);
f(8590196736, 32769);
f(17179607040, 65535);
f(17179869184, 65536);
f(17180131328, 65537);
f(34359476224, 131071);
f(34359738368, 131072);
f(34360000512, 131073);
f(68719214592, 262143);
f(68719476736, 262144);
x = 262145;
f(0, 0);
f(262145, 1);
f(524290, 2);
f(786435, 3);
f(1048580, 4);
f(1310725, 5);
f(1835015, 7);
f(2097160, 8);
f(2359305, 9);
f(3932175, 15);
f(4194320, 16);
f(4456465, 17);
f(8126495, 31);
f(8388640, 32);
f(8650785, 33);
f(16515135, 63);
f(16777280, 64);
f(17039425, 65);
f(33292415, 127);
f(33554560, 128);
f(33816705, 129);
f(66846975, 255);
f(67109120, 256);
f(67371265, 257);
f(133956095, 511);
f(134218240, 512);
f(134480385, 513);
f(268174335, 1023);
f(268436480, 1024);
f(268698625, 1025);
f(536610815, 2047);
f(536872960, 2048);
f(537135105, 2049);
f(1073483775, 4095);
f(1073745920, 4096);
f(1074008065, 4097);
f(2147229695, 8191);
f(2147491840, 8192);
f(2147753985, 8193);
f(4294721535, 16383);
f(4294983680, 16384);
f(4295245825, 16385);
f(8589705215, 32767);
f(8589967360, 32768);
f(8590229505, 32769);
f(17179672575, 65535);
f(17179934720, 65536);
f(17180196865, 65537);
f(34359607295, 131071);
f(34359869440, 131072);
f(34360131585, 131073);
f(68719476735, 262143);
f(68719738880, 262144);
f(68720001025, 262145);
x = 524287;
f(0, 0);
f(524287, 1);
f(1048574, 2);
f(1572861, 3);
f(2097148, 4);
f(2621435, 5);
f(3670009, 7);
f(4194296, 8);
f(4718583, 9);
f(7864305, 15);
f(8388592, 16);
f(8912879, 17);
f(16252897, 31);
f(16777184, 32);
f(17301471, 33);
f(33030081, 63);
f(33554368, 64);
f(34078655, 65);
f(66584449, 127);
f(67108736, 128);
f(67633023, 129);
f(133693185, 255);
f(134217472, 256);
f(134741759, 257);
f(267910657, 511);
f(268434944, 512);
f(268959231, 513);
f(536345601, 1023);
f(536869888, 1024);
f(537394175, 1025);
f(1073215489, 2047);
f(1073739776, 2048);
f(1074264063, 2049);
f(2146955265, 4095);
f(2147479552, 4096);
f(2148003839, 4097);
f(4294434817, 8191);
f(4294959104, 8192);
f(4295483391, 8193);
f(8589393921, 16383);
f(8589918208, 16384);
f(8590442495, 16385);
f(17179312129, 32767);
f(17179836416, 32768);
f(17180360703, 32769);
f(34359148545, 65535);
f(34359672832, 65536);
f(34360197119, 65537);
f(68718821377, 131071);
f(68719345664, 131072);
f(68719869951, 131073);
f(137438167041, 262143);
f(137438691328, 262144);
f(137439215615, 262145);
f(274876858369, 524287);
x = 524288;
f(0, 0);
f(524288, 1);
f(1048576, 2);
f(1572864, 3);
f(2097152, 4);
f(2621440, 5);
f(3670016, 7);
f(4194304, 8);
f(4718592, 9);
f(7864320, 15);
f(8388608, 16);
f(8912896, 17);
f(16252928, 31);
f(16777216, 32);
f(17301504, 33);
f(33030144, 63);
f(33554432, 64);
f(34078720, 65);
f(66584576, 127);
f(67108864, 128);
f(67633152, 129);
f(133693440, 255);
f(134217728, 256);
f(134742016, 257);
f(267911168, 511);
f(268435456, 512);
f(268959744, 513);
f(536346624, 1023);
f(536870912, 1024);
f(537395200, 1025);
f(1073217536, 2047);
f(1073741824, 2048);
f(1074266112, 2049);
f(2146959360, 4095);
f(2147483648, 4096);
f(2148007936, 4097);
f(4294443008, 8191);
f(4294967296, 8192);
f(4295491584, 8193);
f(8589410304, 16383);
f(8589934592, 16384);
f(8590458880, 16385);
f(17179344896, 32767);
f(17179869184, 32768);
f(17180393472, 32769);
f(34359214080, 65535);
f(34359738368, 65536);
f(34360262656, 65537);
f(68718952448, 131071);
f(68719476736, 131072);
f(68720001024, 131073);
f(137438429184, 262143);
f(137438953472, 262144);
f(137439477760, 262145);
f(274877382656, 524287);
f(274877906944, 524288);
x = 524289;
f(0, 0);
f(524289, 1);
f(1048578, 2);
f(1572867, 3);
f(2097156, 4);
f(2621445, 5);
f(3670023, 7);
f(4194312, 8);
f(4718601, 9);
f(7864335, 15);
f(8388624, 16);
f(8912913, 17);
f(16252959, 31);
f(16777248, 32);
f(17301537, 33);
f(33030207, 63);
f(33554496, 64);
f(34078785, 65);
f(66584703, 127);
f(67108992, 128);
f(67633281, 129);
f(133693695, 255);
f(134217984, 256);
f(134742273, 257);
f(267911679, 511);
f(268435968, 512);
f(268960257, 513);
f(536347647, 1023);
f(536871936, 1024);
f(537396225, 1025);
f(1073219583, 2047);
f(1073743872, 2048);
f(1074268161, 2049);
f(2146963455, 4095);
f(2147487744, 4096);
f(2148012033, 4097);
f(4294451199, 8191);
f(4294975488, 8192);
f(4295499777, 8193);
f(8589426687, 16383);
f(8589950976, 16384);
f(8590475265, 16385);
f(17179377663, 32767);
f(17179901952, 32768);
f(17180426241, 32769);
f(34359279615, 65535);
f(34359803904, 65536);
f(34360328193, 65537);
f(68719083519, 131071);
f(68719607808, 131072);
f(68720132097, 131073);
f(137438691327, 262143);
f(137439215616, 262144);
f(137439739905, 262145);
f(274877906943, 524287);
f(274878431232, 524288);
f(274878955521, 524289);
x = 1048575;
f(0, 0);
f(1048575, 1);
f(2097150, 2);
f(3145725, 3);
f(4194300, 4);
f(5242875, 5);
f(7340025, 7);
f(8388600, 8);
f(9437175, 9);
f(15728625, 15);
f(16777200, 16);
f(17825775, 17);
f(32505825, 31);
f(33554400, 32);
f(34602975, 33);
f(66060225, 63);
f(67108800, 64);
f(68157375, 65);
f(133169025, 127);
f(134217600, 128);
f(135266175, 129);
f(267386625, 255);
f(268435200, 256);
f(269483775, 257);
f(535821825, 511);
f(536870400, 512);
f(537918975, 513);
f(1072692225, 1023);
f(1073740800, 1024);
f(1074789375, 1025);
f(2146433025, 2047);
f(2147481600, 2048);
f(2148530175, 2049);
f(4293914625, 4095);
f(4294963200, 4096);
f(4296011775, 4097);
f(8588877825, 8191);
f(8589926400, 8192);
f(8590974975, 8193);
f(17178804225, 16383);
f(17179852800, 16384);
f(17180901375, 16385);
f(34358657025, 32767);
f(34359705600, 32768);
f(34360754175, 32769);
f(68718362625, 65535);
f(68719411200, 65536);
f(68720459775, 65537);
f(137437773825, 131071);
f(137438822400, 131072);
f(137439870975, 131073);
f(274876596225, 262143);
f(274877644800, 262144);
f(274878693375, 262145);
f(549754241025, 524287);
f(549755289600, 524288);
f(549756338175, 524289);
f(1099509530625, 1048575);
x = 1048576;
f(0, 0);
f(1048576, 1);
f(2097152, 2);
f(3145728, 3);
f(4194304, 4);
f(5242880, 5);
f(7340032, 7);
f(8388608, 8);
f(9437184, 9);
f(15728640, 15);
f(16777216, 16);
f(17825792, 17);
f(32505856, 31);
f(33554432, 32);
f(34603008, 33);
f(66060288, 63);
f(67108864, 64);
f(68157440, 65);
f(133169152, 127);
f(134217728, 128);
f(135266304, 129);
f(267386880, 255);
f(268435456, 256);
f(269484032, 257);
f(535822336, 511);
f(536870912, 512);
f(537919488, 513);
f(1072693248, 1023);
f(1073741824, 1024);
f(1074790400, 1025);
f(2146435072, 2047);
f(2147483648, 2048);
f(2148532224, 2049);
f(4293918720, 4095);
f(4294967296, 4096);
f(4296015872, 4097);
f(8588886016, 8191);
f(8589934592, 8192);
f(8590983168, 8193);
f(17178820608, 16383);
f(17179869184, 16384);
f(17180917760, 16385);
f(34358689792, 32767);
f(34359738368, 32768);
f(34360786944, 32769);
f(68718428160, 65535);
f(68719476736, 65536);
f(68720525312, 65537);
f(137437904896, 131071);
f(137438953472, 131072);
f(137440002048, 131073);
f(274876858368, 262143);
f(274877906944, 262144);
f(274878955520, 262145);
f(549754765312, 524287);
f(549755813888, 524288);
f(549756862464, 524289);
f(1099510579200, 1048575);
f(1099511627776, 1048576);
