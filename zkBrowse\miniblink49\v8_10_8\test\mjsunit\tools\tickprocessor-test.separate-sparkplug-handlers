Statistical profiling result from v8.log, (13 ticks, 2 unaccounted, 0 excluded).

 [Shared libraries]:
   ticks  total  nonlib   name
      3   23.1%          /lib32/libm-2.7.so
      1    7.7%          ffffe000-fffff000

 [JavaScript]:
   ticks  total  nonlib   name

 [C++]:
   ticks  total  nonlib   name
      2   15.4%   22.2%  v8::internal::Runtime_Math_exp(v8::internal::Arguments)
      1    7.7%   11.1%  v8::internal::JSObject::LookupOwnRealNamedProperty(v8::internal::String*, v8::internal::LookupResult*)
      1    7.7%   11.1%  v8::internal::HashTable<v8::internal::StringDictionaryShape, v8::internal::String*>::FindEntry(v8::internal::String*)
      1    7.7%   11.1%  exp

 [Summary]:
   ticks  total  nonlib   name
      0    0.0%    0.0%  JavaScript
      5   38.5%   55.6%  C++
      0    0.0%    0.0%  GC
      4   30.8%          Shared libraries
      2   15.4%          Unaccounted

 [C++ entry points]:
   ticks    cpp   total   name
      2   40.0%   15.4%  v8::internal::Runtime_Math_exp(v8::internal::Arguments)
      1   20.0%    7.7%  v8::internal::JSObject::LookupOwnRealNamedProperty(v8::internal::String*, v8::internal::LookupResult*)
      1   20.0%    7.7%  v8::internal::HashTable<v8::internal::StringDictionaryShape, v8::internal::String*>::FindEntry(v8::internal::String*)
      1   20.0%    7.7%  exp

 [Bottom up (heavy) profile]:
  Note: percentage shows a share of a particular caller in the total
  amount of its parent calls.
  Callers occupying less than 1.0% are not shown.

   ticks parent  name
      3   23.1%  /lib32/libm-2.7.so
      3  100.0%    LazyCompile: exp native math.js:41
      3  100.0%      Script: exp.js

      2   15.4%  v8::internal::Runtime_Math_exp(v8::internal::Arguments)
      2  100.0%    LazyCompile: exp native math.js:41
      2  100.0%      Script: exp.js

      2   15.4%  UNKNOWN
      1   50.0%    LazyCompile: exp native math.js:41
      1  100.0%      Script: exp.js

      1    7.7%  v8::internal::JSObject::LookupOwnRealNamedProperty(v8::internal::String*, v8::internal::LookupResult*)
      1  100.0%    Script: exp.js

      1    7.7%  v8::internal::HashTable<v8::internal::StringDictionaryShape, v8::internal::String*>::FindEntry(v8::internal::String*)
      1  100.0%    Script: exp.js

      1    7.7%  ffffe000-fffff000

      1    7.7%  exp
      1  100.0%    LazyCompile: exp native math.js:41
      1  100.0%      Script: exp.js
