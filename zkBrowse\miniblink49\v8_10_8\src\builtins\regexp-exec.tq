// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-regexp-gen.h'

namespace regexp {

@export
transitioning macro RegExpPrototypeExecBodyFast(implicit context: Context)(
    receiver: J<PERSON><PERSON><PERSON><PERSON>, string: String): JSAny {
  return RegExpPrototypeExecBody(receiver, string, true);
}

transitioning macro RegExpPrototypeExecBodySlow(implicit context: Context)(
    receiver: JSReceiver, string: String): J<PERSON>ny {
  return RegExpPrototypeExecBody(receiver, string, false);
}

// Slow path stub for RegExpPrototypeExec to decrease code size.
transitioning builtin
RegExpPrototypeExecSlow(implicit context: Context)(
    regexp: JSRegExp, string: String): JSAny {
  return RegExpPrototypeExecBodySlow(regexp, string);
}

extern macro RegExpBuiltinsAssembler::IsFastRegExpNoPrototype(
    implicit context: Context)(Object): bool;

// ES#sec-regexp.prototype.exec
// RegExp.prototype.exec ( string )
transitioning javascript builtin RegExpPrototypeExec(
    js-implicit context: NativeContext, receiver: JSAny)(string: JSAny): JSAny {
  // Ensure {receiver} is a JSRegExp.
  const receiver = Cast<JSRegExp>(receiver) otherwise ThrowTypeError(
      MessageTemplate::kIncompatibleMethodReceiver, 'RegExp.prototype.exec',
      receiver);
  const string = ToString_Inline(string);

  return IsFastRegExpNoPrototype(receiver) ?
      RegExpPrototypeExecBodyFast(receiver, string) :
      RegExpPrototypeExecSlow(receiver, string);
}
}
