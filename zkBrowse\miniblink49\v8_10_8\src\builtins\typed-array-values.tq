// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-typed-array-gen.h'

namespace typed_array {
const kBuiltinNameValues: constexpr string = '%TypedArray%.prototype.values';

// %TypedArray%.values ()
// https://tc39.github.io/ecma262/#sec-%typedarray%.values
transitioning javascript builtin
TypedArrayPrototypeValues(js-implicit context: NativeContext, receiver: JSAny)(
    ...arguments): JSArrayIterator {
  try {
    const array: JSTypedArray = Cast<JSTypedArray>(receiver)
        otherwise NotTypedArray;

    EnsureAttached(array) otherwise IsDetached;
    return CreateArrayIterator(array, IterationKind::kValues);
  } label NotTypedArray deferred {
    ThrowTypeError(MessageTemplate::kNotTypedArray, kBuiltinNameValues);
  } label IsDetached deferred {
    ThrowTypeError(MessageTemplate::kDetachedOperation, kBuiltinNameValues);
  }
}
}
