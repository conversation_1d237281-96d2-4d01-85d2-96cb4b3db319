﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  SkAAClip.cpp
  SkAdvancedTypefaceMetrics.cpp
  SkAlphaRuns.cpp
  SkAnnotation.cpp
  SkBBHFactory.cpp
  SkBigPicture.cpp
  SkBitmap.cpp
  SkBitmapCache.cpp
  SkBitmapController.cpp
  SkBitmapDevice.cpp
  SkBitmapFilter.cpp
  SkBitmapHeap.cpp
  SkBitmapProcShader.cpp
  SkBitmapProcState.cpp
  SkBitmapProcState_matrixProcs.cpp
  SkBitmapScaler.cpp
  SkBlitMask_D32.cpp
  SkBlitRow_D16.cpp
  SkBlitRow_D32.cpp
  SkBlitter.cpp
  SkBlitter_A8.cpp
  SkBlitter_ARGB32.cpp
  SkBlitter_RGB16.cpp
  SkBlitter_Sprite.cpp
  SkBuffer.cpp
  SkCachedData.cpp
  SkCanvas.cpp
  SkChunkAlloc.cpp
  SkClipStack.cpp
  SkColor.cpp
  SkColorFilter.cpp
  SkColorTable.cpp
  SkComposeShader.cpp
  SkConfig8888.cpp
  SkConvolver.cpp
  SkCubicClipper.cpp
  SkData.cpp
  SkDataTable.cpp
  SkDebug.cpp
  SkDeque.cpp
  SkDevice.cpp
  SkDeviceLooper.cpp
  SkDeviceProfile.cpp
  SkDistanceFieldGen.cpp
  SkDither.cpp
  SkDraw.cpp
  SkDrawLooper.cpp
  SkDrawable.cpp
  SkEdge.cpp
  SkEdgeBuilder.cpp
  SkEdgeClipper.cpp
  SkError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skdata.cpp(74): warning C4291: “void *SkData::operator new(size_t,void *)”: 未找到匹配的删除运算符；如果初始化引发异常，则不会释放内存
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\include\core\skdata.h(174): note: 参见“SkData::operator new”的声明
  SkFilterProc.cpp
  SkFilterShader.cpp
  SkFlate.cpp
  SkFlattenable.cpp
  SkFlattenableSerialization.cpp
  SkFloatBits.cpp
  SkFont.cpp
  SkFontDescriptor.cpp
  SkFontHost.cpp
  SkFontMgr.cpp
  SkFontStream.cpp
  SkFontStyle.cpp
  SkGeometry.cpp
  SkGlyphCache.cpp
  SkGraphics.cpp
  SkHalf.cpp
  SkImageFilter.cpp
  SkImageGenerator.cpp
  SkImageInfo.cpp
  SkLineClipper.cpp
  SkLocalMatrixShader.cpp
  SkMallocPixelRef.cpp
  SkMask.cpp
  SkMaskCache.cpp
  SkMaskFilter.cpp
  SkMaskGamma.cpp
  SkMath.cpp
  SkMatrix.cpp
  SkMatrixImageFilter.cpp
  SkMetaData.cpp
  SkMiniRecorder.cpp
  SkMipMap.cpp
  SkMultiPictureDraw.cpp
  SkNinePatchIter.cpp
  SkPackBits.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skmatrix.cpp(1720): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skmatrix.cpp(1720): warning C4244: “初始化”: 从“int”转换到“const float”，可能丢失数据
  SkPaint.cpp
  SkPaintPriv.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skimagefilter.cpp(343): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
  SkPath.cpp
  SkPathEffect.cpp
  SkPathMeasure.cpp
  SkPathRef.cpp
  SkPicture.cpp
  SkPictureContentInfo.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skmallocpixelref.cpp(72): warning C4244: “=”: 从“int64_t”转换到“int32_t”，可能丢失数据
  SkPictureData.cpp
  SkPictureFlat.cpp
  SkPicturePlayback.cpp
  SkPictureRecord.cpp
  SkPictureRecorder.cpp
  SkPictureShader.cpp
  SkPixelRef.cpp
  SkPixmap.cpp
  SkPoint.cpp
  SkPtrRecorder.cpp
  SkQuadClipper.cpp
  SkRRect.cpp
  SkRTree.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpath.cpp(2523): warning C4244: “=”: 从“int”转换到“SkScalar”，可能丢失数据
  SkRWBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpicture.cpp(138): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpicture.cpp(137): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpicture.cpp(163): warning C4244: “参数”: 从“SkScalar”转换到“T”，可能丢失数据
          with
          [
              T=int32_t
          ]
  SkRasterClip.cpp
  SkRasterizer.cpp
  SkReadBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrrect.cpp(194): warning C4244: “*=”: 从“double”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrrect.cpp(195): warning C4244: “*=”: 从“double”转换到“SkScalar”，可能丢失数据
  SkRecord.cpp
  SkRecordDraw.cpp
  SkRecordOpts.cpp
  SkRecorder.cpp
  SkRect.cpp
  SkRefDict.cpp
  SkRegion.cpp
  SkRegion_path.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpictureshader.cpp(206): warning C4244: “参数”: 从“T”转换到“int32_t”，可能丢失数据
          with
          [
              T=SkScalar
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpictureshader.cpp(206): warning C4244: “初始化”: 从“int32_t”转换到“SkScalar”，可能丢失数据
  SkResourceCache.cpp
  SkScalar.cpp
  SkScalerContext.cpp
  SkScan.cpp
  SkScan_AntiPath.cpp
  SkScan_Antihair.cpp
  SkScan_Hairline.cpp
  SkScan_Path.cpp
  SkSemaphore.cpp
  SkShader.cpp
  SkSharedMutex.cpp
  SkSpinlock.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(395): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(395): warning C4244: “参数”: 从“const int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(412): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(436): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
  SkSpriteBlitter_ARGB32.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skregionpriv.h(71): warning C4244: “参数”: 从“const int64_t”转换到“size_t”，可能丢失数据 (编译源文件 ..\..\third_party\skia\src\core\SkRegion_path.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skregionpriv.h(71): warning C4244: “参数”: 从“const int64_t”转换到“size_t”，可能丢失数据 (编译源文件 ..\..\third_party\skia\src\core\SkRegion.cpp)
  SkSpriteBlitter_RGB16.cpp
  SkStream.cpp
  SkString.cpp
  SkStringUtils.cpp
  SkStroke.cpp
  SkStrokeRec.cpp
  SkStrokerPriv.cpp
  SkTLS.cpp
  SkTSearch.cpp
  SkTaskGroup.cpp
  SkTextBlob.cpp
  SkTime.cpp
  SkTypeface.cpp
  SkTypefaceCache.cpp
  SkUnPreMultiply.cpp
  SkUtils.cpp
  SkUtilsArm.cpp
  SkValidatingReadBuffer.cpp
  SkVarAlloc.cpp
  SkVertState.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skstream.cpp(147): warning C4267: “=”: 从“size_t”转换到“uint8_t”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skstream.cpp(150): warning C4267: “初始化”: 从“size_t”转换到“uint16_t”，可能丢失数据
  SkWriteBuffer.cpp
  SkWriter32.cpp
  SkXfermode.cpp
  SkXfermodeInterpretation.cpp
  SkYUVPlanesCache.cpp
  SkDocument.cpp
  SkDocument_PDF.cpp
  Sk1DPathEffect.cpp
  Sk2DPathEffect.cpp
  SkAlphaThresholdFilter.cpp
  SkArcToPathEffect.cpp
  SkArithmeticMode.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\sktextblob.cpp(598): warning C4291: “void *SkTextBlob::operator new(size_t,void *)”: 未找到匹配的删除运算符；如果初始化引发异常，则不会释放内存
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\include\core\sktextblob.h(91): note: 参见“SkTextBlob::operator new”的声明
  SkArithmeticMode_gpu.cpp
  SkBitmapSource.cpp
  SkBlurDrawLooper.cpp
  SkBlurImageFilter.cpp
  SkBlurMask.cpp
  SkBlurMaskFilter.cpp
  SkColorCubeFilter.cpp
  SkColorFilterImageFilter.cpp
  SkColorFilters.cpp
  SkColorMatrix.cpp
  SkColorMatrixFilter.cpp
  SkComposeImageFilter.cpp
  SkCornerPathEffect.cpp
  SkDashPathEffect.cpp
  SkDiscretePathEffect.cpp
  SkDisplacementMapEffect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\effects\skdisplacementmapeffect.cpp : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  SkDropShadowImageFilter.cpp
  SkEmbossMask.cpp
  SkEmbossMaskFilter.cpp
  SkGpuBlurUtils.cpp
  SkLayerDrawLooper.cpp
  SkLayerRasterizer.cpp
  SkLerpXfermode.cpp
  SkLightingImageFilter.cpp
  SkLumaColorFilter.cpp
  SkMagnifierImageFilter.cpp
  SkMatrixConvolutionImageFilter.cpp
  SkMergeImageFilter.cpp
  SkMorphologyImageFilter.cpp
  SkOffsetImageFilter.cpp
  SkPaintFlagsDrawFilter.cpp
  SkPerlinNoiseShader.cpp
  SkPictureImageFilter.cpp
  SkPixelXorXfermode.cpp
  SkRectShaderImageFilter.cpp
  SkTableColorFilter.cpp
  SkTableMaskFilter.cpp
  SkTestImageFilters.cpp
  SkTileImageFilter.cpp
  SkXfermodeImageFilter.cpp
  SkClampRange.cpp
  SkGradientBitmapCache.cpp
  SkGradientShader.cpp
  SkLinearGradient.cpp
  SkRadialGradient.cpp
  SkSweepGradient.cpp
  SkTwoPointConicalGradient.cpp
  SkTwoPointConicalGradient_gpu.cpp
  SkFontMgr_indirect.cpp
  SkGScalerContext.cpp
  SkRandomScalerContext.cpp
  SkRemotableFontMgr.cpp
  SkTestScalerContext.cpp
  GrAAConvexPathRenderer.cpp
  GrAAConvexTessellator.cpp
  GrAADistanceFieldPathRenderer.cpp
  GrAAHairLinePathRenderer.cpp
  GrAALinearizingConvexPathRenderer.cpp
  GrAARectRenderer.cpp
  GrAddPathRenderers_default.cpp
  GrAtlas.cpp
  GrAtlasTextContext.cpp
  GrBatch.cpp
  GrBatchAtlas.cpp
  GrBatchFontCache.cpp
  GrBatchTarget.cpp
  GrBatchTest.cpp
  GrBlurUtils.cpp
  GrBufferAllocPool.cpp
  GrCaps.cpp
  GrClip.cpp
  GrClipMaskCache.cpp
  GrClipMaskManager.cpp
  GrCommandBuilder.cpp
  GrContext.cpp
  GrContextFactory.cpp
  GrCoordTransform.cpp
  GrDashLinePathRenderer.cpp
  GrDefaultGeoProcFactory.cpp
  GrDefaultPathRenderer.cpp
  GrDrawContext.cpp
  GrDrawTarget.cpp
  GrFontScaler.cpp
  GrGpu.cpp
  GrGpuFactory.cpp
  GrGpuResource.cpp
  GrGpuResourceRef.cpp
  GrImmediateDrawTarget.cpp
  GrInOrderCommandBuilder.cpp
  GrInOrderDrawBuffer.cpp
  GrInvariantOutput.cpp
  GrLayerCache.cpp
  GrLayerHoister.cpp
  GrMemoryPool.cpp
  GrOvalRenderer.cpp
  GrPaint.cpp
  GrPath.cpp
  GrPathProcessor.cpp
  GrPathRange.cpp
  GrPathRenderer.cpp
  GrPathRendererChain.cpp
  GrPathRendering.cpp
  GrPathUtils.cpp
  GrPipeline.cpp
  GrPipelineBuilder.cpp
  GrPrimitiveProcessor.cpp
  GrProcOptInfo.cpp
  GrProcessor.cpp
  GrProgramElement.cpp
  GrRecordReplaceDraw.cpp
  GrRectBatch.cpp
  GrRectanizer_pow2.cpp
  GrRectanizer_skyline.cpp
  GrReducedClip.cpp
  GrRenderTarget.cpp
  GrReorderCommandBuilder.cpp
  GrResourceCache.cpp
  GrResourceProvider.cpp
  GrSWMaskHelper.cpp
  GrSoftwarePathRenderer.cpp
  GrStencil.cpp
  GrStencilAndCoverPathRenderer.cpp
  GrStencilAndCoverTextContext.cpp
  GrStencilAttachment.cpp
  GrStrokeInfo.cpp
  GrSurface.cpp
  GrTargetCommands.cpp
  GrTessellatingPathRenderer.cpp
  GrTest.cpp
  GrTestUtils.cpp
  GrTextBlobCache.cpp
  GrTextContext.cpp
  GrTexture.cpp
  GrTextureAccess.cpp
  GrTextureProvider.cpp
  GrTraceMarker.cpp
  GrXferProcessor.cpp
  SkGpuDevice.cpp
  SkGr.cpp
  SkGrPixelRef.cpp
  SkGrTexturePixelRef.cpp
  GrBezierEffect.cpp
  GrBicubicEffect.cpp
  GrBitmapTextGeoProc.cpp
  GrConfigConversionEffect.cpp
  GrConstColorProcessor.cpp
  GrConvexPolyEffect.cpp
  GrConvolutionEffect.cpp
  GrCoverageSetOpXP.cpp
  GrCustomXfermode.cpp
  GrDashingEffect.cpp
  GrDisableColorXP.cpp
  GrDistanceFieldGeoProc.cpp
  GrDitherEffect.cpp
  GrMatrixConvolutionEffect.cpp
  GrOvalEffect.cpp
  GrPorterDuffXferProcessor.cpp
  GrRRectEffect.cpp
  GrSimpleTextureEffect.cpp
  GrSingleTextureEffect.cpp
  GrTextureDomain.cpp
  GrTextureStripAtlas.cpp
  GrYUVtoRGBEffect.cpp
  GrGLAssembleInterface.cpp
  GrGLBufferImpl.cpp
  GrGLCaps.cpp
  GrGLContext.cpp
  GrGLCreateNativeInterface_none.cpp
  GrGLCreateNullInterface.cpp
  GrGLDefaultInterface_native.cpp
  GrGLExtensions.cpp
  GrGLGLSL.cpp
  GrGLGeometryProcessor.cpp
  GrGLGpu.cpp
  GrGLGpuProgramCache.cpp
  GrGLIndexBuffer.cpp
  GrGLInterface.cpp
  GrGLNameAllocator.cpp
  GrGLNoOpInterface.cpp
  GrGLPath.cpp
  GrGLPathProcessor.cpp
  GrGLPathProgram.cpp
  GrGLPathProgramDataManager.cpp
  GrGLPathRange.cpp
  GrGLPathRendering.cpp
  GrGLPrimitiveProcessor.cpp
  GrGLProgram.cpp
  GrGLProgramDataManager.cpp
  GrGLProgramDesc.cpp
  GrGLRenderTarget.cpp
  GrGLStencilAttachment.cpp
  GrGLTexture.cpp
  GrGLUtil.cpp
  GrGLVertexArray.cpp
  GrGLVertexBuffer.cpp
  GrGLXferProcessor.cpp
  SkGLContext.cpp
  SkNullGLContext.cpp
  GrGLCreateANGLEInterface.cpp
  SkANGLEGLContext.cpp
  GrGLFragmentShaderBuilder.cpp
  GrGLGeometryShaderBuilder.cpp
  GrGLPathProgramBuilder.cpp
  GrGLProgramBuilder.cpp
  GrGLSLPrettyPrint.cpp
  GrGLShaderBuilder.cpp
  GrGLShaderStringBuilder.cpp
  GrGLVertexShaderBuilder.cpp
  GrBufferObj.cpp
  GrDebugGL.cpp
  GrFrameBufferObj.cpp
  GrGLCreateDebugInterface.cpp
  GrProgramObj.cpp
  GrShaderObj.cpp
  GrTextureObj.cpp
  GrTextureUnitObj.cpp
  SkDebugGLContext.cpp
  GrGLSL.cpp
  GrGLSLCaps.cpp
  SkImage.cpp
  SkImage_Gpu.cpp
  SkImage_Raster.cpp
  SkSurface.cpp
  SkSurface_Gpu.cpp
  SkSurface_Raster.cpp
  SkDecodingImageGenerator.cpp
  SkForceLinking.cpp
  SkImageDecoder.cpp
  SkImageDecoder_FactoryDefault.cpp
  SkImageDecoder_FactoryRegistrar.cpp
  SkImageDecoder_libbmp.cpp
  SkImageDecoder_wbmp.cpp
  SkImageEncoder.cpp
  SkImageEncoder_Factory.cpp
  SkImageEncoder_argb.cpp
  SkMovie.cpp
  SkPageFlipper.cpp
  SkScaledBitmapSampler.cpp
  bmpdecoderhelper.cpp
  SkCachingPixelRef.cpp
  SkDiscardableMemoryPool.cpp
  SkDiscardablePixelRef.cpp
  SkBitmapFilter_opts_SSE2.cpp
  SkBitmapProcState_opts_SSE2.cpp
  SkBitmapProcState_opts_SSSE3.cpp
  SkBlitRow_opts_SSE2.cpp
  SkBlitRow_opts_SSE4.cpp
  SkBlurImage_opts_SSE2.cpp
  SkBlurImage_opts_SSE4.cpp
  SkMorphology_opts_SSE2.cpp
  SkTextureCompression_opts_none.cpp
  SkUtils_opts_SSE2.cpp
  SkXfermode_opts_SSE2.cpp
  opts_check_x86.cpp
  SkAddIntersections.cpp
  SkDConicLineIntersection.cpp
  SkDCubicLineIntersection.cpp
  SkDCubicToQuads.cpp
  SkDLineIntersection.cpp
  SkDQuadLineIntersection.cpp
  SkIntersections.cpp
  SkOpAngle.cpp
  SkOpBuilder.cpp
  SkOpCoincidence.cpp
  SkOpContour.cpp
  SkOpCubicHull.cpp
  SkOpEdgeBuilder.cpp
  SkOpSegment.cpp
  SkOpSpan.cpp
  SkPathOpsCommon.cpp
  SkPathOpsConic.cpp
  SkPathOpsCubic.cpp
  SkPathOpsCurve.cpp
  SkPathOpsDebug.cpp
  SkPathOpsLine.cpp
  SkPathOpsOp.cpp
  SkPathOpsPoint.cpp
  SkPathOpsQuad.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skopcoincidence.cpp(172): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skopcoincidence.cpp(173): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skopcoincidence.cpp(174): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skopcoincidence.cpp(175): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
  SkPathOpsRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skopcubichull.cpp(65): warning C4267: “=”: 从“size_t”转换到“char”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skopcubichull.cpp(132): warning C4267: “=”: 从“size_t”转换到“char”，可能丢失数据
  SkPathOpsSimplify.cpp
  SkPathOpsTSect.cpp
  SkPathOpsTightBounds.cpp
  SkPathOpsTypes.cpp
  SkPathOpsWinding.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skpathopscubic.cpp(280): warning C4244: “=”: 从“double”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skpathopscubic.cpp(286): warning C4244: “=”: 从“double”转换到“SkScalar”，可能丢失数据
  SkPathWriter.cpp
  SkReduceOrder.cpp
  SkJpegInfo.cpp
  SkPDFBitmap.cpp
  SkPDFCanon.cpp
  SkPDFDevice.cpp
  SkPDFFont.cpp
  SkPDFFormXObject.cpp
  SkPDFGraphicState.cpp
  SkPDFResourceDict.cpp
  SkPDFShader.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\pathops\skpathopstypes.cpp(107): warning C4244: “参数”: 从“double”转换到“SkScalar”，可能丢失数据
  SkPDFStream.cpp
  SkPDFTypes.cpp
  SkPDFUtils.cpp
  SkGPipeRead.cpp
  SkGPipeWrite.cpp
  SamplePipeControllers.cpp
  SkDebug_win.cpp
  SkDiscardableMemory_none.cpp
  SkFontHost_win.cpp
  SkFontMgr_win_dw.cpp
  SkFontMgr_win_dw_factory.cpp
  SkGlobalInitialization_chromium.cpp
  SkImageGenerator_skia.cpp
  SkMemory_malloc.cpp
  SkOSFile_stdio.cpp
  SkOSFile_win.cpp
  SkRemotableFontMgr_win_dw.cpp
  SkScalerContext_win_dw.cpp
  SkTLS_win.cpp
  SkTime_win.cpp
  SkTypeface_win_dw.cpp
  SkOTUtils.cpp
  SkBase64.cpp
  SkBitSet.cpp
  SkBitmapHasher.cpp
  SkBoundaryPatch.cpp
  SkCamera.cpp
  SkCanvasStack.cpp
  SkCanvasStateUtils.cpp
  SkCubicInterval.cpp
  SkCullPoints.cpp
  SkDashPath.cpp
  No dwrite_2.h is available, font fallback may be affected.
  SkDeferredCanvas.cpp
  SkDumpCanvas.cpp
  SkEventTracer.cpp
  SkFrontBufferedStream.cpp
  SkInterpolator.cpp
  SkLayer.cpp
  SkMD5.cpp
  SkMatrix22.cpp
  SkMatrix44.cpp
  SkMeshUtils.cpp
  SkNWayCanvas.cpp
  SkNinePatch.cpp
  No dwrite_1.h is available, font metrics may be affected.
  SkNullCanvas.cpp
  SkOSFile.cpp
  SkPaintFilterCanvas.cpp
  SkParse.cpp
  SkParseColor.cpp
  SkParsePath.cpp
  SkPatchGrid.cpp
  SkPatchUtils.cpp
  SkRTConf.cpp
  SkSHA1.cpp
  SkTextBox.cpp
  SkTextureCompressor.cpp
  SkTextureCompressor_ASTC.cpp
  SkTextureCompressor_LATC.cpp
  SkTextureCompressor_R11EAC.cpp
  SkThreadUtils_win.cpp
  SkAutoCoInitialize.cpp
  SkDWrite.cpp
  SkDWriteFontFileStream.cpp
  SkDWriteGeometrySink.cpp
  SkHRESULT.cpp
  SkIStream.cpp
SkDumpCanvas.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
SkGrTexturePixelRef.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
GrInvariantOutput.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
SkUtilsArm.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
SkDebug.obj : warning LNK4221: 此对象文件未定义任何之前未定义的公共符号，因此任何耗用此库的链接操作都不会使用此文件
  skia.vcxproj -> E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\..\out\Debug\skia.lib
