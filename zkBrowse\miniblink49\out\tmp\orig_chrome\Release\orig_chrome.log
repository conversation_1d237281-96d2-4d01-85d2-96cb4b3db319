﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  audio_video_metadata_extractor.cc
  media.cc
  media_file_checker.cc
  ffmpeg_common.cc
  audio_file_reader.cc
  ffmpeg_aac_bitstream_converter.cc
  ffmpeg_audio_decoder.cc
  ffmpeg_demuxer.cc
  ffmpeg_glue.cc
  ffmpeg_video_decoder.cc
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_audio_decoder.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\audio_file_reader.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\base\media_file_checker.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\base\audio_video_metadata_extractor.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_glue.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_demuxer.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\base\media.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_video_decoder.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据 (编译源文件 ..\..\orig_chrome\media\ffmpeg\ffmpeg_common.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(52): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(76): error C2440: “初始化”: 无法从“const AVCodec *”转换为“AVCodec *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(76): note: 转换丢失限定符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_file_checker.cc(51): error C2039: “codec”: 不是“AVStream”的成员
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(104): error C2039: “channel_layout”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(104): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(103): error C3861: “ChannelLayoutToChromeChannelLayout”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(110): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(151): error C3861: “avcodec_decode_audio4”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_file_checker.cc(85): error C3861: “avcodec_decode_audio4”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_file_checker.cc(95): error C3861: “avcodec_decode_video2”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(180): error C2039: “channels”: 不是“AVFrame”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(28): note: 参见“AVFrame”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_file_checker.cc(106): error C2039: “CheckFile”: 不是“media::MediaFileChecker”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_file_checker.h(21): note: 参见“media::MediaFileChecker”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_file_checker.cc(108): error C2065: “filename_”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_file_checker.cc(108): error C2660: “avformat_open_input”: 函数不接受 3 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\ffmpeg\libavformat\avformat.h(2268): note: 参见“avformat_open_input”的声明 (编译源文件 ..\..\orig_chrome\media\base\media_file_checker.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(247): error C3861: “ConvertFromTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\audio_file_reader.cc(283): error C3861: “ConvertToTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.cc(188): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.cc(194): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.cc(189): error C2660: “media::`anonymous-namespace'::GenerateAdtsHeader”: 函数不接受 13 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.cc(17): note: 参见“media::`anonymous-namespace'::GenerateAdtsHeader”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_aac_bitstream_converter.cc(206): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(281): warning C4244: “参数”: 从“double”转换到“int64”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(303): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(72): error C2065: “AV_LOCK_CREATE”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(76): error C2065: “AV_LOCK_OBTAIN”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(80): error C2065: “AV_LOCK_RELEASE”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(84): error C2065: “AV_LOCK_DESTROY”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(72): error C2051: case 表达式不是常量
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(76): error C2051: case 表达式不是常量
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(80): error C2051: case 表达式不是常量
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(84): error C2051: case 表达式不是常量
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(88): warning C4060: switch 语句没有包含“case”或“default”标签
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(104): error C3861: “av_lockmgr_register”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(108): error C3861: “av_register_all”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(219): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_glue.cc(221): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\audio_video_metadata_extractor.cc(99): warning C4244: “=”: 从“double”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\audio_video_metadata_extractor.cc(118): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\audio_video_metadata_extractor.cc(121): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\audio_video_metadata_extractor.cc(124): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\audio_video_metadata_extractor.cc(125): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\audio_video_metadata_extractor.cc(126): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media.cc(37): error C3861: “av_get_cpu_flags”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(41): error C2039: “channels”: 不是“AVFrame”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(28): note: 参见“AVFrame”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(58): error C2065: “CODEC_CAP_DR1”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(65): error C3861: “AVSampleFormatToSampleFormat”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(77): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(97): error C2039: “channel_layout”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(97): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(97): error C3861: “ChannelLayoutToChromeChannelLayout”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(273): error C3861: “avcodec_decode_audio4”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(258): warning C4996: 'av_init_packet': 被声明为已否决
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\ffmpeg\libavcodec\packet.h(655): note: 参见“av_init_packet”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(362): error C3861: “AudioDecoderConfigToAVCodecContext”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(366): error C2039: “refcounted_frames”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(368): error C2440: “初始化”: 无法从“const AVCodec *”转换为“AVCodec *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(368): note: 转换丢失限定符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(381): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_audio_decoder.cc(382): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(76): error C3861: “VideoCodecToCodecID”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(96): error C3861: “AVPixelFormatToVideoPixelFormat”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(138): error C3861: “AVColorSpaceToColorSpace”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(153): error C2039: “reordered_opaque”: 不是“AVFrame”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(28): note: 参见“AVFrame”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(153): error C2039: “reordered_opaque”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(302): error C2039: “reordered_opaque”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(306): error C3861: “avcodec_decode_video2”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(340): error C2039: “reordered_opaque”: 不是“AVFrame”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(28): note: 参见“AVFrame”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(293): warning C4996: 'av_init_packet': 被声明为已否决
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\ffmpeg\libavcodec\packet.h(655): note: 参见“av_init_packet”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(364): error C3861: “VideoDecoderConfigToAVCodecContext”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(369): error C2065: “CODEC_FLAG_EMU_EDGE”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(371): error C2039: “refcounted_frames”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(26): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(374): error C2065: “CODEC_FLAG2_CHUNKS”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(376): error C2440: “初始化”: 无法从“const AVCodec *”转换为“AVCodec *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_video_decoder.cc(376): note: 转换丢失限定符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(49): error C3861: “FFmpegUTCDateToTime”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(60): warning C4244: “参数”: 从“double”转换到“int64”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(73): error C3861: “ConvertFromTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(77): error C2039: “pts_buffer”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(78): error C2039: “pts_buffer”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(78): error C3861: “ConvertFromTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(191): error C3861: “HashCodecName”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(194): error C3861: “HashCodecName”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(197): error C3861: “HashCodecName”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(211): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(219): error C3861: “AVStreamToAudioDecoderConfig”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(222): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(230): error C3861: “AVStreamToVideoDecoderConfig”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(266): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(357): error C3861: “av_packet_split_side_data”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(363): error C2664: “uint8_t *av_packet_get_side_data(const AVPacket *,AVPacketSideDataType,size_t *)”: 无法将参数 3 从“int *”转换为“size_t *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(366): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(369): error C2664: “uint8_t *av_packet_get_side_data(const AVPacket *,AVPacketSideDataType,size_t *)”: 无法将参数 3 从“int *”转换为“size_t *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(372): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(381): error C2664: “uint8_t *av_packet_get_side_data(const AVPacket *,AVPacketSideDataType,size_t *)”: 无法将参数 3 从“int *”转换为“size_t *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(384): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(412): error C2664: “uint8_t *av_packet_get_side_data(const AVPacket *,AVPacketSideDataType,size_t *)”: 无法将参数 3 从“int *”转换为“size_t *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(413): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(472): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(630): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(631): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(631): error C2512: “media::FFmpegH264ToAnnexBBitstreamConverter::FFmpegH264ToAnnexBBitstreamConverter”: 没有合适的默认构造函数可用
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(633): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(634): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(634): error C2512: “media::FFmpegH265ToAnnexBBitstreamConverter::FFmpegH265ToAnnexBBitstreamConverter”: 没有合适的默认构造函数可用
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(636): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(637): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(637): error C2512: “media::FFmpegAACBitstreamConverter::FFmpegAACBitstreamConverter”: 没有合适的默认构造函数可用
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(674): error C2039: “cur_dts”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(674): error C2660: “media::FFmpegDemuxerStream::ConvertStreamTimestamp”: 函数不接受 1 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.h(147): note: 参见“media::FFmpegDemuxerStream::ConvertStreamTimestamp”的声明 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_demuxer.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(749): error C3861: “ConvertFromTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(862): error C3861: “ConvertToTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(856): error C2672: “base::PostTaskAndReplyWithResult”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(866): error C2780: “bool base::PostTaskAndReplyWithResult(base::TaskRunner *,const tracked_objects::Location &,const base::Callback<ReturnType(void)> &,const base::Callback<void(ReplyArgType)> &)”: 应输入 4 个参数，却提供了 3 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\base\task_runner_util.h(57): note: 参见“base::PostTaskAndReplyWithResult”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(972): warning C4244: “return”: 从“int64_t”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(977): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(978): warning C4244: “+=”: 从“int64_t”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(991): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(992): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(993): warning C4244: “return”: 从“double”转换到“int”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1058): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1058): error C2143: 语法错误: 缺少“;”(在“*”的前面)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1058): error C2882: “internal”: 在表达式中非法使用命名空间标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1059): error C2882: “internal”: 在表达式中非法使用命名空间标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1060): error C2882: “internal”: 在表达式中非法使用命名空间标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1061): error C2882: “internal”: 在表达式中非法使用命名空间标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1066): error C3861: “ConvertFromTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1085): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1100): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1108): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1109): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1110): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1111): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1112): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1114): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1115): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1152): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1152): error C2660: “media::RecordVideoCodecStats”: 函数不接受 1 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(157): note: 参见“media::RecordVideoCodecStats”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1193): error C3861: “ConvertFromTimeBase”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1215): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1275): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1283): error C2440: “初始化”: 无法从“const AVCodec *”转换为“AVCodec *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1283): note: 转换丢失限定符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1289): error C2039: “channels”: 不是“AVCodecContext”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_h265_to_annex_b_bitstream_converter.h(15): note: 参见“AVCodecContext”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1288): error C2660: “media::MediaLog::SetIntegerProperty”: 函数不接受 1 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\base\media_log.h(68): note: 参见“media::MediaLog::SetIntegerProperty”的声明 (编译源文件 ..\..\orig_chrome\media\filters\ffmpeg_demuxer.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1298): error C2039: “codec”: 不是“AVStream”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.h(31): note: 参见“AVStream”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1301): error C2440: “初始化”: 无法从“const AVCodec *”转换为“AVCodec *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\filters\ffmpeg_demuxer.cc(1301): note: 转换丢失限定符
