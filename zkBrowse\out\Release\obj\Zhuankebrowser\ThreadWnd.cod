; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	??_7ThreadWnd@@6Bthread_base@thread_space@@@	; ThreadWnd::`vftable'
PUBLIC	??_7ThreadWnd@@6BCWindowWnd@DuiLib@@@		; ThreadWnd::`vftable'
EXTRN	?GetClassStyle@CWindowWnd@DuiLib@@MBEIXZ:PROC	; DuiLib::CWindowWnd::GetClassStyle
EXTRN	?OnFinalMessage@CWindowWnd@DuiLib@@MAEXPAUHWND__@@@Z:PROC ; DuiLib::CWindowWnd::OnFinalMessage
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
_BSS	ENDS
;	COMDAT ?threadwnd_@?1??GetInterface@ThreadWnd@@SAPAV2@XZ@4PAV2@A
_BSS	SEGMENT
?threadwnd_@?1??GetInterface@ThreadWnd@@SAPAV2@XZ@4PAV2@A DD 01H DUP (?) ; `ThreadWnd::GetInterface'::`2'::threadwnd_
_BSS	ENDS
;	COMDAT ??_7ThreadWnd@@6BCWindowWnd@DuiLib@@@
CONST	SEGMENT
??_7ThreadWnd@@6BCWindowWnd@DuiLib@@@ DD FLAT:?GetWindowClassName@ThreadWnd@@MBEPB_WXZ ; ThreadWnd::`vftable'
	DD	FLAT:?GetSuperClassName@CWindowWnd@DuiLib@@MBEPB_WXZ
	DD	FLAT:?GetClassStyle@CWindowWnd@DuiLib@@MBEIXZ
	DD	FLAT:?HandleMessage@ThreadWnd@@MAEJIIJ@Z
	DD	FLAT:?OnFinalMessage@CWindowWnd@DuiLib@@MAEXPAUHWND__@@@Z
CONST	ENDS
;	COMDAT ??_7ThreadWnd@@6Bthread_base@thread_space@@@
CONST	SEGMENT
??_7ThreadWnd@@6Bthread_base@thread_space@@@ DD FLAT:?run@ThreadWnd@@UAE_NXZ ; ThreadWnd::`vftable'
	ORG $+2
$SG4294606162 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294606163 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294606160 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294606161 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294606166 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294606167 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294606164 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294606165 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294606154 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294606155 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294606152 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294606153 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294606158 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294606159 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294606156 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294606157 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294606146 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294606147 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294606144 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294606145 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294606150 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294606151 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294606148 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294606149 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294606138 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294606139 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606136 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294606137 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294606142 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606143 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294606140 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294606141 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294606130 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294606131 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294606128 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606129 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294606134 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294606135 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294606132 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294606133 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294606122 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294606123 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294606120 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294606121 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294606126 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294606127 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294606124 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294606125 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294606114 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294606115 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294606112 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294606113 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294606118 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294606119 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294606116 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294606117 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294606106 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606107 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294606104 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294606105 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294606110 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294606111 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294606108 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294606109 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294606098 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294606099 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606096 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294606097 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294606102 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294606103 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294606100 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294606101 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294606090 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606091 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294606088 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606089 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294606094 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294606095 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294606092 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294606093 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294606082 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294606083 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294606080 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294606081 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294606086 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294606087 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294606084 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294606085 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294606074 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294606075 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294606072 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294606073 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294606078 DB 00H, 00H
	ORG $+2
$SG4294606079 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294606076 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294606077 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294606066 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294606067 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294606064 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294606065 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294606070 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294606071 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294606068 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294606069 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294606058 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294606059 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294606056 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294606057 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294606062 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294606063 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294606060 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294606061 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294606050 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294606051 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294606048 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294606049 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294606054 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294606055 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294606052 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294606053 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294606042 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294606043 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294606040 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294606041 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294606046 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294606047 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294606044 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294606045 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294606034 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294606035 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294606032 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294606033 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294606038 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294606039 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294606036 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294606037 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294606026 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294606027 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294606024 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294606025 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294606030 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294606031 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294606028 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606029 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294606018 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606019 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606016 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606017 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606022 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294606023 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294606020 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294606021 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294606010 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294606011 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606008 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294606009 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294606014 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606015 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606012 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606013 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294606002 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294606003 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294606000 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294606001 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294606006 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294606007 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294606004 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294606005 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605994 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294605995 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294605992 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294605993 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294605998 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294605999 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294605996 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294605997 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294605986 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605987 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294605984 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294605985 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605990 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294605991 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605988 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294605989 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605978 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605979 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294605976 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605977 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294605982 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294605983 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294605980 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294605981 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294605970 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294605971 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294605968 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605969 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294605974 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294605975 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294605972 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294605973 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294605962 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294605963 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294605960 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605961 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605966 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294605967 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294605964 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294605965 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294605954 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605955 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605952 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294605953 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294605958 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294605959 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294605956 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294605957 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294605946 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294605947 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294605944 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294605945 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605950 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605951 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294605948 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294605949 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605938 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294605939 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294605936 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294605937 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294605942 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294605943 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294605940 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294605941 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605930 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294605931 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294605928 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294605929 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294605934 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294605935 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294605932 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294605933 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294605922 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294605923 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294605920 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294605921 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294605926 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294605927 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294605924 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294605925 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294605914 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294605915 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294605912 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294605913 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605918 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605919 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294605916 DB 00H, 00H
	ORG $+2
$SG4294605917 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605906 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294605907 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294605904 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294605905 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294605910 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294605911 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294605908 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294605909 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294605898 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294605899 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294605896 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294605897 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294605902 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294605903 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294605900 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294605901 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294605890 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294605891 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294605888 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605889 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605894 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605895 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605892 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294605893 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605882 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605883 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294605880 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605881 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294605886 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294605887 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605884 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294605885 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294605874 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294605875 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294605872 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605873 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605878 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294605879 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294605876 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294605877 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294605866 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294605867 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294605864 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294605865 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294605870 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605871 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294605868 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294605869 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294605858 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294605859 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294605856 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294605857 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294605862 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294605863 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294605860 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294605861 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294605850 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294605851 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294605848 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294605849 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294605854 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294605855 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294605852 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294605853 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294605842 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294605843 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294605840 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294605841 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294605846 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294605847 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294605844 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294605845 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294605834 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294605835 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294605832 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605833 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294605838 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294605839 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294605836 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294605837 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294605826 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605827 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294605824 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294605825 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294605830 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294605831 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294605828 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294605829 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294605818 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294605819 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605816 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605817 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294605822 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294605823 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294605820 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294605821 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294605810 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294605811 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605808 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294605809 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294605814 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294605815 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605812 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605813 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294605802 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294605803 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294605800 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294605801 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294605806 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294605807 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294605804 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294605805 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294605794 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294605795 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605792 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294605793 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294605798 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294605799 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294605796 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294605797 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605786 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294605787 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605784 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294605785 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294605790 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605791 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605788 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294605789 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294605778 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294605779 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294605776 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605777 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294605782 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294605783 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294605780 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294605781 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294605770 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294605771 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294605768 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294605769 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294605774 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294605775 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605772 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605773 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605762 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294605763 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294605760 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605761 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294605766 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605767 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294605764 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294605765 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294605754 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294605755 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294605752 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605753 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605758 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605759 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605756 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294605757 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294605746 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605747 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294605744 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294605745 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605750 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294605751 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605748 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294605749 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294605738 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294605739 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605736 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605737 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605742 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294605743 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294605740 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294605741 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294605730 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294605731 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294605728 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605729 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605734 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605735 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605732 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605733 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605722 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294605723 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294605720 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294605721 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294605726 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605727 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605724 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294605725 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605714 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605715 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605712 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294605713 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294605718 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605719 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294605716 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294605717 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294605706 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294605707 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294605704 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605705 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294605710 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294605711 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294605708 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294605709 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294605698 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294605699 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294605696 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605697 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294605702 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294605703 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294605700 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294605701 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294605690 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294605691 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294605689 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294605694 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294605695 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605692 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294605693 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294605656 DB 'S', 00H, 00H, 00H
$SG4294605650 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605651 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294605648 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294605649 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605654 DB 'D', 00H, 00H, 00H
$SG4294605655 DB 'M', 00H, 00H, 00H
$SG4294605652 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294605653 DB 'B', 00H, 00H, 00H
$SG4294605642 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605643 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605640 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294605641 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294605646 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294605647 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294605644 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294605645 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605638 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294605639 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294605637 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294605606 DB ':', 00H, 00H, 00H
$SG4294605604 DB 00H, 00H
	ORG $+2
$SG4294605605 DB 00H, 00H
	ORG $+2
$SG4294605513 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294604800 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294604799 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294604354 DB 'T', 00H, 'h', 00H, 'r', 00H, 'e', 00H, 'a', 00H, 'd', 00H
	DB	'W', 00H, 'n', 00H, 'd', 00H, 00H, 00H
$SG4294604353 DB 'C', 00H, 'T', 00H, 'h', 00H, 'r', 00H, 'e', 00H, 'a', 00H
	DB	'd', 00H, 'W', 00H, 'n', 00H, 'd', 00H, 00H, 00H
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
PUBLIC	?IsRuning@ThreadWnd@@QAE_NXZ			; ThreadWnd::IsRuning
PUBLIC	?Startthread@ThreadWnd@@QAE_NXZ			; ThreadWnd::Startthread
PUBLIC	?Stopthread@ThreadWnd@@QAE_NXZ			; ThreadWnd::Stopthread
PUBLIC	?GetWindowClassName@ThreadWnd@@MBEPB_WXZ	; ThreadWnd::GetWindowClassName
PUBLIC	?GetInterface@ThreadWnd@@SAPAV1@XZ		; ThreadWnd::GetInterface
PUBLIC	?SetThreadProxy@ThreadWnd@@QAEXPAVThreadWndProxy@@@Z ; ThreadWnd::SetThreadProxy
PUBLIC	?run@ThreadWnd@@UAE_NXZ				; ThreadWnd::run
PUBLIC	?HandleMessage@ThreadWnd@@MAEJIIJ@Z		; ThreadWnd::HandleMessage
PUBLIC	??1ThreadWnd@@QAE@XZ				; ThreadWnd::~ThreadWnd
PUBLIC	??0ThreadWnd@@QAE@XZ				; ThreadWnd::ThreadWnd
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
__ehfuncinfo$?GetInterface@ThreadWnd@@SAPAV1@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?GetInterface@ThreadWnd@@SAPAV1@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?GetInterface@ThreadWnd@@SAPAV1@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?GetInterface@ThreadWnd@@SAPAV1@XZ$0
__ehfuncinfo$??1ThreadWnd@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1ThreadWnd@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1ThreadWnd@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1ThreadWnd@@QAE@XZ$0
__ehfuncinfo$??0ThreadWnd@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0ThreadWnd@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0ThreadWnd@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0ThreadWnd@@QAE@XZ$0
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0ThreadWnd@@QAE@XZ PROC				; ThreadWnd::ThreadWnd
; _this$ = ecx

; 6    : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0ThreadWnd@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0CWindowWnd@DuiLib@@QAE@XZ ; DuiLib::CWindowWnd::CWindowWnd
  0002b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002e	83 c1 28	 add	 ecx, 40			; 00000028H
  00031	e8 00 00 00 00	 call	 ??0thread_base@thread_space@@QAE@XZ ; thread_space::thread_base::thread_base
  00036	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0003d	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00040	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7ThreadWnd@@6BCWindowWnd@DuiLib@@@
  00046	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00049	c7 41 28 00 00
	00 00		 mov	 DWORD PTR [ecx+40], OFFSET ??_7ThreadWnd@@6Bthread_base@thread_space@@@

; 4    : ThreadWnd::ThreadWnd(void) : m_pProxy(NULL)

  00050	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00053	c7 42 54 00 00
	00 00		 mov	 DWORD PTR [edx+84], 0

; 5    : ,m_bStart(false)

  0005a	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0005d	c6 40 58 00	 mov	 BYTE PTR [eax+88], 0

; 7    : }

  00061	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00068	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0006b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0006e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00075	83 c4 10	 add	 esp, 16			; 00000010H
  00078	3b ec		 cmp	 ebp, esp
  0007a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0007f	8b e5		 mov	 esp, ebp
  00081	5d		 pop	 ebp
  00082	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??0ThreadWnd@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 28	 add	 ecx, 40			; 00000028H
  00006	e9 00 00 00 00	 jmp	 ??1thread_base@thread_space@@QAE@XZ ; thread_space::thread_base::~thread_base
__ehhandler$??0ThreadWnd@@QAE@XZ:
  0000b	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0ThreadWnd@@QAE@XZ
  00010	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0ThreadWnd@@QAE@XZ ENDP				; ThreadWnd::ThreadWnd
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1ThreadWnd@@QAE@XZ PROC				; ThreadWnd::~ThreadWnd
; _this$ = ecx

; 10   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1ThreadWnd@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00026	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7ThreadWnd@@6BCWindowWnd@DuiLib@@@
  0002c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002f	c7 41 28 00 00
	00 00		 mov	 DWORD PTR [ecx+40], OFFSET ??_7ThreadWnd@@6Bthread_base@thread_space@@@
  00036	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 11   : }

  0003d	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00044	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00047	83 c1 28	 add	 ecx, 40			; 00000028H
  0004a	e8 00 00 00 00	 call	 ??1thread_base@thread_space@@QAE@XZ ; thread_space::thread_base::~thread_base
  0004f	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00052	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00059	83 c4 10	 add	 esp, 16			; 00000010H
  0005c	3b ec		 cmp	 ebp, esp
  0005e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00063	8b e5		 mov	 esp, ebp
  00065	5d		 pop	 ebp
  00066	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??1ThreadWnd@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 28	 add	 ecx, 40			; 00000028H
  00006	e9 00 00 00 00	 jmp	 ??1thread_base@thread_space@@QAE@XZ ; thread_space::thread_base::~thread_base
__ehhandler$??1ThreadWnd@@QAE@XZ:
  0000b	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1ThreadWnd@@QAE@XZ
  00010	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1ThreadWnd@@QAE@XZ ENDP				; ThreadWnd::~ThreadWnd
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_uMsg$ = 8						; size = 4
_wParam$ = 12						; size = 4
_lParam$ = 16						; size = 4
?HandleMessage@ThreadWnd@@MAEJIIJ@Z PROC		; ThreadWnd::HandleMessage
; _this$ = ecx

; 14   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 15   : 	if (uMsg == WM_USER_ANIMATION)

  0000f	81 7d 08 17 9b
	00 00		 cmp	 DWORD PTR _uMsg$[ebp], 39703 ; 00009b17H
  00016	0f 85 84 00 00
	00		 jne	 $LN2@HandleMess

; 16   : 	{
; 17   : 		if (lParam == 0)

  0001c	83 7d 10 00	 cmp	 DWORD PTR _lParam$[ebp], 0
  00020	75 26		 jne	 SHORT $LN3@HandleMess

; 18   : 		{
; 19   : 			if (m_pProxy)

  00022	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00025	83 78 54 00	 cmp	 DWORD PTR [eax+84], 0
  00029	74 1b		 je	 SHORT $LN5@HandleMess

; 20   : 				m_pProxy->AnimationBegin();

  0002b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002e	8b 51 54	 mov	 edx, DWORD PTR [ecx+84]
  00031	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00034	8b 12		 mov	 edx, DWORD PTR [edx]
  00036	8b f4		 mov	 esi, esp
  00038	8b 48 54	 mov	 ecx, DWORD PTR [eax+84]
  0003b	8b 02		 mov	 eax, DWORD PTR [edx]
  0003d	ff d0		 call	 eax
  0003f	3b f4		 cmp	 esi, esp
  00041	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN5@HandleMess:

; 21   : 		}

  00046	eb 58		 jmp	 SHORT $LN2@HandleMess
$LN3@HandleMess:

; 22   : 		else if (lParam == 1)

  00048	83 7d 10 01	 cmp	 DWORD PTR _lParam$[ebp], 1
  0004c	75 27		 jne	 SHORT $LN6@HandleMess

; 23   : 		{
; 24   : 			if (m_pProxy)

  0004e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00051	83 79 54 00	 cmp	 DWORD PTR [ecx+84], 0
  00055	74 1c		 je	 SHORT $LN8@HandleMess

; 25   : 				m_pProxy->AnimationFrame();

  00057	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  0005a	8b 42 54	 mov	 eax, DWORD PTR [edx+84]
  0005d	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00060	8b 10		 mov	 edx, DWORD PTR [eax]
  00062	8b f4		 mov	 esi, esp
  00064	8b 49 54	 mov	 ecx, DWORD PTR [ecx+84]
  00067	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  0006a	ff d0		 call	 eax
  0006c	3b f4		 cmp	 esi, esp
  0006e	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN8@HandleMess:

; 26   : 		}

  00073	eb 2b		 jmp	 SHORT $LN2@HandleMess
$LN6@HandleMess:

; 27   : 		else if (lParam == 2)

  00075	83 7d 10 02	 cmp	 DWORD PTR _lParam$[ebp], 2
  00079	75 25		 jne	 SHORT $LN2@HandleMess

; 28   : 		{
; 29   : 			if (m_pProxy)

  0007b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0007e	83 79 54 00	 cmp	 DWORD PTR [ecx+84], 0
  00082	74 1c		 je	 SHORT $LN2@HandleMess

; 30   : 				m_pProxy->AnimationEnd();

  00084	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00087	8b 42 54	 mov	 eax, DWORD PTR [edx+84]
  0008a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0008d	8b 10		 mov	 edx, DWORD PTR [eax]
  0008f	8b f4		 mov	 esi, esp
  00091	8b 49 54	 mov	 ecx, DWORD PTR [ecx+84]
  00094	8b 42 08	 mov	 eax, DWORD PTR [edx+8]
  00097	ff d0		 call	 eax
  00099	3b f4		 cmp	 esi, esp
  0009b	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN2@HandleMess:

; 31   : 		}
; 32   : 	}
; 33   : 	return 1;

  000a0	b8 01 00 00 00	 mov	 eax, 1

; 34   : }

  000a5	5e		 pop	 esi
  000a6	83 c4 04	 add	 esp, 4
  000a9	3b ec		 cmp	 ebp, esp
  000ab	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b0	8b e5		 mov	 esp, ebp
  000b2	5d		 pop	 ebp
  000b3	c2 0c 00	 ret	 12			; 0000000cH
?HandleMessage@ThreadWnd@@MAEJIIJ@Z ENDP		; ThreadWnd::HandleMessage
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?run@ThreadWnd@@UAE_NXZ PROC				; ThreadWnd::run
; _this$ = ecx

; 37   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 38   : 	::SendMessage(m_hWnd,WM_USER_ANIMATION,0,0);

  0000f	8b f4		 mov	 esi, esp
  00011	6a 00		 push	 0
  00013	6a 00		 push	 0
  00015	68 17 9b 00 00	 push	 39703			; 00009b17H
  0001a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001d	8b 48 dc	 mov	 ecx, DWORD PTR [eax-36]
  00020	51		 push	 ecx
  00021	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__SendMessageW@16
  00027	3b f4		 cmp	 esi, esp
  00029	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN4@run:

; 39   : 	do 
; 40   : 	{
; 41   : 	   ::SendMessage(m_hWnd,WM_USER_ANIMATION,0,1);

  0002e	8b f4		 mov	 esi, esp
  00030	6a 01		 push	 1
  00032	6a 00		 push	 0
  00034	68 17 9b 00 00	 push	 39703			; 00009b17H
  00039	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  0003c	8b 42 dc	 mov	 eax, DWORD PTR [edx-36]
  0003f	50		 push	 eax
  00040	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__SendMessageW@16
  00046	3b f4		 cmp	 esi, esp
  00048	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 42   : 	   Sleep(10);

  0004d	8b f4		 mov	 esi, esp
  0004f	6a 0a		 push	 10			; 0000000aH
  00051	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__Sleep@4
  00057	3b f4		 cmp	 esi, esp
  00059	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 43   : 	} while (m_bStart);

  0005e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00061	0f b6 51 30	 movzx	 edx, BYTE PTR [ecx+48]
  00065	85 d2		 test	 edx, edx
  00067	75 c5		 jne	 SHORT $LN4@run

; 44   : 
; 45   : 	::SendMessage(m_hWnd,WM_USER_ANIMATION,0,2);

  00069	8b f4		 mov	 esi, esp
  0006b	6a 02		 push	 2
  0006d	6a 00		 push	 0
  0006f	68 17 9b 00 00	 push	 39703			; 00009b17H
  00074	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00077	8b 48 dc	 mov	 ecx, DWORD PTR [eax-36]
  0007a	51		 push	 ecx
  0007b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__SendMessageW@16
  00081	3b f4		 cmp	 esi, esp
  00083	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 46   : 	return true;

  00088	b0 01		 mov	 al, 1

; 47   : }

  0008a	5e		 pop	 esi
  0008b	83 c4 04	 add	 esp, 4
  0008e	3b ec		 cmp	 ebp, esp
  00090	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00095	8b e5		 mov	 esp, ebp
  00097	5d		 pop	 ebp
  00098	c3		 ret	 0
?run@ThreadWnd@@UAE_NXZ ENDP				; ThreadWnd::run
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pProxy$ = 8						; size = 4
?SetThreadProxy@ThreadWnd@@QAEXPAVThreadWndProxy@@@Z PROC ; ThreadWnd::SetThreadProxy
; _this$ = ecx

; 50   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 51   : 	m_pProxy = pProxy;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR _pProxy$[ebp]
  00014	89 48 54	 mov	 DWORD PTR [eax+84], ecx

; 52   : }

  00017	8b e5		 mov	 esp, ebp
  00019	5d		 pop	 ebp
  0001a	c2 04 00	 ret	 4
?SetThreadProxy@ThreadWnd@@QAEXPAVThreadWndProxy@@@Z ENDP ; ThreadWnd::SetThreadProxy
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
tv76 = -24						; size = 4
$T2 = -20						; size = 4
$T3 = -16						; size = 4
__$EHRec$ = -12						; size = 12
?GetInterface@ThreadWnd@@SAPAV1@XZ PROC			; ThreadWnd::GetInterface

; 55   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?GetInterface@ThreadWnd@@SAPAV1@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 0c	 sub	 esp, 12			; 0000000cH
  0001b	c7 45 e8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-24], -858993460 ; ccccccccH
  00022	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00029	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH

; 56   : 	static ThreadWnd* threadwnd_ = NULL;
; 57   : 	if (!threadwnd_){

  00030	83 3d 00 00 00
	00 00		 cmp	 DWORD PTR ?threadwnd_@?1??GetInterface@ThreadWnd@@SAPAV2@XZ@4PAV2@A, 0
  00037	75 64		 jne	 SHORT $LN2@GetInterfa

; 58   : 		threadwnd_ = new ThreadWnd();

  00039	6a 5c		 push	 92			; 0000005cH
  0003b	e8 00 00 00 00	 call	 ??2@YAPAXI@Z		; operator new
  00040	83 c4 04	 add	 esp, 4
  00043	89 45 ec	 mov	 DWORD PTR $T2[ebp], eax
  00046	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0004d	83 7d ec 00	 cmp	 DWORD PTR $T2[ebp], 0
  00051	74 0d		 je	 SHORT $LN4@GetInterfa
  00053	8b 4d ec	 mov	 ecx, DWORD PTR $T2[ebp]
  00056	e8 00 00 00 00	 call	 ??0ThreadWnd@@QAE@XZ	; ThreadWnd::ThreadWnd
  0005b	89 45 e8	 mov	 DWORD PTR tv76[ebp], eax
  0005e	eb 07		 jmp	 SHORT $LN5@GetInterfa
$LN4@GetInterfa:
  00060	c7 45 e8 00 00
	00 00		 mov	 DWORD PTR tv76[ebp], 0
$LN5@GetInterfa:
  00067	8b 45 e8	 mov	 eax, DWORD PTR tv76[ebp]
  0006a	89 45 f0	 mov	 DWORD PTR $T3[ebp], eax
  0006d	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00074	8b 4d f0	 mov	 ecx, DWORD PTR $T3[ebp]
  00077	89 0d 00 00 00
	00		 mov	 DWORD PTR ?threadwnd_@?1??GetInterface@ThreadWnd@@SAPAV2@XZ@4PAV2@A, ecx

; 59   : 		threadwnd_->Create(HWND_MESSAGE,L"ThreadWnd",0,0,0,0,0,0,NULL);

  0007d	6a 00		 push	 0
  0007f	6a 00		 push	 0
  00081	6a 00		 push	 0
  00083	6a 00		 push	 0
  00085	6a 00		 push	 0
  00087	6a 00		 push	 0
  00089	6a 00		 push	 0
  0008b	68 00 00 00 00	 push	 OFFSET $SG4294604354
  00090	6a fd		 push	 -3			; fffffffdH
  00092	8b 0d 00 00 00
	00		 mov	 ecx, DWORD PTR ?threadwnd_@?1??GetInterface@ThreadWnd@@SAPAV2@XZ@4PAV2@A
  00098	e8 00 00 00 00	 call	 ?Create@CWindowWnd@DuiLib@@QAEPAUHWND__@@PAU3@PB_WKKHHHHPAUHMENU__@@@Z ; DuiLib::CWindowWnd::Create
$LN2@GetInterfa:

; 60   : 	}
; 61   : 	return threadwnd_;

  0009d	a1 00 00 00 00	 mov	 eax, DWORD PTR ?threadwnd_@?1??GetInterface@ThreadWnd@@SAPAV2@XZ@4PAV2@A

; 62   : }

  000a2	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000a5	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000ac	83 c4 18	 add	 esp, 24			; 00000018H
  000af	3b ec		 cmp	 ebp, esp
  000b1	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000b6	8b e5		 mov	 esp, ebp
  000b8	5d		 pop	 ebp
  000b9	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?GetInterface@ThreadWnd@@SAPAV1@XZ$0:
  00000	6a 5c		 push	 92			; 0000005cH
  00002	8b 45 ec	 mov	 eax, DWORD PTR $T2[ebp]
  00005	50		 push	 eax
  00006	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  0000b	83 c4 08	 add	 esp, 8
  0000e	c3		 ret	 0
__ehhandler$?GetInterface@ThreadWnd@@SAPAV1@XZ:
  0000f	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?GetInterface@ThreadWnd@@SAPAV1@XZ
  00014	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?GetInterface@ThreadWnd@@SAPAV1@XZ ENDP			; ThreadWnd::GetInterface
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetWindowClassName@ThreadWnd@@MBEPB_WXZ PROC		; ThreadWnd::GetWindowClassName
; _this$ = ecx

; 65   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 66   : 	return L"CThreadWnd";

  0000e	b8 00 00 00 00	 mov	 eax, OFFSET $SG4294604353

; 67   : }

  00013	8b e5		 mov	 esp, ebp
  00015	5d		 pop	 ebp
  00016	c3		 ret	 0
?GetWindowClassName@ThreadWnd@@MBEPB_WXZ ENDP		; ThreadWnd::GetWindowClassName
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?Stopthread@ThreadWnd@@QAE_NXZ PROC			; ThreadWnd::Stopthread
; _this$ = ecx

; 70   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 71   : 	m_bStart = false;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c6 40 58 00	 mov	 BYTE PTR [eax+88], 0

; 72   : 	if (get_thread_handle())

  00015	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00018	83 c1 28	 add	 ecx, 40			; 00000028H
  0001b	e8 00 00 00 00	 call	 ?get_thread_handle@thread_base@thread_space@@QAEPAXXZ ; thread_space::thread_base::get_thread_handle
  00020	85 c0		 test	 eax, eax
  00022	74 0f		 je	 SHORT $LN2@Stopthread

; 73   : 		return stop_thread();

  00024	6a 05		 push	 5
  00026	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00029	83 c1 28	 add	 ecx, 40			; 00000028H
  0002c	e8 00 00 00 00	 call	 ?stop_thread@thread_base@thread_space@@QAE_NK@Z ; thread_space::thread_base::stop_thread
  00031	eb 02		 jmp	 SHORT $LN1@Stopthread
$LN2@Stopthread:

; 74   : 
; 75   : 	return false;

  00033	32 c0		 xor	 al, al
$LN1@Stopthread:

; 76   : }

  00035	83 c4 04	 add	 esp, 4
  00038	3b ec		 cmp	 ebp, esp
  0003a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003f	8b e5		 mov	 esp, ebp
  00041	5d		 pop	 ebp
  00042	c3		 ret	 0
?Stopthread@ThreadWnd@@QAE_NXZ ENDP			; ThreadWnd::Stopthread
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?Startthread@ThreadWnd@@QAE_NXZ PROC			; ThreadWnd::Startthread
; _this$ = ecx

; 79   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 80   : 	m_bStart = true;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c6 40 58 01	 mov	 BYTE PTR [eax+88], 1

; 81   : 	return start_thread();

  00015	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00018	83 c1 28	 add	 ecx, 40			; 00000028H
  0001b	e8 00 00 00 00	 call	 ?start_thread@thread_base@thread_space@@QAE_NXZ ; thread_space::thread_base::start_thread

; 82   : }

  00020	83 c4 04	 add	 esp, 4
  00023	3b ec		 cmp	 ebp, esp
  00025	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002a	8b e5		 mov	 esp, ebp
  0002c	5d		 pop	 ebp
  0002d	c3		 ret	 0
?Startthread@ThreadWnd@@QAE_NXZ ENDP			; ThreadWnd::Startthread
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\thread\threadwnd.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?IsRuning@ThreadWnd@@QAE_NXZ PROC			; ThreadWnd::IsRuning
; _this$ = ecx

; 85   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 86   : 	return m_bStart;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8a 40 58	 mov	 al, BYTE PTR [eax+88]

; 87   : }

  00014	8b e5		 mov	 esp, ebp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?IsRuning@ThreadWnd@@QAE_NXZ ENDP			; ThreadWnd::IsRuning
_TEXT	ENDS
END
