// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-regexp-gen.h'

namespace regexp {

// ES6 *********.
// ES #sec-get-regexp.prototype.source
transitioning javascript builtin RegExpPrototypeSourceGetter(
    js-implicit context: NativeContext, receiver: JSAny)(): JSAny {
  typeswitch (receiver) {
    case (receiver: JSRegExp): {
      return receiver.source;
    }
    case (Object): {
    }
  }
  if (!IsReceiverInitialRegExpPrototype(receiver)) {
    const methodName: constexpr string = 'RegExp.prototype.source';
    ThrowTypeError(MessageTemplate::kRegExpNonRegExp, methodName);
  }
  return '(?:)';
}
}
