// Copyright 2018 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

assertEquals('trim',      String.prototype.trim.name);
assertEquals('trimStart', String.prototype.trimStart.name);
assertEquals('trimStart', String.prototype.trimLeft.name);
assertEquals('trimEnd',   String.prototype.trimEnd.name);
assertEquals('trimEnd',   String.prototype.trimRight.name);
