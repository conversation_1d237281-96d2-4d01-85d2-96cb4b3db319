; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	??_7?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@6B@ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::`vftable'
PUBLIC	?wc@?1??GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@3@XZ@4U43@A ; `ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo'::`2'::wc
PUBLIC	??_7CShellTrayNotifyWnd@@6B@			; CShellTrayNotifyWnd::`vftable'
EXTRN	__imp__RegisterWindowMessageA@4:PROC
EXTRN	__imp__lstrlenA@4:PROC
EXTRN	__imp__Shell_NotifyIconA@8:PROC
;	COMDAT ??_7CShellTrayNotifyWnd@@6B@
CONST	SEGMENT
??_7CShellTrayNotifyWnd@@6B@ DD FLAT:?ProcessWindowMessage@CShellTrayNotifyWnd@@UAEHPAUHWND__@@IIJAAJK@Z ; CShellTrayNotifyWnd::`vftable'
	DD	FLAT:??_ECShellTrayNotifyWnd@@UAEPAXI@Z
	DD	FLAT:?GetWindowProc@CShellTrayNotifyWnd@@EAEP6GJPAUHWND__@@IIJ@ZXZ
	DD	FLAT:?OnFinalMessage@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAEXPAUHWND__@@@Z
CONST	ENDS
;	COMDAT ?wc@?1??GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@3@XZ@4U43@A
_DATA	SEGMENT
?wc@?1??GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@3@XZ@4U43@A DD 030H ; `ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo'::`2'::wc
	DD	0bH
	DD	FLAT:?StartWindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z
	DD	00H
	DD	00H
	DD	00H
	DD	00H
	DD	00H
	DD	06H
	DD	00H
	DD	00H
	DD	00H
	DD	00H
	DD	00H
	DD	07f00H
	DD	01H
	DW	00H
	DB	00H, 00H
	ORG $+72
_DATA	ENDS
;	COMDAT ??_7?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@6B@
CONST	SEGMENT
??_7?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@6B@ DD FLAT:?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::`vftable'
	DD	FLAT:?_Delete_this@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ
	DD	FLAT:??_E?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAEPAXI@Z
	DD	FLAT:?_Get_deleter@_Ref_count_base@std@@UBEPAXABVtype_info@@@Z
	ORG $+1
$SG4294338038 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294338036 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294338037 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294338034 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294338035 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294338032 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294338033 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294338022 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294338023 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294338020 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294338021 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294338018 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294338019 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294338016 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294338017 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294338030 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294338031 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294338028 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294338029 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294338026 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294338027 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294338024 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294338025 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294338006 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294338007 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294338004 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294338005 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294338002 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294338003 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294338000 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294338001 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294338014 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294338015 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294338012 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294338013 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294338010 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294338011 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294338008 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294338009 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337990 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337991 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337988 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294337989 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294337986 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337987 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294337984 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294337985 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337998 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337999 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337996 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337997 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337994 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337995 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337992 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294337993 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337974 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294337975 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294337972 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337973 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294337970 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337971 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294337968 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294337969 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337982 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337983 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337980 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337981 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337978 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337979 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294337976 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294337977 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337958 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337959 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337956 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337957 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337954 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337955 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337952 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294337953 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337966 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337967 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294337964 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294337965 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294337962 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337963 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294337960 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294337961 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337942 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294337943 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294337940 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294337941 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294337938 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294337939 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294337936 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294337937 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294337950 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294337951 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294337948 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294337949 DB 00H, 00H
	ORG $+2
$SG4294337946 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294337947 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294337944 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294337945 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294337926 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294337927 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294337924 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294337925 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294337922 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294337923 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294337920 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294337921 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294337934 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294337935 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294337932 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294337933 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294337930 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294337931 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294337928 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294337929 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294337910 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294337911 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294337908 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337909 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294337906 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294337907 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294337904 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294337905 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294337918 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294337919 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294337916 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294337917 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294337914 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294337915 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294337912 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294337913 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294337894 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294337895 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294337892 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337893 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294337890 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337891 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294337888 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337889 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337902 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294337903 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294337900 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294337901 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294337898 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294337899 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337896 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294337897 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294337878 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337879 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294337876 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337877 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337874 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294337875 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337872 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294337873 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294337886 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337887 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337884 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337885 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337882 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337883 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294337880 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294337881 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294337862 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337863 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294337860 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337861 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294337858 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294337859 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294337856 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337857 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337870 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294337871 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294337868 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294337869 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294337866 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294337867 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294337864 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294337865 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294337846 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294337847 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337844 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294337845 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294337842 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294337843 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294337840 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294337841 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294337854 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294337855 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294337852 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294337853 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294337850 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294337851 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294337848 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294337849 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337830 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294337831 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337828 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294337829 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294337826 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337827 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294337824 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294337825 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337838 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294337839 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337836 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294337837 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294337834 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294337835 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294337832 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337833 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294337814 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294337815 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294337812 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337813 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294337810 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294337811 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294337808 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294337809 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294337822 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294337823 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294337820 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337821 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337818 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294337819 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294337816 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337817 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294337798 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294337799 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294337796 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294337797 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294337794 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294337795 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294337792 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294337793 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294337806 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294337807 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294337804 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294337805 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294337802 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294337803 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294337800 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294337801 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294337782 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294337783 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294337780 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294337781 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294337778 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294337779 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294337776 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294337777 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294337790 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294337791 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294337788 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337789 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337786 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294337787 DB 00H, 00H
	ORG $+2
$SG4294337784 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337785 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294337766 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337767 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294337764 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337765 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337762 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294337763 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294337760 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337761 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294337774 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294337775 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294337772 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294337773 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294337770 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294337771 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294337768 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294337769 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294337750 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294337751 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337748 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294337749 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294337746 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294337747 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294337744 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337745 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294337758 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337759 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337756 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294337757 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294337754 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294337755 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294337752 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294337753 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337734 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294337735 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294337732 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294337733 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294337730 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294337731 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294337728 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294337729 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294337742 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294337743 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337740 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294337741 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337738 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294337739 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294337736 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294337737 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294337718 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294337719 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294337716 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294337717 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294337714 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294337715 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294337712 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294337713 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294337726 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294337727 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294337724 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294337725 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294337722 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294337723 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294337720 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294337721 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294337702 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294337703 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337700 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294337701 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294337698 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294337699 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337696 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294337697 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337710 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294337711 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294337708 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294337709 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294337706 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294337707 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337704 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294337705 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294337686 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337687 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337684 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294337685 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294337682 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337683 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337680 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294337681 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294337694 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294337695 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294337692 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294337693 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294337690 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337691 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294337688 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294337689 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294337670 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294337671 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337668 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337669 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294337666 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337667 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294337664 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337665 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294337678 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294337679 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294337676 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294337677 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294337674 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294337675 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294337672 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294337673 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294337654 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337655 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294337652 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294337653 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294337650 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294337651 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294337648 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294337649 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337662 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337663 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294337660 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337661 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337658 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337659 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294337656 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294337657 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294337638 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294337639 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294337636 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294337637 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337634 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294337635 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294337632 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294337633 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337646 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337647 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337644 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337645 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294337642 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294337643 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337640 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294337641 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294337622 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337623 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337620 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294337621 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294337618 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294337619 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294337616 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337617 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337630 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337631 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337628 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294337629 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337626 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294337627 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337624 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337625 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294337606 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337607 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337604 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337605 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337602 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294337603 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337600 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337601 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294337614 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294337615 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294337612 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337613 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294337610 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337611 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294337608 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337609 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294337590 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294337591 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294337588 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294337589 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337586 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337587 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337584 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294337585 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294337598 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337599 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337596 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337597 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337594 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294337595 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294337592 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294337593 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294337574 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294337575 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337572 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294337573 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294337570 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294337571 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294337568 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294337569 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294337582 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294337583 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294337580 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294337581 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294337578 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294337579 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294337576 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294337577 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294337566 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337567 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337564 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294337565 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294337562 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294337563 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294337560 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294337561 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294337526 DB 'M', 00H, 00H, 00H
$SG4294337527 DB 'S', 00H, 00H, 00H
$SG4294337524 DB 'B', 00H, 00H, 00H
$SG4294337525 DB 'D', 00H, 00H, 00H
$SG4294337522 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294337523 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294337520 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337521 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337510 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294337511 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294337508 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294337509 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337518 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294337519 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294337516 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337517 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294337514 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337515 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294337512 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294337513 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294337474 DB '_CShellTrayNotifyWnd_Msg_', 00H
	ORG $+2
$SG4294337486 DB 00H, 00H
	ORG $+2
$SG4294337487 DB ':', 00H, 00H, 00H
$SG4294337485 DB 00H, 00H
PUBLIC	??$_Enable_shared_from_this1@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@U?$integral_constant@_N$0A@@0@@Z ; std::_Enable_shared_from_this1<CShellTrayNotifyWnd,CShellTrayNotifyWnd>
PUBLIC	??$_Enable_shared_from_this@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@@Z ; std::_Enable_shared_from_this<CShellTrayNotifyWnd,CShellTrayNotifyWnd>
PUBLIC	?_Set_ptr_rep@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@2@@Z ; std::_Ptr_base<CShellTrayNotifyWnd>::_Set_ptr_rep
PUBLIC	??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::~_Ref_count_obj<CShellTrayNotifyWnd>
PUBLIC	??_G?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAEPAXI@Z ; std::_Ref_count_obj<CShellTrayNotifyWnd>::`scalar deleting destructor'
PUBLIC	??$move@AAPAVCShellTrayNotifyWnd@@@std@@YA$$QAPAVCShellTrayNotifyWnd@@AAPAV1@@Z ; std::move<CShellTrayNotifyWnd * &>
PUBLIC	??$_Set_ptr_rep_and_enable_shared@VCShellTrayNotifyWnd@@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@AAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@1@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::_Set_ptr_rep_and_enable_shared<CShellTrayNotifyWnd>
PUBLIC	??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Ref_count_obj<CShellTrayNotifyWnd><>
PUBLIC	?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEJIIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::DefWindowProcW
PUBLIC	?_Getptr@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAEPAVCShellTrayNotifyWnd@@XZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Getptr
PUBLIC	?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Destroy
PUBLIC	?_Delete_this@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Delete_this
PUBLIC	??$swap@PAVCShellTrayNotifyWnd@@X@std@@YAXAAPAVCShellTrayNotifyWnd@@0@Z ; std::swap<CShellTrayNotifyWnd *,void>
PUBLIC	??$_Move_construct_from@VCShellTrayNotifyWnd@@@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEX$$QAV01@@Z ; std::_Ptr_base<CShellTrayNotifyWnd>::_Move_construct_from<CShellTrayNotifyWnd>
PUBLIC	??$move@AAV?$shared_ptr@VCShellTrayNotifyWnd@@@std@@@std@@YA$$QAV?$shared_ptr@VCShellTrayNotifyWnd@@@0@AAV10@@Z ; std::move<std::shared_ptr<CShellTrayNotifyWnd> &>
PUBLIC	??$?CVCShellTrayNotifyWnd@@$0A@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBEPAVCShellTrayNotifyWnd@@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::operator-><CShellTrayNotifyWnd,0>
PUBLIC	??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ ; std::make_shared<CShellTrayNotifyWnd>
PUBLIC	?Create@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@GPAX@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create
PUBLIC	?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::WindowProc
PUBLIC	?StartWindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::StartWindowProc
PUBLIC	??R?$_Func_class@_NH@std@@QBE_NH@Z		; std::_Func_class<bool,int>::operator()
PUBLIC	?_Reset_copy@?$_Func_class@_NH@std@@IAEXABV12@@Z ; std::_Func_class<bool,int>::_Reset_copy
PUBLIC	?GetWndStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z ; ATL::CWinTraits<13107200,128>::GetWndStyle
PUBLIC	?GetWndExStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z ; ATL::CWinTraits<13107200,128>::GetWndExStyle
PUBLIC	?_Swap@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXAAV12@@Z ; std::_Ptr_base<CShellTrayNotifyWnd>::_Swap
PUBLIC	??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z	; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>
PUBLIC	?GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@2@XZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo
PUBLIC	?GetWndCaption@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAPB_WXZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndCaption
PUBLIC	?GetWndStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndStyle
PUBLIC	?GetWndExStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndExStyle
PUBLIC	??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
PUBLIC	?swap@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEXAAV12@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::swap
PUBLIC	?get@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IBEPAVCShellTrayNotifyWnd@@XZ ; std::_Ptr_base<CShellTrayNotifyWnd>::get
PUBLIC	?_Decref@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXXZ ; std::_Ptr_base<CShellTrayNotifyWnd>::_Decref
PUBLIC	??0?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAE@XZ ; std::_Ptr_base<CShellTrayNotifyWnd>::_Ptr_base<CShellTrayNotifyWnd>
PUBLIC	??0?$function@$$A6A_NH@Z@std@@QAE@XZ		; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>
PUBLIC	??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z	; std::function<bool __cdecl(int)>::operator=
PUBLIC	??B?$function@$$A6A_NH@Z@std@@QBE_NXZ		; std::function<bool __cdecl(int)>::operator bool
PUBLIC	?Create@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@PAX@Z ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create
PUBLIC	??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >
PUBLIC	?OnFinalMessage@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAEXPAUHWND__@@@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::OnFinalMessage
PUBLIC	??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
PUBLIC	??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
PUBLIC	??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::operator=
PUBLIC	??B?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBE_NXZ ; std::shared_ptr<CShellTrayNotifyWnd>::operator bool
PUBLIC	?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
PUBLIC	?SetTrayMsgListener@CShellTrayNotifyWnd@@QAEXABV?$function@$$A6A_NH@Z@std@@@Z ; CShellTrayNotifyWnd::SetTrayMsgListener
PUBLIC	?SetIcon@CShellTrayNotifyWnd@@QAEHPAUHICON__@@PBDH11W4_TipTitleIconType@1@I@Z ; CShellTrayNotifyWnd::SetIcon
PUBLIC	?OnTrayIconMsg@CShellTrayNotifyWnd@@AAEJIIJAAH@Z ; CShellTrayNotifyWnd::OnTrayIconMsg
PUBLIC	?WindowProc@CShellTrayNotifyWnd@@CGJPAUHWND__@@IIJ@Z ; CShellTrayNotifyWnd::WindowProc
PUBLIC	?GetWindowProc@CShellTrayNotifyWnd@@EAEP6GJPAUHWND__@@IIJ@ZXZ ; CShellTrayNotifyWnd::GetWindowProc
PUBLIC	?OnDestroy@CShellTrayNotifyWnd@@AAEJIIJAAH@Z	; CShellTrayNotifyWnd::OnDestroy
PUBLIC	??1CShellTrayNotifyWnd@@UAE@XZ			; CShellTrayNotifyWnd::~CShellTrayNotifyWnd
PUBLIC	??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::~CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >
PUBLIC	??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::~CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >
PUBLIC	??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >
PUBLIC	??_GCShellTrayNotifyWnd@@UAEPAXI@Z		; CShellTrayNotifyWnd::`scalar deleting destructor'
PUBLIC	??0CShellTrayNotifyWnd@@QAE@XZ			; CShellTrayNotifyWnd::CShellTrayNotifyWnd
PUBLIC	?ProcessWindowMessage@CShellTrayNotifyWnd@@UAEHPAUHWND__@@IIJAAJK@Z ; CShellTrayNotifyWnd::ProcessWindowMessage
PUBLIC	?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z ; JsApi::ShowTrayBalloonPop
PUBLIC	?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
EXTRN	??_E?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAEPAXI@Z:PROC ; std::_Ref_count_obj<CShellTrayNotifyWnd>::`vector deleting destructor'
EXTRN	??_ECShellTrayNotifyWnd@@UAEPAXI@Z:PROC		; CShellTrayNotifyWnd::`vector deleting destructor'
?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A DQ 01H DUP (?) ; JsApi::m_TrayBalloonPop
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$function@$$A6A_NH@Z@std@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0?$function@$$A6A_NH@Z@std@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??0?$function@$$A6A_NH@Z@std@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0?$function@$$A6A_NH@Z@std@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ$2
	DD	00H
	DD	FLAT:__unwindfunclet$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ$1
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ$0
__ehfuncinfo$??1CShellTrayNotifyWnd@@UAE@XZ DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??1CShellTrayNotifyWnd@@UAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1CShellTrayNotifyWnd@@UAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1CShellTrayNotifyWnd@@UAE@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??1CShellTrayNotifyWnd@@UAE@XZ$1
__ehfuncinfo$??0CShellTrayNotifyWnd@@QAE@XZ DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??0CShellTrayNotifyWnd@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0CShellTrayNotifyWnd@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0CShellTrayNotifyWnd@@QAE@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??0CShellTrayNotifyWnd@@QAE@XZ$1
__ehfuncinfo$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z$0
?m_TrayBalloonPop$initializer$@JsApi@@3P6AXXZA DD FLAT:??__Em_TrayBalloonPop@JsApi@@YAXXZ ; JsApi::m_TrayBalloonPop$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
tv129 = -28						; size = 4
tv130 = -24						; size = 4
$T2 = -20						; size = 8
__$EHRec$ = -12						; size = 12
_hIcon$ = 8						; size = 4
_szTip$ = 12						; size = 4
_bBalloon$ = 16						; size = 4
_szTitle$ = 20						; size = 4
_szBalloonTip$ = 24					; size = 4
_iTipTitleIconType$ = 28				; size = 4
_uTimeoutMs$ = 32					; size = 4
_callback$ = 36						; size = 4
?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z PROC ; JsApi::ShowTrayBalloonPop

; 195  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 10	 sub	 esp, 16			; 00000010H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00023	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00026	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00029	89 45 f0	 mov	 DWORD PTR [ebp-16], eax

; 196  : 		if (!m_TrayBalloonPop) m_TrayBalloonPop = std::make_shared<CShellTrayNotifyWnd>();

  0002c	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
  00031	e8 00 00 00 00	 call	 ??B?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBE_NXZ ; std::shared_ptr<CShellTrayNotifyWnd>::operator bool
  00036	0f b6 c0	 movzx	 eax, al
  00039	85 c0		 test	 eax, eax
  0003b	75 39		 jne	 SHORT $LN2@ShowTrayBa
  0003d	8d 4d ec	 lea	 ecx, DWORD PTR $T2[ebp]
  00040	51		 push	 ecx
  00041	e8 00 00 00 00	 call	 ??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ ; std::make_shared<CShellTrayNotifyWnd>
  00046	83 c4 04	 add	 esp, 4
  00049	89 45 e8	 mov	 DWORD PTR tv130[ebp], eax
  0004c	8b 55 e8	 mov	 edx, DWORD PTR tv130[ebp]
  0004f	89 55 e4	 mov	 DWORD PTR tv129[ebp], edx
  00052	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00059	8b 45 e4	 mov	 eax, DWORD PTR tv129[ebp]
  0005c	50		 push	 eax
  0005d	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
  00062	e8 00 00 00 00	 call	 ??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::operator=
  00067	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0006e	8d 4d ec	 lea	 ecx, DWORD PTR $T2[ebp]
  00071	e8 00 00 00 00	 call	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
$LN2@ShowTrayBa:

; 197  : 		if (callback && m_TrayBalloonPop) {

  00076	8b 4d 24	 mov	 ecx, DWORD PTR _callback$[ebp]
  00079	e8 00 00 00 00	 call	 ??B?$function@$$A6A_NH@Z@std@@QBE_NXZ ; std::function<bool __cdecl(int)>::operator bool
  0007e	0f b6 c8	 movzx	 ecx, al
  00081	85 c9		 test	 ecx, ecx
  00083	74 26		 je	 SHORT $LN3@ShowTrayBa
  00085	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
  0008a	e8 00 00 00 00	 call	 ??B?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBE_NXZ ; std::shared_ptr<CShellTrayNotifyWnd>::operator bool
  0008f	0f b6 d0	 movzx	 edx, al
  00092	85 d2		 test	 edx, edx
  00094	74 15		 je	 SHORT $LN3@ShowTrayBa

; 198  : 			m_TrayBalloonPop->SetTrayMsgListener(callback);

  00096	8b 45 24	 mov	 eax, DWORD PTR _callback$[ebp]
  00099	50		 push	 eax
  0009a	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
  0009f	e8 00 00 00 00	 call	 ??$?CVCShellTrayNotifyWnd@@$0A@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBEPAVCShellTrayNotifyWnd@@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::operator-><CShellTrayNotifyWnd,0>
  000a4	8b c8		 mov	 ecx, eax
  000a6	e8 00 00 00 00	 call	 ?SetTrayMsgListener@CShellTrayNotifyWnd@@QAEXABV?$function@$$A6A_NH@Z@std@@@Z ; CShellTrayNotifyWnd::SetTrayMsgListener
$LN3@ShowTrayBa:

; 199  : 		}
; 200  : 
; 201  : 		m_TrayBalloonPop->SetIcon(hIcon, szTip, bBalloon, szTitle, szBalloonTip, (CShellTrayNotifyWnd::_TipTitleIconType)iTipTitleIconType, uTimeoutMs);

  000ab	8b 4d 20	 mov	 ecx, DWORD PTR _uTimeoutMs$[ebp]
  000ae	51		 push	 ecx
  000af	8b 55 1c	 mov	 edx, DWORD PTR _iTipTitleIconType$[ebp]
  000b2	52		 push	 edx
  000b3	8b 45 18	 mov	 eax, DWORD PTR _szBalloonTip$[ebp]
  000b6	50		 push	 eax
  000b7	8b 4d 14	 mov	 ecx, DWORD PTR _szTitle$[ebp]
  000ba	51		 push	 ecx
  000bb	8b 55 10	 mov	 edx, DWORD PTR _bBalloon$[ebp]
  000be	52		 push	 edx
  000bf	8b 45 0c	 mov	 eax, DWORD PTR _szTip$[ebp]
  000c2	50		 push	 eax
  000c3	8b 4d 08	 mov	 ecx, DWORD PTR _hIcon$[ebp]
  000c6	51		 push	 ecx
  000c7	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
  000cc	e8 00 00 00 00	 call	 ??$?CVCShellTrayNotifyWnd@@$0A@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBEPAVCShellTrayNotifyWnd@@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::operator-><CShellTrayNotifyWnd,0>
  000d1	8b c8		 mov	 ecx, eax
  000d3	e8 00 00 00 00	 call	 ?SetIcon@CShellTrayNotifyWnd@@QAEHPAUHICON__@@PBDH11W4_TipTitleIconType@1@I@Z ; CShellTrayNotifyWnd::SetIcon

; 202  : 	}

  000d8	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000db	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000e2	83 c4 1c	 add	 esp, 28			; 0000001cH
  000e5	3b ec		 cmp	 ebp, esp
  000e7	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000ec	8b e5		 mov	 esp, ebp
  000ee	5d		 pop	 ebp
  000ef	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z$0:
  00000	8d 4d ec	 lea	 ecx, DWORD PTR $T2[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
__ehhandler$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?ShowTrayBalloonPop@JsApi@@YAXPAUHICON__@@PBDH11HIABV?$function@$$A6A_NH@Z@std@@@Z ENDP ; JsApi::ShowTrayBalloonPop
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
;	COMDAT ?ProcessWindowMessage@CShellTrayNotifyWnd@@UAEHPAUHWND__@@IIJAAJK@Z
_TEXT	SEGMENT
tv64 = -20						; size = 4
_bHandled$ = -12					; size = 4
_this$ = -4						; size = 4
_hWnd$ = 8						; size = 4
_uMsg$ = 12						; size = 4
_wParam$ = 16						; size = 4
_lParam$ = 20						; size = 4
_lResult$ = 24						; size = 4
_dwMsgMapID$ = 28					; size = 4
?ProcessWindowMessage@CShellTrayNotifyWnd@@UAEHPAUHWND__@@IIJAAJK@Z PROC ; CShellTrayNotifyWnd::ProcessWindowMessage, COMDAT
; _this$ = ecx

; 52   : 	BEGIN_MSG_MAP(CMyWindow)

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 14	 sub	 esp, 20			; 00000014H
  00006	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000b	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0000e	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  00011	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00014	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00017	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  0001a	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0001d	c7 45 f4 01 00
	00 00		 mov	 DWORD PTR _bHandled$[ebp], 1
  00024	8b 45 1c	 mov	 eax, DWORD PTR _dwMsgMapID$[ebp]
  00027	89 45 ec	 mov	 DWORD PTR tv64[ebp], eax
  0002a	83 7d ec 00	 cmp	 DWORD PTR tv64[ebp], 0
  0002e	74 02		 je	 SHORT $LN4@ProcessWin
  00030	eb 37		 jmp	 SHORT $LN2@ProcessWin
$LN4@ProcessWin:

; 53   : 			MESSAGE_HANDLER(WM_DESTROY, OnDestroy)			

  00032	83 7d 0c 02	 cmp	 DWORD PTR _uMsg$[ebp], 2
  00036	75 31		 jne	 SHORT $LN2@ProcessWin
  00038	c7 45 f4 01 00
	00 00		 mov	 DWORD PTR _bHandled$[ebp], 1
  0003f	8d 4d f4	 lea	 ecx, DWORD PTR _bHandled$[ebp]
  00042	51		 push	 ecx
  00043	8b 55 14	 mov	 edx, DWORD PTR _lParam$[ebp]
  00046	52		 push	 edx
  00047	8b 45 10	 mov	 eax, DWORD PTR _wParam$[ebp]
  0004a	50		 push	 eax
  0004b	8b 4d 0c	 mov	 ecx, DWORD PTR _uMsg$[ebp]
  0004e	51		 push	 ecx
  0004f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00052	e8 00 00 00 00	 call	 ?OnDestroy@CShellTrayNotifyWnd@@AAEJIIJAAH@Z ; CShellTrayNotifyWnd::OnDestroy
  00057	8b 55 18	 mov	 edx, DWORD PTR _lResult$[ebp]
  0005a	89 02		 mov	 DWORD PTR [edx], eax
  0005c	83 7d f4 00	 cmp	 DWORD PTR _bHandled$[ebp], 0
  00060	74 07		 je	 SHORT $LN2@ProcessWin
  00062	b8 01 00 00 00	 mov	 eax, 1
  00067	eb 02		 jmp	 SHORT $LN1@ProcessWin
$LN2@ProcessWin:

; 54   : 	END_MSG_MAP()

  00069	33 c0		 xor	 eax, eax
$LN1@ProcessWin:
  0006b	52		 push	 edx
  0006c	8b cd		 mov	 ecx, ebp
  0006e	50		 push	 eax
  0006f	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@ProcessWin
  00075	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0007a	58		 pop	 eax
  0007b	5a		 pop	 edx
  0007c	83 c4 14	 add	 esp, 20			; 00000014H
  0007f	3b ec		 cmp	 ebp, esp
  00081	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00086	8b e5		 mov	 esp, ebp
  00088	5d		 pop	 ebp
  00089	c2 18 00	 ret	 24			; 00000018H
$LN11@ProcessWin:
  0008c	01 00 00 00	 DD	 1
  00090	00 00 00 00	 DD	 $LN10@ProcessWin
$LN10@ProcessWin:
  00094	f4 ff ff ff	 DD	 -12			; fffffff4H
  00098	04 00 00 00	 DD	 4
  0009c	00 00 00 00	 DD	 $LN9@ProcessWin
$LN9@ProcessWin:
  000a0	62		 DB	 98			; 00000062H
  000a1	48		 DB	 72			; 00000048H
  000a2	61		 DB	 97			; 00000061H
  000a3	6e		 DB	 110			; 0000006eH
  000a4	64		 DB	 100			; 00000064H
  000a5	6c		 DB	 108			; 0000006cH
  000a6	65		 DB	 101			; 00000065H
  000a7	64		 DB	 100			; 00000064H
  000a8	00		 DB	 0
?ProcessWindowMessage@CShellTrayNotifyWnd@@UAEHPAUHWND__@@IIJAAJK@Z ENDP ; CShellTrayNotifyWnd::ProcessWindowMessage
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0CShellTrayNotifyWnd@@QAE@XZ PROC			; CShellTrayNotifyWnd::CShellTrayNotifyWnd
; _this$ = ecx

; 59   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0CShellTrayNotifyWnd@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	56		 push	 esi
  0001a	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00021	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00024	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00027	e8 00 00 00 00	 call	 ??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ
  0002c	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00033	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00036	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CShellTrayNotifyWnd@@6B@

; 58   : CShellTrayNotifyWnd::CShellTrayNotifyWnd():m_dwID(0),m_hIcon(NULL),m_uTrayMsg(0)

  0003c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003f	c7 41 24 00 00
	00 00		 mov	 DWORD PTR [ecx+36], 0
  00046	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00049	c7 42 28 00 00
	00 00		 mov	 DWORD PTR [edx+40], 0
  00050	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00053	c7 40 2c 00 00
	00 00		 mov	 DWORD PTR [eax+44], 0

; 59   : {

  0005a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0005d	81 c1 30 02 00
	00		 add	 ecx, 560		; 00000230H
  00063	e8 00 00 00 00	 call	 ??0?$function@$$A6A_NH@Z@std@@QAE@XZ ; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>
  00068	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1

; 60   : 	m_dwID=GetTickCount();

  0006c	8b f4		 mov	 esi, esp
  0006e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetTickCount@0
  00074	3b f4		 cmp	 esi, esp
  00076	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0007b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0007e	89 41 24	 mov	 DWORD PTR [ecx+36], eax

; 61   : 	m_uTrayMsg=RegisterWindowMessageA("_CShellTrayNotifyWnd_Msg_");

  00081	8b f4		 mov	 esi, esp
  00083	68 00 00 00 00	 push	 OFFSET $SG4294337474
  00088	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__RegisterWindowMessageA@4
  0008e	3b f4		 cmp	 esi, esp
  00090	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00095	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00098	89 42 2c	 mov	 DWORD PTR [edx+44], eax

; 62   : }

  0009b	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000a2	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  000a5	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000a8	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000af	5e		 pop	 esi
  000b0	83 c4 10	 add	 esp, 16			; 00000010H
  000b3	3b ec		 cmp	 ebp, esp
  000b5	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000ba	8b e5		 mov	 esp, ebp
  000bc	5d		 pop	 ebp
  000bd	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??0CShellTrayNotifyWnd@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ
__unwindfunclet$??0CShellTrayNotifyWnd@@QAE@XZ$1:
  00008	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000b	81 c1 30 02 00
	00		 add	 ecx, 560		; 00000230H
  00011	e9 00 00 00 00	 jmp	 ??1?$function@$$A6A_NH@Z@std@@QAE@XZ
__ehhandler$??0CShellTrayNotifyWnd@@QAE@XZ:
  00016	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0CShellTrayNotifyWnd@@QAE@XZ
  0001b	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0CShellTrayNotifyWnd@@QAE@XZ ENDP			; CShellTrayNotifyWnd::CShellTrayNotifyWnd
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_GCShellTrayNotifyWnd@@UAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___flags$ = 8						; size = 4
??_GCShellTrayNotifyWnd@@UAEPAXI@Z PROC			; CShellTrayNotifyWnd::`scalar deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??1CShellTrayNotifyWnd@@UAE@XZ ; CShellTrayNotifyWnd::~CShellTrayNotifyWnd
  00016	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  00019	83 e0 01	 and	 eax, 1
  0001c	74 11		 je	 SHORT $LN2@scalar
  0001e	68 58 02 00 00	 push	 600			; 00000258H
  00023	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	51		 push	 ecx
  00027	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  0002c	83 c4 08	 add	 esp, 8
$LN2@scalar:
  0002f	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00032	83 c4 04	 add	 esp, 4
  00035	3b ec		 cmp	 ebp, esp
  00037	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003c	8b e5		 mov	 esp, ebp
  0003e	5d		 pop	 ebp
  0003f	c2 04 00	 ret	 4
??_GCShellTrayNotifyWnd@@UAEPAXI@Z ENDP			; CShellTrayNotifyWnd::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ PROC ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00032	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00039	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00046	83 c4 10	 add	 esp, 16			; 00000010H
  00049	3b ec		 cmp	 ebp, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00050	8b e5		 mov	 esp, ebp
  00052	5d		 pop	 ebp
  00053	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ
__ehhandler$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAE@XZ ENDP ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ PROC ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::~CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0002a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	e8 00 00 00 00	 call	 ??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ
  00039	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003c	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00043	83 c4 10	 add	 esp, 16			; 00000010H
  00046	3b ec		 cmp	 ebp, esp
  00048	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004d	8b e5		 mov	 esp, ebp
  0004f	5d		 pop	 ebp
  00050	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ
__ehhandler$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ ENDP ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::~CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::~CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0002a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	e8 00 00 00 00	 call	 ??1?$CWindowImplRoot@VCWindow@ATL@@@ATL@@UAE@XZ ; ATL::CWindowImplRoot<ATL::CWindow>::~CWindowImplRoot<ATL::CWindow>
  00039	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003c	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00043	83 c4 10	 add	 esp, 16			; 00000010H
  00046	3b ec		 cmp	 ebp, esp
  00048	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004d	8b e5		 mov	 esp, ebp
  0004f	5d		 pop	 ebp
  00050	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$CWindowImplRoot@VCWindow@ATL@@@ATL@@UAE@XZ ; ATL::CWindowImplRoot<ATL::CWindow>::~CWindowImplRoot<ATL::CWindow>
__ehhandler$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAE@XZ ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::~CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1CShellTrayNotifyWnd@@UAE@XZ PROC			; CShellTrayNotifyWnd::~CShellTrayNotifyWnd
; _this$ = ecx

; 65   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1CShellTrayNotifyWnd@@UAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00026	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CShellTrayNotifyWnd@@6B@
  0002c	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 66   : 	if(m_hIcon) SetIcon(NULL);

  00033	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00036	83 79 28 00	 cmp	 DWORD PTR [ecx+40], 0
  0003a	74 19		 je	 SHORT $LN1@CShellTray
  0003c	68 10 27 00 00	 push	 10000			; 00002710H
  00041	6a 00		 push	 0
  00043	6a 00		 push	 0
  00045	6a 00		 push	 0
  00047	6a 00		 push	 0
  00049	6a 00		 push	 0
  0004b	6a 00		 push	 0
  0004d	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00050	e8 00 00 00 00	 call	 ?SetIcon@CShellTrayNotifyWnd@@QAEHPAUHICON__@@PBDH11W4_TipTitleIconType@1@I@Z ; CShellTrayNotifyWnd::SetIcon
$LN1@CShellTray:

; 67   : }

  00055	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00059	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0005c	81 c1 30 02 00
	00		 add	 ecx, 560		; 00000230H
  00062	e8 00 00 00 00	 call	 ??1?$function@$$A6A_NH@Z@std@@QAE@XZ
  00067	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0006e	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00071	e8 00 00 00 00	 call	 ??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ
  00076	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00079	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00080	83 c4 10	 add	 esp, 16			; 00000010H
  00083	3b ec		 cmp	 ebp, esp
  00085	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0008a	8b e5		 mov	 esp, ebp
  0008c	5d		 pop	 ebp
  0008d	c3		 ret	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$??1CShellTrayNotifyWnd@@UAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@UAE@XZ
__unwindfunclet$??1CShellTrayNotifyWnd@@UAE@XZ$1:
  00008	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000b	81 c1 30 02 00
	00		 add	 ecx, 560		; 00000230H
  00011	e9 00 00 00 00	 jmp	 ??1?$function@$$A6A_NH@Z@std@@QAE@XZ
__ehhandler$??1CShellTrayNotifyWnd@@UAE@XZ:
  00016	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1CShellTrayNotifyWnd@@UAE@XZ
  0001b	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1CShellTrayNotifyWnd@@UAE@XZ ENDP			; CShellTrayNotifyWnd::~CShellTrayNotifyWnd
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_uMsg$ = 8						; size = 4
_wParam$ = 12						; size = 4
_lParam$ = 16						; size = 4
_bHandled$ = 20						; size = 4
?OnDestroy@CShellTrayNotifyWnd@@AAEJIIJAAH@Z PROC	; CShellTrayNotifyWnd::OnDestroy
; _this$ = ecx

; 70   : {	

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 71   : 	this->SetIcon(NULL);

  0000e	68 10 27 00 00	 push	 10000			; 00002710H
  00013	6a 00		 push	 0
  00015	6a 00		 push	 0
  00017	6a 00		 push	 0
  00019	6a 00		 push	 0
  0001b	6a 00		 push	 0
  0001d	6a 00		 push	 0
  0001f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00022	e8 00 00 00 00	 call	 ?SetIcon@CShellTrayNotifyWnd@@QAEHPAUHICON__@@PBDH11W4_TipTitleIconType@1@I@Z ; CShellTrayNotifyWnd::SetIcon

; 72   : 	return 0;

  00027	33 c0		 xor	 eax, eax

; 73   : }

  00029	83 c4 04	 add	 esp, 4
  0002c	3b ec		 cmp	 ebp, esp
  0002e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00033	8b e5		 mov	 esp, ebp
  00035	5d		 pop	 ebp
  00036	c2 10 00	 ret	 16			; 00000010H
?OnDestroy@CShellTrayNotifyWnd@@AAEJIIJAAH@Z ENDP	; CShellTrayNotifyWnd::OnDestroy
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetWindowProc@CShellTrayNotifyWnd@@EAEP6GJPAUHWND__@@IIJ@ZXZ PROC ; CShellTrayNotifyWnd::GetWindowProc
; _this$ = ecx

; 76   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 77   : 	return WindowProc;

  0000e	b8 00 00 00 00	 mov	 eax, OFFSET ?WindowProc@CShellTrayNotifyWnd@@CGJPAUHWND__@@IIJ@Z ; CShellTrayNotifyWnd::WindowProc

; 78   : }

  00013	8b e5		 mov	 esp, ebp
  00015	5d		 pop	 ebp
  00016	c3		 ret	 0
?GetWindowProc@CShellTrayNotifyWnd@@EAEP6GJPAUHWND__@@IIJ@ZXZ ENDP ; CShellTrayNotifyWnd::GetWindowProc
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
_bHandled$ = -48					; size = 4
_stMsg$ = -36						; size = 28
_pThis$ = -4						; size = 4
_hWnd$ = 8						; size = 4
_uMsg$ = 12						; size = 4
_wParam$ = 16						; size = 4
_lParam$ = 20						; size = 4
?WindowProc@CShellTrayNotifyWnd@@CGJPAUHWND__@@IIJ@Z PROC ; CShellTrayNotifyWnd::WindowProc

; 81   : {	

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 34	 sub	 esp, 52			; 00000034H
  00006	57		 push	 edi
  00007	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  0000a	b9 0d 00 00 00	 mov	 ecx, 13			; 0000000dH
  0000f	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00014	f3 ab		 rep stosd

; 82   : 	CShellTrayNotifyWnd *pThis=(CShellTrayNotifyWnd*)hWnd;

  00016	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  00019	89 45 fc	 mov	 DWORD PTR _pThis$[ebp], eax

; 83   : 	
; 84   : 	MSG stMsg={hWnd,uMsg,wParam,lParam,0,{0,0}};

  0001c	8b 4d 08	 mov	 ecx, DWORD PTR _hWnd$[ebp]
  0001f	89 4d dc	 mov	 DWORD PTR _stMsg$[ebp], ecx
  00022	8b 55 0c	 mov	 edx, DWORD PTR _uMsg$[ebp]
  00025	89 55 e0	 mov	 DWORD PTR _stMsg$[ebp+4], edx
  00028	8b 45 10	 mov	 eax, DWORD PTR _wParam$[ebp]
  0002b	89 45 e4	 mov	 DWORD PTR _stMsg$[ebp+8], eax
  0002e	8b 4d 14	 mov	 ecx, DWORD PTR _lParam$[ebp]
  00031	89 4d e8	 mov	 DWORD PTR _stMsg$[ebp+12], ecx
  00034	c7 45 ec 00 00
	00 00		 mov	 DWORD PTR _stMsg$[ebp+16], 0
  0003b	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR _stMsg$[ebp+20], 0
  00042	c7 45 f4 00 00
	00 00		 mov	 DWORD PTR _stMsg$[ebp+24], 0

; 85   : 	BOOL bHandled=FALSE;

  00049	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR _bHandled$[ebp], 0

; 86   : 	if(pThis->m_uTrayMsg==uMsg) pThis->OnTrayIconMsg(uMsg,wParam,lParam,bHandled);

  00050	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  00053	8b 42 2c	 mov	 eax, DWORD PTR [edx+44]
  00056	3b 45 0c	 cmp	 eax, DWORD PTR _uMsg$[ebp]
  00059	75 18		 jne	 SHORT $LN2@WindowProc
  0005b	8d 4d d0	 lea	 ecx, DWORD PTR _bHandled$[ebp]
  0005e	51		 push	 ecx
  0005f	8b 55 14	 mov	 edx, DWORD PTR _lParam$[ebp]
  00062	52		 push	 edx
  00063	8b 45 10	 mov	 eax, DWORD PTR _wParam$[ebp]
  00066	50		 push	 eax
  00067	8b 4d 0c	 mov	 ecx, DWORD PTR _uMsg$[ebp]
  0006a	51		 push	 ecx
  0006b	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0006e	e8 00 00 00 00	 call	 ?OnTrayIconMsg@CShellTrayNotifyWnd@@AAEJIIJAAH@Z ; CShellTrayNotifyWnd::OnTrayIconMsg
$LN2@WindowProc:

; 87   : 			 
; 88   : 	return CWindowImpl<CShellTrayNotifyWnd, CWindow,CWinTraits< WS_CAPTION|WS_SYSMENU, WS_EX_TOOLWINDOW>>::WindowProc(hWnd,uMsg,wParam,lParam);

  00073	8b 55 14	 mov	 edx, DWORD PTR _lParam$[ebp]
  00076	52		 push	 edx
  00077	8b 45 10	 mov	 eax, DWORD PTR _wParam$[ebp]
  0007a	50		 push	 eax
  0007b	8b 4d 0c	 mov	 ecx, DWORD PTR _uMsg$[ebp]
  0007e	51		 push	 ecx
  0007f	8b 55 08	 mov	 edx, DWORD PTR _hWnd$[ebp]
  00082	52		 push	 edx
  00083	e8 00 00 00 00	 call	 ?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::WindowProc

; 89   : }	

  00088	52		 push	 edx
  00089	8b cd		 mov	 ecx, ebp
  0008b	50		 push	 eax
  0008c	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN7@WindowProc
  00092	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00097	58		 pop	 eax
  00098	5a		 pop	 edx
  00099	5f		 pop	 edi
  0009a	83 c4 34	 add	 esp, 52			; 00000034H
  0009d	3b ec		 cmp	 ebp, esp
  0009f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000a4	8b e5		 mov	 esp, ebp
  000a6	5d		 pop	 ebp
  000a7	c2 10 00	 ret	 16			; 00000010H
  000aa	66 90		 npad	 2
$LN7@WindowProc:
  000ac	02 00 00 00	 DD	 2
  000b0	00 00 00 00	 DD	 $LN6@WindowProc
$LN6@WindowProc:
  000b4	dc ff ff ff	 DD	 -36			; ffffffdcH
  000b8	1c 00 00 00	 DD	 28			; 0000001cH
  000bc	00 00 00 00	 DD	 $LN4@WindowProc
  000c0	d0 ff ff ff	 DD	 -48			; ffffffd0H
  000c4	04 00 00 00	 DD	 4
  000c8	00 00 00 00	 DD	 $LN5@WindowProc
$LN5@WindowProc:
  000cc	62		 DB	 98			; 00000062H
  000cd	48		 DB	 72			; 00000048H
  000ce	61		 DB	 97			; 00000061H
  000cf	6e		 DB	 110			; 0000006eH
  000d0	64		 DB	 100			; 00000064H
  000d1	6c		 DB	 108			; 0000006cH
  000d2	65		 DB	 101			; 00000065H
  000d3	64		 DB	 100			; 00000064H
  000d4	00		 DB	 0
$LN4@WindowProc:
  000d5	73		 DB	 115			; 00000073H
  000d6	74		 DB	 116			; 00000074H
  000d7	4d		 DB	 77			; 0000004dH
  000d8	73		 DB	 115			; 00000073H
  000d9	67		 DB	 103			; 00000067H
  000da	00		 DB	 0
?WindowProc@CShellTrayNotifyWnd@@CGJPAUHWND__@@IIJ@Z ENDP ; CShellTrayNotifyWnd::WindowProc
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
tv64 = -8						; size = 4
_this$ = -4						; size = 4
_uMsg$ = 8						; size = 4
_wParam$ = 12						; size = 4
_lParam$ = 16						; size = 4
_bHandled$ = 20						; size = 4
?OnTrayIconMsg@CShellTrayNotifyWnd@@AAEJIIJAAH@Z PROC	; CShellTrayNotifyWnd::OnTrayIconMsg
; _this$ = ecx

; 93   : {	

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 94   : 	switch(lParam)

  00017	8b 45 10	 mov	 eax, DWORD PTR _lParam$[ebp]
  0001a	89 45 f8	 mov	 DWORD PTR tv64[ebp], eax
  0001d	81 7d f8 02 04
	00 00		 cmp	 DWORD PTR tv64[ebp], 1026 ; 00000402H
  00024	7f 29		 jg	 SHORT $LN15@OnTrayIcon
  00026	81 7d f8 02 04
	00 00		 cmp	 DWORD PTR tv64[ebp], 1026 ; 00000402H
  0002d	74 34		 je	 SHORT $LN4@OnTrayIcon
  0002f	8b 4d f8	 mov	 ecx, DWORD PTR tv64[ebp]
  00032	81 e9 00 02 00
	00		 sub	 ecx, 512		; 00000200H
  00038	89 4d f8	 mov	 DWORD PTR tv64[ebp], ecx
  0003b	83 7d f8 06	 cmp	 DWORD PTR tv64[ebp], 6
  0003f	0f 87 94 00 00
	00		 ja	 $LN2@OnTrayIcon
  00045	8b 55 f8	 mov	 edx, DWORD PTR tv64[ebp]
  00048	ff 24 95 00 00
	00 00		 jmp	 DWORD PTR $LN16@OnTrayIcon[edx*4]
$LN15@OnTrayIcon:
  0004f	81 7d f8 04 04
	00 00		 cmp	 DWORD PTR tv64[ebp], 1028 ; 00000404H
  00056	74 17		 je	 SHORT $LN5@OnTrayIcon
  00058	81 7d f8 05 04
	00 00		 cmp	 DWORD PTR tv64[ebp], 1029 ; 00000405H
  0005f	74 1a		 je	 SHORT $LN6@OnTrayIcon
  00061	eb 76		 jmp	 SHORT $LN2@OnTrayIcon
$LN4@OnTrayIcon:

; 95   : 	{
; 96   :     case 0x402:
; 97   : 		OnTrayMsg(TRAY_MSG_TIP_POP);break;

  00063	6a 00		 push	 0
  00065	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00068	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  0006d	eb 6a		 jmp	 SHORT $LN2@OnTrayIcon
$LN5@OnTrayIcon:

; 98   :     case 0x404:
; 99   : 		OnTrayMsg(TRAY_MSG_TIP_CLOSE_CLICK);break;

  0006f	6a 01		 push	 1
  00071	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00074	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  00079	eb 5e		 jmp	 SHORT $LN2@OnTrayIcon
$LN6@OnTrayIcon:

; 100  :     case 0x405:
; 101  : 		OnTrayMsg(TRAY_MSG_TIP_DISAPPEAR);break;

  0007b	6a 02		 push	 2
  0007d	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00080	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  00085	eb 52		 jmp	 SHORT $LN2@OnTrayIcon
$LN7@OnTrayIcon:

; 102  :     case 0x200:
; 103  : 		OnTrayMsg(TRAY_MSG_MOUSE_MOVE);break;

  00087	6a 03		 push	 3
  00089	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0008c	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  00091	eb 46		 jmp	 SHORT $LN2@OnTrayIcon
$LN8@OnTrayIcon:

; 104  :     case 0x201:
; 105  : 		OnTrayMsg(TRAY_MSG_LBUTTON_DOWN);break;

  00093	6a 04		 push	 4
  00095	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00098	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  0009d	eb 3a		 jmp	 SHORT $LN2@OnTrayIcon
$LN9@OnTrayIcon:

; 106  :     case 0x202:
; 107  : 		OnTrayMsg(TRAY_MSG_LBUTTON_UP);break;

  0009f	6a 05		 push	 5
  000a1	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000a4	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  000a9	eb 2e		 jmp	 SHORT $LN2@OnTrayIcon
$LN10@OnTrayIcon:

; 108  :     case 0x203:
; 109  : 		OnTrayMsg(TRAY_MSG_LBUTTON_DBLCLK);break;

  000ab	6a 08		 push	 8
  000ad	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000b0	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  000b5	eb 22		 jmp	 SHORT $LN2@OnTrayIcon
$LN11@OnTrayIcon:

; 110  :     case 0x204:
; 111  : 		OnTrayMsg(TRAY_MSG_RBUTTON_DOWN);break;

  000b7	6a 06		 push	 6
  000b9	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000bc	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  000c1	eb 16		 jmp	 SHORT $LN2@OnTrayIcon
$LN12@OnTrayIcon:

; 112  :     case 0x205:
; 113  : 		OnTrayMsg(TRAY_MSG_RBUTTON_UP);break;

  000c3	6a 07		 push	 7
  000c5	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000c8	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
  000cd	eb 0a		 jmp	 SHORT $LN2@OnTrayIcon
$LN13@OnTrayIcon:

; 114  :     case 0x206:
; 115  : 		OnTrayMsg(TRAY_MSG_RBUTTON_DBLCLK);break;

  000cf	6a 09		 push	 9
  000d1	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000d4	e8 00 00 00 00	 call	 ?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ; CShellTrayNotifyWnd::OnTrayMsg
$LN2@OnTrayIcon:

; 116  : 	}
; 117  : 
; 118  : 	return 0;

  000d9	33 c0		 xor	 eax, eax

; 119  : }

  000db	83 c4 08	 add	 esp, 8
  000de	3b ec		 cmp	 ebp, esp
  000e0	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000e5	8b e5		 mov	 esp, ebp
  000e7	5d		 pop	 ebp
  000e8	c2 10 00	 ret	 16			; 00000010H
  000eb	90		 npad	 1
$LN16@OnTrayIcon:
  000ec	00 00 00 00	 DD	 $LN7@OnTrayIcon
  000f0	00 00 00 00	 DD	 $LN8@OnTrayIcon
  000f4	00 00 00 00	 DD	 $LN9@OnTrayIcon
  000f8	00 00 00 00	 DD	 $LN10@OnTrayIcon
  000fc	00 00 00 00	 DD	 $LN11@OnTrayIcon
  00100	00 00 00 00	 DD	 $LN12@OnTrayIcon
  00104	00 00 00 00	 DD	 $LN13@OnTrayIcon
?OnTrayIconMsg@CShellTrayNotifyWnd@@AAEJIIJAAH@Z ENDP	; CShellTrayNotifyWnd::OnTrayIconMsg
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
_stNID$ = -524						; size = 508
_dwAction$ = -12					; size = 4
_bRet$ = -8						; size = 4
_this$ = -4						; size = 4
_hIcon$ = 8						; size = 4
_szTip$ = 12						; size = 4
_bBalloon$ = 16						; size = 4
_szTitle$ = 20						; size = 4
_szBalloonTip$ = 24					; size = 4
_eIcon$ = 28						; size = 4
_uTimeoutMs$ = 32					; size = 4
?SetIcon@CShellTrayNotifyWnd@@QAEHPAUHICON__@@PBDH11W4_TipTitleIconType@1@I@Z PROC ; CShellTrayNotifyWnd::SetIcon
; _this$ = ecx

; 123  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	81 ec 18 02 00
	00		 sub	 esp, 536		; 00000218H
  00009	56		 push	 esi
  0000a	57		 push	 edi
  0000b	51		 push	 ecx
  0000c	8d bd e8 fd ff
	ff		 lea	 edi, DWORD PTR [ebp-536]
  00012	b9 86 00 00 00	 mov	 ecx, 134		; 00000086H
  00017	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0001c	f3 ab		 rep stosd
  0001e	59		 pop	 ecx
  0001f	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 124  : 	BOOL bRet=FALSE;

  00022	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR _bRet$[ebp], 0

; 125  : 	DWORD dwAction=NIM_ADD;

  00029	c7 45 f4 00 00
	00 00		 mov	 DWORD PTR _dwAction$[ebp], 0

; 126  : 	if(m_hIcon) dwAction=NIM_MODIFY;

  00030	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00033	83 78 28 00	 cmp	 DWORD PTR [eax+40], 0
  00037	74 07		 je	 SHORT $LN2@SetIcon
  00039	c7 45 f4 01 00
	00 00		 mov	 DWORD PTR _dwAction$[ebp], 1
$LN2@SetIcon:

; 127  : 
; 128  : 	m_hIcon=hIcon;

  00040	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00043	8b 55 08	 mov	 edx, DWORD PTR _hIcon$[ebp]
  00046	89 51 28	 mov	 DWORD PTR [ecx+40], edx

; 129  : 	if(!hIcon) dwAction=NIM_DELETE;

  00049	83 7d 08 00	 cmp	 DWORD PTR _hIcon$[ebp], 0
  0004d	75 07		 jne	 SHORT $LN3@SetIcon
  0004f	c7 45 f4 02 00
	00 00		 mov	 DWORD PTR _dwAction$[ebp], 2
$LN3@SetIcon:

; 130  : 
; 131  : 	if(!m_hWnd) this->Create(NULL);

  00056	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00059	83 78 04 00	 cmp	 DWORD PTR [eax+4], 0
  0005d	75 26		 jne	 SHORT $LN4@SetIcon
  0005f	6a 00		 push	 0
  00061	51		 push	 ecx
  00062	8b cc		 mov	 ecx, esp
  00064	6a 00		 push	 0
  00066	e8 00 00 00 00	 call	 ??0_U_MENUorID@ATL@@QAE@I@Z ; ATL::_U_MENUorID::_U_MENUorID
  0006b	6a 00		 push	 0
  0006d	6a 00		 push	 0
  0006f	6a 00		 push	 0
  00071	51		 push	 ecx
  00072	8b cc		 mov	 ecx, esp
  00074	6a 00		 push	 0
  00076	e8 00 00 00 00	 call	 ??0_U_RECT@ATL@@QAE@PAUtagRECT@@@Z ; ATL::_U_RECT::_U_RECT
  0007b	6a 00		 push	 0
  0007d	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00080	e8 00 00 00 00	 call	 ?Create@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@PAX@Z ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create
$LN4@SetIcon:

; 132  : 
; 133  : 	NOTIFYICONDATAA stNID={0};

  00085	68 fc 01 00 00	 push	 508			; 000001fcH
  0008a	6a 00		 push	 0
  0008c	8d 8d f4 fd ff
	ff		 lea	 ecx, DWORD PTR _stNID$[ebp]
  00092	51		 push	 ecx
  00093	e8 00 00 00 00	 call	 _memset
  00098	83 c4 0c	 add	 esp, 12			; 0000000cH

; 134  : 	stNID.cbSize=sizeof(NOTIFYICONDATAA);

  0009b	c7 85 f4 fd ff
	ff fc 01 00 00	 mov	 DWORD PTR _stNID$[ebp], 508 ; 000001fcH

; 135  : 	stNID.uFlags= NIF_ICON | NIF_MESSAGE;

  000a5	c7 85 00 fe ff
	ff 03 00 00 00	 mov	 DWORD PTR _stNID$[ebp+12], 3

; 136  : 	if(szTip && lstrlenA(szTip))

  000af	83 7d 0c 00	 cmp	 DWORD PTR _szTip$[ebp], 0
  000b3	74 45		 je	 SHORT $LN5@SetIcon
  000b5	8b f4		 mov	 esi, esp
  000b7	8b 55 0c	 mov	 edx, DWORD PTR _szTip$[ebp]
  000ba	52		 push	 edx
  000bb	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrlenA@4
  000c1	3b f4		 cmp	 esi, esp
  000c3	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000c8	85 c0		 test	 eax, eax
  000ca	74 2e		 je	 SHORT $LN5@SetIcon

; 137  : 	{
; 138  : 		 stNID.uFlags|=NIF_TIP;

  000cc	8b 85 00 fe ff
	ff		 mov	 eax, DWORD PTR _stNID$[ebp+12]
  000d2	83 c8 04	 or	 eax, 4
  000d5	89 85 00 fe ff
	ff		 mov	 DWORD PTR _stNID$[ebp+12], eax

; 139  : 		 lstrcpynA(stNID.szTip, szTip, 128);

  000db	8b f4		 mov	 esi, esp
  000dd	68 80 00 00 00	 push	 128			; 00000080H
  000e2	8b 4d 0c	 mov	 ecx, DWORD PTR _szTip$[ebp]
  000e5	51		 push	 ecx
  000e6	8d 95 0c fe ff
	ff		 lea	 edx, DWORD PTR _stNID$[ebp+24]
  000ec	52		 push	 edx
  000ed	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcpynA@12
  000f3	3b f4		 cmp	 esi, esp
  000f5	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN5@SetIcon:

; 140  : 	}
; 141  : 
; 142  : 	if(bBalloon)

  000fa	83 7d 10 00	 cmp	 DWORD PTR _bBalloon$[ebp], 0
  000fe	74 53		 je	 SHORT $LN6@SetIcon

; 143  : 	{
; 144  : 		stNID.uFlags|=NIF_INFO;

  00100	8b 85 00 fe ff
	ff		 mov	 eax, DWORD PTR _stNID$[ebp+12]
  00106	83 c8 10	 or	 eax, 16			; 00000010H
  00109	89 85 00 fe ff
	ff		 mov	 DWORD PTR _stNID$[ebp+12], eax

; 145  : 		szBalloonTip && lstrcpynA(stNID.szInfo, szBalloonTip, 256);

  0010f	83 7d 18 00	 cmp	 DWORD PTR _szBalloonTip$[ebp], 0
  00113	74 1f		 je	 SHORT $LN8@SetIcon
  00115	8b f4		 mov	 esi, esp
  00117	68 00 01 00 00	 push	 256			; 00000100H
  0011c	8b 4d 18	 mov	 ecx, DWORD PTR _szBalloonTip$[ebp]
  0011f	51		 push	 ecx
  00120	8d 95 94 fe ff
	ff		 lea	 edx, DWORD PTR _stNID$[ebp+160]
  00126	52		 push	 edx
  00127	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcpynA@12
  0012d	3b f4		 cmp	 esi, esp
  0012f	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN8@SetIcon:

; 146  : 		szTitle && lstrcpynA(stNID.szInfoTitle, szTitle, 64);

  00134	83 7d 14 00	 cmp	 DWORD PTR _szTitle$[ebp], 0
  00138	74 19		 je	 SHORT $LN6@SetIcon
  0013a	8b f4		 mov	 esi, esp
  0013c	6a 40		 push	 64			; 00000040H
  0013e	8b 45 14	 mov	 eax, DWORD PTR _szTitle$[ebp]
  00141	50		 push	 eax
  00142	8d 4d 98	 lea	 ecx, DWORD PTR _stNID$[ebp+420]
  00145	51		 push	 ecx
  00146	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__lstrcpynA@12
  0014c	3b f4		 cmp	 esi, esp
  0014e	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN6@SetIcon:

; 147  : 
; 148  : 	}
; 149  : 
; 150  : 	stNID.hIcon=hIcon;

  00153	8b 55 08	 mov	 edx, DWORD PTR _hIcon$[ebp]
  00156	89 95 08 fe ff
	ff		 mov	 DWORD PTR _stNID$[ebp+20], edx

; 151  : 	stNID.hWnd=m_hWnd;

  0015c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0015f	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  00162	89 8d f8 fd ff
	ff		 mov	 DWORD PTR _stNID$[ebp+4], ecx

; 152  : 	stNID.uID=m_dwID;

  00168	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  0016b	8b 42 24	 mov	 eax, DWORD PTR [edx+36]
  0016e	89 85 fc fd ff
	ff		 mov	 DWORD PTR _stNID$[ebp+8], eax

; 153  : 	
; 154  : 	stNID.uTimeout=uTimeoutMs;

  00174	8b 4d 20	 mov	 ecx, DWORD PTR _uTimeoutMs$[ebp]
  00177	89 4d 94	 mov	 DWORD PTR _stNID$[ebp+416], ecx

; 155  : 	stNID.dwInfoFlags=eIcon;

  0017a	8b 55 1c	 mov	 edx, DWORD PTR _eIcon$[ebp]
  0017d	89 55 d8	 mov	 DWORD PTR _stNID$[ebp+484], edx

; 156  : 	stNID.uCallbackMessage=this->m_uTrayMsg;

  00180	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00183	8b 48 2c	 mov	 ecx, DWORD PTR [eax+44]
  00186	89 8d 04 fe ff
	ff		 mov	 DWORD PTR _stNID$[ebp+16], ecx

; 157  : 
; 158  : 	m_stNID = stNID;

  0018c	8b 7d fc	 mov	 edi, DWORD PTR _this$[ebp]
  0018f	83 c7 30	 add	 edi, 48			; 00000030H
  00192	b9 7f 00 00 00	 mov	 ecx, 127		; 0000007fH
  00197	8d b5 f4 fd ff
	ff		 lea	 esi, DWORD PTR _stNID$[ebp]
  0019d	f3 a5		 rep movsd

; 159  : 	bRet=Shell_NotifyIconA(dwAction,&stNID);

  0019f	8b f4		 mov	 esi, esp
  001a1	8d 95 f4 fd ff
	ff		 lea	 edx, DWORD PTR _stNID$[ebp]
  001a7	52		 push	 edx
  001a8	8b 45 f4	 mov	 eax, DWORD PTR _dwAction$[ebp]
  001ab	50		 push	 eax
  001ac	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__Shell_NotifyIconA@8
  001b2	3b f4		 cmp	 esi, esp
  001b4	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001b9	89 45 f8	 mov	 DWORD PTR _bRet$[ebp], eax

; 160  : 
; 161  : 	return bRet;

  001bc	8b 45 f8	 mov	 eax, DWORD PTR _bRet$[ebp]

; 162  : }

  001bf	52		 push	 edx
  001c0	8b cd		 mov	 ecx, ebp
  001c2	50		 push	 eax
  001c3	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN12@SetIcon
  001c9	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  001ce	58		 pop	 eax
  001cf	5a		 pop	 edx
  001d0	5f		 pop	 edi
  001d1	5e		 pop	 esi
  001d2	81 c4 18 02 00
	00		 add	 esp, 536		; 00000218H
  001d8	3b ec		 cmp	 ebp, esp
  001da	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001df	8b e5		 mov	 esp, ebp
  001e1	5d		 pop	 ebp
  001e2	c2 1c 00	 ret	 28			; 0000001cH
  001e5	0f 1f 00	 npad	 3
$LN12@SetIcon:
  001e8	01 00 00 00	 DD	 1
  001ec	00 00 00 00	 DD	 $LN11@SetIcon
$LN11@SetIcon:
  001f0	f4 fd ff ff	 DD	 -524			; fffffdf4H
  001f4	fc 01 00 00	 DD	 508			; 000001fcH
  001f8	00 00 00 00	 DD	 $LN10@SetIcon
$LN10@SetIcon:
  001fc	73		 DB	 115			; 00000073H
  001fd	74		 DB	 116			; 00000074H
  001fe	4e		 DB	 78			; 0000004eH
  001ff	49		 DB	 73			; 00000049H
  00200	44		 DB	 68			; 00000044H
  00201	00		 DB	 0
?SetIcon@CShellTrayNotifyWnd@@QAEHPAUHICON__@@PBDH11W4_TipTitleIconType@1@I@Z ENDP ; CShellTrayNotifyWnd::SetIcon
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_fn$ = 8						; size = 4
?SetTrayMsgListener@CShellTrayNotifyWnd@@QAEXABV?$function@$$A6A_NH@Z@std@@@Z PROC ; CShellTrayNotifyWnd::SetTrayMsgListener
; _this$ = ecx

; 164  : void CShellTrayNotifyWnd::SetTrayMsgListener(const std::function<bool(int emsg) > & fn) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 165  : 	m_fnTrayMsgListener = fn;

  0000e	8b 45 08	 mov	 eax, DWORD PTR _fn$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	81 c1 30 02 00
	00		 add	 ecx, 560		; 00000230H
  0001b	e8 00 00 00 00	 call	 ??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z ; std::function<bool __cdecl(int)>::operator=

; 166  : }

  00020	83 c4 04	 add	 esp, 4
  00023	3b ec		 cmp	 ebp, esp
  00025	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002a	8b e5		 mov	 esp, ebp
  0002c	5d		 pop	 ebp
  0002d	c2 04 00	 ret	 4
?SetTrayMsgListener@CShellTrayNotifyWnd@@QAEXABV?$function@$$A6A_NH@Z@std@@@Z ENDP ; CShellTrayNotifyWnd::SetTrayMsgListener
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
_TEXT	SEGMENT
tv79 = -1040						; size = 4
_stNID$1 = -1032					; size = 508
_stNID$2 = -516						; size = 508
_this$ = -4						; size = 4
_emsg$ = 8						; size = 4
?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z PROC ; CShellTrayNotifyWnd::OnTrayMsg
; _this$ = ecx

; 169  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	81 ec 10 04 00
	00		 sub	 esp, 1040		; 00000410H
  00009	56		 push	 esi
  0000a	57		 push	 edi
  0000b	51		 push	 ecx
  0000c	8d bd f0 fb ff
	ff		 lea	 edi, DWORD PTR [ebp-1040]
  00012	b9 04 01 00 00	 mov	 ecx, 260		; 00000104H
  00017	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0001c	f3 ab		 rep stosd
  0001e	59		 pop	 ecx
  0001f	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 170  : 	//
; 171  : 	if (m_fnTrayMsgListener) {

  00022	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00025	81 c1 30 02 00
	00		 add	 ecx, 560		; 00000230H
  0002b	e8 00 00 00 00	 call	 ??B?$function@$$A6A_NH@Z@std@@QBE_NXZ ; std::function<bool __cdecl(int)>::operator bool
  00030	0f b6 c0	 movzx	 eax, al
  00033	85 c0		 test	 eax, eax
  00035	74 1b		 je	 SHORT $LN4@OnTrayMsg

; 172  : 		if(m_fnTrayMsgListener((int)emsg)) return;

  00037	8b 4d 08	 mov	 ecx, DWORD PTR _emsg$[ebp]
  0003a	51		 push	 ecx
  0003b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0003e	81 c1 30 02 00
	00		 add	 ecx, 560		; 00000230H
  00044	e8 00 00 00 00	 call	 ??R?$_Func_class@_NH@std@@QBE_NH@Z ; std::_Func_class<bool,int>::operator()
  00049	0f b6 d0	 movzx	 edx, al
  0004c	85 d2		 test	 edx, edx
  0004e	74 02		 je	 SHORT $LN4@OnTrayMsg
  00050	eb 76		 jmp	 SHORT $LN1@OnTrayMsg
$LN4@OnTrayMsg:

; 173  : 	}
; 174  : 
; 175  : 	switch (emsg) {

  00052	8b 45 08	 mov	 eax, DWORD PTR _emsg$[ebp]
  00055	89 85 f0 fb ff
	ff		 mov	 DWORD PTR tv79[ebp], eax
  0005b	83 bd f0 fb ff
	ff 05		 cmp	 DWORD PTR tv79[ebp], 5
  00062	74 0b		 je	 SHORT $LN6@OnTrayMsg
  00064	83 bd f0 fb ff
	ff 07		 cmp	 DWORD PTR tv79[ebp], 7
  0006b	74 2f		 je	 SHORT $LN7@OnTrayMsg
  0006d	eb 59		 jmp	 SHORT $LN1@OnTrayMsg
$LN6@OnTrayMsg:

; 176  : 	case TRAY_MSG_LBUTTON_UP:
; 177  : 		{
; 178  : 			NOTIFYICONDATAA stNID = m_stNID;

  0006f	8b 75 fc	 mov	 esi, DWORD PTR _this$[ebp]
  00072	83 c6 30	 add	 esi, 48			; 00000030H
  00075	b9 7f 00 00 00	 mov	 ecx, 127		; 0000007fH
  0007a	8d bd fc fd ff
	ff		 lea	 edi, DWORD PTR _stNID$2[ebp]
  00080	f3 a5		 rep movsd

; 179  : 			Shell_NotifyIconA(NIM_MODIFY, &stNID);

  00082	8b f4		 mov	 esi, esp
  00084	8d 8d fc fd ff
	ff		 lea	 ecx, DWORD PTR _stNID$2[ebp]
  0008a	51		 push	 ecx
  0008b	6a 01		 push	 1
  0008d	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__Shell_NotifyIconA@8
  00093	3b f4		 cmp	 esi, esp
  00095	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 180  : 		}
; 181  : 		break;

  0009a	eb 2c		 jmp	 SHORT $LN1@OnTrayMsg
$LN7@OnTrayMsg:

; 182  : 	case TRAY_MSG_RBUTTON_UP:
; 183  : 		{
; 184  : 			NOTIFYICONDATAA stNID = m_stNID;

  0009c	8b 75 fc	 mov	 esi, DWORD PTR _this$[ebp]
  0009f	83 c6 30	 add	 esi, 48			; 00000030H
  000a2	b9 7f 00 00 00	 mov	 ecx, 127		; 0000007fH
  000a7	8d bd f8 fb ff
	ff		 lea	 edi, DWORD PTR _stNID$1[ebp]
  000ad	f3 a5		 rep movsd

; 185  : 			this->SetIcon(NULL);

  000af	68 10 27 00 00	 push	 10000			; 00002710H
  000b4	6a 00		 push	 0
  000b6	6a 00		 push	 0
  000b8	6a 00		 push	 0
  000ba	6a 00		 push	 0
  000bc	6a 00		 push	 0
  000be	6a 00		 push	 0
  000c0	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000c3	e8 00 00 00 00	 call	 ?SetIcon@CShellTrayNotifyWnd@@QAEHPAUHICON__@@PBDH11W4_TipTitleIconType@1@I@Z ; CShellTrayNotifyWnd::SetIcon
$LN1@OnTrayMsg:

; 186  : 		}
; 187  : 		break;
; 188  : 	}
; 189  : }

  000c8	52		 push	 edx
  000c9	8b cd		 mov	 ecx, ebp
  000cb	50		 push	 eax
  000cc	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN12@OnTrayMsg
  000d2	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000d7	58		 pop	 eax
  000d8	5a		 pop	 edx
  000d9	5f		 pop	 edi
  000da	5e		 pop	 esi
  000db	81 c4 10 04 00
	00		 add	 esp, 1040		; 00000410H
  000e1	3b ec		 cmp	 ebp, esp
  000e3	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000e8	8b e5		 mov	 esp, ebp
  000ea	5d		 pop	 ebp
  000eb	c2 04 00	 ret	 4
  000ee	66 90		 npad	 2
$LN12@OnTrayMsg:
  000f0	02 00 00 00	 DD	 2
  000f4	00 00 00 00	 DD	 $LN11@OnTrayMsg
$LN11@OnTrayMsg:
  000f8	fc fd ff ff	 DD	 -516			; fffffdfcH
  000fc	fc 01 00 00	 DD	 508			; 000001fcH
  00100	00 00 00 00	 DD	 $LN9@OnTrayMsg
  00104	f8 fb ff ff	 DD	 -1032			; fffffbf8H
  00108	fc 01 00 00	 DD	 508			; 000001fcH
  0010c	00 00 00 00	 DD	 $LN10@OnTrayMsg
$LN10@OnTrayMsg:
  00110	73		 DB	 115			; 00000073H
  00111	74		 DB	 116			; 00000074H
  00112	4e		 DB	 78			; 0000004eH
  00113	49		 DB	 73			; 00000049H
  00114	44		 DB	 68			; 00000044H
  00115	00		 DB	 0
$LN9@OnTrayMsg:
  00116	73		 DB	 115			; 00000073H
  00117	74		 DB	 116			; 00000074H
  00118	4e		 DB	 78			; 0000004eH
  00119	49		 DB	 73			; 00000049H
  0011a	44		 DB	 68			; 00000044H
  0011b	00		 DB	 0
?OnTrayMsg@CShellTrayNotifyWnd@@QAEXW4ETRAY_MSG@@@Z ENDP ; CShellTrayNotifyWnd::OnTrayMsg
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
;	COMDAT ??__Em_TrayBalloonPop@JsApi@@YAXXZ
text$di	SEGMENT
??__Em_TrayBalloonPop@JsApi@@YAXXZ PROC			; JsApi::`dynamic initializer for 'm_TrayBalloonPop'', COMDAT

; 192  : 	std::shared_ptr<CShellTrayNotifyWnd> m_TrayBalloonPop;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
  00008	e8 00 00 00 00	 call	 ??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
  0000d	68 00 00 00 00	 push	 OFFSET ??__Fm_TrayBalloonPop@JsApi@@YAXXZ ; JsApi::`dynamic atexit destructor for 'm_TrayBalloonPop''
  00012	e8 00 00 00 00	 call	 _atexit
  00017	83 c4 04	 add	 esp, 4
  0001a	3b ec		 cmp	 ebp, esp
  0001c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00021	5d		 pop	 ebp
  00022	c3		 ret	 0
??__Em_TrayBalloonPop@JsApi@@YAXXZ ENDP			; JsApi::`dynamic initializer for 'm_TrayBalloonPop''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__Fm_TrayBalloonPop@JsApi@@YAXXZ
text$yd	SEGMENT
??__Fm_TrayBalloonPop@JsApi@@YAXXZ PROC			; JsApi::`dynamic atexit destructor for 'm_TrayBalloonPop'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?m_TrayBalloonPop@JsApi@@3V?$shared_ptr@VCShellTrayNotifyWnd@@@std@@A ; JsApi::m_TrayBalloonPop
  00008	e8 00 00 00 00	 call	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__Fm_TrayBalloonPop@JsApi@@YAXXZ ENDP			; JsApi::`dynamic atexit destructor for 'm_TrayBalloonPop''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??B?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBE_NXZ
_TEXT	SEGMENT
tv71 = -8						; size = 4
_this$ = -4						; size = 4
??B?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBE_NXZ PROC ; std::shared_ptr<CShellTrayNotifyWnd>::operator bool, COMDAT
; _this$ = ecx

; 1510 : 		{	// test if shared_ptr object owns a resource

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1511 : 		return (get() != nullptr);

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?get@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IBEPAVCShellTrayNotifyWnd@@XZ ; std::_Ptr_base<CShellTrayNotifyWnd>::get
  0001f	85 c0		 test	 eax, eax
  00021	74 09		 je	 SHORT $LN3@operator
  00023	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv71[ebp], 1
  0002a	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  0002c	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv71[ebp], 0
$LN4@operator:
  00033	8a 45 f8	 mov	 al, BYTE PTR tv71[ebp]

; 1512 : 		}

  00036	83 c4 08	 add	 esp, 8
  00039	3b ec		 cmp	 ebp, esp
  0003b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00040	8b e5		 mov	 esp, ebp
  00042	5d		 pop	 ebp
  00043	c3		 ret	 0
??B?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBE_NXZ ENDP ; std::shared_ptr<CShellTrayNotifyWnd>::operator bool
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z
_TEXT	SEGMENT
tv74 = -32						; size = 4
tv75 = -28						; size = 4
$T2 = -24						; size = 8
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
__Right$ = 8						; size = 4
??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z PROC ; std::shared_ptr<CShellTrayNotifyWnd>::operator=, COMDAT
; _this$ = ecx

; 1420 : 		{	// take resource from _Right

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 14	 sub	 esp, 20			; 00000014H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00023	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00026	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00029	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0002c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0002f	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1421 : 		shared_ptr(_STD move(_Right)).swap(*this);

  00032	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  00035	50		 push	 eax
  00036	e8 00 00 00 00	 call	 ??$move@AAV?$shared_ptr@VCShellTrayNotifyWnd@@@std@@@std@@YA$$QAV?$shared_ptr@VCShellTrayNotifyWnd@@@0@AAV10@@Z ; std::move<std::shared_ptr<CShellTrayNotifyWnd> &>
  0003b	83 c4 04	 add	 esp, 4
  0003e	50		 push	 eax
  0003f	8d 4d e8	 lea	 ecx, DWORD PTR $T2[ebp]
  00042	e8 00 00 00 00	 call	 ??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
  00047	89 45 e4	 mov	 DWORD PTR tv75[ebp], eax
  0004a	8b 4d e4	 mov	 ecx, DWORD PTR tv75[ebp]
  0004d	89 4d e0	 mov	 DWORD PTR tv74[ebp], ecx
  00050	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00057	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0005a	52		 push	 edx
  0005b	8b 4d e0	 mov	 ecx, DWORD PTR tv74[ebp]
  0005e	e8 00 00 00 00	 call	 ?swap@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEXAAV12@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::swap
  00063	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0006a	8d 4d e8	 lea	 ecx, DWORD PTR $T2[ebp]
  0006d	e8 00 00 00 00	 call	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>

; 1422 : 		return (*this);

  00072	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]

; 1423 : 		}

  00075	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00078	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0007f	83 c4 20	 add	 esp, 32			; 00000020H
  00082	3b ec		 cmp	 ebp, esp
  00084	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00089	8b e5		 mov	 esp, ebp
  0008b	5d		 pop	 ebp
  0008c	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR $T2[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
__ehhandler$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??4?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEAAV01@$$QAV01@@Z ENDP ; std::shared_ptr<CShellTrayNotifyWnd>::operator=
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ PROC	; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>, COMDAT
; _this$ = ecx

; 1402 : 		{	// release resource

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1403 : 		this->_Decref();

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?_Decref@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXXZ ; std::_Ptr_base<CShellTrayNotifyWnd>::_Decref

; 1404 : 		}

  0002b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0002e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00035	83 c4 10	 add	 esp, 16			; 00000010H
  00038	3b ec		 cmp	 ebp, esp
  0003a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003f	8b e5		 mov	 esp, ebp
  00041	5d		 pop	 ebp
  00042	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ENDP	; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ PROC	; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>, COMDAT
; _this$ = ecx

; 1271 : 	constexpr shared_ptr() noexcept

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1272 : 		{	// construct empty shared_ptr

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??0?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAE@XZ ; std::_Ptr_base<CShellTrayNotifyWnd>::_Ptr_base<CShellTrayNotifyWnd>

; 1273 : 		}

  00016	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ENDP	; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?OnFinalMessage@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAEXPAUHWND__@@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 4
?OnFinalMessage@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAEXPAUHWND__@@@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::OnFinalMessage, COMDAT
; _this$ = ecx

; 3503 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 3504 : 		// override to do something, if needed
; 3505 : 	}

  0000e	8b e5		 mov	 esp, ebp
  00010	5d		 pop	 ebp
  00011	c2 04 00	 ret	 4
?OnFinalMessage@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@UAEXPAUHWND__@@@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::OnFinalMessage
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >, COMDAT
; _this$ = ecx

; 3427 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0?$CWindowImplRoot@VCWindow@ATL@@@ATL@@QAE@XZ ; ATL::CWindowImplRoot<ATL::CWindow>::CWindowImplRoot<ATL::CWindow>
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 3426 : 	CWindowImplBaseT() : m_pfnSuperWindowProc(::DefWindowProc)

  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	8b 0d 00 00 00
	00		 mov	 ecx, DWORD PTR __imp__DefWindowProcW@16
  0003b	89 48 20	 mov	 DWORD PTR [eax+32], ecx

; 3428 : 	}

  0003e	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00045	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00048	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0004b	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00052	83 c4 10	 add	 esp, 16			; 00000010H
  00055	3b ec		 cmp	 ebp, esp
  00057	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005c	8b e5		 mov	 esp, ebp
  0005e	5d		 pop	 ebp
  0005f	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$CWindowImplRoot@VCWindow@ATL@@@ATL@@UAE@XZ ; ATL::CWindowImplRoot<ATL::CWindow>::~CWindowImplRoot<ATL::CWindow>
__ehhandler$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAE@XZ ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?Create@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@PAX@Z
_TEXT	SEGMENT
_atom$ = -8						; size = 2
_this$ = -4						; size = 4
_hWndParent$ = 8					; size = 4
_rect$ = 12						; size = 4
_szWindowName$ = 16					; size = 4
_dwStyle$ = 20						; size = 4
_dwExStyle$ = 24					; size = 4
_MenuOrID$ = 28						; size = 4
_lpCreateParam$ = 32					; size = 4
?Create@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@PAX@Z PROC ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create, COMDAT
; _this$ = ecx

; 3695 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 3696 : 		if (T::GetWndClassInfo().m_lpszOrigName == NULL)

  00018	e8 00 00 00 00	 call	 ?GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@2@XZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo
  0001d	83 78 30 00	 cmp	 DWORD PTR [eax+48], 0
  00021	75 0f		 jne	 SHORT $LN2@Create

; 3697 : 			T::GetWndClassInfo().m_lpszOrigName = this->GetWndClassName();

  00023	e8 00 00 00 00	 call	 ?GetWndClassName@CWindow@ATL@@SAPB_WXZ ; ATL::CWindow::GetWndClassName
  00028	8b f0		 mov	 esi, eax
  0002a	e8 00 00 00 00	 call	 ?GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@2@XZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo
  0002f	89 70 30	 mov	 DWORD PTR [eax+48], esi
$LN2@Create:

; 3698 : 		ATOM atom = T::GetWndClassInfo().Register(&this->m_pfnSuperWindowProc);

  00032	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00035	83 c0 20	 add	 eax, 32			; 00000020H
  00038	50		 push	 eax
  00039	e8 00 00 00 00	 call	 ?GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@2@XZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo
  0003e	8b c8		 mov	 ecx, eax
  00040	e8 00 00 00 00	 call	 ?Register@_ATL_WNDCLASSINFOW@ATL@@QAEGPAP6GJPAUHWND__@@IIJ@Z@Z ; ATL::_ATL_WNDCLASSINFOW::Register
  00045	66 89 45 f8	 mov	 WORD PTR _atom$[ebp], ax

; 3699 : 
; 3700 : 		dwStyle = T::GetWndStyle(dwStyle);

  00049	8b 4d 14	 mov	 ecx, DWORD PTR _dwStyle$[ebp]
  0004c	51		 push	 ecx
  0004d	e8 00 00 00 00	 call	 ?GetWndStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndStyle
  00052	83 c4 04	 add	 esp, 4
  00055	89 45 14	 mov	 DWORD PTR _dwStyle$[ebp], eax

; 3701 : 		dwExStyle = T::GetWndExStyle(dwExStyle);

  00058	8b 55 18	 mov	 edx, DWORD PTR _dwExStyle$[ebp]
  0005b	52		 push	 edx
  0005c	e8 00 00 00 00	 call	 ?GetWndExStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndExStyle
  00061	83 c4 04	 add	 esp, 4
  00064	89 45 18	 mov	 DWORD PTR _dwExStyle$[ebp], eax

; 3702 : 
; 3703 : 		// set caption
; 3704 : 		if (szWindowName == NULL)

  00067	83 7d 10 00	 cmp	 DWORD PTR _szWindowName$[ebp], 0
  0006b	75 08		 jne	 SHORT $LN3@Create

; 3705 : 			szWindowName = T::GetWndCaption();

  0006d	e8 00 00 00 00	 call	 ?GetWndCaption@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAPB_WXZ ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndCaption
  00072	89 45 10	 mov	 DWORD PTR _szWindowName$[ebp], eax
$LN3@Create:

; 3706 : 
; 3707 : 		return CWindowImplBaseT< TBase, TWinTraits >::Create(hWndParent, rect, szWindowName,

  00075	8b 45 20	 mov	 eax, DWORD PTR _lpCreateParam$[ebp]
  00078	50		 push	 eax
  00079	0f b7 4d f8	 movzx	 ecx, WORD PTR _atom$[ebp]
  0007d	51		 push	 ecx
  0007e	8b 55 1c	 mov	 edx, DWORD PTR _MenuOrID$[ebp]
  00081	52		 push	 edx
  00082	8b 45 18	 mov	 eax, DWORD PTR _dwExStyle$[ebp]
  00085	50		 push	 eax
  00086	8b 4d 14	 mov	 ecx, DWORD PTR _dwStyle$[ebp]
  00089	51		 push	 ecx
  0008a	8b 55 10	 mov	 edx, DWORD PTR _szWindowName$[ebp]
  0008d	52		 push	 edx
  0008e	8b 45 0c	 mov	 eax, DWORD PTR _rect$[ebp]
  00091	50		 push	 eax
  00092	8b 4d 08	 mov	 ecx, DWORD PTR _hWndParent$[ebp]
  00095	51		 push	 ecx
  00096	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00099	e8 00 00 00 00	 call	 ?Create@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@GPAX@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create

; 3708 : 			dwStyle, dwExStyle, MenuOrID, atom, lpCreateParam);
; 3709 : 	}

  0009e	5e		 pop	 esi
  0009f	83 c4 08	 add	 esp, 8
  000a2	3b ec		 cmp	 ebp, esp
  000a4	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000a9	8b e5		 mov	 esp, ebp
  000ab	5d		 pop	 ebp
  000ac	c2 1c 00	 ret	 28			; 0000001cH
?Create@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@PAX@Z ENDP ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ??B?$function@$$A6A_NH@Z@std@@QBE_NXZ
_TEXT	SEGMENT
tv72 = -8						; size = 4
_this$ = -4						; size = 4
??B?$function@$$A6A_NH@Z@std@@QBE_NXZ PROC		; std::function<bool __cdecl(int)>::operator bool, COMDAT
; _this$ = ecx

; 1597 : 		{	// test if wrapper holds function object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1598 : 		return (!this->_Empty());

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	e8 00 00 00 00	 call	 ?_Empty@?$_Func_class@_NH@std@@IBE_NXZ ; std::_Func_class<bool,int>::_Empty
  0001f	0f b6 c0	 movzx	 eax, al
  00022	85 c0		 test	 eax, eax
  00024	75 09		 jne	 SHORT $LN3@operator
  00026	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv72[ebp], 1
  0002d	eb 07		 jmp	 SHORT $LN4@operator
$LN3@operator:
  0002f	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv72[ebp], 0
$LN4@operator:
  00036	8a 45 f8	 mov	 al, BYTE PTR tv72[ebp]

; 1599 : 		}

  00039	83 c4 08	 add	 esp, 8
  0003c	3b ec		 cmp	 ebp, esp
  0003e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00043	8b e5		 mov	 esp, ebp
  00045	5d		 pop	 ebp
  00046	c3		 ret	 0
??B?$function@$$A6A_NH@Z@std@@QBE_NXZ ENDP		; std::function<bool __cdecl(int)>::operator bool
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z
_TEXT	SEGMENT
tv72 = -64						; size = 4
tv73 = -60						; size = 4
$T2 = -56						; size = 40
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
__Right$ = 8						; size = 4
??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z PROC	; std::function<bool __cdecl(int)>::operator=, COMDAT
; _this$ = ecx

; 1532 : 		{	// assign _Right

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 34	 sub	 esp, 52			; 00000034H
  0001b	57		 push	 edi
  0001c	51		 push	 ecx
  0001d	8d 7d c0	 lea	 edi, DWORD PTR [ebp-64]
  00020	b9 0d 00 00 00	 mov	 ecx, 13			; 0000000dH
  00025	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002a	f3 ab		 rep stosd
  0002c	59		 pop	 ecx
  0002d	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1533 : 		function(_Right).swap(*this);

  00030	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  00033	50		 push	 eax
  00034	8d 4d c8	 lea	 ecx, DWORD PTR $T2[ebp]
  00037	e8 00 00 00 00	 call	 ??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z ; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>
  0003c	89 45 c4	 mov	 DWORD PTR tv73[ebp], eax
  0003f	8b 4d c4	 mov	 ecx, DWORD PTR tv73[ebp]
  00042	89 4d c0	 mov	 DWORD PTR tv72[ebp], ecx
  00045	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0004c	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0004f	52		 push	 edx
  00050	8b 4d c0	 mov	 ecx, DWORD PTR tv72[ebp]
  00053	e8 00 00 00 00	 call	 ?swap@?$function@$$A6A_NH@Z@std@@QAEXAAV12@@Z ; std::function<bool __cdecl(int)>::swap
  00058	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0005f	8d 4d c8	 lea	 ecx, DWORD PTR $T2[ebp]
  00062	e8 00 00 00 00	 call	 ??1?$function@$$A6A_NH@Z@std@@QAE@XZ

; 1534 : 		return (*this);

  00067	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]

; 1535 : 		}

  0006a	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0006d	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00074	5f		 pop	 edi
  00075	83 c4 40	 add	 esp, 64			; 00000040H
  00078	3b ec		 cmp	 ebp, esp
  0007a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0007f	8b e5		 mov	 esp, ebp
  00081	5d		 pop	 ebp
  00082	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z$0:
  00000	8d 4d c8	 lea	 ecx, DWORD PTR $T2[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$function@$$A6A_NH@Z@std@@QAE@XZ
__ehhandler$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??4?$function@$$A6A_NH@Z@std@@QAEAAV01@ABV01@@Z ENDP	; std::function<bool __cdecl(int)>::operator=
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ??0?$function@$$A6A_NH@Z@std@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0?$function@$$A6A_NH@Z@std@@QAE@XZ PROC		; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>, COMDAT
; _this$ = ecx

; 1485 : 	function() noexcept

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$function@$$A6A_NH@Z@std@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1486 : 		{	// construct empty function wrapper

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0?$_Func_class@_NH@std@@QAE@XZ ; std::_Func_class<bool,int>::_Func_class<bool,int>
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 1487 : 		}

  00032	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00039	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00046	83 c4 10	 add	 esp, 16			; 00000010H
  00049	3b ec		 cmp	 ebp, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00050	8b e5		 mov	 esp, ebp
  00052	5d		 pop	 ebp
  00053	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0?$function@$$A6A_NH@Z@std@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$_Func_class@_NH@std@@QAE@XZ ; std::_Func_class<bool,int>::~_Func_class<bool,int>
__ehhandler$??0?$function@$$A6A_NH@Z@std@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$function@$$A6A_NH@Z@std@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$function@$$A6A_NH@Z@std@@QAE@XZ ENDP		; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp
;	COMDAT ??0?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAE@XZ PROC	; std::_Ptr_base<CShellTrayNotifyWnd>::_Ptr_base<CShellTrayNotifyWnd>, COMDAT
; _this$ = ecx

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory

; 1157 : 	element_type * _Ptr{nullptr};

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 1158 : 	_Ref_count_base * _Rep{nullptr};

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	c7 41 04 00 00
	00 00		 mov	 DWORD PTR [ecx+4], 0
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_shelltraynotify.cpp

  00021	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00024	8b e5		 mov	 esp, ebp
  00026	5d		 pop	 ebp
  00027	c3		 ret	 0
??0?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAE@XZ ENDP	; std::_Ptr_base<CShellTrayNotifyWnd>::_Ptr_base<CShellTrayNotifyWnd>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?_Decref@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Decref@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXXZ PROC ; std::_Ptr_base<CShellTrayNotifyWnd>::_Decref, COMDAT
; _this$ = ecx

; 1117 : 		{	// decrement reference count

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1118 : 		if (_Rep)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	83 78 04 00	 cmp	 DWORD PTR [eax+4], 0
  00015	74 0b		 je	 SHORT $LN1@Decref

; 1119 : 			{
; 1120 : 			_Rep->_Decref();

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	8b 49 04	 mov	 ecx, DWORD PTR [ecx+4]
  0001d	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QAEXXZ ; std::_Ref_count_base::_Decref
$LN1@Decref:

; 1121 : 			}
; 1122 : 		}

  00022	83 c4 04	 add	 esp, 4
  00025	3b ec		 cmp	 ebp, esp
  00027	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002c	8b e5		 mov	 esp, ebp
  0002e	5d		 pop	 ebp
  0002f	c3		 ret	 0
?_Decref@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXXZ ENDP ; std::_Ptr_base<CShellTrayNotifyWnd>::_Decref
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?get@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IBEPAVCShellTrayNotifyWnd@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?get@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IBEPAVCShellTrayNotifyWnd@@XZ PROC ; std::_Ptr_base<CShellTrayNotifyWnd>::get, COMDAT
; _this$ = ecx

; 1058 : 		{	// return pointer to resource

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1059 : 		return (_Ptr);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 00		 mov	 eax, DWORD PTR [eax]

; 1060 : 		}

  00013	8b e5		 mov	 esp, ebp
  00015	5d		 pop	 ebp
  00016	c3		 ret	 0
?get@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IBEPAVCShellTrayNotifyWnd@@XZ ENDP ; std::_Ptr_base<CShellTrayNotifyWnd>::get
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?swap@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEXAAV12@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Other$ = 8						; size = 4
?swap@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEXAAV12@@Z PROC ; std::shared_ptr<CShellTrayNotifyWnd>::swap, COMDAT
; _this$ = ecx

; 1450 : 		{	// swap pointers

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1451 : 		this->_Swap(_Other);

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Other$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	e8 00 00 00 00	 call	 ?_Swap@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXAAV12@@Z ; std::_Ptr_base<CShellTrayNotifyWnd>::_Swap

; 1452 : 		}

  0001a	83 c4 04	 add	 esp, 4
  0001d	3b ec		 cmp	 ebp, esp
  0001f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00024	8b e5		 mov	 esp, ebp
  00026	5d		 pop	 ebp
  00027	c2 04 00	 ret	 4
?swap@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAEXAAV12@@Z ENDP ; std::shared_ptr<CShellTrayNotifyWnd>::swap
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
__Right$ = 8						; size = 4
??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z PROC ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>, COMDAT
; _this$ = ecx

; 1345 : 	shared_ptr(shared_ptr&& _Right) noexcept

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1346 : 		{	// construct shared_ptr object that takes resource from _Right

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAE@XZ ; std::_Ptr_base<CShellTrayNotifyWnd>::_Ptr_base<CShellTrayNotifyWnd>

; 1347 : 		this->_Move_construct_from(_STD move(_Right));

  0002b	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  0002e	50		 push	 eax
  0002f	e8 00 00 00 00	 call	 ??$move@AAV?$shared_ptr@VCShellTrayNotifyWnd@@@std@@@std@@YA$$QAV?$shared_ptr@VCShellTrayNotifyWnd@@@0@AAV10@@Z ; std::move<std::shared_ptr<CShellTrayNotifyWnd> &>
  00034	83 c4 04	 add	 esp, 4
  00037	50		 push	 eax
  00038	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003b	e8 00 00 00 00	 call	 ??$_Move_construct_from@VCShellTrayNotifyWnd@@@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEX$$QAV01@@Z ; std::_Ptr_base<CShellTrayNotifyWnd>::_Move_construct_from<CShellTrayNotifyWnd>

; 1348 : 		}

  00040	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00043	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00046	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0004d	83 c4 10	 add	 esp, 16			; 00000010H
  00050	3b ec		 cmp	 ebp, esp
  00052	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00057	8b e5		 mov	 esp, ebp
  00059	5d		 pop	 ebp
  0005a	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z ENDP ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?GetWndExStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z
_TEXT	SEGMENT
_dwExStyle$ = 8						; size = 4
?GetWndExStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndExStyle, COMDAT

; 3435 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 3436 : 		return TWinTraits::GetWndExStyle(dwExStyle);

  00003	8b 45 08	 mov	 eax, DWORD PTR _dwExStyle$[ebp]
  00006	50		 push	 eax
  00007	e8 00 00 00 00	 call	 ?GetWndExStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z ; ATL::CWinTraits<13107200,128>::GetWndExStyle
  0000c	83 c4 04	 add	 esp, 4

; 3437 : 	}

  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?GetWndExStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndExStyle
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?GetWndStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z
_TEXT	SEGMENT
_dwStyle$ = 8						; size = 4
?GetWndStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndStyle, COMDAT

; 3431 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 3432 : 		return TWinTraits::GetWndStyle(dwStyle);

  00003	8b 45 08	 mov	 eax, DWORD PTR _dwStyle$[ebp]
  00006	50		 push	 eax
  00007	e8 00 00 00 00	 call	 ?GetWndStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z ; ATL::CWinTraits<13107200,128>::GetWndStyle
  0000c	83 c4 04	 add	 esp, 4

; 3433 : 	}

  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?GetWndStyle@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SAKK@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndStyle
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?GetWndCaption@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAPB_WXZ
_TEXT	SEGMENT
?GetWndCaption@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAPB_WXZ PROC ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndCaption, COMDAT

; 3683 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 3684 : 		return NULL;

  00003	33 c0		 xor	 eax, eax

; 3685 : 	}

  00005	5d		 pop	 ebp
  00006	c3		 ret	 0
?GetWndCaption@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAPB_WXZ ENDP ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndCaption
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@2@XZ
_TEXT	SEGMENT
?GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@2@XZ PROC ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo, COMDAT

; 3680 : 	DECLARE_WND_CLASS2(NULL, CWindowImpl)

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b8 00 00 00 00	 mov	 eax, OFFSET ?wc@?1??GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@3@XZ@4U43@A ; `ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo'::`2'::wc
  00008	5d		 pop	 ebp
  00009	c3		 ret	 0
?GetWndClassInfo@?$CWindowImpl@VCShellTrayNotifyWnd@@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@3@@ATL@@SAAAU_ATL_WNDCLASSINFOW@2@XZ ENDP ; ATL::CWindowImpl<CShellTrayNotifyWnd,ATL::CWindow,ATL::CWinTraits<13107200,128> >::GetWndClassInfo
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
__Right$ = 8						; size = 4
??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z PROC		; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>, COMDAT
; _this$ = ecx

; 1493 : 	function(const function& _Right)

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1494 : 		{	// construct holding copy of _Right

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0?$_Func_class@_NH@std@@QAE@XZ ; std::_Func_class<bool,int>::_Func_class<bool,int>
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 1495 : 		this->_Reset_copy(_Right);

  00032	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  00035	50		 push	 eax
  00036	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00039	e8 00 00 00 00	 call	 ?_Reset_copy@?$_Func_class@_NH@std@@IAEXABV12@@Z ; std::_Func_class<bool,int>::_Reset_copy

; 1496 : 		}

  0003e	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00045	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00048	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0004b	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00052	83 c4 10	 add	 esp, 16			; 00000010H
  00055	3b ec		 cmp	 ebp, esp
  00057	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005c	8b e5		 mov	 esp, ebp
  0005e	5d		 pop	 ebp
  0005f	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$_Func_class@_NH@std@@QAE@XZ ; std::_Func_class<bool,int>::~_Func_class<bool,int>
__ehhandler$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$function@$$A6A_NH@Z@std@@QAE@ABV01@@Z ENDP		; std::function<bool __cdecl(int)>::function<bool __cdecl(int)>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?_Swap@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXAAV12@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
?_Swap@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXAAV12@@Z PROC ; std::_Ptr_base<CShellTrayNotifyWnd>::_Swap, COMDAT
; _this$ = ecx

; 1125 : 		{	// swap pointers

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1126 : 		_STD swap(_Ptr, _Right._Ptr);

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	51		 push	 ecx
  00016	e8 00 00 00 00	 call	 ??$swap@PAVCShellTrayNotifyWnd@@X@std@@YAXAAPAVCShellTrayNotifyWnd@@0@Z ; std::swap<CShellTrayNotifyWnd *,void>
  0001b	83 c4 08	 add	 esp, 8

; 1127 : 		_STD swap(_Rep, _Right._Rep);

  0001e	8b 55 08	 mov	 edx, DWORD PTR __Right$[ebp]
  00021	83 c2 04	 add	 edx, 4
  00024	52		 push	 edx
  00025	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00028	83 c0 04	 add	 eax, 4
  0002b	50		 push	 eax
  0002c	e8 00 00 00 00	 call	 ??$swap@PAV_Ref_count_base@std@@X@std@@YAXAAPAV_Ref_count_base@0@0@Z ; std::swap<std::_Ref_count_base *,void>
  00031	83 c4 08	 add	 esp, 8

; 1128 : 		}

  00034	83 c4 04	 add	 esp, 4
  00037	3b ec		 cmp	 ebp, esp
  00039	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003e	8b e5		 mov	 esp, ebp
  00040	5d		 pop	 ebp
  00041	c2 04 00	 ret	 4
?_Swap@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXAAV12@@Z ENDP ; std::_Ptr_base<CShellTrayNotifyWnd>::_Swap
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?GetWndExStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z
_TEXT	SEGMENT
tv65 = -4						; size = 4
_dwExStyle$ = 8						; size = 4
?GetWndExStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z PROC ; ATL::CWinTraits<13107200,128>::GetWndExStyle, COMDAT

; 3175 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 3176 : 		return dwExStyle == 0 ? t_dwExStyle : dwExStyle;

  0000b	83 7d 08 00	 cmp	 DWORD PTR _dwExStyle$[ebp], 0
  0000f	75 09		 jne	 SHORT $LN3@GetWndExSt
  00011	c7 45 fc 80 00
	00 00		 mov	 DWORD PTR tv65[ebp], 128 ; 00000080H
  00018	eb 06		 jmp	 SHORT $LN4@GetWndExSt
$LN3@GetWndExSt:
  0001a	8b 45 08	 mov	 eax, DWORD PTR _dwExStyle$[ebp]
  0001d	89 45 fc	 mov	 DWORD PTR tv65[ebp], eax
$LN4@GetWndExSt:
  00020	8b 45 fc	 mov	 eax, DWORD PTR tv65[ebp]

; 3177 : 	}

  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?GetWndExStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z ENDP ; ATL::CWinTraits<13107200,128>::GetWndExStyle
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?GetWndStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z
_TEXT	SEGMENT
tv65 = -4						; size = 4
_dwStyle$ = 8						; size = 4
?GetWndStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z PROC ; ATL::CWinTraits<13107200,128>::GetWndStyle, COMDAT

; 3171 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 3172 : 		return dwStyle == 0 ? t_dwStyle : dwStyle;

  0000b	83 7d 08 00	 cmp	 DWORD PTR _dwStyle$[ebp], 0
  0000f	75 09		 jne	 SHORT $LN3@GetWndStyl
  00011	c7 45 fc 00 00
	c8 00		 mov	 DWORD PTR tv65[ebp], 13107200 ; 00c80000H
  00018	eb 06		 jmp	 SHORT $LN4@GetWndStyl
$LN3@GetWndStyl:
  0001a	8b 45 08	 mov	 eax, DWORD PTR _dwStyle$[ebp]
  0001d	89 45 fc	 mov	 DWORD PTR tv65[ebp], eax
$LN4@GetWndStyl:
  00020	8b 45 fc	 mov	 eax, DWORD PTR tv65[ebp]

; 3173 : 	}

  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?GetWndStyle@?$CWinTraits@$0MIAAAA@$0IA@@ATL@@SAKK@Z ENDP ; ATL::CWinTraits<13107200,128>::GetWndStyle
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
;	COMDAT ?_Reset_copy@?$_Func_class@_NH@std@@IAEXABV12@@Z
_TEXT	SEGMENT
tv72 = -8						; size = 4
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
?_Reset_copy@?$_Func_class@_NH@std@@IAEXABV12@@Z PROC	; std::_Func_class<bool,int>::_Reset_copy, COMDAT
; _this$ = ecx

; 1287 : 		{	// copy _Right's stored object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1288 : 		if (!_Right._Empty())

  00018	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  0001b	e8 00 00 00 00	 call	 ?_Empty@?$_Func_class@_NH@std@@IBE_NXZ ; std::_Func_class<bool,int>::_Empty
  00020	0f b6 c0	 movzx	 eax, al
  00023	85 c0		 test	 eax, eax
  00025	75 32		 jne	 SHORT $LN1@Reset_copy

; 1289 : 			{
; 1290 : 			_Set(_Right._Getimpl()->_Copy(_Getspace()));

  00027	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  0002a	e8 00 00 00 00	 call	 ?_Getimpl@?$_Func_class@_NH@std@@ABEPAV?$_Func_base@_NH@2@XZ ; std::_Func_class<bool,int>::_Getimpl
  0002f	89 45 f8	 mov	 DWORD PTR tv72[ebp], eax
  00032	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00035	e8 00 00 00 00	 call	 ?_Getspace@?$_Func_class@_NH@std@@AAEPAXXZ ; std::_Func_class<bool,int>::_Getspace
  0003a	8b f4		 mov	 esi, esp
  0003c	50		 push	 eax
  0003d	8b 4d f8	 mov	 ecx, DWORD PTR tv72[ebp]
  00040	8b 11		 mov	 edx, DWORD PTR [ecx]
  00042	8b 4d f8	 mov	 ecx, DWORD PTR tv72[ebp]
  00045	8b 02		 mov	 eax, DWORD PTR [edx]
  00047	ff d0		 call	 eax
  00049	3b f4		 cmp	 esi, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00050	50		 push	 eax
  00051	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00054	e8 00 00 00 00	 call	 ?_Set@?$_Func_class@_NH@std@@AAEXPAV?$_Func_base@_NH@2@@Z ; std::_Func_class<bool,int>::_Set
$LN1@Reset_copy:

; 1291 : 			}
; 1292 : 		}

  00059	5e		 pop	 esi
  0005a	83 c4 08	 add	 esp, 8
  0005d	3b ec		 cmp	 ebp, esp
  0005f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00064	8b e5		 mov	 esp, ebp
  00066	5d		 pop	 ebp
  00067	c2 04 00	 ret	 4
?_Reset_copy@?$_Func_class@_NH@std@@IAEXABV12@@Z ENDP	; std::_Func_class<bool,int>::_Reset_copy
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??R?$_Func_class@_NH@std@@QBE_NH@Z
_TEXT	SEGMENT
__Impl$ = -8						; size = 4
_this$ = -4						; size = 4
_<_Args_0>$ = 8						; size = 4
??R?$_Func_class@_NH@std@@QBE_NH@Z PROC			; std::_Func_class<bool,int>::operator(), COMDAT
; _this$ = ecx
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\functional
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  00018	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001b	e8 00 00 00 00	 call	 ?_Empty@?$_Func_class@_NH@std@@IBE_NXZ ; std::_Func_class<bool,int>::_Empty
  00020	0f b6 c0	 movzx	 eax, al
  00023	85 c0		 test	 eax, eax
  00025	74 05		 je	 SHORT $LN2@operator
  00027	e8 00 00 00 00	 call	 ?_Xbad_function_call@std@@YAXXZ ; std::_Xbad_function_call
$LN2@operator:
  0002c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002f	e8 00 00 00 00	 call	 ?_Getimpl@?$_Func_class@_NH@std@@ABEPAV?$_Func_base@_NH@2@XZ ; std::_Func_class<bool,int>::_Getimpl
  00034	89 45 f8	 mov	 DWORD PTR __Impl$[ebp], eax
  00037	8d 4d 08	 lea	 ecx, DWORD PTR _<_Args_0>$[ebp]
  0003a	51		 push	 ecx
  0003b	e8 00 00 00 00	 call	 ??$forward@H@std@@YA$$QAHAAH@Z ; std::forward<int>
  00040	83 c4 04	 add	 esp, 4
  00043	8b f4		 mov	 esi, esp
  00045	50		 push	 eax
  00046	8b 55 f8	 mov	 edx, DWORD PTR __Impl$[ebp]
  00049	8b 02		 mov	 eax, DWORD PTR [edx]
  0004b	8b 4d f8	 mov	 ecx, DWORD PTR __Impl$[ebp]
  0004e	8b 50 08	 mov	 edx, DWORD PTR [eax+8]
  00051	ff d2		 call	 edx
  00053	3b f4		 cmp	 esi, esp
  00055	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN3@operator:
  0005a	5e		 pop	 esi
  0005b	83 c4 08	 add	 esp, 8
  0005e	3b ec		 cmp	 ebp, esp
  00060	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00065	8b e5		 mov	 esp, ebp
  00067	5d		 pop	 ebp
  00068	c2 04 00	 ret	 4
??R?$_Func_class@_NH@std@@QBE_NH@Z ENDP			; std::_Func_class<bool,int>::operator()
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?StartWindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z
_TEXT	SEGMENT
_pOldProc$ = -12					; size = 4
_pProc$ = -8						; size = 4
_pThis$ = -4						; size = 4
_hWnd$ = 8						; size = 4
_uMsg$ = 12						; size = 4
_wParam$ = 16						; size = 4
_lParam$ = 20						; size = 4
?StartWindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::StartWindowProc, COMDAT

; 3516 : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	56		 push	 esi
  00007	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000e	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00015	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 3517 : 	CWindowImplBaseT< TBase, TWinTraits >* pThis = (CWindowImplBaseT< TBase, TWinTraits >*)_AtlWinModule.ExtractCreateWndData();

  0001c	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlWinModule@ATL@@3VCAtlWinModule@1@A ; ATL::_AtlWinModule
  00021	e8 00 00 00 00	 call	 ?ExtractCreateWndData@CAtlWinModule@ATL@@QAEPAXXZ ; ATL::CAtlWinModule::ExtractCreateWndData
  00026	89 45 fc	 mov	 DWORD PTR _pThis$[ebp], eax

; 3518 : 	ATLASSERT(pThis != NULL);
; 3519 : 	if(!pThis)

  00029	83 7d fc 00	 cmp	 DWORD PTR _pThis$[ebp], 0
  0002d	75 04		 jne	 SHORT $LN2@StartWindo

; 3520 : 	{
; 3521 : 		return 0;

  0002f	33 c0		 xor	 eax, eax
  00031	eb 6e		 jmp	 SHORT $LN1@StartWindo
$LN2@StartWindo:

; 3522 : 	}
; 3523 : 	pThis->m_hWnd = hWnd;

  00033	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  00036	8b 4d 08	 mov	 ecx, DWORD PTR _hWnd$[ebp]
  00039	89 48 04	 mov	 DWORD PTR [eax+4], ecx

; 3524 : 
; 3525 : 	// Initialize the thunk.  This is allocated in CWindowImplBaseT::Create,
; 3526 : 	// so failure is unexpected here.
; 3527 : 
; 3528 : 	pThis->m_thunk.Init(pThis->GetWindowProc(), pThis);

  0003c	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  0003f	52		 push	 edx
  00040	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  00043	8b 10		 mov	 edx, DWORD PTR [eax]
  00045	8b f4		 mov	 esi, esp
  00047	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0004a	8b 42 08	 mov	 eax, DWORD PTR [edx+8]
  0004d	ff d0		 call	 eax
  0004f	3b f4		 cmp	 esi, esp
  00051	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00056	50		 push	 eax
  00057	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0005a	83 c1 08	 add	 ecx, 8
  0005d	e8 00 00 00 00	 call	 ?Init@CWndProcThunk@ATL@@QAEHP6GJPAUHWND__@@IIJ@ZPAX@Z ; ATL::CWndProcThunk::Init

; 3529 : 	WNDPROC pProc = pThis->m_thunk.GetWNDPROC();

  00062	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00065	83 c1 08	 add	 ecx, 8
  00068	e8 00 00 00 00	 call	 ?GetWNDPROC@CWndProcThunk@ATL@@QAEP6GJPAUHWND__@@IIJ@ZXZ ; ATL::CWndProcThunk::GetWNDPROC
  0006d	89 45 f8	 mov	 DWORD PTR _pProc$[ebp], eax

; 3530 : 	WNDPROC pOldProc = (WNDPROC)::SetWindowLongPtr(hWnd, GWLP_WNDPROC, (LONG_PTR)pProc);

  00070	8b 4d f8	 mov	 ecx, DWORD PTR _pProc$[ebp]
  00073	51		 push	 ecx
  00074	6a fc		 push	 -4			; fffffffcH
  00076	8b 55 08	 mov	 edx, DWORD PTR _hWnd$[ebp]
  00079	52		 push	 edx
  0007a	e8 00 00 00 00	 call	 ?SetWindowLongPtrW@@YAJPAUHWND__@@HJ@Z ; SetWindowLongPtrW
  0007f	83 c4 0c	 add	 esp, 12			; 0000000cH
  00082	89 45 f4	 mov	 DWORD PTR _pOldProc$[ebp], eax

; 3531 : #ifdef _DEBUG
; 3532 : 	// check if somebody has subclassed us already since we discard it
; 3533 : 	if(pOldProc != StartWindowProc)
; 3534 : 		ATLTRACE(atlTraceWindowing, 0, _T("Subclassing through a hook discarded.\n"));
; 3535 : #else
; 3536 : 	(pOldProc);	// avoid unused warning
; 3537 : #endif
; 3538 : 	return pProc(hWnd, uMsg, wParam, lParam);

  00085	8b f4		 mov	 esi, esp
  00087	8b 45 14	 mov	 eax, DWORD PTR _lParam$[ebp]
  0008a	50		 push	 eax
  0008b	8b 4d 10	 mov	 ecx, DWORD PTR _wParam$[ebp]
  0008e	51		 push	 ecx
  0008f	8b 55 0c	 mov	 edx, DWORD PTR _uMsg$[ebp]
  00092	52		 push	 edx
  00093	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  00096	50		 push	 eax
  00097	ff 55 f8	 call	 DWORD PTR _pProc$[ebp]
  0009a	3b f4		 cmp	 esi, esp
  0009c	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@StartWindo:

; 3539 : }

  000a1	5e		 pop	 esi
  000a2	83 c4 0c	 add	 esp, 12			; 0000000cH
  000a5	3b ec		 cmp	 ebp, esp
  000a7	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000ac	8b e5		 mov	 esp, ebp
  000ae	5d		 pop	 ebp
  000af	c2 10 00	 ret	 16			; 00000010H
?StartWindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::StartWindowProc
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z
_TEXT	SEGMENT
_hWndThis$1 = -76					; size = 4
_pfnWndProc$2 = -72					; size = 4
_bRet$ = -68						; size = 4
_lRes$ = -60						; size = 4
_pOldMsg$ = -52						; size = 4
_msg$ = -44						; size = 36
_pThis$ = -4						; size = 4
_hWnd$ = 8						; size = 4
_uMsg$ = 12						; size = 4
_wParam$ = 16						; size = 4
_lParam$ = 20						; size = 4
?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::WindowProc, COMDAT

; 3547 : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 4c	 sub	 esp, 76			; 0000004cH
  00006	56		 push	 esi
  00007	57		 push	 edi
  00008	8d 7d b4	 lea	 edi, DWORD PTR [ebp-76]
  0000b	b9 13 00 00 00	 mov	 ecx, 19			; 00000013H
  00010	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00015	f3 ab		 rep stosd

; 3548 : 	CWindowImplBaseT< TBase, TWinTraits >* pThis = (CWindowImplBaseT< TBase, TWinTraits >*)hWnd;

  00017	8b 45 08	 mov	 eax, DWORD PTR _hWnd$[ebp]
  0001a	89 45 fc	 mov	 DWORD PTR _pThis$[ebp], eax

; 3549 : 	// set a ptr to this message and save the old value
; 3550 : 	_ATL_MSG msg(pThis->m_hWnd, uMsg, wParam, lParam);

  0001d	6a 01		 push	 1
  0001f	8b 4d 14	 mov	 ecx, DWORD PTR _lParam$[ebp]
  00022	51		 push	 ecx
  00023	8b 55 10	 mov	 edx, DWORD PTR _wParam$[ebp]
  00026	52		 push	 edx
  00027	8b 45 0c	 mov	 eax, DWORD PTR _uMsg$[ebp]
  0002a	50		 push	 eax
  0002b	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0002e	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  00031	52		 push	 edx
  00032	8d 4d d4	 lea	 ecx, DWORD PTR _msg$[ebp]
  00035	e8 00 00 00 00	 call	 ??0_ATL_MSG@ATL@@QAE@PAUHWND__@@IIJH@Z ; ATL::_ATL_MSG::_ATL_MSG

; 3551 : 	const _ATL_MSG* pOldMsg = pThis->m_pCurrentMsg;

  0003a	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  0003d	8b 48 18	 mov	 ecx, DWORD PTR [eax+24]
  00040	89 4d cc	 mov	 DWORD PTR _pOldMsg$[ebp], ecx

; 3552 : 	pThis->m_pCurrentMsg = &msg;

  00043	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  00046	8d 45 d4	 lea	 eax, DWORD PTR _msg$[ebp]
  00049	89 42 18	 mov	 DWORD PTR [edx+24], eax

; 3553 : 	// pass to the message map to process
; 3554 : 	LRESULT lRes = 0;

  0004c	c7 45 c4 00 00
	00 00		 mov	 DWORD PTR _lRes$[ebp], 0

; 3555 : 	BOOL bRet = pThis->ProcessWindowMessage(pThis->m_hWnd, uMsg, wParam, lParam, lRes, 0);

  00053	8b f4		 mov	 esi, esp
  00055	6a 00		 push	 0
  00057	8d 4d c4	 lea	 ecx, DWORD PTR _lRes$[ebp]
  0005a	51		 push	 ecx
  0005b	8b 55 14	 mov	 edx, DWORD PTR _lParam$[ebp]
  0005e	52		 push	 edx
  0005f	8b 45 10	 mov	 eax, DWORD PTR _wParam$[ebp]
  00062	50		 push	 eax
  00063	8b 4d 0c	 mov	 ecx, DWORD PTR _uMsg$[ebp]
  00066	51		 push	 ecx
  00067	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  0006a	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  0006d	50		 push	 eax
  0006e	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00071	8b 11		 mov	 edx, DWORD PTR [ecx]
  00073	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00076	8b 02		 mov	 eax, DWORD PTR [edx]
  00078	ff d0		 call	 eax
  0007a	3b f4		 cmp	 esi, esp
  0007c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00081	89 45 bc	 mov	 DWORD PTR _bRet$[ebp], eax

; 3556 : 	// restore saved value for the current message
; 3557 : 	ATLASSERT(pThis->m_pCurrentMsg == &msg);
; 3558 : 
; 3559 : 	// do the default processing if message was not handled
; 3560 : 	if(!bRet)

  00084	83 7d bc 00	 cmp	 DWORD PTR _bRet$[ebp], 0
  00088	0f 85 98 00 00
	00		 jne	 $LN2@WindowProc

; 3561 : 	{
; 3562 : 		if(uMsg != WM_NCDESTROY)

  0008e	81 7d 0c 82 00
	00 00		 cmp	 DWORD PTR _uMsg$[ebp], 130 ; 00000082H
  00095	74 19		 je	 SHORT $LN3@WindowProc

; 3563 : 			lRes = pThis->DefWindowProc(uMsg, wParam, lParam);

  00097	8b 4d 14	 mov	 ecx, DWORD PTR _lParam$[ebp]
  0009a	51		 push	 ecx
  0009b	8b 55 10	 mov	 edx, DWORD PTR _wParam$[ebp]
  0009e	52		 push	 edx
  0009f	8b 45 0c	 mov	 eax, DWORD PTR _uMsg$[ebp]
  000a2	50		 push	 eax
  000a3	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  000a6	e8 00 00 00 00	 call	 ?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEJIIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::DefWindowProcW
  000ab	89 45 c4	 mov	 DWORD PTR _lRes$[ebp], eax

; 3564 : 		else

  000ae	eb 76		 jmp	 SHORT $LN2@WindowProc
$LN3@WindowProc:

; 3565 : 		{
; 3566 : 			// unsubclass, if needed
; 3567 : 			LONG_PTR pfnWndProc = ::GetWindowLongPtr(pThis->m_hWnd, GWLP_WNDPROC);

  000b0	6a fc		 push	 -4			; fffffffcH
  000b2	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  000b5	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  000b8	52		 push	 edx
  000b9	e8 00 00 00 00	 call	 ?GetWindowLongPtrW@@YAJPAUHWND__@@H@Z ; GetWindowLongPtrW
  000be	83 c4 08	 add	 esp, 8
  000c1	89 45 b8	 mov	 DWORD PTR _pfnWndProc$2[ebp], eax

; 3568 : 			lRes = pThis->DefWindowProc(uMsg, wParam, lParam);

  000c4	8b 45 14	 mov	 eax, DWORD PTR _lParam$[ebp]
  000c7	50		 push	 eax
  000c8	8b 4d 10	 mov	 ecx, DWORD PTR _wParam$[ebp]
  000cb	51		 push	 ecx
  000cc	8b 55 0c	 mov	 edx, DWORD PTR _uMsg$[ebp]
  000cf	52		 push	 edx
  000d0	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  000d3	e8 00 00 00 00	 call	 ?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEJIIJ@Z ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::DefWindowProcW
  000d8	89 45 c4	 mov	 DWORD PTR _lRes$[ebp], eax

; 3569 : 			if(pThis->m_pfnSuperWindowProc != ::DefWindowProc && ::GetWindowLongPtr(pThis->m_hWnd, GWLP_WNDPROC) == pfnWndProc)

  000db	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  000de	8b 48 20	 mov	 ecx, DWORD PTR [eax+32]
  000e1	3b 0d 00 00 00
	00		 cmp	 ecx, DWORD PTR __imp__DefWindowProcW@16
  000e7	74 2e		 je	 SHORT $LN5@WindowProc
  000e9	6a fc		 push	 -4			; fffffffcH
  000eb	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  000ee	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  000f1	50		 push	 eax
  000f2	e8 00 00 00 00	 call	 ?GetWindowLongPtrW@@YAJPAUHWND__@@H@Z ; GetWindowLongPtrW
  000f7	83 c4 08	 add	 esp, 8
  000fa	3b 45 b8	 cmp	 eax, DWORD PTR _pfnWndProc$2[ebp]
  000fd	75 18		 jne	 SHORT $LN5@WindowProc

; 3570 : 				::SetWindowLongPtr(pThis->m_hWnd, GWLP_WNDPROC, (LONG_PTR)pThis->m_pfnSuperWindowProc);

  000ff	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00102	8b 51 20	 mov	 edx, DWORD PTR [ecx+32]
  00105	52		 push	 edx
  00106	6a fc		 push	 -4			; fffffffcH
  00108	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  0010b	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  0010e	51		 push	 ecx
  0010f	e8 00 00 00 00	 call	 ?SetWindowLongPtrW@@YAJPAUHWND__@@HJ@Z ; SetWindowLongPtrW
  00114	83 c4 0c	 add	 esp, 12			; 0000000cH
$LN5@WindowProc:

; 3571 : 			// mark window as destroyed
; 3572 : 			pThis->m_dwState |= CWindowImplRoot<TBase>::WINSTATE_DESTROYED;

  00117	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  0011a	8b 42 1c	 mov	 eax, DWORD PTR [edx+28]
  0011d	83 c8 01	 or	 eax, 1
  00120	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00123	89 41 1c	 mov	 DWORD PTR [ecx+28], eax
$LN2@WindowProc:

; 3573 : 		}
; 3574 : 	}
; 3575 : 	if((pThis->m_dwState & CWindowImplRoot<TBase>::WINSTATE_DESTROYED) && pOldMsg== NULL)

  00126	8b 55 fc	 mov	 edx, DWORD PTR _pThis$[ebp]
  00129	8b 42 1c	 mov	 eax, DWORD PTR [edx+28]
  0012c	83 e0 01	 and	 eax, 1
  0012f	74 4d		 je	 SHORT $LN6@WindowProc
  00131	83 7d cc 00	 cmp	 DWORD PTR _pOldMsg$[ebp], 0
  00135	75 47		 jne	 SHORT $LN6@WindowProc

; 3576 : 	{
; 3577 : 		// clear out window handle
; 3578 : 		HWND hWndThis = pThis->m_hWnd;

  00137	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0013a	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0013d	89 55 b4	 mov	 DWORD PTR _hWndThis$1[ebp], edx

; 3579 : 		pThis->m_hWnd = NULL;

  00140	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  00143	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [eax+4], 0

; 3580 : 		pThis->m_dwState &= ~CWindowImplRoot<TBase>::WINSTATE_DESTROYED;

  0014a	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0014d	8b 51 1c	 mov	 edx, DWORD PTR [ecx+28]
  00150	83 e2 fe	 and	 edx, -2			; fffffffeH
  00153	8b 45 fc	 mov	 eax, DWORD PTR _pThis$[ebp]
  00156	89 50 1c	 mov	 DWORD PTR [eax+28], edx

; 3581 : 		// clean up after window is destroyed
; 3582 : 		pThis->m_pCurrentMsg = pOldMsg;

  00159	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0015c	8b 55 cc	 mov	 edx, DWORD PTR _pOldMsg$[ebp]
  0015f	89 51 18	 mov	 DWORD PTR [ecx+24], edx

; 3583 : 		pThis->OnFinalMessage(hWndThis);

  00162	8b f4		 mov	 esi, esp
  00164	8b 45 b4	 mov	 eax, DWORD PTR _hWndThis$1[ebp]
  00167	50		 push	 eax
  00168	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  0016b	8b 11		 mov	 edx, DWORD PTR [ecx]
  0016d	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00170	8b 42 0c	 mov	 eax, DWORD PTR [edx+12]
  00173	ff d0		 call	 eax
  00175	3b f4		 cmp	 esi, esp
  00177	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 3584 : 	}else {

  0017c	eb 09		 jmp	 SHORT $LN7@WindowProc
$LN6@WindowProc:

; 3585 : 		pThis->m_pCurrentMsg = pOldMsg;

  0017e	8b 4d fc	 mov	 ecx, DWORD PTR _pThis$[ebp]
  00181	8b 55 cc	 mov	 edx, DWORD PTR _pOldMsg$[ebp]
  00184	89 51 18	 mov	 DWORD PTR [ecx+24], edx
$LN7@WindowProc:

; 3586 : 	}
; 3587 : 	return lRes;

  00187	8b 45 c4	 mov	 eax, DWORD PTR _lRes$[ebp]

; 3588 : }

  0018a	52		 push	 edx
  0018b	8b cd		 mov	 ecx, ebp
  0018d	50		 push	 eax
  0018e	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN12@WindowProc
  00194	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00199	58		 pop	 eax
  0019a	5a		 pop	 edx
  0019b	5f		 pop	 edi
  0019c	5e		 pop	 esi
  0019d	83 c4 4c	 add	 esp, 76			; 0000004cH
  001a0	3b ec		 cmp	 ebp, esp
  001a2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  001a7	8b e5		 mov	 esp, ebp
  001a9	5d		 pop	 ebp
  001aa	c2 10 00	 ret	 16			; 00000010H
  001ad	0f 1f 00	 npad	 3
$LN12@WindowProc:
  001b0	02 00 00 00	 DD	 2
  001b4	00 00 00 00	 DD	 $LN11@WindowProc
$LN11@WindowProc:
  001b8	d4 ff ff ff	 DD	 -44			; ffffffd4H
  001bc	24 00 00 00	 DD	 36			; 00000024H
  001c0	00 00 00 00	 DD	 $LN9@WindowProc
  001c4	c4 ff ff ff	 DD	 -60			; ffffffc4H
  001c8	04 00 00 00	 DD	 4
  001cc	00 00 00 00	 DD	 $LN10@WindowProc
$LN10@WindowProc:
  001d0	6c		 DB	 108			; 0000006cH
  001d1	52		 DB	 82			; 00000052H
  001d2	65		 DB	 101			; 00000065H
  001d3	73		 DB	 115			; 00000073H
  001d4	00		 DB	 0
$LN9@WindowProc:
  001d5	6d		 DB	 109			; 0000006dH
  001d6	73		 DB	 115			; 00000073H
  001d7	67		 DB	 103			; 00000067H
  001d8	00		 DB	 0
?WindowProc@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@SGJPAUHWND__@@IIJ@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::WindowProc
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?Create@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@GPAX@Z
_TEXT	SEGMENT
_hWnd$ = -12						; size = 4
_result$ = -8						; size = 4
_this$ = -4						; size = 4
_hWndParent$ = 8					; size = 4
_rect$ = 12						; size = 4
_szWindowName$ = 16					; size = 4
_dwStyle$ = 20						; size = 4
_dwExStyle$ = 24					; size = 4
_MenuOrID$ = 28						; size = 4
_atom$ = 32						; size = 2
_lpCreateParam$ = 36					; size = 4
?Create@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@GPAX@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create, COMDAT
; _this$ = ecx

; 3600 : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	56		 push	 esi
  00007	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000e	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00015	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0001c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
$LN2@Create:

; 3601 : 	ATLASSUME(this->m_hWnd == NULL);

  0001f	33 c0		 xor	 eax, eax
  00021	75 fc		 jne	 SHORT $LN2@Create

; 3602 : 
; 3603 : 	// Allocate the thunk structure here, where we can fail gracefully.
; 3604 : 	BOOL result = this->m_thunk.Init(NULL,NULL);

  00023	6a 00		 push	 0
  00025	6a 00		 push	 0
  00027	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002a	83 c1 08	 add	 ecx, 8
  0002d	e8 00 00 00 00	 call	 ?Init@CWndProcThunk@ATL@@QAEHP6GJPAUHWND__@@IIJ@ZPAX@Z ; ATL::CWndProcThunk::Init
  00032	89 45 f8	 mov	 DWORD PTR _result$[ebp], eax

; 3605 : 	if (result == FALSE) {

  00035	83 7d f8 00	 cmp	 DWORD PTR _result$[ebp], 0
  00039	75 18		 jne	 SHORT $LN8@Create

; 3606 : 		SetLastError(ERROR_OUTOFMEMORY);

  0003b	8b f4		 mov	 esi, esp
  0003d	6a 0e		 push	 14			; 0000000eH
  0003f	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__SetLastError@4
  00045	3b f4		 cmp	 esi, esp
  00047	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 3607 : 		return NULL;

  0004c	33 c0		 xor	 eax, eax
  0004e	e9 af 00 00 00	 jmp	 $LN1@Create
$LN8@Create:

; 3608 : 	}
; 3609 : 
; 3610 : 	if(atom == 0)

  00053	0f b7 4d 20	 movzx	 ecx, WORD PTR _atom$[ebp]
  00057	85 c9		 test	 ecx, ecx
  00059	75 07		 jne	 SHORT $LN9@Create

; 3611 : 		return NULL;

  0005b	33 c0		 xor	 eax, eax
  0005d	e9 a0 00 00 00	 jmp	 $LN1@Create
$LN9@Create:

; 3612 : 
; 3613 : 	_AtlWinModule.AddCreateWndData(&this->m_thunk.cd, this);

  00062	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00065	52		 push	 edx
  00066	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00069	83 c0 08	 add	 eax, 8
  0006c	50		 push	 eax
  0006d	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlWinModule@ATL@@3VCAtlWinModule@1@A ; ATL::_AtlWinModule
  00072	e8 00 00 00 00	 call	 ?AddCreateWndData@CAtlWinModule@ATL@@QAEXPAU_AtlCreateWndData@2@PAX@Z ; ATL::CAtlWinModule::AddCreateWndData

; 3614 : 
; 3615 : 	if(MenuOrID.m_hMenu == NULL && (dwStyle & WS_CHILD))

  00077	83 7d 1c 00	 cmp	 DWORD PTR _MenuOrID$[ebp], 0
  0007b	75 11		 jne	 SHORT $LN10@Create
  0007d	8b 4d 14	 mov	 ecx, DWORD PTR _dwStyle$[ebp]
  00080	81 e1 00 00 00
	40		 and	 ecx, 1073741824		; 40000000H
  00086	74 06		 je	 SHORT $LN10@Create

; 3616 : 		MenuOrID.m_hMenu = (HMENU)(UINT_PTR)this;

  00088	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  0008b	89 55 1c	 mov	 DWORD PTR _MenuOrID$[ebp], edx
$LN10@Create:

; 3617 : 	if(rect.m_lpRect == NULL)

  0008e	83 7d 0c 00	 cmp	 DWORD PTR _rect$[ebp], 0
  00092	75 07		 jne	 SHORT $LN11@Create

; 3618 : 		rect.m_lpRect = &TBase::rcDefault;

  00094	c7 45 0c 00 00
	00 00		 mov	 DWORD PTR _rect$[ebp], OFFSET ?rcDefault@CWindow@ATL@@2UtagRECT@@A ; ATL::CWindow::rcDefault
$LN11@Create:

; 3619 : 
; 3620 : 	HWND hWnd = ::CreateWindowEx(dwExStyle, MAKEINTATOM(atom), szWindowName,

  0009b	8b f4		 mov	 esi, esp
  0009d	8b 45 24	 mov	 eax, DWORD PTR _lpCreateParam$[ebp]
  000a0	50		 push	 eax
  000a1	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlBaseModule@ATL@@3VCAtlBaseModule@1@A ; ATL::_AtlBaseModule
  000a6	e8 00 00 00 00	 call	 ?GetModuleInstance@CAtlBaseModule@ATL@@QAEPAUHINSTANCE__@@XZ ; ATL::CAtlBaseModule::GetModuleInstance
  000ab	50		 push	 eax
  000ac	8b 4d 1c	 mov	 ecx, DWORD PTR _MenuOrID$[ebp]
  000af	51		 push	 ecx
  000b0	8b 55 08	 mov	 edx, DWORD PTR _hWndParent$[ebp]
  000b3	52		 push	 edx
  000b4	8b 45 0c	 mov	 eax, DWORD PTR _rect$[ebp]
  000b7	8b 4d 0c	 mov	 ecx, DWORD PTR _rect$[ebp]
  000ba	8b 50 0c	 mov	 edx, DWORD PTR [eax+12]
  000bd	2b 51 04	 sub	 edx, DWORD PTR [ecx+4]
  000c0	52		 push	 edx
  000c1	8b 45 0c	 mov	 eax, DWORD PTR _rect$[ebp]
  000c4	8b 4d 0c	 mov	 ecx, DWORD PTR _rect$[ebp]
  000c7	8b 50 08	 mov	 edx, DWORD PTR [eax+8]
  000ca	2b 11		 sub	 edx, DWORD PTR [ecx]
  000cc	52		 push	 edx
  000cd	8b 45 0c	 mov	 eax, DWORD PTR _rect$[ebp]
  000d0	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  000d3	51		 push	 ecx
  000d4	8b 55 0c	 mov	 edx, DWORD PTR _rect$[ebp]
  000d7	8b 02		 mov	 eax, DWORD PTR [edx]
  000d9	50		 push	 eax
  000da	8b 4d 14	 mov	 ecx, DWORD PTR _dwStyle$[ebp]
  000dd	51		 push	 ecx
  000de	8b 55 10	 mov	 edx, DWORD PTR _szWindowName$[ebp]
  000e1	52		 push	 edx
  000e2	0f b7 45 20	 movzx	 eax, WORD PTR _atom$[ebp]
  000e6	50		 push	 eax
  000e7	8b 4d 18	 mov	 ecx, DWORD PTR _dwExStyle$[ebp]
  000ea	51		 push	 ecx
  000eb	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__CreateWindowExW@48
  000f1	3b f4		 cmp	 esi, esp
  000f3	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000f8	89 45 f4	 mov	 DWORD PTR _hWnd$[ebp], eax
$LN5@Create:

; 3621 : 		dwStyle, rect.m_lpRect->left, rect.m_lpRect->top, rect.m_lpRect->right - rect.m_lpRect->left,
; 3622 : 		rect.m_lpRect->bottom - rect.m_lpRect->top, hWndParent, MenuOrID.m_hMenu,
; 3623 : 		_AtlBaseModule.GetModuleInstance(), lpCreateParam);
; 3624 : 
; 3625 : 	ATLASSUME(this->m_hWnd == hWnd);

  000fb	33 d2		 xor	 edx, edx
  000fd	75 fc		 jne	 SHORT $LN5@Create

; 3626 : 
; 3627 : 	return hWnd;

  000ff	8b 45 f4	 mov	 eax, DWORD PTR _hWnd$[ebp]
$LN1@Create:

; 3628 : }

  00102	5e		 pop	 esi
  00103	83 c4 0c	 add	 esp, 12			; 0000000cH
  00106	3b ec		 cmp	 ebp, esp
  00108	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0010d	8b e5		 mov	 esp, ebp
  0010f	5d		 pop	 ebp
  00110	c2 20 00	 ret	 32			; 00000020H
?Create@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEPAUHWND__@@PAU3@V_U_RECT@2@PB_WKKV_U_MENUorID@2@GPAX@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::Create
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ
_TEXT	SEGMENT
tv75 = -48						; size = 4
$T2 = -44						; size = 4
$T3 = -40						; size = 4
$T4 = -36						; size = 4
__Ret$ = -28						; size = 8
__Rx$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ PROC ; std::make_shared<CShellTrayNotifyWnd>, COMDAT

; 1865 : 	{	// make a shared_ptr

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 24	 sub	 esp, 36			; 00000024H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 d0	 mov	 DWORD PTR [ebp-48], eax
  00023	89 45 d4	 mov	 DWORD PTR [ebp-44], eax
  00026	89 45 d8	 mov	 DWORD PTR [ebp-40], eax
  00029	89 45 dc	 mov	 DWORD PTR [ebp-36], eax
  0002c	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  0002f	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00032	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00035	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  00038	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0003b	c7 45 d4 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 1866 : 	const auto _Rx = new _Ref_count_obj<_Ty>(_STD forward<_Types>(_Args)...);

  00042	68 68 02 00 00	 push	 616			; 00000268H
  00047	e8 00 00 00 00	 call	 ??2@YAPAXI@Z		; operator new
  0004c	83 c4 04	 add	 esp, 4
  0004f	89 45 d8	 mov	 DWORD PTR $T3[ebp], eax
  00052	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
  00059	83 7d d8 00	 cmp	 DWORD PTR $T3[ebp], 0
  0005d	74 0d		 je	 SHORT $LN3@make_share
  0005f	8b 4d d8	 mov	 ecx, DWORD PTR $T3[ebp]
  00062	e8 00 00 00 00	 call	 ??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Ref_count_obj<CShellTrayNotifyWnd><>
  00067	89 45 d0	 mov	 DWORD PTR tv75[ebp], eax
  0006a	eb 07		 jmp	 SHORT $LN4@make_share
$LN3@make_share:
  0006c	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR tv75[ebp], 0
$LN4@make_share:
  00073	8b 45 d0	 mov	 eax, DWORD PTR tv75[ebp]
  00076	89 45 dc	 mov	 DWORD PTR $T4[ebp], eax
  00079	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  0007d	8b 4d dc	 mov	 ecx, DWORD PTR $T4[ebp]
  00080	89 4d f0	 mov	 DWORD PTR __Rx$[ebp], ecx

; 1867 : 
; 1868 : 	shared_ptr<_Ty> _Ret;

  00083	8d 4d e4	 lea	 ecx, DWORD PTR __Ret$[ebp]
  00086	e8 00 00 00 00	 call	 ??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
  0008b	c7 45 fc 02 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 2

; 1869 : 	_Ret._Set_ptr_rep_and_enable_shared(_Rx->_Getptr(), _Rx);

  00092	8b 55 f0	 mov	 edx, DWORD PTR __Rx$[ebp]
  00095	52		 push	 edx
  00096	8b 4d f0	 mov	 ecx, DWORD PTR __Rx$[ebp]
  00099	e8 00 00 00 00	 call	 ?_Getptr@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAEPAVCShellTrayNotifyWnd@@XZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Getptr
  0009e	50		 push	 eax
  0009f	8d 4d e4	 lea	 ecx, DWORD PTR __Ret$[ebp]
  000a2	e8 00 00 00 00	 call	 ??$_Set_ptr_rep_and_enable_shared@VCShellTrayNotifyWnd@@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@AAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@1@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::_Set_ptr_rep_and_enable_shared<CShellTrayNotifyWnd>

; 1870 : 	return (_Ret);

  000a7	8d 45 e4	 lea	 eax, DWORD PTR __Ret$[ebp]
  000aa	50		 push	 eax
  000ab	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  000ae	e8 00 00 00 00	 call	 ??0?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@$$QAV01@@Z ; std::shared_ptr<CShellTrayNotifyWnd>::shared_ptr<CShellTrayNotifyWnd>
  000b3	8b 4d d4	 mov	 ecx, DWORD PTR $T2[ebp]
  000b6	83 c9 01	 or	 ecx, 1
  000b9	89 4d d4	 mov	 DWORD PTR $T2[ebp], ecx
  000bc	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000c0	8d 4d e4	 lea	 ecx, DWORD PTR __Ret$[ebp]
  000c3	e8 00 00 00 00	 call	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
  000c8	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 1871 : 	}

  000cb	52		 push	 edx
  000cc	8b cd		 mov	 ecx, ebp
  000ce	50		 push	 eax
  000cf	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN12@make_share
  000d5	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000da	58		 pop	 eax
  000db	5a		 pop	 edx
  000dc	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000df	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000e6	83 c4 30	 add	 esp, 48			; 00000030H
  000e9	3b ec		 cmp	 ebp, esp
  000eb	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000f0	8b e5		 mov	 esp, ebp
  000f2	5d		 pop	 ebp
  000f3	c3		 ret	 0
$LN12@make_share:
  000f4	01 00 00 00	 DD	 1
  000f8	00 00 00 00	 DD	 $LN11@make_share
$LN11@make_share:
  000fc	e4 ff ff ff	 DD	 -28			; ffffffe4H
  00100	08 00 00 00	 DD	 8
  00104	00 00 00 00	 DD	 $LN9@make_share
$LN9@make_share:
  00108	5f		 DB	 95			; 0000005fH
  00109	52		 DB	 82			; 00000052H
  0010a	65		 DB	 101			; 00000065H
  0010b	74		 DB	 116			; 00000074H
  0010c	00		 DB	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ$0:
  00000	68 68 02 00 00	 push	 616			; 00000268H
  00005	8b 45 d8	 mov	 eax, DWORD PTR $T3[ebp]
  00008	50		 push	 eax
  00009	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  0000e	83 c4 08	 add	 esp, 8
  00011	c3		 ret	 0
__unwindfunclet$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ$1:
  00012	8d 4d e4	 lea	 ecx, DWORD PTR __Ret$[ebp]
  00015	e9 00 00 00 00	 jmp	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
__unwindfunclet$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ$2:
  0001a	8b 45 d4	 mov	 eax, DWORD PTR $T2[ebp]
  0001d	83 e0 01	 and	 eax, 1
  00020	0f 84 0c 00 00
	00		 je	 $LN8@make_share
  00026	83 65 d4 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  0002a	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0002d	e9 00 00 00 00	 jmp	 ??1?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QAE@XZ ; std::shared_ptr<CShellTrayNotifyWnd>::~shared_ptr<CShellTrayNotifyWnd>
$LN8@make_share:
  00032	c3		 ret	 0
__ehhandler$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ:
  00033	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ
  00038	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??$make_shared@VCShellTrayNotifyWnd@@$$V@std@@YA?AV?$shared_ptr@VCShellTrayNotifyWnd@@@0@XZ ENDP ; std::make_shared<CShellTrayNotifyWnd>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??$?CVCShellTrayNotifyWnd@@$0A@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBEPAVCShellTrayNotifyWnd@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??$?CVCShellTrayNotifyWnd@@$0A@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBEPAVCShellTrayNotifyWnd@@XZ PROC ; std::shared_ptr<CShellTrayNotifyWnd>::operator-><CShellTrayNotifyWnd,0>, COMDAT
; _this$ = ecx

; 1492 : 		{	// return pointer to resource

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1493 : 		return (get());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?get@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IBEPAVCShellTrayNotifyWnd@@XZ ; std::_Ptr_base<CShellTrayNotifyWnd>::get

; 1494 : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
??$?CVCShellTrayNotifyWnd@@$0A@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@QBEPAVCShellTrayNotifyWnd@@XZ ENDP ; std::shared_ptr<CShellTrayNotifyWnd>::operator-><CShellTrayNotifyWnd,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$move@AAV?$shared_ptr@VCShellTrayNotifyWnd@@@std@@@std@@YA$$QAV?$shared_ptr@VCShellTrayNotifyWnd@@@0@AAV10@@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$move@AAV?$shared_ptr@VCShellTrayNotifyWnd@@@std@@@std@@YA$$QAV?$shared_ptr@VCShellTrayNotifyWnd@@@0@AAV10@@Z PROC ; std::move<std::shared_ptr<CShellTrayNotifyWnd> &>, COMDAT

; 1588 : 	{	// forward _Arg as movable

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1589 : 	return (static_cast<remove_reference_t<_Ty>&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1590 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$move@AAV?$shared_ptr@VCShellTrayNotifyWnd@@@std@@@std@@YA$$QAV?$shared_ptr@VCShellTrayNotifyWnd@@@0@AAV10@@Z ENDP ; std::move<std::shared_ptr<CShellTrayNotifyWnd> &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??$_Move_construct_from@VCShellTrayNotifyWnd@@@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEX$$QAV01@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Right$ = 8						; size = 4
??$_Move_construct_from@VCShellTrayNotifyWnd@@@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEX$$QAV01@@Z PROC ; std::_Ptr_base<CShellTrayNotifyWnd>::_Move_construct_from<CShellTrayNotifyWnd>, COMDAT
; _this$ = ecx

; 1068 : 		{	// implement shared_ptr's (converting) move ctor and weak_ptr's move ctor

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1069 : 		_Ptr = _Right._Ptr;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  00014	8b 11		 mov	 edx, DWORD PTR [ecx]
  00016	89 10		 mov	 DWORD PTR [eax], edx

; 1070 : 		_Rep = _Right._Rep;

  00018	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001b	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  0001e	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  00021	89 50 04	 mov	 DWORD PTR [eax+4], edx

; 1071 : 
; 1072 : 		_Right._Ptr = nullptr;

  00024	8b 45 08	 mov	 eax, DWORD PTR __Right$[ebp]
  00027	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 1073 : 		_Right._Rep = nullptr;

  0002d	8b 4d 08	 mov	 ecx, DWORD PTR __Right$[ebp]
  00030	c7 41 04 00 00
	00 00		 mov	 DWORD PTR [ecx+4], 0

; 1074 : 		}

  00037	8b e5		 mov	 esp, ebp
  00039	5d		 pop	 ebp
  0003a	c2 04 00	 ret	 4
??$_Move_construct_from@VCShellTrayNotifyWnd@@@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEX$$QAV01@@Z ENDP ; std::_Ptr_base<CShellTrayNotifyWnd>::_Move_construct_from<CShellTrayNotifyWnd>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\utility
;	COMDAT ??$swap@PAVCShellTrayNotifyWnd@@X@std@@YAXAAPAVCShellTrayNotifyWnd@@0@Z
_TEXT	SEGMENT
__Tmp$ = -8						; size = 4
__Left$ = 8						; size = 4
__Right$ = 12						; size = 4
??$swap@PAVCShellTrayNotifyWnd@@X@std@@YAXAAPAVCShellTrayNotifyWnd@@0@Z PROC ; std::swap<CShellTrayNotifyWnd *,void>, COMDAT

; 66   : 	{	// exchange values stored at _Left and _Right

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 0c	 sub	 esp, 12			; 0000000cH
  00006	c7 45 f4 cc cc
	cc cc		 mov	 DWORD PTR [ebp-12], -858993460 ; ccccccccH
  0000d	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  00014	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 67   : 	_Ty _Tmp = _STD move(_Left);

  0001b	8b 45 08	 mov	 eax, DWORD PTR __Left$[ebp]
  0001e	50		 push	 eax
  0001f	e8 00 00 00 00	 call	 ??$move@AAPAVCShellTrayNotifyWnd@@@std@@YA$$QAPAVCShellTrayNotifyWnd@@AAPAV1@@Z ; std::move<CShellTrayNotifyWnd * &>
  00024	83 c4 04	 add	 esp, 4
  00027	8b 08		 mov	 ecx, DWORD PTR [eax]
  00029	89 4d f8	 mov	 DWORD PTR __Tmp$[ebp], ecx

; 68   : 	_Left = _STD move(_Right);

  0002c	8b 55 0c	 mov	 edx, DWORD PTR __Right$[ebp]
  0002f	52		 push	 edx
  00030	e8 00 00 00 00	 call	 ??$move@AAPAVCShellTrayNotifyWnd@@@std@@YA$$QAPAVCShellTrayNotifyWnd@@AAPAV1@@Z ; std::move<CShellTrayNotifyWnd * &>
  00035	83 c4 04	 add	 esp, 4
  00038	8b 4d 08	 mov	 ecx, DWORD PTR __Left$[ebp]
  0003b	8b 10		 mov	 edx, DWORD PTR [eax]
  0003d	89 11		 mov	 DWORD PTR [ecx], edx

; 69   : 	_Right = _STD move(_Tmp);

  0003f	8d 45 f8	 lea	 eax, DWORD PTR __Tmp$[ebp]
  00042	50		 push	 eax
  00043	e8 00 00 00 00	 call	 ??$move@AAPAVCShellTrayNotifyWnd@@@std@@YA$$QAPAVCShellTrayNotifyWnd@@AAPAV1@@Z ; std::move<CShellTrayNotifyWnd * &>
  00048	83 c4 04	 add	 esp, 4
  0004b	8b 4d 0c	 mov	 ecx, DWORD PTR __Right$[ebp]
  0004e	8b 10		 mov	 edx, DWORD PTR [eax]
  00050	89 11		 mov	 DWORD PTR [ecx], edx

; 70   : 	}

  00052	52		 push	 edx
  00053	8b cd		 mov	 ecx, ebp
  00055	50		 push	 eax
  00056	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN5@swap
  0005c	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00061	58		 pop	 eax
  00062	5a		 pop	 edx
  00063	83 c4 0c	 add	 esp, 12			; 0000000cH
  00066	3b ec		 cmp	 ebp, esp
  00068	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0006d	8b e5		 mov	 esp, ebp
  0006f	5d		 pop	 ebp
  00070	c3		 ret	 0
  00071	0f 1f 00	 npad	 3
$LN5@swap:
  00074	01 00 00 00	 DD	 1
  00078	00 00 00 00	 DD	 $LN4@swap
$LN4@swap:
  0007c	f8 ff ff ff	 DD	 -8			; fffffff8H
  00080	04 00 00 00	 DD	 4
  00084	00 00 00 00	 DD	 $LN3@swap
$LN3@swap:
  00088	5f		 DB	 95			; 0000005fH
  00089	54		 DB	 84			; 00000054H
  0008a	6d		 DB	 109			; 0000006dH
  0008b	70		 DB	 112			; 00000070H
  0008c	00		 DB	 0
??$swap@PAVCShellTrayNotifyWnd@@X@std@@YAXAAPAVCShellTrayNotifyWnd@@0@Z ENDP ; std::swap<CShellTrayNotifyWnd *,void>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?_Delete_this@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ
_TEXT	SEGMENT
tv74 = -16						; size = 4
$T1 = -12						; size = 4
$T2 = -8						; size = 4
_this$ = -4						; size = 4
?_Delete_this@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ PROC ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Delete_this, COMDAT
; _this$ = ecx

; 1816 : 		{	// destroy self

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 10	 sub	 esp, 16			; 00000010H
  00006	56		 push	 esi
  00007	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0000f	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00012	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00015	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  00018	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1817 : 		delete this;

  0001b	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001e	89 45 f4	 mov	 DWORD PTR $T1[ebp], eax
  00021	8b 4d f4	 mov	 ecx, DWORD PTR $T1[ebp]
  00024	89 4d f8	 mov	 DWORD PTR $T2[ebp], ecx
  00027	83 7d f8 00	 cmp	 DWORD PTR $T2[ebp], 0
  0002b	74 1d		 je	 SHORT $LN3@Delete_thi
  0002d	8b f4		 mov	 esi, esp
  0002f	6a 01		 push	 1
  00031	8b 55 f8	 mov	 edx, DWORD PTR $T2[ebp]
  00034	8b 02		 mov	 eax, DWORD PTR [edx]
  00036	8b 4d f8	 mov	 ecx, DWORD PTR $T2[ebp]
  00039	8b 50 08	 mov	 edx, DWORD PTR [eax+8]
  0003c	ff d2		 call	 edx
  0003e	3b f4		 cmp	 esi, esp
  00040	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00045	89 45 f0	 mov	 DWORD PTR tv74[ebp], eax
  00048	eb 07		 jmp	 SHORT $LN1@Delete_thi
$LN3@Delete_thi:
  0004a	c7 45 f0 00 00
	00 00		 mov	 DWORD PTR tv74[ebp], 0
$LN1@Delete_thi:

; 1818 : 		}

  00051	5e		 pop	 esi
  00052	83 c4 10	 add	 esp, 16			; 00000010H
  00055	3b ec		 cmp	 ebp, esp
  00057	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005c	8b e5		 mov	 esp, ebp
  0005e	5d		 pop	 ebp
  0005f	c3		 ret	 0
?_Delete_this@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ ENDP ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Delete_this
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ
_TEXT	SEGMENT
tv71 = -20						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ PROC ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Destroy, COMDAT
; _this$ = ecx

; 1811 : 		{	// destroy managed resource

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 08	 sub	 esp, 8
  0001b	56		 push	 esi
  0001c	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00023	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  0002a	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1812 : 		_Getptr()->~_Ty();

  0002d	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00030	e8 00 00 00 00	 call	 ?_Getptr@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAEPAVCShellTrayNotifyWnd@@XZ ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Getptr
  00035	89 45 ec	 mov	 DWORD PTR tv71[ebp], eax
  00038	8b f4		 mov	 esi, esp
  0003a	6a 00		 push	 0
  0003c	8b 45 ec	 mov	 eax, DWORD PTR tv71[ebp]
  0003f	8b 10		 mov	 edx, DWORD PTR [eax]
  00041	8b 4d ec	 mov	 ecx, DWORD PTR tv71[ebp]
  00044	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  00047	ff d0		 call	 eax
  00049	3b f4		 cmp	 esi, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 1813 : 		}

  00050	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00053	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0005a	5e		 pop	 esi
  0005b	83 c4 14	 add	 esp, 20			; 00000014H
  0005e	3b ec		 cmp	 ebp, esp
  00060	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00065	8b e5		 mov	 esp, ebp
  00067	5d		 pop	 ebp
  00068	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?_Destroy@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@EAEXXZ ENDP ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Destroy
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?_Getptr@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAEPAVCShellTrayNotifyWnd@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?_Getptr@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAEPAVCShellTrayNotifyWnd@@XZ PROC ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Getptr, COMDAT
; _this$ = ecx

; 1805 : 		{	// get pointer

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1806 : 		return (reinterpret_cast<_Ty *>(&_Storage));

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	83 c0 10	 add	 eax, 16			; 00000010H

; 1807 : 		}

  00014	8b e5		 mov	 esp, ebp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?_Getptr@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAEPAVCShellTrayNotifyWnd@@XZ ENDP ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Getptr
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwin.h
;	COMDAT ?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEJIIJ@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_uMsg$ = 8						; size = 4
_wParam$ = 12						; size = 4
_lParam$ = 16						; size = 4
?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEJIIJ@Z PROC ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::DefWindowProcW, COMDAT
; _this$ = ecx

; 3494 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 3495 : #ifdef STRICT
; 3496 : 		return ::CallWindowProc(m_pfnSuperWindowProc, this->m_hWnd, uMsg, wParam, lParam);

  0000f	8b f4		 mov	 esi, esp
  00011	8b 45 10	 mov	 eax, DWORD PTR _lParam$[ebp]
  00014	50		 push	 eax
  00015	8b 4d 0c	 mov	 ecx, DWORD PTR _wParam$[ebp]
  00018	51		 push	 ecx
  00019	8b 55 08	 mov	 edx, DWORD PTR _uMsg$[ebp]
  0001c	52		 push	 edx
  0001d	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00020	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  00023	51		 push	 ecx
  00024	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00027	8b 42 20	 mov	 eax, DWORD PTR [edx+32]
  0002a	50		 push	 eax
  0002b	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__CallWindowProcW@20
  00031	3b f4		 cmp	 esi, esp
  00033	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 3497 : #else
; 3498 : 		return ::CallWindowProc((FARPROC)m_pfnSuperWindowProc, this->m_hWnd, uMsg, wParam, lParam);
; 3499 : #endif
; 3500 : 	}

  00038	5e		 pop	 esi
  00039	83 c4 04	 add	 esp, 4
  0003c	3b ec		 cmp	 ebp, esp
  0003e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00043	8b e5		 mov	 esp, ebp
  00045	5d		 pop	 ebp
  00046	c2 0c 00	 ret	 12			; 0000000cH
?DefWindowProcW@?$CWindowImplBaseT@VCWindow@ATL@@V?$CWinTraits@$0MIAAAA@$0IA@@2@@ATL@@QAEJIIJ@Z ENDP ; ATL::CWindowImplBaseT<ATL::CWindow,ATL::CWinTraits<13107200,128> >::DefWindowProcW
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ
_TEXT	SEGMENT
$T2 = -20						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ PROC ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Ref_count_obj<CShellTrayNotifyWnd><>, COMDAT
; _this$ = ecx

; 1800 : 		{	// construct from argument list

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 08	 sub	 esp, 8
  0001b	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00022	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00029	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1799 : 		: _Ref_count_base()

  0002c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002f	e8 00 00 00 00	 call	 ??0_Ref_count_base@std@@IAE@XZ ; std::_Ref_count_base::_Ref_count_base
  00034	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 1800 : 		{	// construct from argument list

  0003b	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003e	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@6B@

; 1801 : 		::new (static_cast<void *>(&_Storage)) _Ty(_STD forward<_Types>(_Args)...);

  00044	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00047	83 c1 10	 add	 ecx, 16			; 00000010H
  0004a	51		 push	 ecx
  0004b	68 58 02 00 00	 push	 600			; 00000258H
  00050	e8 00 00 00 00	 call	 ??2@YAPAXIPAX@Z		; operator new
  00055	83 c4 08	 add	 esp, 8
  00058	89 45 ec	 mov	 DWORD PTR $T2[ebp], eax
  0005b	8b 4d ec	 mov	 ecx, DWORD PTR $T2[ebp]
  0005e	e8 00 00 00 00	 call	 ??0CShellTrayNotifyWnd@@QAE@XZ ; CShellTrayNotifyWnd::CShellTrayNotifyWnd

; 1802 : 		}

  00063	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0006a	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0006d	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00070	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00077	83 c4 14	 add	 esp, 20			; 00000014H
  0007a	3b ec		 cmp	 ebp, esp
  0007c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00081	8b e5		 mov	 esp, ebp
  00083	5d		 pop	 ebp
  00084	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1_Ref_count_base@std@@UAE@XZ ; std::_Ref_count_base::~_Ref_count_base
__ehhandler$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??$?0$$V@?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@QAE@XZ ENDP ; std::_Ref_count_obj<CShellTrayNotifyWnd>::_Ref_count_obj<CShellTrayNotifyWnd><>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??$_Set_ptr_rep_and_enable_shared@VCShellTrayNotifyWnd@@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@AAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Px$ = 8						; size = 4
__Rx$ = 12						; size = 4
??$_Set_ptr_rep_and_enable_shared@VCShellTrayNotifyWnd@@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@AAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@1@@Z PROC ; std::shared_ptr<CShellTrayNotifyWnd>::_Set_ptr_rep_and_enable_shared<CShellTrayNotifyWnd>, COMDAT
; _this$ = ecx

; 1581 : 		{	// take ownership of _Px

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1582 : 		this->_Set_ptr_rep(_Px, _Rx);

  0000e	8b 45 0c	 mov	 eax, DWORD PTR __Rx$[ebp]
  00011	50		 push	 eax
  00012	8b 4d 08	 mov	 ecx, DWORD PTR __Px$[ebp]
  00015	51		 push	 ecx
  00016	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00019	e8 00 00 00 00	 call	 ?_Set_ptr_rep@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@2@@Z ; std::_Ptr_base<CShellTrayNotifyWnd>::_Set_ptr_rep

; 1583 : 		_Enable_shared_from_this(*this, _Px);

  0001e	8b 55 08	 mov	 edx, DWORD PTR __Px$[ebp]
  00021	52		 push	 edx
  00022	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00025	50		 push	 eax
  00026	e8 00 00 00 00	 call	 ??$_Enable_shared_from_this@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@@Z ; std::_Enable_shared_from_this<CShellTrayNotifyWnd,CShellTrayNotifyWnd>
  0002b	83 c4 08	 add	 esp, 8

; 1584 : 		}

  0002e	83 c4 04	 add	 esp, 4
  00031	3b ec		 cmp	 ebp, esp
  00033	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00038	8b e5		 mov	 esp, ebp
  0003a	5d		 pop	 ebp
  0003b	c2 08 00	 ret	 8
??$_Set_ptr_rep_and_enable_shared@VCShellTrayNotifyWnd@@@?$shared_ptr@VCShellTrayNotifyWnd@@@std@@AAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@1@@Z ENDP ; std::shared_ptr<CShellTrayNotifyWnd>::_Set_ptr_rep_and_enable_shared<CShellTrayNotifyWnd>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\type_traits
;	COMDAT ??$move@AAPAVCShellTrayNotifyWnd@@@std@@YA$$QAPAVCShellTrayNotifyWnd@@AAPAV1@@Z
_TEXT	SEGMENT
__Arg$ = 8						; size = 4
??$move@AAPAVCShellTrayNotifyWnd@@@std@@YA$$QAPAVCShellTrayNotifyWnd@@AAPAV1@@Z PROC ; std::move<CShellTrayNotifyWnd * &>, COMDAT

; 1588 : 	{	// forward _Arg as movable

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1589 : 	return (static_cast<remove_reference_t<_Ty>&&>(_Arg));

  00003	8b 45 08	 mov	 eax, DWORD PTR __Arg$[ebp]

; 1590 : 	}

  00006	5d		 pop	 ebp
  00007	c3		 ret	 0
??$move@AAPAVCShellTrayNotifyWnd@@@std@@YA$$QAPAVCShellTrayNotifyWnd@@AAPAV1@@Z ENDP ; std::move<CShellTrayNotifyWnd * &>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_G?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___flags$ = 8						; size = 4
??_G?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAEPAXI@Z PROC ; std::_Ref_count_obj<CShellTrayNotifyWnd>::`scalar deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ
  00016	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  00019	83 e0 01	 and	 eax, 1
  0001c	74 11		 je	 SHORT $LN2@scalar
  0001e	68 68 02 00 00	 push	 616			; 00000268H
  00023	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	51		 push	 ecx
  00027	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  0002c	83 c4 08	 add	 esp, 8
$LN2@scalar:
  0002f	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00032	83 c4 04	 add	 esp, 4
  00035	3b ec		 cmp	 ebp, esp
  00037	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003c	8b e5		 mov	 esp, ebp
  0003e	5d		 pop	 ebp
  0003f	c2 04 00	 ret	 4
??_G?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAEPAXI@Z ENDP ; std::_Ref_count_obj<CShellTrayNotifyWnd>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ PROC ; std::_Ref_count_obj<CShellTrayNotifyWnd>::~_Ref_count_obj<CShellTrayNotifyWnd>, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0002a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	e8 00 00 00 00	 call	 ??1_Ref_count_base@std@@UAE@XZ ; std::_Ref_count_base::~_Ref_count_base
  00039	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003c	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00043	83 c4 10	 add	 esp, 16			; 00000010H
  00046	3b ec		 cmp	 ebp, esp
  00048	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004d	8b e5		 mov	 esp, ebp
  0004f	5d		 pop	 ebp
  00050	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1_Ref_count_base@std@@UAE@XZ ; std::_Ref_count_base::~_Ref_count_base
__ehhandler$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$_Ref_count_obj@VCShellTrayNotifyWnd@@@std@@UAE@XZ ENDP ; std::_Ref_count_obj<CShellTrayNotifyWnd>::~_Ref_count_obj<CShellTrayNotifyWnd>
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ?_Set_ptr_rep@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@2@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Other_ptr$ = 8					; size = 4
__Other_rep$ = 12					; size = 4
?_Set_ptr_rep@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@2@@Z PROC ; std::_Ptr_base<CShellTrayNotifyWnd>::_Set_ptr_rep, COMDAT
; _this$ = ecx

; 1131 : 		{	// take new resource

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1132 : 		_Ptr = _Other_ptr;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Other_ptr$[ebp]
  00014	89 08		 mov	 DWORD PTR [eax], ecx

; 1133 : 		_Rep = _Other_rep;

  00016	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00019	8b 45 0c	 mov	 eax, DWORD PTR __Other_rep$[ebp]
  0001c	89 42 04	 mov	 DWORD PTR [edx+4], eax

; 1134 : 		}

  0001f	8b e5		 mov	 esp, ebp
  00021	5d		 pop	 ebp
  00022	c2 08 00	 ret	 8
?_Set_ptr_rep@?$_Ptr_base@VCShellTrayNotifyWnd@@@std@@IAEXPAVCShellTrayNotifyWnd@@PAV_Ref_count_base@2@@Z ENDP ; std::_Ptr_base<CShellTrayNotifyWnd>::_Set_ptr_rep
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??$_Enable_shared_from_this@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@@Z
_TEXT	SEGMENT
$T1 = -1						; size = 1
__This$ = 8						; size = 4
__Ptr$ = 12						; size = 4
??$_Enable_shared_from_this@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@@Z PROC ; std::_Enable_shared_from_this<CShellTrayNotifyWnd,CShellTrayNotifyWnd>, COMDAT

; 1028 : 	{	// possibly enable shared_from_this

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 1029 : 	_Enable_shared_from_this1(_This, _Ptr, bool_constant<conjunction_v<

  0000b	33 c0		 xor	 eax, eax
  0000d	88 45 ff	 mov	 BYTE PTR $T1[ebp], al
  00010	0f b6 4d ff	 movzx	 ecx, BYTE PTR $T1[ebp]
  00014	51		 push	 ecx
  00015	8b 55 0c	 mov	 edx, DWORD PTR __Ptr$[ebp]
  00018	52		 push	 edx
  00019	8b 45 08	 mov	 eax, DWORD PTR __This$[ebp]
  0001c	50		 push	 eax
  0001d	e8 00 00 00 00	 call	 ??$_Enable_shared_from_this1@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@U?$integral_constant@_N$0A@@0@@Z ; std::_Enable_shared_from_this1<CShellTrayNotifyWnd,CShellTrayNotifyWnd>
  00022	83 c4 0c	 add	 esp, 12			; 0000000cH

; 1030 : 		negation<is_array<_Other>>,
; 1031 : 		negation<is_volatile<_Yty>>,
; 1032 : 		_Can_enable_shared<_Yty>>>{});
; 1033 : 	}

  00025	83 c4 04	 add	 esp, 4
  00028	3b ec		 cmp	 ebp, esp
  0002a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002f	8b e5		 mov	 esp, ebp
  00031	5d		 pop	 ebp
  00032	c3		 ret	 0
??$_Enable_shared_from_this@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@@Z ENDP ; std::_Enable_shared_from_this<CShellTrayNotifyWnd,CShellTrayNotifyWnd>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\memory
;	COMDAT ??$_Enable_shared_from_this1@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@U?$integral_constant@_N$0A@@0@@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
___formal$ = 12						; size = 4
___formal$ = 16						; size = 1
??$_Enable_shared_from_this1@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@U?$integral_constant@_N$0A@@0@@Z PROC ; std::_Enable_shared_from_this1<CShellTrayNotifyWnd,CShellTrayNotifyWnd>, COMDAT

; 1022 : 	{	// don't enable shared_from_this

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1023 : 	}

  00003	5d		 pop	 ebp
  00004	c3		 ret	 0
??$_Enable_shared_from_this1@VCShellTrayNotifyWnd@@V1@@std@@YAXABV?$shared_ptr@VCShellTrayNotifyWnd@@@0@PAVCShellTrayNotifyWnd@@U?$integral_constant@_N$0A@@0@@Z ENDP ; std::_Enable_shared_from_this1<CShellTrayNotifyWnd,CShellTrayNotifyWnd>
_TEXT	ENDS
END
