; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
_BSS	ENDS
$SG4294356998 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294356996 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294356997 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294356994 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356995 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294356992 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356993 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294356982 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294356983 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294356980 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294356981 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294356978 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294356979 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294356976 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294356977 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294356990 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356991 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294356988 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356989 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356986 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356987 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294356984 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
	ORG $+2
$SG4294356985 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294356966 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356967 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356964 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356965 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294356962 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356963 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356960 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356961 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356974 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294356975 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356972 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356973 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356970 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356971 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356968 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356969 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356950 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356951 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356948 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294356949 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294356946 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356947 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294356944 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294356945 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356958 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356959 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356956 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356957 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356954 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356955 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356952 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356953 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356934 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294356935 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294356932 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356933 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356930 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356931 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294356928 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294356929 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356942 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356943 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356940 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356941 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356938 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356939 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294356936 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294356937 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356918 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356919 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356916 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356917 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356914 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356915 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356912 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294356913 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356926 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356927 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356924 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294356925 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294356922 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356923 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294356920 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356921 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356902 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294356903 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294356900 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294356901 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294356898 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294356899 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294356896 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294356897 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294356910 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294356911 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294356908 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294356909 DB 00H, 00H
	ORG $+2
$SG4294356906 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294356907 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294356904 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294356905 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294356886 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294356887 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294356884 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294356885 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294356882 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294356883 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294356880 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294356881 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294356894 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294356895 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294356892 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294356893 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294356890 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294356891 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294356888 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294356889 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294356870 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294356871 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294356868 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356869 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294356866 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294356867 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294356864 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294356865 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294356878 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294356879 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294356876 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294356877 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294356874 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294356875 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294356872 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294356873 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294356854 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294356855 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294356852 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356853 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294356850 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356851 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294356848 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356849 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356862 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294356863 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294356860 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294356861 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294356858 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294356859 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356856 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294356857 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294356838 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356839 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294356836 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356837 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356834 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294356835 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356832 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294356833 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294356846 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356847 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356844 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356845 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356842 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356843 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294356840 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294356841 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294356822 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356823 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294356820 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356821 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294356818 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294356819 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294356816 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356817 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356830 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294356831 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294356828 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294356829 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294356826 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294356827 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294356824 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294356825 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294356806 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294356807 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356804 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294356805 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294356802 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294356803 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294356800 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294356801 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294356814 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294356815 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294356812 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294356813 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294356810 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294356811 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294356808 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294356809 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356790 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294356791 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356788 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294356789 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294356786 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356787 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294356784 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294356785 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356798 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294356799 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356796 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294356797 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294356794 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294356795 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294356792 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356793 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294356774 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294356775 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294356772 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356773 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294356770 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294356771 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294356768 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294356769 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294356782 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294356783 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294356780 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356781 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356778 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294356779 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294356776 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356777 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294356758 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294356759 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294356756 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294356757 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294356754 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294356755 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294356752 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294356753 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294356766 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294356767 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294356764 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294356765 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294356762 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294356763 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294356760 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294356761 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294356742 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294356743 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294356740 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294356741 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294356738 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294356739 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294356736 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294356737 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294356750 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294356751 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294356748 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356749 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356746 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294356747 DB 00H, 00H
	ORG $+2
$SG4294356744 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356745 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294356726 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356727 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294356724 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356725 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356722 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294356723 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294356720 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356721 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294356734 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294356735 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294356732 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294356733 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294356730 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294356731 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294356728 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294356729 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294356710 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294356711 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356708 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294356709 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294356706 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294356707 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294356704 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356705 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294356718 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356719 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356716 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356717 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294356714 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294356715 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294356712 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294356713 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356694 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294356695 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294356692 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294356693 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294356690 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294356691 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294356688 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294356689 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294356702 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294356703 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356700 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294356701 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356698 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294356699 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294356696 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294356697 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294356678 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294356679 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294356676 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294356677 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294356674 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294356675 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294356672 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294356673 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294356686 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294356687 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356684 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294356685 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294356682 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294356683 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294356680 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294356681 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294356662 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294356663 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356660 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294356661 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294356658 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294356659 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356656 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294356657 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356670 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294356671 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294356668 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294356669 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294356666 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294356667 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356664 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294356665 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356646 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356647 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356644 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294356645 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294356642 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356643 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356640 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294356641 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294356654 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294356655 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294356652 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294356653 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294356650 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356651 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294356648 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356649 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294356630 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294356631 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356628 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356629 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294356626 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356627 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294356624 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356625 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294356638 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294356639 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356636 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294356637 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294356634 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294356635 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294356632 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294356633 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356614 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356615 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294356612 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294356613 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294356610 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294356611 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294356608 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294356609 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356622 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356623 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294356620 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356621 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356618 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356619 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294356616 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294356617 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294356598 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294356599 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294356596 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294356597 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356594 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294356595 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294356592 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294356593 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356606 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356607 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356604 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356605 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294356602 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294356603 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356600 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294356601 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294356582 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356583 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356580 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294356581 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294356578 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294356579 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294356576 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356577 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356590 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356591 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356588 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294356589 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356586 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294356587 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356584 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356585 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294356566 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356567 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356564 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356565 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356562 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294356563 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356560 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356561 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294356574 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294356575 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294356572 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356573 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294356570 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356571 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294356568 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356569 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294356550 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294356551 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294356548 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294356549 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356546 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356547 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356544 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294356545 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294356558 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356559 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356556 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356557 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356554 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294356555 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294356552 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294356553 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294356534 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294356535 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356532 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294356533 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294356530 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294356531 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294356528 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294356529 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294356542 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294356543 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294356540 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294356541 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294356538 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294356539 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294356536 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294356537 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294356526 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356527 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356524 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294356525 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294356522 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294356523 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294356520 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294356521 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294356486 DB 'M', 00H, 00H, 00H
$SG4294356487 DB 'S', 00H, 00H, 00H
$SG4294356484 DB 'B', 00H, 00H, 00H
$SG4294356485 DB 'D', 00H, 00H, 00H
$SG4294356482 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294356483 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294356480 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356481 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356470 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294356471 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294356468 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294356469 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356478 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294356479 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294356476 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356477 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294356474 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356475 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294356472 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294356473 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294356436 DB 00H, 00H
	ORG $+2
$SG4294356437 DB ':', 00H, 00H, 00H
$SG4294356435 DB 00H, 00H
	ORG $+2
$SG4294356344 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294355630 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294355631 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
PUBLIC	?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z ; JsApi::GlobalExtraOptions::LoadScreenSize
PUBLIC	?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z ; JsApi::GlobalExtraOptions::SetProxy
PUBLIC	?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z ; JsApi::GlobalExtraOptions::SetProxy
PUBLIC	?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z ; JsApi::GlobalExtraOptions::SetFlashCachePath
PUBLIC	?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z ; JsApi::GlobalExtraOptions::SetCookieAndLocalStoragePath
PUBLIC	?DisableFlash@GlobalExtraOptions@JsApi@@QAE_N_N@Z ; JsApi::GlobalExtraOptions::DisableFlash
PUBLIC	?HardwareConcurrency@GlobalExtraOptions@JsApi@@QAEHXZ ; JsApi::GlobalExtraOptions::HardwareConcurrency
PUBLIC	?SetHardwareConcurrency@GlobalExtraOptions@JsApi@@QAEHH@Z ; JsApi::GlobalExtraOptions::SetHardwareConcurrency
PUBLIC	?EnableConsoleLog@GlobalExtraOptions@JsApi@@QAE_N_N@Z ; JsApi::GlobalExtraOptions::EnableConsoleLog
PUBLIC	?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; JsApi::GlobalExtraOptions::AddPluginsDir
PUBLIC	??4ProxyData@JsApi@@QAEAAU01@ABU01@@Z		; JsApi::ProxyData::operator=
PUBLIC	??4_Atomic_int@std@@QAEHH@Z			; std::_Atomic_int::operator=
PUBLIC	?atomic_store@std@@YAXPAU_Atomic_int@1@H@Z	; std::atomic_store
PUBLIC	?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z ; std::atomic_store_explicit
PUBLIC	??4?$atomic@H@std@@QAEHH@Z			; std::atomic<int>::operator=
PUBLIC	?_Atomic_store_4@std@@YAXPCKKW4memory_order@1@@Z ; std::_Atomic_store_4
PUBLIC	?_Store_seq_cst_4@std@@YAXPCKK@Z		; std::_Store_seq_cst_4
PUBLIC	?_Store_release_4@std@@YAXPCKK@Z		; std::_Store_release_4
PUBLIC	?_Store_relaxed_4@std@@YAXPCKK@Z		; std::_Store_relaxed_4
PUBLIC	?SetNewWindowEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z ; JsApi::GlobalExtraOptions::SetNewWindowEnabled
PUBLIC	?SetCspCheckEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z ; JsApi::GlobalExtraOptions::SetCspCheckEnabled
PUBLIC	?SetTouchEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z ; JsApi::GlobalExtraOptions::SetTouchEnabled
PUBLIC	?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z ; JsApi::GlobalExtraOptions::SetScreenSize
PUBLIC	?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z	; JsApi::GlobalExtraOptions::Apply
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__ehfuncinfo$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z$0
__ehfuncinfo$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z$0
__ehfuncinfo$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z$0
__ehfuncinfo$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$2
	DD	00H
	DD	FLAT:__unwindfunclet$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$1
__ehfuncinfo$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$2
	DD	00H
	DD	FLAT:__unwindfunclet$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$0
	DD	01H
	DD	FLAT:__unwindfunclet$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$1
__ehfuncinfo$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0
__ehfuncinfo$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z$0
__ehfuncinfo$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z$0
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z$1
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z$2
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_lock$2 = -56						; size = 4
_lock$3 = -44						; size = 4
_n$4 = -36						; size = 4
_i$5 = -32						; size = 4
_lock$6 = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_webView$ = 8						; size = 4
?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z PROC		; JsApi::GlobalExtraOptions::Apply
; _this$ = ecx

; 7    : 	bool GlobalExtraOptions::Apply(_WebView webView) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 30	 sub	 esp, 48			; 00000030H
  0001b	56		 push	 esi
  0001c	57		 push	 edi
  0001d	51		 push	 ecx
  0001e	8d 7d c4	 lea	 edi, DWORD PTR [ebp-60]
  00021	b9 0c 00 00 00	 mov	 ecx, 12			; 0000000cH
  00026	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002b	f3 ab		 rep stosd
  0002d	59		 pop	 ecx
  0002e	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 8    : 		using namespace std;
; 9    : 
; 10   : 		//hardwareConcurrency
; 11   : 		{
; 12   : 			JsApi::SetHardwareConcurrency(m_hardwareConcurrency);

  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	83 c1 40	 add	 ecx, 64			; 00000040H
  00037	e8 00 00 00 00	 call	 ??B_Atomic_int@std@@QBEHXZ ; std::_Atomic_int::operator int
  0003c	50		 push	 eax
  0003d	e8 00 00 00 00	 call	 ?SetHardwareConcurrency@JsApi@@YA_NH@Z ; JsApi::SetHardwareConcurrency
  00042	83 c4 04	 add	 esp, 4

; 13   : 		}
; 14   : 
; 15   : 		//enableconsole
; 16   : 		{
; 17   : 			JsApi::EnableConsoleLog(m_bEnableConsoleLog);

  00045	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00048	83 c1 3c	 add	 ecx, 60			; 0000003cH
  0004b	e8 00 00 00 00	 call	 ??B_Atomic_bool@std@@QBE_NXZ ; std::_Atomic_bool::operator bool
  00050	0f b6 c0	 movzx	 eax, al
  00053	50		 push	 eax
  00054	e8 00 00 00 00	 call	 ?EnableConsoleLog@JsApi@@YA_N_N@Z ; JsApi::EnableConsoleLog
  00059	83 c4 04	 add	 esp, 4

; 18   : 		}
; 19   : 
; 20   : 		//pluginsdir
; 21   : 		{
; 22   : 			lock_guard<mutex> lock(m_mutex_pluginsdir);

  0005c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0005f	83 c1 0c	 add	 ecx, 12			; 0000000cH
  00062	51		 push	 ecx
  00063	8d 4d e8	 lea	 ecx, DWORD PTR _lock$6[ebp]
  00066	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  0006b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 23   : 			for (size_t i = 0, n = m_pluginsdir.size(); i < n; i++) {

  00072	c7 45 e0 00 00
	00 00		 mov	 DWORD PTR _i$5[ebp], 0
  00079	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0007c	e8 00 00 00 00	 call	 ?size@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QBEIXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::size
  00081	89 45 dc	 mov	 DWORD PTR _n$4[ebp], eax
  00084	eb 09		 jmp	 SHORT $LN4@Apply
$LN2@Apply:
  00086	8b 55 e0	 mov	 edx, DWORD PTR _i$5[ebp]
  00089	83 c2 01	 add	 edx, 1
  0008c	89 55 e0	 mov	 DWORD PTR _i$5[ebp], edx
$LN4@Apply:
  0008f	8b 45 e0	 mov	 eax, DWORD PTR _i$5[ebp]
  00092	3b 45 dc	 cmp	 eax, DWORD PTR _n$4[ebp]
  00095	73 17		 jae	 SHORT $LN3@Apply

; 24   : 				JsApi::AddPluginsDir(m_pluginsdir[i]);

  00097	8b 4d e0	 mov	 ecx, DWORD PTR _i$5[ebp]
  0009a	51		 push	 ecx
  0009b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0009e	e8 00 00 00 00	 call	 ??A?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEAAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@I@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::operator[]
  000a3	50		 push	 eax
  000a4	e8 00 00 00 00	 call	 ?AddPluginsDir@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; JsApi::AddPluginsDir
  000a9	83 c4 04	 add	 esp, 4

; 25   : 			}

  000ac	eb d8		 jmp	 SHORT $LN2@Apply
$LN3@Apply:

; 26   : 		}

  000ae	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000b5	8d 4d e8	 lea	 ecx, DWORD PTR _lock$6[ebp]
  000b8	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>

; 27   : 
; 28   : 		//cookie path
; 29   : 		{
; 30   : 			lock_guard<mutex> lock(m_mutex_CookieAndLocalStoragePath);

  000bd	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  000c0	83 c2 60	 add	 edx, 96			; 00000060H
  000c3	52		 push	 edx
  000c4	8d 4d d4	 lea	 ecx, DWORD PTR _lock$3[ebp]
  000c7	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  000cc	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 31   : 			JsApi::SetCookieAndLocalStoragePath(m_CookieAndLocalStoragePath);

  000d3	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  000d6	83 c0 48	 add	 eax, 72			; 00000048H
  000d9	50		 push	 eax
  000da	e8 00 00 00 00	 call	 ?SetCookieAndLocalStoragePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; JsApi::SetCookieAndLocalStoragePath
  000df	83 c4 04	 add	 esp, 4

; 32   : 		}

  000e2	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000e9	8d 4d d4	 lea	 ecx, DWORD PTR _lock$3[ebp]
  000ec	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>

; 33   : 
; 34   : 		//proxy
; 35   : 		{
; 36   : 			lock_guard<mutex> lock(m_mutex_proxy);

  000f1	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000f4	81 c1 28 01 00
	00		 add	 ecx, 296		; 00000128H
  000fa	51		 push	 ecx
  000fb	8d 4d c8	 lea	 ecx, DWORD PTR _lock$2[ebp]
  000fe	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00103	c7 45 fc 02 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 2

; 37   : 			JsApi::SetProxy(webView,m_proxy.type, m_proxy.hostname.c_str(), m_proxy.port, m_proxy.username.c_str(), m_proxy.password.c_str());

  0010a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0010d	81 c1 10 01 00
	00		 add	 ecx, 272		; 00000110H
  00113	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00118	50		 push	 eax
  00119	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0011c	81 c1 f8 00 00
	00		 add	 ecx, 248		; 000000f8H
  00122	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00127	50		 push	 eax
  00128	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0012b	0f b7 82 f4 00
	00 00		 movzx	 eax, WORD PTR [edx+244]
  00132	50		 push	 eax
  00133	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00136	81 c1 dc 00 00
	00		 add	 ecx, 220		; 000000dcH
  0013c	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBEPBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00141	50		 push	 eax
  00142	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00145	8b 91 d8 00 00
	00		 mov	 edx, DWORD PTR [ecx+216]
  0014b	52		 push	 edx
  0014c	8b 45 08	 mov	 eax, DWORD PTR _webView$[ebp]
  0014f	50		 push	 eax
  00150	e8 00 00 00 00	 call	 ?SetProxy@JsApi@@YA_NHW4mbProxyType@@PBDG11@Z ; JsApi::SetProxy
  00155	83 c4 18	 add	 esp, 24			; 00000018H

; 38   : 		}

  00158	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0015f	8d 4d c8	 lea	 ecx, DWORD PTR _lock$2[ebp]
  00162	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>

; 39   : 
; 40   : 		//TouchEnabled
; 41   : 		wkeSetTouchEnabled(nullptr, m_TouchEnabled);

  00167	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0016a	81 c1 a0 01 00
	00		 add	 ecx, 416		; 000001a0H
  00170	e8 00 00 00 00	 call	 ??B_Atomic_bool@std@@QBE_NXZ ; std::_Atomic_bool::operator bool
  00175	8b f4		 mov	 esi, esp
  00177	0f b6 c8	 movzx	 ecx, al
  0017a	51		 push	 ecx
  0017b	6a 00		 push	 0
  0017d	ff 15 00 00 00
	00		 call	 DWORD PTR ?wkeSetTouchEnabled@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetTouchEnabled
  00183	83 c4 08	 add	 esp, 8
  00186	3b f4		 cmp	 esi, esp
  00188	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 42   : 		
; 43   : 		//cspcheck
; 44   : 		wkeSetCspCheckEnable(nullptr, m_CspCheckEnabled);

  0018d	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00190	81 c1 a1 01 00
	00		 add	 ecx, 417		; 000001a1H
  00196	e8 00 00 00 00	 call	 ??B_Atomic_bool@std@@QBE_NXZ ; std::_Atomic_bool::operator bool
  0019b	8b f4		 mov	 esi, esp
  0019d	0f b6 d0	 movzx	 edx, al
  001a0	52		 push	 edx
  001a1	6a 00		 push	 0
  001a3	ff 15 00 00 00
	00		 call	 DWORD PTR ?wkeSetCspCheckEnable@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetCspCheckEnable
  001a9	83 c4 08	 add	 esp, 8
  001ac	3b f4		 cmp	 esi, esp
  001ae	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 45   : 
; 46   : 		//m_NewWindowEnabled
; 47   : 		wkeSetNavigationToNewWindowEnable(nullptr, m_NewWindowEnabled);

  001b3	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  001b6	81 c1 a2 01 00
	00		 add	 ecx, 418		; 000001a2H
  001bc	e8 00 00 00 00	 call	 ??B_Atomic_bool@std@@QBE_NXZ ; std::_Atomic_bool::operator bool
  001c1	8b f4		 mov	 esi, esp
  001c3	0f b6 c0	 movzx	 eax, al
  001c6	50		 push	 eax
  001c7	6a 00		 push	 0
  001c9	ff 15 00 00 00
	00		 call	 DWORD PTR ?wkeSetNavigationToNewWindowEnable@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetNavigationToNewWindowEnable
  001cf	83 c4 08	 add	 esp, 8
  001d2	3b f4		 cmp	 esi, esp
  001d4	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 48   : 
; 49   : 		//hook flash cache path
; 50   : 		JsApi::HookFlashCachePath(m_FlashCachePath);

  001d9	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  001dc	81 c1 90 00 00
	00		 add	 ecx, 144		; 00000090H
  001e2	51		 push	 ecx
  001e3	e8 00 00 00 00	 call	 ?HookFlashCachePath@JsApi@@YA_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; JsApi::HookFlashCachePath
  001e8	83 c4 04	 add	 esp, 4

; 51   : 
; 52   : 		return true;

  001eb	b0 01		 mov	 al, 1

; 53   : 	}

  001ed	52		 push	 edx
  001ee	8b cd		 mov	 ecx, ebp
  001f0	50		 push	 eax
  001f1	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN14@Apply
  001f7	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  001fc	58		 pop	 eax
  001fd	5a		 pop	 edx
  001fe	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00201	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00208	5f		 pop	 edi
  00209	5e		 pop	 esi
  0020a	83 c4 3c	 add	 esp, 60			; 0000003cH
  0020d	3b ec		 cmp	 ebp, esp
  0020f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00214	8b e5		 mov	 esp, ebp
  00216	5d		 pop	 ebp
  00217	c2 04 00	 ret	 4
  0021a	66 90		 npad	 2
$LN14@Apply:
  0021c	03 00 00 00	 DD	 3
  00220	00 00 00 00	 DD	 $LN13@Apply
$LN13@Apply:
  00224	e8 ff ff ff	 DD	 -24			; ffffffe8H
  00228	04 00 00 00	 DD	 4
  0022c	00 00 00 00	 DD	 $LN9@Apply
  00230	d4 ff ff ff	 DD	 -44			; ffffffd4H
  00234	04 00 00 00	 DD	 4
  00238	00 00 00 00	 DD	 $LN10@Apply
  0023c	c8 ff ff ff	 DD	 -56			; ffffffc8H
  00240	04 00 00 00	 DD	 4
  00244	00 00 00 00	 DD	 $LN11@Apply
$LN11@Apply:
  00248	6c		 DB	 108			; 0000006cH
  00249	6f		 DB	 111			; 0000006fH
  0024a	63		 DB	 99			; 00000063H
  0024b	6b		 DB	 107			; 0000006bH
  0024c	00		 DB	 0
$LN10@Apply:
  0024d	6c		 DB	 108			; 0000006cH
  0024e	6f		 DB	 111			; 0000006fH
  0024f	63		 DB	 99			; 00000063H
  00250	6b		 DB	 107			; 0000006bH
  00251	00		 DB	 0
$LN9@Apply:
  00252	6c		 DB	 108			; 0000006cH
  00253	6f		 DB	 111			; 0000006fH
  00254	63		 DB	 99			; 00000063H
  00255	6b		 DB	 107			; 0000006bH
  00256	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$6[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__unwindfunclet$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z$1:
  00008	8d 4d d4	 lea	 ecx, DWORD PTR _lock$3[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__unwindfunclet$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z$2:
  00010	8d 4d c8	 lea	 ecx, DWORD PTR _lock$2[ebp]
  00013	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__ehhandler$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z:
  00018	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z
  0001d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?Apply@GlobalExtraOptions@JsApi@@QAE_NH@Z ENDP		; JsApi::GlobalExtraOptions::Apply
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
$T2 = -29						; size = 1
_lock$ = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_width$ = 8						; size = 4
_height$ = 12						; size = 4
_availWidth$ = 16					; size = 4
_availHeight$ = 20					; size = 4
_pixelDepth$ = 24					; size = 4
?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z PROC ; JsApi::GlobalExtraOptions::SetScreenSize
; _this$ = ecx

; 121  : 	bool GlobalExtraOptions::SetScreenSize(int width, int height, int availWidth, int availHeight,int pixelDepth) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 14	 sub	 esp, 20			; 00000014H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00023	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00026	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00029	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0002c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0002f	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 122  : 		using namespace std;
; 123  : 		lock_guard<mutex> lock(m_mutex_screenSize);

  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	05 70 01 00 00	 add	 eax, 368		; 00000170H
  0003a	50		 push	 eax
  0003b	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  0003e	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00043	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 124  : 		if (availWidth < 0) availWidth = width;

  0004a	83 7d 10 00	 cmp	 DWORD PTR _availWidth$[ebp], 0
  0004e	7d 06		 jge	 SHORT $LN2@SetScreenS
  00050	8b 4d 08	 mov	 ecx, DWORD PTR _width$[ebp]
  00053	89 4d 10	 mov	 DWORD PTR _availWidth$[ebp], ecx
$LN2@SetScreenS:

; 125  : 		if (availHeight < 0) availHeight = height;

  00056	83 7d 14 00	 cmp	 DWORD PTR _availHeight$[ebp], 0
  0005a	7d 06		 jge	 SHORT $LN3@SetScreenS
  0005c	8b 55 0c	 mov	 edx, DWORD PTR _height$[ebp]
  0005f	89 55 14	 mov	 DWORD PTR _availHeight$[ebp], edx
$LN3@SetScreenS:

; 126  : 
; 127  : 		if (width >= 0) m_screenSize.width = width; 

  00062	83 7d 08 00	 cmp	 DWORD PTR _width$[ebp], 0
  00066	7c 0c		 jl	 SHORT $LN4@SetScreenS
  00068	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0006b	8b 4d 08	 mov	 ecx, DWORD PTR _width$[ebp]
  0006e	89 88 58 01 00
	00		 mov	 DWORD PTR [eax+344], ecx
$LN4@SetScreenS:

; 128  : 		if (height >= 0) m_screenSize.height = height;

  00074	83 7d 0c 00	 cmp	 DWORD PTR _height$[ebp], 0
  00078	7c 0c		 jl	 SHORT $LN5@SetScreenS
  0007a	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0007d	8b 45 0c	 mov	 eax, DWORD PTR _height$[ebp]
  00080	89 82 5c 01 00
	00		 mov	 DWORD PTR [edx+348], eax
$LN5@SetScreenS:

; 129  : 		if (availWidth >= 0) m_screenSize.availWidth = availWidth; 

  00086	83 7d 10 00	 cmp	 DWORD PTR _availWidth$[ebp], 0
  0008a	7c 0c		 jl	 SHORT $LN6@SetScreenS
  0008c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0008f	8b 55 10	 mov	 edx, DWORD PTR _availWidth$[ebp]
  00092	89 91 60 01 00
	00		 mov	 DWORD PTR [ecx+352], edx
$LN6@SetScreenS:

; 130  : 		if (availHeight >= 0) m_screenSize.availHeight = availHeight;

  00098	83 7d 14 00	 cmp	 DWORD PTR _availHeight$[ebp], 0
  0009c	7c 0c		 jl	 SHORT $LN7@SetScreenS
  0009e	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  000a1	8b 4d 14	 mov	 ecx, DWORD PTR _availHeight$[ebp]
  000a4	89 88 64 01 00
	00		 mov	 DWORD PTR [eax+356], ecx
$LN7@SetScreenS:

; 131  : 		if (pixelDepth >= 0) m_screenSize.pixelDepth = pixelDepth;

  000aa	83 7d 18 00	 cmp	 DWORD PTR _pixelDepth$[ebp], 0
  000ae	7c 0c		 jl	 SHORT $LN8@SetScreenS
  000b0	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  000b3	8b 45 18	 mov	 eax, DWORD PTR _pixelDepth$[ebp]
  000b6	89 82 68 01 00
	00		 mov	 DWORD PTR [edx+360], eax
$LN8@SetScreenS:

; 132  : 		return true;

  000bc	c6 45 e3 01	 mov	 BYTE PTR $T2[ebp], 1
  000c0	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000c7	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  000ca	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
  000cf	8a 45 e3	 mov	 al, BYTE PTR $T2[ebp]

; 133  : 	}

  000d2	52		 push	 edx
  000d3	8b cd		 mov	 ecx, ebp
  000d5	50		 push	 eax
  000d6	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN14@SetScreenS
  000dc	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000e1	58		 pop	 eax
  000e2	5a		 pop	 edx
  000e3	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000e6	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000ed	83 c4 20	 add	 esp, 32			; 00000020H
  000f0	3b ec		 cmp	 ebp, esp
  000f2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000f7	8b e5		 mov	 esp, ebp
  000f9	5d		 pop	 ebp
  000fa	c2 14 00	 ret	 20			; 00000014H
  000fd	0f 1f 00	 npad	 3
$LN14@SetScreenS:
  00100	01 00 00 00	 DD	 1
  00104	00 00 00 00	 DD	 $LN13@SetScreenS
$LN13@SetScreenS:
  00108	e8 ff ff ff	 DD	 -24			; ffffffe8H
  0010c	04 00 00 00	 DD	 4
  00110	00 00 00 00	 DD	 $LN11@SetScreenS
$LN11@SetScreenS:
  00114	6c		 DB	 108			; 0000006cH
  00115	6f		 DB	 111			; 0000006fH
  00116	63		 DB	 99			; 00000063H
  00117	6b		 DB	 107			; 0000006bH
  00118	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__ehhandler$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?SetScreenSize@GlobalExtraOptions@JsApi@@QAE_NHHHHH@Z ENDP ; JsApi::GlobalExtraOptions::SetScreenSize
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_enable$ = 8						; size = 1
?SetTouchEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z PROC ; JsApi::GlobalExtraOptions::SetTouchEnabled
; _this$ = ecx

; 146  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 147  : 		m_TouchEnabled = enable;

  0000e	0f b6 45 08	 movzx	 eax, BYTE PTR _enable$[ebp]
  00012	50		 push	 eax
  00013	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00016	81 c1 a0 01 00
	00		 add	 ecx, 416		; 000001a0H
  0001c	e8 00 00 00 00	 call	 ??4?$atomic@_N@std@@QAE_N_N@Z ; std::atomic<bool>::operator=

; 148  : 		return true;

  00021	b0 01		 mov	 al, 1

; 149  : 	}

  00023	83 c4 04	 add	 esp, 4
  00026	3b ec		 cmp	 ebp, esp
  00028	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002d	8b e5		 mov	 esp, ebp
  0002f	5d		 pop	 ebp
  00030	c2 04 00	 ret	 4
?SetTouchEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z ENDP ; JsApi::GlobalExtraOptions::SetTouchEnabled
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_enable$ = 8						; size = 1
?SetCspCheckEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z PROC ; JsApi::GlobalExtraOptions::SetCspCheckEnabled
; _this$ = ecx

; 152  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 153  : 		m_CspCheckEnabled = enable;

  0000e	0f b6 45 08	 movzx	 eax, BYTE PTR _enable$[ebp]
  00012	50		 push	 eax
  00013	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00016	81 c1 a1 01 00
	00		 add	 ecx, 417		; 000001a1H
  0001c	e8 00 00 00 00	 call	 ??4?$atomic@_N@std@@QAE_N_N@Z ; std::atomic<bool>::operator=

; 154  : 		return true;

  00021	b0 01		 mov	 al, 1

; 155  : 	}

  00023	83 c4 04	 add	 esp, 4
  00026	3b ec		 cmp	 ebp, esp
  00028	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002d	8b e5		 mov	 esp, ebp
  0002f	5d		 pop	 ebp
  00030	c2 04 00	 ret	 4
?SetCspCheckEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z ENDP ; JsApi::GlobalExtraOptions::SetCspCheckEnabled
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
_enable$ = 8						; size = 1
?SetNewWindowEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z PROC ; JsApi::GlobalExtraOptions::SetNewWindowEnabled
; _this$ = ecx

; 158  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 159  : 		m_NewWindowEnabled = enable;

  0000e	0f b6 45 08	 movzx	 eax, BYTE PTR _enable$[ebp]
  00012	50		 push	 eax
  00013	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00016	81 c1 a2 01 00
	00		 add	 ecx, 418		; 000001a2H
  0001c	e8 00 00 00 00	 call	 ??4?$atomic@_N@std@@QAE_N_N@Z ; std::atomic<bool>::operator=

; 160  : 		return true;

  00021	b0 01		 mov	 al, 1

; 161  : 	}

  00023	83 c4 04	 add	 esp, 4
  00026	3b ec		 cmp	 ebp, esp
  00028	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002d	8b e5		 mov	 esp, ebp
  0002f	5d		 pop	 ebp
  00030	c2 04 00	 ret	 4
?SetNewWindowEnabled@GlobalExtraOptions@JsApi@@QAE_N_N@Z ENDP ; JsApi::GlobalExtraOptions::SetNewWindowEnabled
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Store_relaxed_4@std@@YAXPCKK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
?_Store_relaxed_4@std@@YAXPCKK@Z PROC			; std::_Store_relaxed_4, COMDAT

; 1251 : 	{	/* store _Value atomically with relaxed memory order */

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1252 : 
; 1253 :  #if defined(_M_ARM) || defined(_M_ARM64)
; 1254 : 	__iso_volatile_store32((volatile int *)_Tgt, static_cast<int>(_Value));
; 1255 : 
; 1256 :  #else
; 1257 : 	*_Tgt = _Value;

  00003	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  00006	8b 4d 0c	 mov	 ecx, DWORD PTR __Value$[ebp]
  00009	89 08		 mov	 DWORD PTR [eax], ecx

; 1258 :  #endif
; 1259 : 	}

  0000b	5d		 pop	 ebp
  0000c	c3		 ret	 0
?_Store_relaxed_4@std@@YAXPCKK@Z ENDP			; std::_Store_relaxed_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Store_release_4@std@@YAXPCKK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
?_Store_release_4@std@@YAXPCKK@Z PROC			; std::_Store_release_4, COMDAT

; 1262 : 	{	/* store _Value atomically with release memory order */

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1263 : 
; 1264 :  #if defined(_M_ARM) || defined(_M_ARM64)
; 1265 : 	_Memory_barrier();
; 1266 : 	__iso_volatile_store32((volatile int *)_Tgt, static_cast<int>(_Value));
; 1267 : 
; 1268 :  #else
; 1269 : 	_Compiler_barrier();
; 1270 : 	*_Tgt = _Value;

  00003	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  00006	8b 4d 0c	 mov	 ecx, DWORD PTR __Value$[ebp]
  00009	89 08		 mov	 DWORD PTR [eax], ecx

; 1271 :  #endif
; 1272 : 	}

  0000b	5d		 pop	 ebp
  0000c	c3		 ret	 0
?_Store_release_4@std@@YAXPCKK@Z ENDP			; std::_Store_release_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Store_seq_cst_4@std@@YAXPCKK@Z
_TEXT	SEGMENT
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
?_Store_seq_cst_4@std@@YAXPCKK@Z PROC			; std::_Store_seq_cst_4, COMDAT

; 1275 : 	{	/* store _Value atomically with

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 1276 : 			sequentially consistent memory order */
; 1277 : 
; 1278 :  #if defined(_M_ARM) || defined(_M_ARM64)
; 1279 : 	_Memory_barrier();
; 1280 : 	__iso_volatile_store32((volatile int *)_Tgt, static_cast<int>(_Value));
; 1281 : 	_Memory_barrier();
; 1282 : 
; 1283 :  #else
; 1284 : 	_INTRIN_SEQ_CST(_InterlockedExchange)((volatile long *)_Tgt, static_cast<long>(_Value));

  00003	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00006	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  00009	87 01		 xchg	 DWORD PTR [ecx], eax

; 1285 :  #endif
; 1286 : 	}

  0000b	5d		 pop	 ebp
  0000c	c3		 ret	 0
?_Store_seq_cst_4@std@@YAXPCKK@Z ENDP			; std::_Store_seq_cst_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xatomic.h
;	COMDAT ?_Atomic_store_4@std@@YAXPCKKW4memory_order@1@@Z
_TEXT	SEGMENT
tv64 = -4						; size = 4
__Tgt$ = 8						; size = 4
__Value$ = 12						; size = 4
__Order$ = 16						; size = 4
?_Atomic_store_4@std@@YAXPCKKW4memory_order@1@@Z PROC	; std::_Atomic_store_4, COMDAT

; 1290 : 	{	/* store _Value atomically */

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 1291 : 	switch (_Order)

  0000b	8b 45 10	 mov	 eax, DWORD PTR __Order$[ebp]
  0000e	89 45 fc	 mov	 DWORD PTR tv64[ebp], eax
  00011	83 7d fc 00	 cmp	 DWORD PTR tv64[ebp], 0
  00015	74 0e		 je	 SHORT $LN4@Atomic_sto
  00017	83 7d fc 03	 cmp	 DWORD PTR tv64[ebp], 3
  0001b	74 1a		 je	 SHORT $LN5@Atomic_sto
  0001d	83 7d fc 05	 cmp	 DWORD PTR tv64[ebp], 5
  00021	74 26		 je	 SHORT $LN6@Atomic_sto
  00023	eb 34		 jmp	 SHORT $LN1@Atomic_sto
$LN4@Atomic_sto:

; 1292 : 		{
; 1293 : 		case memory_order_relaxed:
; 1294 : 			_Store_relaxed_4(_Tgt, _Value);

  00025	8b 4d 0c	 mov	 ecx, DWORD PTR __Value$[ebp]
  00028	51		 push	 ecx
  00029	8b 55 08	 mov	 edx, DWORD PTR __Tgt$[ebp]
  0002c	52		 push	 edx
  0002d	e8 00 00 00 00	 call	 ?_Store_relaxed_4@std@@YAXPCKK@Z ; std::_Store_relaxed_4
  00032	83 c4 08	 add	 esp, 8

; 1295 : 			break;

  00035	eb 22		 jmp	 SHORT $LN1@Atomic_sto
$LN5@Atomic_sto:

; 1296 : 
; 1297 : 		case memory_order_release:
; 1298 : 			_Store_release_4(_Tgt, _Value);

  00037	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  0003a	50		 push	 eax
  0003b	8b 4d 08	 mov	 ecx, DWORD PTR __Tgt$[ebp]
  0003e	51		 push	 ecx
  0003f	e8 00 00 00 00	 call	 ?_Store_release_4@std@@YAXPCKK@Z ; std::_Store_release_4
  00044	83 c4 08	 add	 esp, 8

; 1299 : 			break;

  00047	eb 10		 jmp	 SHORT $LN1@Atomic_sto
$LN6@Atomic_sto:

; 1300 : 
; 1301 : 		case memory_order_seq_cst:
; 1302 : 			_Store_seq_cst_4(_Tgt, _Value);

  00049	8b 55 0c	 mov	 edx, DWORD PTR __Value$[ebp]
  0004c	52		 push	 edx
  0004d	8b 45 08	 mov	 eax, DWORD PTR __Tgt$[ebp]
  00050	50		 push	 eax
  00051	e8 00 00 00 00	 call	 ?_Store_seq_cst_4@std@@YAXPCKK@Z ; std::_Store_seq_cst_4
  00056	83 c4 08	 add	 esp, 8
$LN1@Atomic_sto:

; 1303 : 			break;
; 1304 : 
; 1305 : 		case memory_order_consume:
; 1306 : 		case memory_order_acquire:
; 1307 : 		case memory_order_acq_rel:
; 1308 : 		default:
; 1309 : 			_INVALID_MEMORY_ORDER;
; 1310 : 			break;
; 1311 : 		}
; 1312 : 	}

  00059	83 c4 04	 add	 esp, 4
  0005c	3b ec		 cmp	 ebp, esp
  0005e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00063	8b e5		 mov	 esp, ebp
  00065	5d		 pop	 ebp
  00066	c3		 ret	 0
?_Atomic_store_4@std@@YAXPCKKW4memory_order@1@@Z ENDP	; std::_Atomic_store_4
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ??4?$atomic@H@std@@QAEHH@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Val$ = 8						; size = 4
??4?$atomic@H@std@@QAEHH@Z PROC				; std::atomic<int>::operator=, COMDAT
; _this$ = ecx

; 181  : 		{	// assign from _Val

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 182  : 		return (_ATOMIC_ITYPE::operator=(_Val));

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Val$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	e8 00 00 00 00	 call	 ??4_Atomic_int@std@@QAEHH@Z ; std::_Atomic_int::operator=

; 183  : 		}

  0001a	83 c4 04	 add	 esp, 4
  0001d	3b ec		 cmp	 ebp, esp
  0001f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00024	8b e5		 mov	 esp, ebp
  00026	5d		 pop	 ebp
  00027	c2 04 00	 ret	 4
??4?$atomic@H@std@@QAEHH@Z ENDP				; std::atomic<int>::operator=
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z
_TEXT	SEGMENT
__$EHRec$ = -12						; size = 12
__Atom$ = 8						; size = 4
__Value$ = 12						; size = 4
__Order$ = 16						; size = 4
?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z PROC ; std::atomic_store_explicit, COMDAT

; 473  : 	{	// store _Value into *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp

; 474  : 	_ATOMIC_STORE(_Atom, _Value, _Order);

  00018	8b 45 10	 mov	 eax, DWORD PTR __Order$[ebp]
  0001b	50		 push	 eax
  0001c	8b 4d 0c	 mov	 ecx, DWORD PTR __Value$[ebp]
  0001f	51		 push	 ecx
  00020	8b 55 08	 mov	 edx, DWORD PTR __Atom$[ebp]
  00023	52		 push	 edx
  00024	e8 00 00 00 00	 call	 ?_Atomic_store_4@std@@YAXPCKKW4memory_order@1@@Z ; std::_Atomic_store_4
  00029	83 c4 0c	 add	 esp, 12			; 0000000cH

; 475  : 	}

  0002c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0002f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00036	83 c4 0c	 add	 esp, 12			; 0000000cH
  00039	3b ec		 cmp	 ebp, esp
  0003b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00040	8b e5		 mov	 esp, ebp
  00042	5d		 pop	 ebp
  00043	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z ENDP ; std::atomic_store_explicit
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ?atomic_store@std@@YAXPAU_Atomic_int@1@H@Z
_TEXT	SEGMENT
__Atom$ = 8						; size = 4
__Value$ = 12						; size = 4
?atomic_store@std@@YAXPAU_Atomic_int@1@H@Z PROC		; std::atomic_store, COMDAT

; 483  : 	{	// store _Value into *_Atom

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 484  : 	atomic_store_explicit(_Atom, _Value, memory_order_seq_cst);

  00003	6a 05		 push	 5
  00005	8b 45 0c	 mov	 eax, DWORD PTR __Value$[ebp]
  00008	50		 push	 eax
  00009	8b 4d 08	 mov	 ecx, DWORD PTR __Atom$[ebp]
  0000c	51		 push	 ecx
  0000d	e8 00 00 00 00	 call	 ?atomic_store_explicit@std@@YAXPAU_Atomic_int@1@HW4memory_order@1@@Z ; std::atomic_store_explicit
  00012	83 c4 0c	 add	 esp, 12			; 0000000cH

; 485  : 	}

  00015	3b ec		 cmp	 ebp, esp
  00017	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0001c	5d		 pop	 ebp
  0001d	c3		 ret	 0
?atomic_store@std@@YAXPAU_Atomic_int@1@H@Z ENDP		; std::atomic_store
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xxatomic
;	COMDAT ??4_Atomic_int@std@@QAEHH@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Value$ = 8						; size = 4
??4_Atomic_int@std@@QAEHH@Z PROC			; std::_Atomic_int::operator=, COMDAT
; _this$ = ecx

; 597  : 	{	// assign _Value to *this

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 598  : 	atomic_store(this, _Value);

  0000e	8b 45 08	 mov	 eax, DWORD PTR __Value$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	51		 push	 ecx
  00016	e8 00 00 00 00	 call	 ?atomic_store@std@@YAXPAU_Atomic_int@1@H@Z ; std::atomic_store
  0001b	83 c4 08	 add	 esp, 8

; 599  : 	return (_Value);

  0001e	8b 45 08	 mov	 eax, DWORD PTR __Value$[ebp]

; 600  : 	}

  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c2 04 00	 ret	 4
??4_Atomic_int@std@@QAEHH@Z ENDP			; std::_Atomic_int::operator=
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_utils.h
;	COMDAT ??4ProxyData@JsApi@@QAEAAU01@ABU01@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_rhs$ = 8						; size = 4
??4ProxyData@JsApi@@QAEAAU01@ABU01@@Z PROC		; JsApi::ProxyData::operator=, COMDAT
; _this$ = ecx

; 40   : 		ProxyData& operator=(const ProxyData& rhs) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 41   : 			type = rhs.type; hostname = rhs.hostname; port =rhs.port ; username = rhs.username; password = rhs.password;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR _rhs$[ebp]
  00014	8b 11		 mov	 edx, DWORD PTR [ecx]
  00016	89 10		 mov	 DWORD PTR [eax], edx
  00018	8b 45 08	 mov	 eax, DWORD PTR _rhs$[ebp]
  0001b	83 c0 04	 add	 eax, 4
  0001e	50		 push	 eax
  0001f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00022	83 c1 04	 add	 ecx, 4
  00025	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
  0002a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002d	8b 55 08	 mov	 edx, DWORD PTR _rhs$[ebp]
  00030	66 8b 42 1c	 mov	 ax, WORD PTR [edx+28]
  00034	66 89 41 1c	 mov	 WORD PTR [ecx+28], ax
  00038	8b 4d 08	 mov	 ecx, DWORD PTR _rhs$[ebp]
  0003b	83 c1 20	 add	 ecx, 32			; 00000020H
  0003e	51		 push	 ecx
  0003f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00042	83 c1 20	 add	 ecx, 32			; 00000020H
  00045	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
  0004a	8b 55 08	 mov	 edx, DWORD PTR _rhs$[ebp]
  0004d	83 c2 38	 add	 edx, 56			; 00000038H
  00050	52		 push	 edx
  00051	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00054	83 c1 38	 add	 ecx, 56			; 00000038H
  00057	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=

; 42   : 			return *this;

  0005c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 43   : 		}

  0005f	83 c4 04	 add	 esp, 4
  00062	3b ec		 cmp	 ebp, esp
  00064	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00069	8b e5		 mov	 esp, ebp
  0006b	5d		 pop	 ebp
  0006c	c2 04 00	 ret	 4
??4ProxyData@JsApi@@QAEAAU01@ABU01@@Z ENDP		; JsApi::ProxyData::operator=
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
$T2 = -29						; size = 1
_lock$ = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_path$ = 8						; size = 4
?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z PROC ; JsApi::GlobalExtraOptions::AddPluginsDir
; _this$ = ecx

; 55   : 	bool GlobalExtraOptions::AddPluginsDir(const std::string & path) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 14	 sub	 esp, 20			; 00000014H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00023	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00026	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00029	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0002c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0002f	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 56   : 		std::lock_guard<std::mutex> lock(m_mutex_pluginsdir);

  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	83 c0 0c	 add	 eax, 12			; 0000000cH
  00038	50		 push	 eax
  00039	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  0003c	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00041	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 57   : 		m_pluginsdir.push_back(path);

  00048	8b 4d 08	 mov	 ecx, DWORD PTR _path$[ebp]
  0004b	51		 push	 ecx
  0004c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0004f	e8 00 00 00 00	 call	 ?push_back@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QAEXABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@@Z ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::push_back

; 58   : 		return true;

  00054	c6 45 e3 01	 mov	 BYTE PTR $T2[ebp], 1
  00058	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0005f	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00062	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
  00067	8a 45 e3	 mov	 al, BYTE PTR $T2[ebp]

; 59   : 	}

  0006a	52		 push	 edx
  0006b	8b cd		 mov	 ecx, ebp
  0006d	50		 push	 eax
  0006e	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN7@AddPlugins
  00074	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00079	58		 pop	 eax
  0007a	5a		 pop	 edx
  0007b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0007e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00085	83 c4 20	 add	 esp, 32			; 00000020H
  00088	3b ec		 cmp	 ebp, esp
  0008a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0008f	8b e5		 mov	 esp, ebp
  00091	5d		 pop	 ebp
  00092	c2 04 00	 ret	 4
  00095	0f 1f 00	 npad	 3
$LN7@AddPlugins:
  00098	01 00 00 00	 DD	 1
  0009c	00 00 00 00	 DD	 $LN6@AddPlugins
$LN6@AddPlugins:
  000a0	e8 ff ff ff	 DD	 -24			; ffffffe8H
  000a4	04 00 00 00	 DD	 4
  000a8	00 00 00 00	 DD	 $LN4@AddPlugins
$LN4@AddPlugins:
  000ac	6c		 DB	 108			; 0000006cH
  000ad	6f		 DB	 111			; 0000006fH
  000ae	63		 DB	 99			; 00000063H
  000af	6b		 DB	 107			; 0000006bH
  000b0	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__ehhandler$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?AddPluginsDir@GlobalExtraOptions@JsApi@@QAE_NABV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ENDP ; JsApi::GlobalExtraOptions::AddPluginsDir
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_old$ = -5						; size = 1
_this$ = -4						; size = 4
_enable$ = 8						; size = 1
?EnableConsoleLog@GlobalExtraOptions@JsApi@@QAE_N_N@Z PROC ; JsApi::GlobalExtraOptions::EnableConsoleLog
; _this$ = ecx

; 61   : 	bool GlobalExtraOptions::EnableConsoleLog(bool enable) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 62   : 		bool old = m_bEnableConsoleLog;

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	83 c1 3c	 add	 ecx, 60			; 0000003cH
  0001d	e8 00 00 00 00	 call	 ??B_Atomic_bool@std@@QBE_NXZ ; std::_Atomic_bool::operator bool
  00022	88 45 fb	 mov	 BYTE PTR _old$[ebp], al

; 63   : 		m_bEnableConsoleLog = enable;

  00025	0f b6 45 08	 movzx	 eax, BYTE PTR _enable$[ebp]
  00029	50		 push	 eax
  0002a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002d	83 c1 3c	 add	 ecx, 60			; 0000003cH
  00030	e8 00 00 00 00	 call	 ??4?$atomic@_N@std@@QAE_N_N@Z ; std::atomic<bool>::operator=

; 64   : 		return old;

  00035	8a 45 fb	 mov	 al, BYTE PTR _old$[ebp]

; 65   : 	}

  00038	83 c4 08	 add	 esp, 8
  0003b	3b ec		 cmp	 ebp, esp
  0003d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00042	8b e5		 mov	 esp, ebp
  00044	5d		 pop	 ebp
  00045	c2 04 00	 ret	 4
?EnableConsoleLog@GlobalExtraOptions@JsApi@@QAE_N_N@Z ENDP ; JsApi::GlobalExtraOptions::EnableConsoleLog
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_old$ = -8						; size = 4
_this$ = -4						; size = 4
_hardwareConcurrency$ = 8				; size = 4
?SetHardwareConcurrency@GlobalExtraOptions@JsApi@@QAEHH@Z PROC ; JsApi::GlobalExtraOptions::SetHardwareConcurrency
; _this$ = ecx

; 68   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 69   : 		int old = m_hardwareConcurrency;

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	83 c1 40	 add	 ecx, 64			; 00000040H
  0001d	e8 00 00 00 00	 call	 ??B_Atomic_int@std@@QBEHXZ ; std::_Atomic_int::operator int
  00022	89 45 f8	 mov	 DWORD PTR _old$[ebp], eax

; 70   : 		m_hardwareConcurrency = hardwareConcurrency;

  00025	8b 45 08	 mov	 eax, DWORD PTR _hardwareConcurrency$[ebp]
  00028	50		 push	 eax
  00029	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002c	83 c1 40	 add	 ecx, 64			; 00000040H
  0002f	e8 00 00 00 00	 call	 ??4?$atomic@H@std@@QAEHH@Z ; std::atomic<int>::operator=

; 71   : 		return old;

  00034	8b 45 f8	 mov	 eax, DWORD PTR _old$[ebp]

; 72   : 	}

  00037	83 c4 08	 add	 esp, 8
  0003a	3b ec		 cmp	 ebp, esp
  0003c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00041	8b e5		 mov	 esp, ebp
  00043	5d		 pop	 ebp
  00044	c2 04 00	 ret	 4
?SetHardwareConcurrency@GlobalExtraOptions@JsApi@@QAEHH@Z ENDP ; JsApi::GlobalExtraOptions::SetHardwareConcurrency
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
?HardwareConcurrency@GlobalExtraOptions@JsApi@@QAEHXZ PROC ; JsApi::GlobalExtraOptions::HardwareConcurrency
; _this$ = ecx

; 75   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 76   : 		return m_hardwareConcurrency;

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	83 c1 40	 add	 ecx, 64			; 00000040H
  00014	e8 00 00 00 00	 call	 ??B_Atomic_int@std@@QBEHXZ ; std::_Atomic_int::operator int

; 77   : 	}

  00019	83 c4 04	 add	 esp, 4
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	8b e5		 mov	 esp, ebp
  00025	5d		 pop	 ebp
  00026	c3		 ret	 0
?HardwareConcurrency@GlobalExtraOptions@JsApi@@QAEHXZ ENDP ; JsApi::GlobalExtraOptions::HardwareConcurrency
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
_old$ = -5						; size = 1
_this$ = -4						; size = 4
_disable$ = 8						; size = 1
?DisableFlash@GlobalExtraOptions@JsApi@@QAE_N_N@Z PROC	; JsApi::GlobalExtraOptions::DisableFlash
; _this$ = ecx

; 79   : 	bool GlobalExtraOptions::DisableFlash(bool disable) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 80   : 		bool old = m_disableFlash;

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	83 c1 44	 add	 ecx, 68			; 00000044H
  0001d	e8 00 00 00 00	 call	 ??B_Atomic_bool@std@@QBE_NXZ ; std::_Atomic_bool::operator bool
  00022	88 45 fb	 mov	 BYTE PTR _old$[ebp], al

; 81   : 		m_disableFlash = disable;

  00025	0f b6 45 08	 movzx	 eax, BYTE PTR _disable$[ebp]
  00029	50		 push	 eax
  0002a	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002d	83 c1 44	 add	 ecx, 68			; 00000044H
  00030	e8 00 00 00 00	 call	 ??4?$atomic@_N@std@@QAE_N_N@Z ; std::atomic<bool>::operator=

; 82   : 		return old;

  00035	8a 45 fb	 mov	 al, BYTE PTR _old$[ebp]

; 83   : 	}

  00038	83 c4 08	 add	 esp, 8
  0003b	3b ec		 cmp	 ebp, esp
  0003d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00042	8b e5		 mov	 esp, ebp
  00044	5d		 pop	 ebp
  00045	c2 04 00	 ret	 4
?DisableFlash@GlobalExtraOptions@JsApi@@QAE_N_N@Z ENDP	; JsApi::GlobalExtraOptions::DisableFlash
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
$T2 = -64						; size = 4
_old$ = -56						; size = 24
_lock$ = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_path$ = 12						; size = 4
?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z PROC ; JsApi::GlobalExtraOptions::SetCookieAndLocalStoragePath
; _this$ = ecx

; 85   : 	std::string GlobalExtraOptions::SetCookieAndLocalStoragePath(const std::string& path) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 34	 sub	 esp, 52			; 00000034H
  0001b	57		 push	 edi
  0001c	51		 push	 ecx
  0001d	8d 7d c0	 lea	 edi, DWORD PTR [ebp-64]
  00020	b9 0d 00 00 00	 mov	 ecx, 13			; 0000000dH
  00025	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002a	f3 ab		 rep stosd
  0002c	59		 pop	 ecx
  0002d	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00030	c7 45 c0 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 86   : 		using namespace std;
; 87   : 		lock_guard<mutex> lock(m_mutex_CookieAndLocalStoragePath);

  00037	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003a	83 c0 60	 add	 eax, 96			; 00000060H
  0003d	50		 push	 eax
  0003e	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00041	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00046	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 88   : 		string old = m_CookieAndLocalStoragePath;

  0004d	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00050	83 c1 48	 add	 ecx, 72			; 00000048H
  00053	51		 push	 ecx
  00054	8d 4d c8	 lea	 ecx, DWORD PTR _old$[ebp]
  00057	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0005c	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2

; 89   : 		m_CookieAndLocalStoragePath = path;

  00060	8b 55 0c	 mov	 edx, DWORD PTR _path$[ebp]
  00063	52		 push	 edx
  00064	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00067	83 c1 48	 add	 ecx, 72			; 00000048H
  0006a	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=

; 90   : 		return old;

  0006f	8d 45 c8	 lea	 eax, DWORD PTR _old$[ebp]
  00072	50		 push	 eax
  00073	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00076	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0007b	8b 4d c0	 mov	 ecx, DWORD PTR $T2[ebp]
  0007e	83 c9 01	 or	 ecx, 1
  00081	89 4d c0	 mov	 DWORD PTR $T2[ebp], ecx
  00084	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00088	8d 4d c8	 lea	 ecx, DWORD PTR _old$[ebp]
  0008b	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00090	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00094	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00097	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
  0009c	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 91   : 	}

  0009f	52		 push	 edx
  000a0	8b cd		 mov	 ecx, ebp
  000a2	50		 push	 eax
  000a3	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@SetCookieA
  000a9	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000ae	58		 pop	 eax
  000af	5a		 pop	 edx
  000b0	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000b3	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000ba	5f		 pop	 edi
  000bb	83 c4 40	 add	 esp, 64			; 00000040H
  000be	3b ec		 cmp	 ebp, esp
  000c0	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000c5	8b e5		 mov	 esp, ebp
  000c7	5d		 pop	 ebp
  000c8	c2 08 00	 ret	 8
  000cb	90		 npad	 1
$LN11@SetCookieA:
  000cc	02 00 00 00	 DD	 2
  000d0	00 00 00 00	 DD	 $LN10@SetCookieA
$LN10@SetCookieA:
  000d4	e8 ff ff ff	 DD	 -24			; ffffffe8H
  000d8	04 00 00 00	 DD	 4
  000dc	00 00 00 00	 DD	 $LN7@SetCookieA
  000e0	c8 ff ff ff	 DD	 -56			; ffffffc8H
  000e4	18 00 00 00	 DD	 24			; 00000018H
  000e8	00 00 00 00	 DD	 $LN8@SetCookieA
$LN8@SetCookieA:
  000ec	6f		 DB	 111			; 0000006fH
  000ed	6c		 DB	 108			; 0000006cH
  000ee	64		 DB	 100			; 00000064H
  000ef	00		 DB	 0
$LN7@SetCookieA:
  000f0	6c		 DB	 108			; 0000006cH
  000f1	6f		 DB	 111			; 0000006fH
  000f2	63		 DB	 99			; 00000063H
  000f3	6b		 DB	 107			; 0000006bH
  000f4	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__unwindfunclet$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$1:
  00008	8d 4d c8	 lea	 ecx, DWORD PTR _old$[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$2:
  00010	8b 45 c0	 mov	 eax, DWORD PTR $T2[ebp]
  00013	83 e0 01	 and	 eax, 1
  00016	0f 84 0c 00 00
	00		 je	 $LN6@SetCookieA
  0001c	83 65 c0 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00020	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00023	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN6@SetCookieA:
  00028	c3		 ret	 0
__ehhandler$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z:
  00029	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z
  0002e	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?SetCookieAndLocalStoragePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z ENDP ; JsApi::GlobalExtraOptions::SetCookieAndLocalStoragePath
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
$T2 = -64						; size = 4
_old$ = -56						; size = 24
_lock$ = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
_path$ = 12						; size = 4
?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z PROC ; JsApi::GlobalExtraOptions::SetFlashCachePath
; _this$ = ecx

; 93   : 	std::string GlobalExtraOptions::SetFlashCachePath(const std::string& path) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 34	 sub	 esp, 52			; 00000034H
  0001b	57		 push	 edi
  0001c	51		 push	 ecx
  0001d	8d 7d c0	 lea	 edi, DWORD PTR [ebp-64]
  00020	b9 0d 00 00 00	 mov	 ecx, 13			; 0000000dH
  00025	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0002a	f3 ab		 rep stosd
  0002c	59		 pop	 ecx
  0002d	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00030	c7 45 c0 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0

; 94   : 		using namespace std;
; 95   : 		lock_guard<mutex> lock(m_mutex_FlashCachePath);

  00037	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003a	05 a8 00 00 00	 add	 eax, 168		; 000000a8H
  0003f	50		 push	 eax
  00040	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00043	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00048	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 96   : 		string old = m_FlashCachePath;

  0004f	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00052	81 c1 90 00 00
	00		 add	 ecx, 144		; 00000090H
  00058	51		 push	 ecx
  00059	8d 4d c8	 lea	 ecx, DWORD PTR _old$[ebp]
  0005c	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00061	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2

; 97   : 		m_FlashCachePath = path;

  00065	8b 55 0c	 mov	 edx, DWORD PTR _path$[ebp]
  00068	52		 push	 edx
  00069	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0006c	81 c1 90 00 00
	00		 add	 ecx, 144		; 00000090H
  00072	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=

; 98   : 		return old;

  00077	8d 45 c8	 lea	 eax, DWORD PTR _old$[ebp]
  0007a	50		 push	 eax
  0007b	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0007e	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00083	8b 4d c0	 mov	 ecx, DWORD PTR $T2[ebp]
  00086	83 c9 01	 or	 ecx, 1
  00089	89 4d c0	 mov	 DWORD PTR $T2[ebp], ecx
  0008c	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00090	8d 4d c8	 lea	 ecx, DWORD PTR _old$[ebp]
  00093	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00098	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  0009c	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  0009f	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
  000a4	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]

; 99   : 	}

  000a7	52		 push	 edx
  000a8	8b cd		 mov	 ecx, ebp
  000aa	50		 push	 eax
  000ab	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@SetFlashCa
  000b1	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000b6	58		 pop	 eax
  000b7	5a		 pop	 edx
  000b8	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000bb	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000c2	5f		 pop	 edi
  000c3	83 c4 40	 add	 esp, 64			; 00000040H
  000c6	3b ec		 cmp	 ebp, esp
  000c8	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000cd	8b e5		 mov	 esp, ebp
  000cf	5d		 pop	 ebp
  000d0	c2 08 00	 ret	 8
  000d3	90		 npad	 1
$LN11@SetFlashCa:
  000d4	02 00 00 00	 DD	 2
  000d8	00 00 00 00	 DD	 $LN10@SetFlashCa
$LN10@SetFlashCa:
  000dc	e8 ff ff ff	 DD	 -24			; ffffffe8H
  000e0	04 00 00 00	 DD	 4
  000e4	00 00 00 00	 DD	 $LN7@SetFlashCa
  000e8	c8 ff ff ff	 DD	 -56			; ffffffc8H
  000ec	18 00 00 00	 DD	 24			; 00000018H
  000f0	00 00 00 00	 DD	 $LN8@SetFlashCa
$LN8@SetFlashCa:
  000f4	6f		 DB	 111			; 0000006fH
  000f5	6c		 DB	 108			; 0000006cH
  000f6	64		 DB	 100			; 00000064H
  000f7	00		 DB	 0
$LN7@SetFlashCa:
  000f8	6c		 DB	 108			; 0000006cH
  000f9	6f		 DB	 111			; 0000006fH
  000fa	63		 DB	 99			; 00000063H
  000fb	6b		 DB	 107			; 0000006bH
  000fc	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__unwindfunclet$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$1:
  00008	8d 4d c8	 lea	 ecx, DWORD PTR _old$[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z$2:
  00010	8b 45 c0	 mov	 eax, DWORD PTR $T2[ebp]
  00013	83 e0 01	 and	 eax, 1
  00016	0f 84 0c 00 00
	00		 je	 $LN6@SetFlashCa
  0001c	83 65 c0 fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00020	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00023	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN6@SetFlashCa:
  00028	c3		 ret	 0
__ehhandler$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z:
  00029	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z
  0002e	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?SetFlashCachePath@GlobalExtraOptions@JsApi@@QAE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@ABV34@@Z ENDP ; JsApi::GlobalExtraOptions::SetFlashCachePath
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
$T2 = -29						; size = 1
_lock$ = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_type$ = 8						; size = 4
_hostname$ = 12						; size = 4
_port$ = 16						; size = 2
_username$ = 20						; size = 4
_password$ = 24						; size = 4
?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z PROC ; JsApi::GlobalExtraOptions::SetProxy
; _this$ = ecx

; 102  : 		const char* username , const char* password) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 14	 sub	 esp, 20			; 00000014H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00023	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00026	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00029	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0002c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0002f	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 103  : 		using namespace std;
; 104  : 		lock_guard<mutex> lock(m_mutex_proxy);

  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	05 28 01 00 00	 add	 eax, 296		; 00000128H
  0003a	50		 push	 eax
  0003b	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  0003e	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00043	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 105  : 		m_proxy.type = type; m_proxy.port = port;

  0004a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0004d	8b 55 08	 mov	 edx, DWORD PTR _type$[ebp]
  00050	89 91 d8 00 00
	00		 mov	 DWORD PTR [ecx+216], edx
  00056	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00059	66 8b 4d 10	 mov	 cx, WORD PTR _port$[ebp]
  0005d	66 89 88 f4 00
	00 00		 mov	 WORD PTR [eax+244], cx

; 106  : 		if(hostname) m_proxy.hostname= hostname;

  00064	83 7d 0c 00	 cmp	 DWORD PTR _hostname$[ebp], 0
  00068	74 12		 je	 SHORT $LN2@SetProxy
  0006a	8b 55 0c	 mov	 edx, DWORD PTR _hostname$[ebp]
  0006d	52		 push	 edx
  0006e	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00071	81 c1 dc 00 00
	00		 add	 ecx, 220		; 000000dcH
  00077	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
$LN2@SetProxy:

; 107  : 		if(username) m_proxy.username=username;

  0007c	83 7d 14 00	 cmp	 DWORD PTR _username$[ebp], 0
  00080	74 12		 je	 SHORT $LN3@SetProxy
  00082	8b 45 14	 mov	 eax, DWORD PTR _username$[ebp]
  00085	50		 push	 eax
  00086	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00089	81 c1 f8 00 00
	00		 add	 ecx, 248		; 000000f8H
  0008f	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
$LN3@SetProxy:

; 108  : 		if(password) m_proxy.password = password;

  00094	83 7d 18 00	 cmp	 DWORD PTR _password$[ebp], 0
  00098	74 12		 je	 SHORT $LN4@SetProxy
  0009a	8b 4d 18	 mov	 ecx, DWORD PTR _password$[ebp]
  0009d	51		 push	 ecx
  0009e	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000a1	81 c1 10 01 00
	00		 add	 ecx, 272		; 00000110H
  000a7	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@QBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
$LN4@SetProxy:

; 109  : 
; 110  : 		return true;

  000ac	c6 45 e3 01	 mov	 BYTE PTR $T2[ebp], 1
  000b0	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000b7	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  000ba	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
  000bf	8a 45 e3	 mov	 al, BYTE PTR $T2[ebp]

; 111  : 	}

  000c2	52		 push	 edx
  000c3	8b cd		 mov	 ecx, ebp
  000c5	50		 push	 eax
  000c6	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN10@SetProxy
  000cc	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000d1	58		 pop	 eax
  000d2	5a		 pop	 edx
  000d3	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000d6	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000dd	83 c4 20	 add	 esp, 32			; 00000020H
  000e0	3b ec		 cmp	 ebp, esp
  000e2	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000e7	8b e5		 mov	 esp, ebp
  000e9	5d		 pop	 ebp
  000ea	c2 14 00	 ret	 20			; 00000014H
  000ed	0f 1f 00	 npad	 3
$LN10@SetProxy:
  000f0	01 00 00 00	 DD	 1
  000f4	00 00 00 00	 DD	 $LN9@SetProxy
$LN9@SetProxy:
  000f8	e8 ff ff ff	 DD	 -24			; ffffffe8H
  000fc	04 00 00 00	 DD	 4
  00100	00 00 00 00	 DD	 $LN7@SetProxy
$LN7@SetProxy:
  00104	6c		 DB	 108			; 0000006cH
  00105	6f		 DB	 111			; 0000006fH
  00106	63		 DB	 99			; 00000063H
  00107	6b		 DB	 107			; 0000006bH
  00108	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__ehhandler$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?SetProxy@GlobalExtraOptions@JsApi@@QAE_NW4mbProxyType@@PBDG11@Z ENDP ; JsApi::GlobalExtraOptions::SetProxy
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
$T2 = -29						; size = 1
_lock$ = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_proxy$ = 8						; size = 4
?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z PROC ; JsApi::GlobalExtraOptions::SetProxy
; _this$ = ecx

; 113  : 	bool GlobalExtraOptions::SetProxy(const ProxyData& proxy) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 14	 sub	 esp, 20			; 00000014H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00023	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00026	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00029	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0002c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0002f	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 114  : 		using namespace std;
; 115  : 		lock_guard<mutex> lock(m_mutex_proxy);

  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	05 28 01 00 00	 add	 eax, 296		; 00000128H
  0003a	50		 push	 eax
  0003b	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  0003e	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00043	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 116  : 		m_proxy = proxy;

  0004a	8b 4d 08	 mov	 ecx, DWORD PTR _proxy$[ebp]
  0004d	51		 push	 ecx
  0004e	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00051	81 c1 d8 00 00
	00		 add	 ecx, 216		; 000000d8H
  00057	e8 00 00 00 00	 call	 ??4ProxyData@JsApi@@QAEAAU01@ABU01@@Z ; JsApi::ProxyData::operator=

; 117  : 
; 118  : 		return true;

  0005c	c6 45 e3 01	 mov	 BYTE PTR $T2[ebp], 1
  00060	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00067	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  0006a	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
  0006f	8a 45 e3	 mov	 al, BYTE PTR $T2[ebp]

; 119  : 	}

  00072	52		 push	 edx
  00073	8b cd		 mov	 ecx, ebp
  00075	50		 push	 eax
  00076	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN7@SetProxy
  0007c	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00081	58		 pop	 eax
  00082	5a		 pop	 edx
  00083	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00086	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0008d	83 c4 20	 add	 esp, 32			; 00000020H
  00090	3b ec		 cmp	 ebp, esp
  00092	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00097	8b e5		 mov	 esp, ebp
  00099	5d		 pop	 ebp
  0009a	c2 04 00	 ret	 4
  0009d	0f 1f 00	 npad	 3
$LN7@SetProxy:
  000a0	01 00 00 00	 DD	 1
  000a4	00 00 00 00	 DD	 $LN6@SetProxy
$LN6@SetProxy:
  000a8	e8 ff ff ff	 DD	 -24			; ffffffe8H
  000ac	04 00 00 00	 DD	 4
  000b0	00 00 00 00	 DD	 $LN4@SetProxy
$LN4@SetProxy:
  000b4	6c		 DB	 108			; 0000006cH
  000b5	6f		 DB	 111			; 0000006fH
  000b6	63		 DB	 99			; 00000063H
  000b7	6b		 DB	 107			; 0000006bH
  000b8	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__ehhandler$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?SetProxy@GlobalExtraOptions@JsApi@@QAE_NABUProxyData@2@@Z ENDP ; JsApi::GlobalExtraOptions::SetProxy
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\jsapi\jsapi_option.cpp
_TEXT	SEGMENT
$T2 = -29						; size = 1
_lock$ = -24						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
_width$ = 8						; size = 4
_height$ = 12						; size = 4
_availWidth$ = 16					; size = 4
_availHeight$ = 20					; size = 4
?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z PROC ; JsApi::GlobalExtraOptions::LoadScreenSize
; _this$ = ecx

; 135  : 	bool GlobalExtraOptions::LoadScreenSize(int& width, int& height, int& availWidth, int& availHeight) {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 14	 sub	 esp, 20			; 00000014H
  0001b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00020	89 45 e0	 mov	 DWORD PTR [ebp-32], eax
  00023	89 45 e4	 mov	 DWORD PTR [ebp-28], eax
  00026	89 45 e8	 mov	 DWORD PTR [ebp-24], eax
  00029	89 45 ec	 mov	 DWORD PTR [ebp-20], eax
  0002c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0002f	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 136  : 		using namespace std;
; 137  : 		lock_guard<mutex> lock(m_mutex_screenSize);

  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	05 70 01 00 00	 add	 eax, 368		; 00000170H
  0003a	50		 push	 eax
  0003b	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  0003e	e8 00 00 00 00	 call	 ??0?$lock_guard@Vmutex@std@@@std@@QAE@AAVmutex@1@@Z ; std::lock_guard<std::mutex>::lock_guard<std::mutex>
  00043	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 138  : 		if(m_screenSize.width>=0) width = m_screenSize.width; 

  0004a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0004d	83 b9 58 01 00
	00 00		 cmp	 DWORD PTR [ecx+344], 0
  00054	7c 0e		 jl	 SHORT $LN2@LoadScreen
  00056	8b 55 08	 mov	 edx, DWORD PTR _width$[ebp]
  00059	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0005c	8b 88 58 01 00
	00		 mov	 ecx, DWORD PTR [eax+344]
  00062	89 0a		 mov	 DWORD PTR [edx], ecx
$LN2@LoadScreen:

; 139  : 		if(m_screenSize.height>=0) height = m_screenSize.height;

  00064	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00067	83 ba 5c 01 00
	00 00		 cmp	 DWORD PTR [edx+348], 0
  0006e	7c 0e		 jl	 SHORT $LN3@LoadScreen
  00070	8b 45 0c	 mov	 eax, DWORD PTR _height$[ebp]
  00073	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00076	8b 91 5c 01 00
	00		 mov	 edx, DWORD PTR [ecx+348]
  0007c	89 10		 mov	 DWORD PTR [eax], edx
$LN3@LoadScreen:

; 140  : 		if(m_screenSize.availWidth>=0) availWidth = m_screenSize.availWidth; 

  0007e	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00081	83 b8 60 01 00
	00 00		 cmp	 DWORD PTR [eax+352], 0
  00088	7c 0e		 jl	 SHORT $LN4@LoadScreen
  0008a	8b 4d 10	 mov	 ecx, DWORD PTR _availWidth$[ebp]
  0008d	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00090	8b 82 60 01 00
	00		 mov	 eax, DWORD PTR [edx+352]
  00096	89 01		 mov	 DWORD PTR [ecx], eax
$LN4@LoadScreen:

; 141  : 		if(m_screenSize.availHeight>=0) availHeight = m_screenSize.availHeight;

  00098	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0009b	83 b9 64 01 00
	00 00		 cmp	 DWORD PTR [ecx+356], 0
  000a2	7c 0e		 jl	 SHORT $LN5@LoadScreen
  000a4	8b 55 14	 mov	 edx, DWORD PTR _availHeight$[ebp]
  000a7	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  000aa	8b 88 64 01 00
	00		 mov	 ecx, DWORD PTR [eax+356]
  000b0	89 0a		 mov	 DWORD PTR [edx], ecx
$LN5@LoadScreen:

; 142  : 		return true;

  000b2	c6 45 e3 01	 mov	 BYTE PTR $T2[ebp], 1
  000b6	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000bd	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  000c0	e8 00 00 00 00	 call	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
  000c5	8a 45 e3	 mov	 al, BYTE PTR $T2[ebp]

; 143  : 	}

  000c8	52		 push	 edx
  000c9	8b cd		 mov	 ecx, ebp
  000cb	50		 push	 eax
  000cc	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN11@LoadScreen
  000d2	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  000d7	58		 pop	 eax
  000d8	5a		 pop	 edx
  000d9	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000dc	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000e3	83 c4 20	 add	 esp, 32			; 00000020H
  000e6	3b ec		 cmp	 ebp, esp
  000e8	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000ed	8b e5		 mov	 esp, ebp
  000ef	5d		 pop	 ebp
  000f0	c2 10 00	 ret	 16			; 00000010H
  000f3	90		 npad	 1
$LN11@LoadScreen:
  000f4	01 00 00 00	 DD	 1
  000f8	00 00 00 00	 DD	 $LN10@LoadScreen
$LN10@LoadScreen:
  000fc	e8 ff ff ff	 DD	 -24			; ffffffe8H
  00100	04 00 00 00	 DD	 4
  00104	00 00 00 00	 DD	 $LN8@LoadScreen
$LN8@LoadScreen:
  00108	6c		 DB	 108			; 0000006cH
  00109	6f		 DB	 111			; 0000006fH
  0010a	63		 DB	 99			; 00000063H
  0010b	6b		 DB	 107			; 0000006bH
  0010c	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z$0:
  00000	8d 4d e8	 lea	 ecx, DWORD PTR _lock$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$lock_guard@Vmutex@std@@@std@@QAE@XZ ; std::lock_guard<std::mutex>::~lock_guard<std::mutex>
__ehhandler$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?LoadScreenSize@GlobalExtraOptions@JsApi@@QAE_NAAH000@Z ENDP ; JsApi::GlobalExtraOptions::LoadScreenSize
END
