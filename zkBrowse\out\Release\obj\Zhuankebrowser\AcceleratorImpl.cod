; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	?wkeNetFreePostBodyElements@@3P6AXPAU_wkePostBodyElements@@@ZA ; wkeNetFreePostBodyElements
PUBLIC	?mbGetCursorInfoType@@3P6GHH@ZA			; mbGetCursorInfoType
PUBLIC	?wkeUtilEncodeURLEscape@@3P6APBDPBD@ZA		; wkeUtilEncodeURLEscape
PUBLIC	?mbUtilsSilentPrint@@3P6GHHPBD@ZA		; mbUtilsSilentPrint
PUBLIC	?mbFireContextMenuEvent@@3P6GHHHHI@ZA		; mbFireContextMenuEvent
PUBLIC	?mbCreateInitSettings@@3P6GPAU_mbSettings@@XZA	; mbCreateInitSettings
PUBLIC	?mbGetContentAsMarkup@@3P6GXHP6GXHPAXPBDI@Z00@ZA ; mbGetContentAsMarkup
PUBLIC	?mbJsToV8Value@@3P6GPAXPAX_J@ZA			; mbJsToV8Value
PUBLIC	?wkeWebFrameGetMainFrame@@3P6APAXPAVCWebView@wke@@@ZA ; wkeWebFrameGetMainFrame
PUBLIC	?wkeCanGoForward@@3P6A_NPAVCWebView@wke@@@ZA	; wkeCanGoForward
PUBLIC	?mbGetTitle@@3P6GPBDH@ZA			; mbGetTitle
PUBLIC	?wkeNetSetMIMEType@@3P6AXPAXPBD@ZA		; wkeNetSetMIMEType
PUBLIC	?wkeNetCreateWebUrlRequest2@@3P6APAUwkeWebUrlRequest@@QAVWebURLRequest@blink@@@ZA ; wkeNetCreateWebUrlRequest2
PUBLIC	?mbFireWindowsMessage@@3P6GHHPAUHWND__@@IIJPAJ@ZA ; mbFireWindowsMessage
PUBLIC	?wkeToString@@3P6APBDQAVCString@wke@@@ZA	; wkeToString
PUBLIC	?mbNetSendWsText@@3P6GXPAXPBDI@ZA		; mbNetSendWsText
PUBLIC	?wkePrintToBitmap@@3P6APBU_wkeMemBuf@@PAVCWebView@wke@@PAXPBU_wkeScreenshotSettings@@@ZA ; wkePrintToBitmap
PUBLIC	?wkeCreateMemBuf@@3P6APAU_wkeMemBuf@@PAVCWebView@wke@@PAXI@ZA ; wkeCreateMemBuf
PUBLIC	?wkeNetSetData@@3P6AXPAX0H@ZA			; wkeNetSetData
PUBLIC	?mbFireMouseWheelEvent@@3P6GHHHHHI@ZA		; mbFireMouseWheelEvent
PUBLIC	?wkeDestroyWebWindow@@3P6AXPAVCWebView@wke@@@ZA	; wkeDestroyWebWindow
PUBLIC	?wkeNetGetHTTPHeaderFieldFromResponse@@3P6APBDPAXPBD@ZA ; wkeNetGetHTTPHeaderFieldFromResponse
PUBLIC	?wkeGetHeight@@3P6AHPAVCWebView@wke@@@ZA	; wkeGetHeight
PUBLIC	?wkeUtilBase64Decode@@3P6APBDPBD@ZA		; wkeUtilBase64Decode
PUBLIC	?wkeGetTitleW@@3P6APB_WPAVCWebView@wke@@@ZA	; wkeGetTitleW
PUBLIC	?wkeIsLoading@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsLoading
PUBLIC	?wkeShowWindow@@3P6AXPAVCWebView@wke@@_N@ZA	; wkeShowWindow
PUBLIC	?wkeKeyDown@@3P6A_NPAVCWebView@wke@@II_N@ZA	; wkeKeyDown
PUBLIC	?wkeUtilDecodeURLEscape@@3P6APBDPBD@ZA		; wkeUtilDecodeURLEscape
PUBLIC	?wkeFreeMemBuf@@3P6AXPAU_wkeMemBuf@@@ZA		; wkeFreeMemBuf
PUBLIC	?jsIsString@@3P6A_N_J@ZA			; jsIsString
PUBLIC	?jsBoolean@@3P6A_J_N@ZA				; jsBoolean
PUBLIC	?mbOnDownloadInBlinkThread@@3P6GXHP6G?AW4_mbDownloadOpt@@HPAXIPBD110PAU_mbNetJobDataBind@@@Z0@ZA ; mbOnDownloadInBlinkThread
PUBLIC	?wkeShowDevtools@@3P6AXPAVCWebView@wke@@PB_WP6AX0PAXPAUHWND__@@@Z2@ZA ; wkeShowDevtools
PUBLIC	?wkeGetBlinkMainThreadIsolate@@3P6APAXXZA	; wkeGetBlinkMainThreadIsolate
PUBLIC	?wkeDeleteString@@3P6AXPAVCString@wke@@@ZA	; wkeDeleteString
PUBLIC	?mbGetContentWidth@@3P6GHH@ZA			; mbGetContentWidth
PUBLIC	?wkeSetFileSystem@@3P6AXP6APAXPBD@ZP6AXPAX@ZP6AI2@ZP6AH22I@ZP6AH2HH@Z@ZA ; wkeSetFileSystem
PUBLIC	?mbNetGetRawResponseHeadInBlinkThread@@3P6GPBU_mbSlist@@PAX@ZA ; mbNetGetRawResponseHeadInBlinkThread
PUBLIC	?wkeStopLoading@@3P6AXPAVCWebView@wke@@@ZA	; wkeStopLoading
PUBLIC	?wkeNetCreatePostBodyElement@@3P6APAU_wkePostBodyElement@@PAVCWebView@wke@@@ZA ; wkeNetCreatePostBodyElement
PUBLIC	?wkeIsInitialize@@3P6A_NXZA			; wkeIsInitialize
PUBLIC	?mbEnableHighDPISupport@@3P6GXXZA		; mbEnableHighDPISupport
PUBLIC	?wkeSetCookieEnabled@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetCookieEnabled
PUBLIC	?wkeCut@@3P6AXPAVCWebView@wke@@@ZA		; wkeCut
PUBLIC	?mbSetDragDropEnable@@3P6GXHH@ZA		; mbSetDragDropEnable
PUBLIC	?jsToInt@@3P6AHPAUJsExecStateInfo@@_J@ZA	; jsToInt
PUBLIC	?wkeFireKeyUpEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA ; wkeFireKeyUpEvent
PUBLIC	??_7CWin32Heap@ATL@@6B@				; ATL::CWin32Heap::`vftable'
PUBLIC	?mbOnConsole@@3P6GXHP6GXHPAXW4mbConsoleLevel@@PBD2I2@Z0@ZA ; mbOnConsole
PUBLIC	?mbSetHandle@@3P6GXHPAUHWND__@@@ZA		; mbSetHandle
PUBLIC	?mbNetGetReferrer@@3P6GPBDPAX@ZA		; mbNetGetReferrer
PUBLIC	?mbNetGetHttpStatusCode@@3P6GHPAUmbWebUrlResponse@@@ZA ; mbNetGetHttpStatusCode
PUBLIC	_IID_IRegistrar
PUBLIC	?mbCanGoBack@@**********************************@@H@Z0@ZA ; mbCanGoBack
PUBLIC	?wkeEditorSelectAll@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorSelectAll
PUBLIC	?mbUtilScreenshot@@3P6GXHPBU_mbScreenshotSettings@@P6GXHPAXPBDI@Z1@ZA ; mbUtilScreenshot
PUBLIC	?wkeNodeOnCreateProcess@@3P6AXPAVCWebView@wke@@P6AX0PAXPB_W2PAU_STARTUPINFOW@@@Z1@ZA ; wkeNodeOnCreateProcess
PUBLIC	?wkeNetAddHTTPHeaderFieldToUrlRequest@@3P6AXPAUwkeWebUrlRequest@@PBD1@ZA ; wkeNetAddHTTPHeaderFieldToUrlRequest
PUBLIC	?wkeSetCspCheckEnable@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetCspCheckEnable
PUBLIC	?wkeIsLoadingSucceeded@@3P6A_NPAVCWebView@wke@@@ZA ; wkeIsLoadingSucceeded
PUBLIC	?mbPerformCookieCommand@@3P6GXHW4mbCookieCommand@@@ZA ; mbPerformCookieCommand
PUBLIC	?jsGetLength@@3P6AHPAUJsExecStateInfo@@_J@ZA	; jsGetLength
PUBLIC	?mbEditorSelectAll@@3P6GXH@ZA			; mbEditorSelectAll
PUBLIC	?wkeNetGetResponseUrl@@3P6APBDPAUwkeWebUrlResponse@@@ZA ; wkeNetGetResponseUrl
PUBLIC	?wkeFireMouseWheelEvent@@3P6A_NPAVCWebView@wke@@HHHI@ZA ; wkeFireMouseWheelEvent
PUBLIC	?wkeNetSetHTTPHeaderField@@3P6AXPAXPB_W1_N@ZA	; wkeNetSetHTTPHeaderField
PUBLIC	_IID_IAxWinAmbientDispatchEx
PUBLIC	?wkeGoForward@@3P6A_NPAVCWebView@wke@@@ZA	; wkeGoForward
PUBLIC	?wkeSetDeviceParameter@@3P6AXPAVCWebView@wke@@PBD1HM@ZA ; wkeSetDeviceParameter
PUBLIC	?jsArg@@3P6A_JPAUJsExecStateInfo@@H@ZA		; jsArg
PUBLIC	?mbSetCspCheckEnable@@3P6GXHH@ZA		; mbSetCspCheckEnable
PUBLIC	?wkeSetHeadlessEnabled@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetHeadlessEnabled
PUBLIC	?jsToBoolean@@3P6A_NPAUJsExecStateInfo@@_J@ZA	; jsToBoolean
PUBLIC	?mbGetUrl@@3P6GPBDH@ZA				; mbGetUrl
PUBLIC	?mbNetCreatePostBodyElement@@3P6GPAU_mbPostBodyElement@@H@ZA ; mbNetCreatePostBodyElement
PUBLIC	?wkeRunJsByFrame@@3P6A_JPAVCWebView@wke@@PAXPBD_N@ZA ; wkeRunJsByFrame
PUBLIC	?wkeNetOnResponse@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD1@Z1@ZA ; wkeNetOnResponse
PUBLIC	?wkeCopy@@3P6AXPAVCWebView@wke@@@ZA		; wkeCopy
PUBLIC	?mbWebFrameGetMainWorldScriptContext@@3P6GXHPAX0@ZA ; mbWebFrameGetMainWorldScriptContext
PUBLIC	?jsBindGetter@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@Z@ZA ; jsBindGetter
PUBLIC	?jsFunction@@3P6A_JPAUJsExecStateInfo@@PAUtagjsData@@@ZA ; jsFunction
PUBLIC	?wkeIsWebviewAlive@@3P6A_NH@ZA			; wkeIsWebviewAlive
PUBLIC	?wkeNetGetHTTPHeaderField@@3P6APBDPAXPBD@ZA	; wkeNetGetHTTPHeaderField
PUBLIC	?wkeNetStartUrlRequest@@3P6AHPAVCWebView@wke@@PAUwkeWebUrlRequest@@PAXPBU_wkeUrlRequestCallbacks@@@ZA ; wkeNetStartUrlRequest
PUBLIC	?mbSetDiskCacheLimitDisk@@3P6GXHI@ZA		; mbSetDiskCacheLimitDisk
PUBLIC	?wkeHeight@@3P6AHPAVCWebView@wke@@@ZA		; wkeHeight
PUBLIC	?wkeEditorUndo@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorUndo
PUBLIC	?mbSetDiskCachePath@@3P6GXHPB_W@ZA		; mbSetDiskCachePath
PUBLIC	?wkeSetViewNetInterface@@3P6AXPAVCWebView@wke@@PBD@ZA ; wkeSetViewNetInterface
PUBLIC	?wkeUnfocus@@3P6AXPAVCWebView@wke@@@ZA		; wkeUnfocus
PUBLIC	?wkeWebFrameGetMainWorldScriptContext@@3P6AXPAVCWebView@wke@@PAX1@ZA ; wkeWebFrameGetMainWorldScriptContext
PUBLIC	?mbGetProcAddr@@3P6GPAXPBD@ZA			; mbGetProcAddr
PUBLIC	?wkeOnConsole@@3P6AXPAVCWebView@wke@@P6AX0PAXW4_wkeConsoleLevel@@QAVCString@2@3I3@Z1@ZA ; wkeOnConsole
PUBLIC	?jsString@@3P6A_JPAUJsExecStateInfo@@PBD@ZA	; jsString
PUBLIC	?wkeOnWillReleaseScriptContext@@3P6AXPAVCWebView@wke@@P6AX0PAX11H@Z1@ZA ; wkeOnWillReleaseScriptContext
PUBLIC	?mbSetContextMenuEnabled@@3P6GXHH@ZA		; mbSetContextMenuEnabled
PUBLIC	?jsIsUndefined@@3P6A_N_J@ZA			; jsIsUndefined
PUBLIC	?jsArrayBuffer@@3P6A_JPAUJsExecStateInfo@@PADI@ZA ; jsArrayBuffer
PUBLIC	?wkeLoadHTMLW@@3P6AXPAVCWebView@wke@@PB_W@ZA	; wkeLoadHTMLW
PUBLIC	?mbOnPrinting@@3P6GHHP6GHHPAXW4_mbPrintintStep@@PAUHDC__@@PBU_mbPrintintSettings@@H@Z0@ZA ; mbOnPrinting
PUBLIC	?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS1<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
PUBLIC	?wkeCreateWebView@@3P6APAVCWebView@wke@@XZA	; wkeCreateWebView
PUBLIC	?wkeNetCancelRequest@@3P6AXPAX@ZA		; wkeNetCancelRequest
PUBLIC	?mbNetGetHTTPHeaderField@@3P6GPBDPAXPBDH@ZA	; mbNetGetHTTPHeaderField
PUBLIC	?wkeOnDownload@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD@Z1@ZA ; wkeOnDownload
PUBLIC	?wkeCreateWebWindow@@3P6APAVCWebView@wke@@W4_wkeWindowType@@PAUHWND__@@HHHH@ZA ; wkeCreateWebWindow
PUBLIC	?mbOnLoadUrlFinish@@3P6GXHP6GXHPAXPBD0H@Z0@ZA	; mbOnLoadUrlFinish
PUBLIC	?mbFastReload@@3P6GXH@ZA			; mbFastReload
PUBLIC	?jsReleaseRef@@3P6A_NPAUJsExecStateInfo@@_J@ZA	; jsReleaseRef
PUBLIC	?wkeSetHandleOffset@@3P6AXPAVCWebView@wke@@HH@ZA ; wkeSetHandleOffset
PUBLIC	?mbSetNpapiPluginsEnabled@@3P6GXHH@ZA		; mbSetNpapiPluginsEnabled
PUBLIC	?wkeResize@@3P6AXPAVCWebView@wke@@HH@ZA		; wkeResize
PUBLIC	?jsToDouble@@3P6ANPAUJsExecStateInfo@@_J@ZA	; jsToDouble
PUBLIC	?mbUtilDecodeURLEscape@@3P6GPBDPBD@ZA		; mbUtilDecodeURLEscape
PUBLIC	?mbSetAudioMuted@@3P6GXHH@ZA			; mbSetAudioMuted
PUBLIC	?mbCreateString@@3P6GPAUmbString@@PBDI@ZA	; mbCreateString
PUBLIC	?wkeDragTargetDrop@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1H@ZA ; wkeDragTargetDrop
PUBLIC	?mbOnBlinkThreadInit@@3P6GXP6GXPAX0@Z00@ZA	; mbOnBlinkThreadInit
PUBLIC	?mbSetWindowTitle@@3P6GXHPBD@ZA			; mbSetWindowTitle
PUBLIC	?jsIsValidExecState@@3P6A_NPAUJsExecStateInfo@@@ZA ; jsIsValidExecState
PUBLIC	?wkeNetFreePostBodyElement@@3P6AXPAU_wkePostBodyElement@@@ZA ; wkeNetFreePostBodyElement
PUBLIC	?wkeNetGetExpectedContentLength@@3P6A_JPAUwkeWebUrlResponse@@@ZA ; wkeNetGetExpectedContentLength
PUBLIC	?wkeSetViewSettings@@3P6AXPAVCWebView@wke@@PBU_wkeViewSettings@@@ZA ; wkeSetViewSettings
PUBLIC	?wkeSetClientHandler@@3P6AXPAVCWebView@wke@@PBU_wkeClientHandler@@@ZA ; wkeSetClientHandler
PUBLIC	?mbGetStringLen@@3P6GIPAUmbString@@@ZA		; mbGetStringLen
PUBLIC	?wkeNetGetMIMEType@@3P6APBDPAXPAVCString@wke@@@ZA ; wkeNetGetMIMEType
PUBLIC	?wkeKeyUp@@3P6A_NPAVCWebView@wke@@II_N@ZA	; wkeKeyUp
PUBLIC	?mbOnClose@@3P6GHHP6GHHPAX0@Z0@ZA		; mbOnClose
PUBLIC	?wkeAddNpapiPlugin@@3P6AXPAVCWebView@wke@@PAX11@ZA ; wkeAddNpapiPlugin
PUBLIC	?jsEmptyArray@@3P6A_JPAUJsExecStateInfo@@@ZA	; jsEmptyArray
PUBLIC	?jsIsArray@@3P6A_N_J@ZA				; jsIsArray
PUBLIC	?mbLoadURL@@3P6GXHPBD@ZA			; mbLoadURL
PUBLIC	?mbSetZoomFactor@@3P6GXHM@ZA			; mbSetZoomFactor
PUBLIC	?g_hMiniblinkMod@@3PAUHINSTANCE__@@A		; g_hMiniblinkMod
PUBLIC	?wkeSetCurlHookUrl@@3P6AXP6APAXPBD@Z@ZA		; wkeSetCurlHookUrl
PUBLIC	?wkeRegisterEmbedderCustomElement@@3P6A_NPAVCWebView@wke@@PAXPBD11@ZA ; wkeRegisterEmbedderCustomElement
PUBLIC	?mbSetContextMenuItemShow@@3P6GXHW4_mbMenuItemId@@H@ZA ; mbSetContextMenuItemShow
PUBLIC	?wkeSetZoomFactor@@3P6AXPAVCWebView@wke@@M@ZA	; wkeSetZoomFactor
PUBLIC	?jsStringW@@3P6A_JPAUJsExecStateInfo@@PB_W@ZA	; jsStringW
PUBLIC	?wkeSetMouseEnabled@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetMouseEnabled
PUBLIC	?wkeGlobalExec@@3P6APAUJsExecStateInfo@@PAVCWebView@wke@@@ZA ; wkeGlobalExec
PUBLIC	?wkeSetDirty@@3P6AXPAVCWebView@wke@@_N@ZA	; wkeSetDirty
PUBLIC	?jsSetAt@@3P6AXPAUJsExecStateInfo@@_JH1@ZA	; jsSetAt
PUBLIC	?wkeJsBindFunction@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2I@ZA ; wkeJsBindFunction
PUBLIC	?jsArgType@@3P6A?AW4_jsType@@PAUJsExecStateInfo@@H@ZA ; jsArgType
PUBLIC	?mbRegisterEmbedderCustomElement@@3P6GHHPAXPBD00@ZA ; mbRegisterEmbedderCustomElement
PUBLIC	?mbCreateMemBuf@@3P6GPAU_mbMemBuf@@HPAXI@ZA	; mbCreateMemBuf
PUBLIC	?kMbMainDllPath@@3PB_WB				; kMbMainDllPath
PUBLIC	?mbOnLoadUrlFail@@3P6GXHP6GXHPAXPBD0@Z0@ZA	; mbOnLoadUrlFail
PUBLIC	?wkeCreateStringW@@3P6APAVCString@wke@@PB_WI@ZA	; wkeCreateStringW
PUBLIC	?mbMoveWindow@@3P6GXHHHHH@ZA			; mbMoveWindow
PUBLIC	?jsAddRef@@3P6A_NPAUJsExecStateInfo@@_J@ZA	; jsAddRef
PUBLIC	?wkeVersionString@@3P6APBDXZA			; wkeVersionString
PUBLIC	?mbSetDebugConfig@@3P6GXHPBD0@ZA		; mbSetDebugConfig
PUBLIC	?wkeGetStringW@@3P6APB_WQAVCString@wke@@@ZA	; wkeGetStringW
PUBLIC	?wkeOnLoadUrlFail@@3P6AXPAVCWebView@wke@@P6AX0PAXPBD1@Z1@ZA ; wkeOnLoadUrlFail
PUBLIC	?mbCallBlinkThreadAsync@@3P6GXP6GXPAX0@Z00@ZA	; mbCallBlinkThreadAsync
PUBLIC	?wkePaint2@@3P6AXPAVCWebView@wke@@PAXHHHHHHHH_N@ZA ; wkePaint2
PUBLIC	?mbNetCreateWebUrlRequest@@3P6GPAUmbWebUrlRequest@@PBD00@ZA ; mbNetCreateWebUrlRequest
PUBLIC	?wkeIsMainFrame@@3P6A_NPAVCWebView@wke@@PAX@ZA	; wkeIsMainFrame
PUBLIC	?wkeGetTempCallbackInfo@@3P6APAU_wkeTempCallbackInfo@@PAVCWebView@wke@@@ZA ; wkeGetTempCallbackInfo
PUBLIC	?mbJsToBoolean@@3P6GHPAX_J@ZA			; mbJsToBoolean
PUBLIC	?mbUtilPrintToBitmap@@3P6GXHPAXPBU_mbScreenshotSettings@@P6GXH0PBDI@Z0@ZA ; mbUtilPrintToBitmap
PUBLIC	?wkeGetDocumentCompleteURL@@3P6APBDPAVCWebView@wke@@PAXPBD@ZA ; wkeGetDocumentCompleteURL
PUBLIC	?mbFireKeyDownEvent@@3P6GHHIIH@ZA		; mbFireKeyDownEvent
PUBLIC	?jsToTempString@@3P6APBDPAUJsExecStateInfo@@_J@ZA ; jsToTempString
PUBLIC	?mbNavigateAtIndex@@3P6GXHH@ZA			; mbNavigateAtIndex
PUBLIC	?mbOnThreadIdle@@3P6GXP6GXPAX0@Z00@ZA		; mbOnThreadIdle
PUBLIC	?jsGetLastErrorIfException@@3P6APAU_jsExceptionInfo@@PAUJsExecStateInfo@@@ZA ; jsGetLastErrorIfException
PUBLIC	?wkeGetWidth@@3P6AHPAVCWebView@wke@@@ZA		; wkeGetWidth
PUBLIC	?mbOnNetGetFavicon@@3P6GXHP6GXHPAXPBDPAU_mbMemBuf@@@Z0@ZA ; mbOnNetGetFavicon
PUBLIC	?mbFreeMemBuf@@3P6GXPAU_mbMemBuf@@@ZA		; mbFreeMemBuf
PUBLIC	?mbUtilSerializeToMHTML@@3P6GXHP6GXHPAXPBD@Z0@ZA ; mbUtilSerializeToMHTML
PUBLIC	?wkeGetURL@@3P6APBDPAVCWebView@wke@@@ZA		; wkeGetURL
PUBLIC	?mbSetMouseEnabled@@3P6GXHH@ZA			; mbSetMouseEnabled
PUBLIC	?jsBindSetter@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@Z@ZA ; jsBindSetter
PUBLIC	?mbGetJsValueType@@3P6G?AW4mbJsType@@PAX_J@ZA	; mbGetJsValueType
PUBLIC	?wkeWebViewName@@3P6APBDPAVCWebView@wke@@@ZA	; wkeWebViewName
PUBLIC	?wkeOnURLChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA ; wkeOnURLChanged
PUBLIC	?mbUtilIsRegistered@@3P6GHPB_W@ZA		; mbUtilIsRegistered
PUBLIC	?wkeSetTouchEnabled@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetTouchEnabled
PUBLIC	?mbOnConfirmBox@@3P6GXHP6GHHPAXPBD@Z0@ZA	; mbOnConfirmBox
PUBLIC	?wkeFireContextMenuEvent@@3P6A_NPAVCWebView@wke@@HHI@ZA ; wkeFireContextMenuEvent
PUBLIC	?mbUtilCreateRequestCode@@3P6GPBDPBD@ZA		; mbUtilCreateRequestCode
PUBLIC	?jsGetKeys@@3P6APAU_jsKeys@@PAUJsExecStateInfo@@_J@ZA ; jsGetKeys
PUBLIC	?wkeCookieEnabled@@3P6A_NPAVCWebView@wke@@@ZA	; wkeCookieEnabled
PUBLIC	?wkeDragTargetDragOver@@3P6A?AW4_wkeWebDragOperation@@PAVCWebView@wke@@PBUtagPOINT@@1W41@H@ZA ; wkeDragTargetDragOver
PUBLIC	?mbGetwkeView@@3P6GPAXH@ZA			; mbGetwkeView
PUBLIC	?mbOnLoadUrlEnd@@3P6GXHP6GXHPAXPBD00H@Z0@ZA	; mbOnLoadUrlEnd
PUBLIC	?mbPopupDownloadMgr@@3P6GHHPBDPAX@ZA		; mbPopupDownloadMgr
PUBLIC	?wkeLayoutIfNeeded@@3P6AXPAVCWebView@wke@@@ZA	; wkeLayoutIfNeeded
PUBLIC	?jsFalse@@3P6A_JXZA				; jsFalse
PUBLIC	?wkeGetContentHeight@@3P6AHPAVCWebView@wke@@@ZA	; wkeGetContentHeight
PUBLIC	?wkeGetContentAsMarkup@@3P6APBDPAVCWebView@wke@@PAXPAI@ZA ; wkeGetContentAsMarkup
PUBLIC	?wkeSetMemoryCacheEnable@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetMemoryCacheEnable
PUBLIC	?jsGet@@3P6A_JPAUJsExecStateInfo@@_JPBD@ZA	; jsGet
PUBLIC	?wkeSetUserKeyValue@@3P6AXPAVCWebView@wke@@PBDPAX@ZA ; wkeSetUserKeyValue
PUBLIC	?mbNetEnableResPacket@@3P6GXHPB_W@ZA		; mbNetEnableResPacket
PUBLIC	?wkeSaveMemoryCache@@3P6AXPAVCWebView@wke@@@ZA	; wkeSaveMemoryCache
PUBLIC	?mbGetCookieOnBlinkThread@@3P6GPBDH@ZA		; mbGetCookieOnBlinkThread
PUBLIC	?wkeGetWebViewByNData@@3P6APAVCWebView@wke@@PAX@ZA ; wkeGetWebViewByNData
PUBLIC	?wkePostURL@@3P6AXPAVCWebView@wke@@PBD1H@ZA	; wkePostURL
PUBLIC	?wkeReload@@3P6AXPAVCWebView@wke@@@ZA		; wkeReload
PUBLIC	?mbSetDiskCacheLimit@@3P6GXHI@ZA		; mbSetDiskCacheLimit
PUBLIC	?mbGetHostHWND@@3P6GPAUHWND__@@H@ZA		; mbGetHostHWND
PUBLIC	?wkeOnAlertBox@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA ; wkeOnAlertBox
PUBLIC	?wkeRunMessageLoop@@3P6AXXZA			; wkeRunMessageLoop
PUBLIC	?mbSetCookie@@3P6GXHPBD0@ZA			; mbSetCookie
PUBLIC	?mbOnDocumentReadyInBlinkThread@@3P6GXHP6GXHPAX0@Z0@ZA ; mbOnDocumentReadyInBlinkThread
PUBLIC	?wkeGetTitle@@3P6APBDPAVCWebView@wke@@@ZA	; wkeGetTitle
PUBLIC	?mbSetHandleOffset@@3P6GXHHH@ZA			; mbSetHandleOffset
PUBLIC	?wkeDragTargetDragEnter@@3P6A?AW4_wkeWebDragOperation@@PAVCWebView@wke@@PBU_wkeWebDragData@@PBUtagPOINT@@2W41@H@ZA ; wkeDragTargetDragEnter
PUBLIC	?wkeIsLoadFailed@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsLoadFailed
PUBLIC	?wkeZoomFactor@@3P6AMPAVCWebView@wke@@@ZA	; wkeZoomFactor
PUBLIC	?mbEditorCut@@3P6GXH@ZA				; mbEditorCut
PUBLIC	?mbOnLoadUrlHeadersReceived@@3P6GXHP6GXHPAXPBD0@Z0@ZA ; mbOnLoadUrlHeadersReceived
PUBLIC	?mbAddPluginDirectory@@3P6GXHPB_W@ZA		; mbAddPluginDirectory
PUBLIC	?wkeFocus@@3P6AXPAVCWebView@wke@@@ZA		; wkeFocus
PUBLIC	_IID_IAxWinHostWindow
PUBLIC	?mbUnlockViewDC@@3P6GXH@ZA			; mbUnlockViewDC
PUBLIC	?mbGetNavigateIndex@@3P6GHH@ZA			; mbGetNavigateIndex
PUBLIC	?wkeNetHoldJobToAsynCommit@@3P6AHPAX@ZA		; wkeNetHoldJobToAsynCommit
PUBLIC	?wkeSetLanguage@@3P6AXPAVCWebView@wke@@PBD@ZA	; wkeSetLanguage
PUBLIC	?wkeMoveToCenter@@3P6AXPAVCWebView@wke@@@ZA	; wkeMoveToCenter
PUBLIC	?wkeTitle@@3P6APBDPAVCWebView@wke@@@ZA		; wkeTitle
PUBLIC	?wkeJsBindSetter@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2@ZA ; wkeJsBindSetter
PUBLIC	?wkeSetDragEnable@@3P6AXPAVCWebView@wke@@_N@ZA	; wkeSetDragEnable
PUBLIC	?wkeEditorPaste@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorPaste
PUBLIC	?mbEditorRedo@@3P6GXH@ZA			; mbEditorRedo
PUBLIC	_IID_IPrintDialogCallback
PUBLIC	?mbFireKeyUpEvent@@3P6GHHIIH@ZA			; mbFireKeyUpEvent
PUBLIC	?mbOnURLChanged@@3P6GXHP6GXHPAXPBDHH@Z0@ZA	; mbOnURLChanged
PUBLIC	?jsIsFunction@@3P6A_N_J@ZA			; jsIsFunction
PUBLIC	?mbSetCookieJarPath@@3P6GXHPB_W@ZA		; mbSetCookieJarPath
PUBLIC	?mbGetCookie@@**********************************@@PBD@Z0@ZA ; mbGetCookie
PUBLIC	?jsSetLength@@3P6AXPAUJsExecStateInfo@@_JH@ZA	; jsSetLength
PUBLIC	?wkeDelete@@3P6AXPAVCWebView@wke@@@ZA		; wkeDelete
PUBLIC	?mbUtilSetDefaultPrinterSettings@@3P6GXHPBU_mbDefaultPrinterSettings@@@ZA ; mbUtilSetDefaultPrinterSettings
PUBLIC	?wkeSelectAll@@3P6AXPAVCWebView@wke@@@ZA	; wkeSelectAll
PUBLIC	?jsToV8Value@@3P6APAXPAUJsExecStateInfo@@_J@ZA	; jsToV8Value
PUBLIC	?mbOnPluginList@@3P6GXHP6GHHPAX0@Z0@ZA		; mbOnPluginList
PUBLIC	?mbGetString@@3P6GPBDPAUmbString@@@ZA		; mbGetString
PUBLIC	?wkeOnDocumentReady2@@3P6AXPAVCWebView@wke@@P6AX0PAX1@Z1@ZA ; wkeOnDocumentReady2
PUBLIC	?mbShowWindow@@3P6GXHH@ZA			; mbShowWindow
PUBLIC	?jsEval@@3P6A_JPAUJsExecStateInfo@@PBD@ZA	; jsEval
PUBLIC	?jsEmptyObject@@3P6A_JPAUJsExecStateInfo@@@ZA	; jsEmptyObject
PUBLIC	?jsToTempStringW@@3P6APB_WPAUJsExecStateInfo@@_J@ZA ; jsToTempStringW
PUBLIC	?jsTypeOf@@3P6A?AW4_jsType@@_J@ZA		; jsTypeOf
PUBLIC	_IID_IDocHostUIHandlerDispatch
PUBLIC	?mbNetSetWebsocketCallback@@3P6GXHPBU_mbWebsocketHookCallbacks@@PAX@ZA ; mbNetSetWebsocketCallback
PUBLIC	?mbNetSendWsBlob@@3P6GXPAXPBDI@ZA		; mbNetSendWsBlob
PUBLIC	?jsSetGlobal@@3P6AXPAUJsExecStateInfo@@PBD_J@ZA	; jsSetGlobal
PUBLIC	?wkeInsertCSSByFrame@@3P6AXPAVCWebView@wke@@PAXPBD@ZA ; wkeInsertCSSByFrame
PUBLIC	?mbIsAudioMuted@@3P6GHH@ZA			; mbIsAudioMuted
PUBLIC	?wkeIsWebRemoteFrame@@3P6A_NPAVCWebView@wke@@PAX@ZA ; wkeIsWebRemoteFrame
PUBLIC	?jsInt@@3P6A_JH@ZA				; jsInt
PUBLIC	?wkeCreateString@@3P6APAVCString@wke@@PBDI@ZA	; wkeCreateString
PUBLIC	?mbUtilPrintToPdf@@3P6GXHPAXPBU_mbPrintSettings@@P6GXH0PBU_mbPdfDatas@@@Z0@ZA ; mbUtilPrintToPdf
PUBLIC	?jsGetData@@3P6APAUtagjsData@@PAUJsExecStateInfo@@_J@ZA ; jsGetData
PUBLIC	?mbSetDeviceParameter@@3P6GXHPBD0HM@ZA		; mbSetDeviceParameter
PUBLIC	?jsThrowException@@3P6A_JPAUJsExecStateInfo@@PBD@ZA ; jsThrowException
PUBLIC	_LIBID_ATLLib
PUBLIC	?mbFireKeyPressEvent@@3P6GHHIIH@ZA		; mbFireKeyPressEvent
PUBLIC	?mbOnDestroy@@3P6GHHP6GHHPAX0@Z0@ZA		; mbOnDestroy
PUBLIC	?mbNetHookRequest@@3P6GXPAX@ZA			; mbNetHookRequest
PUBLIC	?wkeUtilCreateV8Snapshot@@3P6APBU_wkeMemBuf@@PBD@ZA ; wkeUtilCreateV8Snapshot
PUBLIC	?mbOnPromptBox@@3P6GXHP6GPAUmbString@@HPAXPBD1@Z0@ZA ; mbOnPromptBox
PUBLIC	?mbSetUserAgent@@3P6GXHPBD@ZA			; mbSetUserAgent
PUBLIC	?mbNetCreatePostBodyElements@@3P6GPAU_mbPostBodyElements@@HI@ZA ; mbNetCreatePostBodyElements
PUBLIC	?jsEvalW@@3P6A_JPAUJsExecStateInfo@@PB_W@ZA	; jsEvalW
PUBLIC	?wkeContentsHeight@@3P6AHPAVCWebView@wke@@@ZA	; wkeContentsHeight
PUBLIC	?jsIsObject@@3P6A_N_J@ZA			; jsIsObject
PUBLIC	?wkeLoadHTML@@3P6AXPAVCWebView@wke@@PBD@ZA	; wkeLoadHTML
PUBLIC	?jsToString@@3P6APBDPAUJsExecStateInfo@@_J@ZA	; jsToString
PUBLIC	?wkeEditorDelete@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorDelete
PUBLIC	?wkeMoveWindow@@3P6AXPAVCWebView@wke@@HHHH@ZA	; wkeMoveWindow
PUBLIC	?mbGoForward@@3P6GXH@ZA				; mbGoForward
PUBLIC	?wkeGetCursorInfoType@@3P6AHPAVCWebView@wke@@@ZA ; wkeGetCursorInfoType
PUBLIC	?jsBindFunction@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@ZI@ZA ; jsBindFunction
PUBLIC	?wkeWidth@@3P6AHPAVCWebView@wke@@@ZA		; wkeWidth
PUBLIC	?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS0<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
PUBLIC	?mbContextMenuItemClick@@3P6GXHP6G_NHPAXW4_wkeOnContextMenuItemClickType@@W4_wkeOnContextMenuItemClickStep@@00@Z0@ZA ; mbContextMenuItemClick
PUBLIC	?mbUtilCreateV8Snapshot@@3P6GPBU_mbMemBuf@@PBD@ZA ; mbUtilCreateV8Snapshot
PUBLIC	?wkeIsDocumentReady@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsDocumentReady
PUBLIC	?mbGetZoomFactor@@3P6GMH@ZA			; mbGetZoomFactor
PUBLIC	?wkeRepaintIfNeeded@@3P6AXPAVCWebView@wke@@@ZA	; wkeRepaintIfNeeded
PUBLIC	?mbIsMainFrame@@3P6GHHPAX@ZA			; mbIsMainFrame
PUBLIC	?wkeGetCookieW@@3P6APB_WPAVCWebView@wke@@@ZA	; wkeGetCookieW
PUBLIC	?wkeOnCreateView@@3P6AXPAVCWebView@wke@@P6APAV12@0PAXW4_wkeNavigationType@@QAVCString@2@PBU_wkeWindowFeatures@@@Z1@ZA ; wkeOnCreateView
PUBLIC	?wkeGetUserKeyValue@@3P6APAXPAVCWebView@wke@@PBD@ZA ; wkeGetUserKeyValue
PUBLIC	?mbDownloadByPath@@3P6G?AW4_mbDownloadOpt@@HPBU_mbDownloadOptions@@PB_WIPBD22PAXPAU_mbNetJobDataBind@@PAU_mbDownloadBind@@@ZA ; mbDownloadByPath
PUBLIC	?wkeNetDeleteBlinkWebURLRequestPtr@@3P6AXPAVWebURLRequest@blink@@@ZA ; wkeNetDeleteBlinkWebURLRequestPtr
PUBLIC	?wkeSleep@@3P6AXPAVCWebView@wke@@@ZA		; wkeSleep
PUBLIC	?strHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4VCWin32Heap@3@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strHeap
PUBLIC	?wkeNetCopyWebUrlRequest@@3P6APAVWebURLRequest@blink@@PAX_N@ZA ; wkeNetCopyWebUrlRequest
PUBLIC	?wkeOnDocumentReady@@3P6AXPAVCWebView@wke@@P6AX0PAX@Z1@ZA ; wkeOnDocumentReady
PUBLIC	?jsFloat@@3P6A_JM@ZA				; jsFloat
PUBLIC	?wkeGetViewDC@@3P6APAUHDC__@@PAVCWebView@wke@@@ZA ; wkeGetViewDC
PUBLIC	?mbGoBack@@3P6GXH@ZA				; mbGoBack
PUBLIC	?wkeUtilRelasePrintPdfDatas@@3P6AXPBU_wkePdfDatas@@@ZA ; wkeUtilRelasePrintPdfDatas
PUBLIC	?wkeSetName@@3P6AXPAVCWebView@wke@@PBD@ZA	; wkeSetName
PUBLIC	?wkeOnStartDragging@@3P6AXPAVCWebView@wke@@P6AX0PAX1PBU_wkeWebDragData@@W4_wkeWebDragOperation@@PBXPBU_wkePoint@@@Z1@ZA ; wkeOnStartDragging
PUBLIC	?mbCanGoForward@@**********************************@@H@Z0@ZA ; mbCanGoForward
PUBLIC	?mbCreateWebCustomWindow@@3P6GHPAUHWND__@@KKHHHH@ZA ; mbCreateWebCustomWindow
PUBLIC	?wkeSetNpapiPluginsEnabled@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetNpapiPluginsEnabled
PUBLIC	?wkeGetMediaVolume@@3P6AMPAVCWebView@wke@@@ZA	; wkeGetMediaVolume
PUBLIC	?wkePerformCookieCommand@@3P6AXPAVCWebView@wke@@W4_wkeCookieCommand@@@ZA ; wkePerformCookieCommand
PUBLIC	?mbNetGetExpectedContentLength@@3P6G_JPAUmbWebUrlResponse@@@ZA ; mbNetGetExpectedContentLength
PUBLIC	?wkeIsAwake@@3P6A_NPAVCWebView@wke@@@ZA		; wkeIsAwake
PUBLIC	?wkeEditorCopy@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorCopy
PUBLIC	?mbNetAddHTTPHeaderFieldToUrlRequest@@3P6GXPAUmbWebUrlRequest@@PBD1@ZA ; mbNetAddHTTPHeaderFieldToUrlRequest
PUBLIC	?mbOnAcceleratedPaint@@3P6GXHP6GXHPAXHPBU_mbRect@@I0@Z0@ZA ; mbOnAcceleratedPaint
PUBLIC	?wkeOnPluginFind@@3P6AXPAVCWebView@wke@@PBDP6AX0PAX1222@Z2@ZA ; wkeOnPluginFind
PUBLIC	?wkeNetGetRequestMethod@@3P6A?AW4_wkeRequestType@@PAX@ZA ; wkeNetGetRequestMethod
PUBLIC	?wkeGetWebviewId@@3P6AHPAVCWebView@wke@@@ZA	; wkeGetWebviewId
PUBLIC	?jsToStringW@@3P6APB_WPAUJsExecStateInfo@@_J@ZA	; jsToStringW
PUBLIC	?wkeOnWillMediaLoad@@3P6AXPAVCWebView@wke@@P6AX0PAXPBDPAU_wkeMediaLoadInfo@@@Z1@ZA ; wkeOnWillMediaLoad
PUBLIC	?wkeEditorRedo@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorRedo
PUBLIC	?wkeNetGetFavicon@@3P6AHPAVCWebView@wke@@P6AX0PAXPBDPAU_wkeMemBuf@@@Z1@ZA ; wkeNetGetFavicon
PUBLIC	?wkeGetVersion@@3P6AIXZA			; wkeGetVersion
PUBLIC	?wkeShutdown@@3P6AXXZA				; wkeShutdown
PUBLIC	?jsSet@@3P6AXPAUJsExecStateInfo@@_JPBD1@ZA	; jsSet
PUBLIC	?jsArgCount@@3P6AHPAUJsExecStateInfo@@@ZA	; jsArgCount
PUBLIC	?wkeOnLoadingFinish@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@W4_wkeLoadingResult@@2@Z1@ZA ; wkeOnLoadingFinish
PUBLIC	?mbNetGetResponseUrl@@3P6GPBDPAUmbWebUrlResponse@@@ZA ; mbNetGetResponseUrl
PUBLIC	?wkeSetViewProxy@@3P6AXPAVCWebView@wke@@PAU_wkeProxy@@@ZA ; wkeSetViewProxy
PUBLIC	?wkeOnTitleChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA ; wkeOnTitleChanged
PUBLIC	?mbOnImageBufferToDataURL@@3P6GXHP6GPAUmbString@@HPAXPBDI@Z0@ZA ; mbOnImageBufferToDataURL
PUBLIC	?wkeEditorCut@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorCut
PUBLIC	?wkeIsProcessingUserGesture@@3P6A_NPAVCWebView@wke@@@ZA ; wkeIsProcessingUserGesture
PUBLIC	?jsUndefined@@3P6A_JXZA				; jsUndefined
PUBLIC	?wkeGoBack@@3P6A_NPAVCWebView@wke@@@ZA		; wkeGoBack
PUBLIC	?wkeVersion@@3P6AIXZA				; wkeVersion
PUBLIC	?wkeOnPrint@@3P6AXPAVCWebView@wke@@P6AX0PAX11@Z1@ZA ; wkeOnPrint
PUBLIC	??_7CAtlStringMgr@ATL@@6B@			; ATL::CAtlStringMgr::`vftable'
PUBLIC	?jsIsNull@@3P6A_N_J@ZA				; jsIsNull
PUBLIC	?mbJsToDouble@@3P6GNPAX_J@ZA			; mbJsToDouble
PUBLIC	?wkeAddPluginDirectory@@3P6AXPAVCWebView@wke@@PB_W@ZA ; wkeAddPluginDirectory
PUBLIC	?jsDeleteObjectProp@@3P6AXPAUJsExecStateInfo@@_JPBD@ZA ; jsDeleteObjectProp
PUBLIC	?wkeMouseEvent@@3P6A_NPAVCWebView@wke@@IHHI@ZA	; wkeMouseEvent
PUBLIC	?wkeAddDirtyArea@@3P6AXPAVCWebView@wke@@HHHH@ZA	; wkeAddDirtyArea
PUBLIC	?mbNetGetPostBody@@3P6GPAU_mbPostBodyElements@@PAX@ZA ; mbNetGetPostBody
PUBLIC	?wkeGetCaretRect@@3P6A?AU_wkeRect@@PAVCWebView@wke@@@ZA ; wkeGetCaretRect
PUBLIC	?jsEvalExW@@3P6A_JPAUJsExecStateInfo@@PB_W_N@ZA	; jsEvalExW
PUBLIC	?mbSetTouchEnabled@@3P6GXHH@ZA			; mbSetTouchEnabled
PUBLIC	?mbWake@@3P6GXH@ZA				; mbWake
PUBLIC	?mbCallUiThreadSync@@3P6GXP6GXPAX0@Z00@ZA	; mbCallUiThreadSync
PUBLIC	?mbNetCancelRequest@@3P6GXPAX@ZA		; mbNetCancelRequest
PUBLIC	?kWkeDllPath@@3PB_WB				; kWkeDllPath
PUBLIC	?wkeSetContextMenuItemShow@@3P6AXPAVCWebView@wke@@W4_wkeMenuItemId@@_N@ZA ; wkeSetContextMenuItemShow
PUBLIC	?mbStopLoading@@3P6GXH@ZA			; mbStopLoading
PUBLIC	?mbNetFreePostBodyElement@@3P6GXPAU_mbPostBodyElement@@@ZA ; mbNetFreePostBodyElement
PUBLIC	?wkeSetNavigationToNewWindowEnable@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetNavigationToNewWindowEnable
PUBLIC	?jsGlobalObject@@3P6A_JPAUJsExecStateInfo@@@ZA	; jsGlobalObject
PUBLIC	?wkeResizeWindow@@3P6AXPAVCWebView@wke@@HH@ZA	; wkeResizeWindow
PUBLIC	?wkeIsLoadingFailed@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsLoadingFailed
PUBLIC	?mbNetSetData@@3P6GXPAX0H@ZA			; mbNetSetData
PUBLIC	?wkeLoadURL@@3P6AXPAVCWebView@wke@@PBD@ZA	; wkeLoadURL
PUBLIC	?mbUninit@@3P6GXXZA				; mbUninit
PUBLIC	?wkeGetDebugConfig@@3P6APAXPAVCWebView@wke@@PBD@ZA ; wkeGetDebugConfig
PUBLIC	?kMbDllPath@@3PB_WB				; kMbDllPath
PUBLIC	?mbSetDiskCacheLevel@@3P6GXHH@ZA		; mbSetDiskCacheLevel
PUBLIC	?mbRunJsSync@@3P6G_JHPAXPBDH@ZA			; mbRunJsSync
PUBLIC	?mbGoToOffset@@3P6GXHH@ZA			; mbGoToOffset
PUBLIC	?wkeFireMouseEvent@@3P6A_NPAVCWebView@wke@@IHHI@ZA ; wkeFireMouseEvent
PUBLIC	?wkeGoToOffset@@3P6AXPAVCWebView@wke@@H@ZA	; wkeGoToOffset
PUBLIC	?wkeIsLoaded@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsLoaded
PUBLIC	?wkeLoadFile@@3P6AXPAVCWebView@wke@@PBD@ZA	; wkeLoadFile
PUBLIC	?wkeContentsWidth@@3P6AHPAVCWebView@wke@@@ZA	; wkeContentsWidth
PUBLIC	?mbSetEditable@@3P6GXH_N@ZA			; mbSetEditable
PUBLIC	?wkeGetCaret@@3P6A?AU_wkeRect@@PAVCWebView@wke@@@ZA ; wkeGetCaret
PUBLIC	?wkeOnLoadUrlBegin@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD1@Z1@ZA ; wkeOnLoadUrlBegin
PUBLIC	?wkeGetGlobalExecByFrame@@3P6APAUJsExecStateInfo@@PAVCWebView@wke@@PAX@ZA ; wkeGetGlobalExecByFrame
PUBLIC	?wkeSetWebViewName@@3P6AXPAVCWebView@wke@@PBD@ZA ; wkeSetWebViewName
PUBLIC	?mbPluginListBuilderAddMediaTypeToLastPlugin@@3P6GXPAXPBD1@ZA ; mbPluginListBuilderAddMediaTypeToLastPlugin
PUBLIC	?wkeGetVersionString@@3P6APBDXZA		; wkeGetVersionString
PUBLIC	?jsCallGlobal@@3P6A_JPAUJsExecStateInfo@@_JPA_JH@ZA ; jsCallGlobal
PUBLIC	?mbOnDidCreateScriptContext@@3P6GXHP6GXHPAX00HH@Z0@ZA ; mbOnDidCreateScriptContext
PUBLIC	?wkeFinalize@@3P6AXXZA				; wkeFinalize
PUBLIC	?wkeOnPromptBox@@3P6AXPAVCWebView@wke@@P6A_N0PAXQAVCString@2@2PAV32@@Z1@ZA ; wkeOnPromptBox
PUBLIC	?jsGetAt@@3P6A_JPAUJsExecStateInfo@@_JH@ZA	; jsGetAt
PUBLIC	?mbOnCreateView@@********************************@@PBDPBUmbWindowFeatures@@@Z0@ZA ; mbOnCreateView
PUBLIC	?mbCreateStringWithoutNullTermination@@3P6GPAUmbString@@PBDI@ZA ; mbCreateStringWithoutNullTermination
PUBLIC	?mbEditorUndo@@3P6GXH@ZA			; mbEditorUndo
PUBLIC	?jsIsNumber@@3P6A_N_J@ZA			; jsIsNumber
PUBLIC	?jsGetWebView@@3P6APAVCWebView@wke@@PAUJsExecStateInfo@@@ZA ; jsGetWebView
PUBLIC	?mbResponseQuery@@3P6GXH_JHPBD@ZA		; mbResponseQuery
PUBLIC	?mbNetCancelWebUrlRequest@@3P6GXH@ZA		; mbNetCancelWebUrlRequest
PUBLIC	?wkeUtilBase64Encode@@3P6APBDPBD@ZA		; wkeUtilBase64Encode
PUBLIC	?wkeNetChangeRequestUrl@@3P6AXPAXPBD@ZA		; wkeNetChangeRequestUrl
PUBLIC	_IID_IInternalConnection
PUBLIC	?wkeSetFocus@@3P6AXPAVCWebView@wke@@@ZA		; wkeSetFocus
PUBLIC	?mbOnDownload@@3P6GXHP6GHHPAX0PBD0@Z0@ZA	; mbOnDownload
PUBLIC	?mbSetLocalStorageFullPath@@3P6GXHPB_W@ZA	; mbSetLocalStorageFullPath
PUBLIC	?wkePaint@@3P6AXPAVCWebView@wke@@PAXH@ZA	; wkePaint
PUBLIC	?mbOnLoadingFinish@@3P6GXHP6GXHPAX0PBDW4mbLoadingResult@@1@Z0@ZA ; mbOnLoadingFinish
PUBLIC	?mbGetUserKeyValue@@3P6GPAXHPBD@ZA		; mbGetUserKeyValue
PUBLIC	?wkeOnConfirmBox@@3P6AXPAVCWebView@wke@@P6A_N0PAXQAVCString@2@@Z1@ZA ; wkeOnConfirmBox
PUBLIC	?wkeContextMenuEvent@@3P6A_NPAVCWebView@wke@@HHI@ZA ; wkeContextMenuEvent
PUBLIC	?wkeIsCookieEnabled@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsCookieEnabled
PUBLIC	?mbSetSystemTouchEnabled@@3P6GXHH@ZA		; mbSetSystemTouchEnabled
PUBLIC	?wkeDragTargetEnd@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1W4_wkeWebDragOperation@@@ZA ; wkeDragTargetEnd
PUBLIC	?wkeGetFrameUrl@@3P6APBDPAVCWebView@wke@@PAX@ZA	; wkeGetFrameUrl
PUBLIC	?wkeOnWindowClosing@@3P6AXPAVCWebView@wke@@P6A_N0PAX@Z1@ZA ; wkeOnWindowClosing
PUBLIC	?wkeSetHandle@@3P6AXPAVCWebView@wke@@PAUHWND__@@@ZA ; wkeSetHandle
PUBLIC	?jsTrue@@3P6A_JXZA				; jsTrue
PUBLIC	?mbOnLoadUrlBegin@@3P6GXHP6GHHPAXPBD0@Z0@ZA	; mbOnLoadUrlBegin
PUBLIC	?wkeSetWindowTitleW@@3P6AXPAVCWebView@wke@@PB_W@ZA ; wkeSetWindowTitleW
PUBLIC	?wkePaste@@3P6AXPAVCWebView@wke@@@ZA		; wkePaste
PUBLIC	?mbNetOnResponse@@3P6GXHP6GHHPAXPBD0@Z0@ZA	; mbNetOnResponse
PUBLIC	?mbGetPdfPageData@@3P6GXHP6GXHPAX0I@Z0@ZA	; mbGetPdfPageData
PUBLIC	?mbLoadHtmlWithBaseUrl@@3P6GXHPBD0@ZA		; mbLoadHtmlWithBaseUrl
PUBLIC	?mbSetAutoDrawToHwnd@@3P6GXHH@ZA		; mbSetAutoDrawToHwnd
PUBLIC	?wkeLoadW@@3P6AXPAVCWebView@wke@@PB_W@ZA	; wkeLoadW
PUBLIC	?mbUtilBase64Decode@@3P6GPBDPBD@ZA		; mbUtilBase64Decode
PUBLIC	?jsToFloat@@3P6AMPAUJsExecStateInfo@@_J@ZA	; jsToFloat
PUBLIC	?mbRunMessageLoop@@3P6GXXZA			; mbRunMessageLoop
PUBLIC	?wkeSetResourceGc@@3P6AXPAVCWebView@wke@@J@ZA	; wkeSetResourceGc
PUBLIC	?wkeSetString@@3P6AXPAVCString@wke@@PBDI@ZA	; wkeSetString
PUBLIC	?mbNetGetMIMEType@@3P6GPBDPAX@ZA		; mbNetGetMIMEType
PUBLIC	?wkeIsDirty@@3P6A_NPAVCWebView@wke@@@ZA		; wkeIsDirty
PUBLIC	?mbPopupDialogAndDownload@@3P6G?AW4_mbDownloadOpt@@HPBU_mbDialogOptions@@IPBD11PAXPAU_mbNetJobDataBind@@PAU_mbDownloadBind@@@ZA ; mbPopupDialogAndDownload
PUBLIC	?wkeNetGetPostBody@@3P6APAU_wkePostBodyElements@@PAX@ZA ; wkeNetGetPostBody
PUBLIC	?mbGetLockedViewDC@@3P6GPAUHDC__@@H@ZA		; mbGetLockedViewDC
PUBLIC	?WM_ATLGETHOST@ATL@@3IA				; ATL::WM_ATLGETHOST
PUBLIC	?szValToken@ATL@@3QB_WB				; ATL::szValToken
PUBLIC	?WM_ATLGETCONTROL@ATL@@3IA			; ATL::WM_ATLGETCONTROL
PUBLIC	?_pAtlAutoThreadModule@ATL@@3PAUIAtlAutoThreadModule@1@A ; ATL::_pAtlAutoThreadModule
PUBLIC	?_pAtlModule@ATL@@3PAVCAtlModule@1@A		; ATL::_pAtlModule
PUBLIC	?chQuote@ATL@@3_WB				; ATL::chQuote
PUBLIC	?szDelete@ATL@@3QB_WB				; ATL::szDelete
PUBLIC	?_AtlRegisterPerUser@ATL@@3_NA			; ATL::_AtlRegisterPerUser
PUBLIC	?m_bInitFailed@CAtlBaseModule@ATL@@2_NA		; ATL::CAtlBaseModule::m_bInitFailed
PUBLIC	___pobjMapEntryLast
PUBLIC	?szBinaryVal@ATL@@3QB_WB			; ATL::szBinaryVal
PUBLIC	___pobjMapEntryFirst
PUBLIC	?szForceRemove@ATL@@3QB_WB			; ATL::szForceRemove
PUBLIC	?szStringVal@ATL@@3QB_WB			; ATL::szStringVal
PUBLIC	?multiszStringVal@ATL@@3QB_WB			; ATL::multiszStringVal
PUBLIC	?chDirSep@ATL@@3_WB				; ATL::chDirSep
PUBLIC	?_pPerfRegFunc@ATL@@3P6AJPAUHINSTANCE__@@@ZA	; ATL::_pPerfRegFunc
PUBLIC	?szNoRemove@ATL@@3QB_WB				; ATL::szNoRemove
PUBLIC	__pAtlLocaleNameToIndexTable
PUBLIC	?chRightBracket@ATL@@3_WB			; ATL::chRightBracket
PUBLIC	__pAtlLcidToLocaleNameTable
PUBLIC	?chEquals@ATL@@3_WB				; ATL::chEquals
PUBLIC	?szDwordVal@ATL@@3QB_WB				; ATL::szDwordVal
PUBLIC	?_pModule@ATL@@3PAVCComModule@1@A		; ATL::_pModule
PUBLIC	?_pPerfUnRegFunc@ATL@@3P6AJXZA			; ATL::_pPerfUnRegFunc
PUBLIC	?chLeftBracket@ATL@@3_WB			; ATL::chLeftBracket
PUBLIC	?wkeSetMediaPlayerFactory@@3P6AXPAVCWebView@wke@@P6APAVWkeMediaPlayer@2@0PAVWkeMediaPlayerClient@2@PAX2@ZP6A_NPBD@Z@ZA ; wkeSetMediaPlayerFactory
PUBLIC	?wkeIsTransparent@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsTransparent
PUBLIC	_CLSID_Registrar
PUBLIC	?mbSetDragEnable@@3P6GXHH@ZA			; mbSetDragEnable
PUBLIC	?wkeSetDragFiles@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1QAPAVCString@2@H@ZA ; wkeSetDragFiles
PUBLIC	?mbGetGlobalExecByFrame@@3P6GPAXHPAX@ZA		; mbGetGlobalExecByFrame
PUBLIC	?mbCallUiThreadAsync@@3P6GXP6GXPAX0@Z00@ZA	; mbCallUiThreadAsync
PUBLIC	?wkeKillFocus@@3P6AXPAVCWebView@wke@@@ZA	; wkeKillFocus
PUBLIC	?wkeFireWindowsMessage@@3P6A_NPAVCWebView@wke@@PAUHWND__@@IIJPAJ@ZA ; wkeFireWindowsMessage
PUBLIC	?wkeUtilSetUiCallback@@3P6AXP6AHPAUHWND__@@P6AX0PAX@Z1@Z@ZA ; wkeUtilSetUiCallback
PUBLIC	?jsIsTrue@@3P6A_N_J@ZA				; jsIsTrue
PUBLIC	?wkeNetGetUrlByJob@@3P6APBDPAX@ZA		; wkeNetGetUrlByJob
PUBLIC	?mbSetViewProxy@@3P6GXHPBUmbProxy@@@ZA		; mbSetViewProxy
PUBLIC	?wkeEnableWindow@@3P6AXPAVCWebView@wke@@_N@ZA	; wkeEnableWindow
PUBLIC	?wkeSetTransparent@@3P6AXPAVCWebView@wke@@_N@ZA	; wkeSetTransparent
PUBLIC	?wkeKeyPress@@3P6A_NPAVCWebView@wke@@II_N@ZA	; wkeKeyPress
PUBLIC	?wkeGetClientHandler@@3P6APBU_wkeClientHandler@@PAVCWebView@wke@@@ZA ; wkeGetClientHandler
PUBLIC	?mbNetContinueJob@@3P6GXPAX@ZA			; mbNetContinueJob
PUBLIC	?mbSetTransparent@@3P6GXHH@ZA			; mbSetTransparent
PUBLIC	?mbOnTitleChanged@@3P6GXHP6GXHPAXPBD@Z0@ZA	; mbOnTitleChanged
PUBLIC	?mbPluginListBuilderAddPlugin@@3P6GXPAXPBD11@ZA	; mbPluginListBuilderAddPlugin
PUBLIC	?wkePostURLW@@3P6AXPAVCWebView@wke@@PB_WPBDH@ZA	; wkePostURLW
PUBLIC	?wkeGetString@@3P6APBDQAVCString@wke@@@ZA	; wkeGetString
PUBLIC	?mbKillFocus@@3P6GXH@ZA				; mbKillFocus
PUBLIC	?wkeSetCookie@@3P6AXPAVCWebView@wke@@PBD1@ZA	; wkeSetCookie
PUBLIC	?mbSetHeadlessEnabled@@3P6GXHH@ZA		; mbSetHeadlessEnabled
PUBLIC	?mbSetFocus@@3P6GXH@ZA				; mbSetFocus
PUBLIC	?wkeRunJSW@@3P6A_JPAVCWebView@wke@@PB_W@ZA	; wkeRunJSW
PUBLIC	?wkeDestroyWebView@@3P6AXPAVCWebView@wke@@@ZA	; wkeDestroyWebView
PUBLIC	?mbNetSetHTTPHeaderField@@3P6GXPAXPB_W1H@ZA	; mbNetSetHTTPHeaderField
PUBLIC	?jsObject@@3P6A_JPAUJsExecStateInfo@@PAUtagjsData@@@ZA ; jsObject
PUBLIC	?mbOnNavigationSync@@********************************@@PBD@Z0@ZA ; mbOnNavigationSync
PUBLIC	?mbEditorPaste@@3P6GXH@ZA			; mbEditorPaste
PUBLIC	?wkeIsLoadComplete@@3P6A_NPAVCWebView@wke@@@ZA	; wkeIsLoadComplete
PUBLIC	?wkeSetCookieJarPath@@3P6AXPAVCWebView@wke@@PB_W@ZA ; wkeSetCookieJarPath
PUBLIC	?wkeJsBindGetter@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2@ZA ; wkeJsBindGetter
PUBLIC	?wkeGetUserAgent@@3P6APBDPAVCWebView@wke@@@ZA	; wkeGetUserAgent
PUBLIC	?wkeUtilSerializeToMHTML@@3P6APBDPAVCWebView@wke@@@ZA ; wkeUtilSerializeToMHTML
PUBLIC	?wkeLoadFileW@@3P6AXPAVCWebView@wke@@PB_W@ZA	; wkeLoadFileW
PUBLIC	?jsNull@@3P6A_JXZA				; jsNull
PUBLIC	?mbPostURL@@3P6GXHPBD0H@ZA			; mbPostURL
PUBLIC	?mbSetWindowTitleW@@3P6GXHPB_W@ZA		; mbSetWindowTitleW
PUBLIC	?wkeGetSource@@3P6APBDPAVCWebView@wke@@@ZA	; wkeGetSource
PUBLIC	?wkeSetWindowTitle@@3P6AXPAVCWebView@wke@@PBD@ZA ; wkeSetWindowTitle
PUBLIC	?mbUtilBase64Encode@@3P6GPBDPBD@ZA		; mbUtilBase64Encode
PUBLIC	?wkeNetHookRequest@@3P6AXPAX@ZA			; wkeNetHookRequest
PUBLIC	?mbCreateWebWindow@@3P6GHW4_mbWindowType@@PAUHWND__@@HHHH@ZA ; mbCreateWebWindow
PUBLIC	?mbSetViewSettings@@3P6GXHPBU_mbViewSettings@@@ZA ; mbSetViewSettings
PUBLIC	?wkeOnPaintBitUpdated@@3P6AXPAVCWebView@wke@@P6AX0PAXPBXPBU_wkeRect@@HH@Z1@ZA ; wkeOnPaintBitUpdated
PUBLIC	?mbSetNavigationToNewWindowEnable@@3P6GXHH@ZA	; mbSetNavigationToNewWindowEnable
PUBLIC	?wkeIsLoadingCompleted@@3P6A_NPAVCWebView@wke@@@ZA ; wkeIsLoadingCompleted
PUBLIC	?wkeSetEditable@@3P6AXPAVCWebView@wke@@_N@ZA	; wkeSetEditable
PUBLIC	?mbOnWillReleaseScriptContext@@3P6GXHP6GXHPAX00H@Z0@ZA ; mbOnWillReleaseScriptContext
PUBLIC	?wkeOnContextMenuItemClick@@3P6AXPAVCWebView@wke@@P6A_N0PAXW4_wkeOnContextMenuItemClickType@@W4_wkeOnContextMenuItemClickStep@@11@Z1@ZA ; wkeOnContextMenuItemClick
PUBLIC	?wkeGetHostHWND@@3P6APAUHWND__@@PAVCWebView@wke@@@ZA ; wkeGetHostHWND
PUBLIC	?mbOnPaintUpdated@@3P6GXHP6GXHPAXQAUHDC__@@HHHH@Z0@ZA ; mbOnPaintUpdated
PUBLIC	?jsDouble@@3P6A_JN@ZA				; jsDouble
PUBLIC	?wkeSetProxy@@3P6AXPBU_wkeProxy@@@ZA		; wkeSetProxy
PUBLIC	?mbSetNodeJsEnable@@3P6GXHH@ZA			; mbSetNodeJsEnable
PUBLIC	?mbGetSource@@3P6GXHP6GXHPAXPBD@Z0@ZA		; mbGetSource
PUBLIC	?mbMoveToCenter@@3P6GXH@ZA			; mbMoveToCenter
PUBLIC	?wkeLoadURLW@@3P6AXPAVCWebView@wke@@PB_W@ZA	; wkeLoadURLW
PUBLIC	?mbInsertCSSByFrame@@3P6GXHPAXPBD@ZA		; mbInsertCSSByFrame
PUBLIC	?mbSetUserKeyValue@@3P6GXHPBDPAX@ZA		; mbSetUserKeyValue
PUBLIC	?mbCreateWebView@@3P6GHXZA			; mbCreateWebView
PUBLIC	?mbEditorCopy@@3P6GXH@ZA			; mbEditorCopy
PUBLIC	?wkeSetUIThreadCallback@@3P6AXPAVCWebView@wke@@P6AX0P6AX0PAX@Z1@Z1@ZA ; wkeSetUIThreadCallback
PUBLIC	?wkeOnWindowDestroy@@3P6AXPAVCWebView@wke@@P6AX0PAX@Z1@ZA ; wkeOnWindowDestroy
PUBLIC	?wkeSetCursorInfoType@@3P6AXPAVCWebView@wke@@H@ZA ; wkeSetCursorInfoType
PUBLIC	?wkeGC@@3P6AXPAVCWebView@wke@@J@ZA		; wkeGC
PUBLIC	?wkeOnDraggableRegionsChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXPBUwkeDraggableRegion@@H@Z1@ZA ; wkeOnDraggableRegionsChanged
PUBLIC	?wkeLoadHtmlWithBaseUrl@@3P6AXPAVCWebView@wke@@PBD1@ZA ; wkeLoadHtmlWithBaseUrl
PUBLIC	?wkeUtilPrintToPdf@@3P6APBU_wkePdfDatas@@PAVCWebView@wke@@PAXPBU_wkePrintSettings@@@ZA ; wkeUtilPrintToPdf
PUBLIC	?wkeGetName@@3P6APBDPAVCWebView@wke@@@ZA	; wkeGetName
PUBLIC	?wkeUpdate@@3P6AXXZA				; wkeUpdate
PUBLIC	?wkeGetZoomFactor@@3P6AMPAVCWebView@wke@@@ZA	; wkeGetZoomFactor
PUBLIC	?wkeMediaVolume@@3P6AMPAVCWebView@wke@@@ZA	; wkeMediaVolume
PUBLIC	?wkeSetMediaVolume@@3P6AXPAVCWebView@wke@@M@ZA	; wkeSetMediaVolume
PUBLIC	?jsGC@@3P6AXXZA					; jsGC
PUBLIC	?mbNetGetRequestMethod@@3P6G?AW4_mbRequestType@@PAX@ZA ; mbNetGetRequestMethod
PUBLIC	?wkeSetUserAgent@@3P6AXPAVCWebView@wke@@PBD@ZA	; wkeSetUserAgent
PUBLIC	?wkeSetDebugConfig@@3P6AXPAVCWebView@wke@@PBD1PBX2@ZA ; wkeSetDebugConfig
PUBLIC	?mbNetStartUrlRequest@@3P6GHHPAUmbWebUrlRequest@@PAXPBU_mbUrlRequestCallbacks@@@ZA ; mbNetStartUrlRequest
PUBLIC	?mbResize@@3P6GXHHH@ZA				; mbResize
PUBLIC	?mbNetFreePostBodyElements@@3P6GXPAU_mbPostBodyElements@@@ZA ; mbNetFreePostBodyElements
PUBLIC	?wkeOnMouseOverUrlChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA ; wkeOnMouseOverUrlChanged
PUBLIC	?wkeGoToIndex@@3P6AXPAVCWebView@wke@@H@ZA	; wkeGoToIndex
PUBLIC	?wkeToStringW@@3P6APB_WQAVCString@wke@@@ZA	; wkeToStringW
PUBLIC	?wkeNetCreateWebUrlRequest@@3P6APAUwkeWebUrlRequest@@PBD00@ZA ; wkeNetCreateWebUrlRequest
PUBLIC	?mbSetMemoryCacheEnable@@3P6GXHH@ZA		; mbSetMemoryCacheEnable
PUBLIC	?wkeGetCookie@@3P6APBDPAVCWebView@wke@@@ZA	; wkeGetCookie
PUBLIC	?jsCall@@3P6A_JPAUJsExecStateInfo@@_J1PA_JH@ZA	; jsCall
PUBLIC	?mbGetContentHeight@@3P6GHH@ZA			; mbGetContentHeight
PUBLIC	?mbJsToString@@3P6GPBDPAX_J@ZA			; mbJsToString
PUBLIC	?mbNetGetRawHttpHeadInBlinkThread@@3P6GPBU_mbSlist@@PAX@ZA ; mbNetGetRawHttpHeadInBlinkThread
PUBLIC	?mbSetProxy@@3P6GXHPBUmbProxy@@@ZA		; mbSetProxy
PUBLIC	?wkeOnDidCreateScriptContext@@3P6AXPAVCWebView@wke@@P6AX0PAX11HH@Z1@ZA ; wkeOnDidCreateScriptContext
PUBLIC	?mbEditorUnSelect@@3P6GXH@ZA			; mbEditorUnSelect
PUBLIC	?wkeOnOtherLoad@@3P6AXPAVCWebView@wke@@P6AX0PAXW4_wkeOtherLoadType@@PAU_wkeTempCallbackInfo@@@Z1@ZA ; wkeOnOtherLoad
PUBLIC	?wkeFireKeyPressEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA ; wkeFireKeyPressEvent
PUBLIC	?mbPluginListBuilderAddFileExtensionToLastMediaType@@3P6GXPAXPBD@ZA ; mbPluginListBuilderAddFileExtensionToLastMediaType
PUBLIC	?mbRunJs@@3P6GXHPAXPBDHP6GXH00_J@Z00@ZA		; mbRunJs
PUBLIC	?mbOnNavigation@@********************************@@PBD@Z0@ZA ; mbOnNavigation
PUBLIC	?wkeNetContinueJob@@3P6AXPAX@ZA			; wkeNetContinueJob
PUBLIC	?jsGetArrayBuffer@@3P6APAU_wkeMemBuf@@PAUJsExecStateInfo@@_J@ZA ; jsGetArrayBuffer
PUBLIC	?wkeSetDragDropEnable@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetDragDropEnable
PUBLIC	?mbOnPaintBitUpdated@@3P6GXHP6GXHPAXPBXPBU_mbRect@@HH@Z0@ZA ; mbOnPaintBitUpdated
PUBLIC	?mbOnNodeCreateProcess@@3P6GXHP6GXHPAXPB_W1PAU_STARTUPINFOW@@@Z0@ZA ; mbOnNodeCreateProcess
PUBLIC	?wkeEditorUnSelect@@3P6AXPAVCWebView@wke@@@ZA	; wkeEditorUnSelect
PUBLIC	?wkeWake@@3P6AXPAVCWebView@wke@@@ZA		; wkeWake
PUBLIC	?strMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4V23@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strMgr
PUBLIC	?wkeSetStringW@@3P6AXPAVCString@wke@@PB_WI@ZA	; wkeSetStringW
PUBLIC	?wkeDragTargetDragLeave@@3P6AXPAVCWebView@wke@@@ZA ; wkeDragTargetDragLeave
PUBLIC	?mbNetChangeRequestUrl@@3P6GXPAXPBD@ZA		; mbNetChangeRequestUrl
PUBLIC	?wkeOnPaintUpdated@@3P6AXPAVCWebView@wke@@P6AX0PAXQAUHDC__@@HHHH@Z1@ZA ; wkeOnPaintUpdated
PUBLIC	?wkeSetLocalStorageFullPath@@3P6AXPAVCWebView@wke@@PB_W@ZA ; wkeSetLocalStorageFullPath
PUBLIC	_IID_IAxWinAmbientDispatch
PUBLIC	?wkeNetGetHttpStatusCode@@3P6AHPAUwkeWebUrlResponse@@@ZA ; wkeNetGetHttpStatusCode
PUBLIC	?wkeOnLoadUrlEnd@@3P6AXPAVCWebView@wke@@P6AX0PAXPBD11H@Z1@ZA ; wkeOnLoadUrlEnd
PUBLIC	?mbGetCaretRect@@3P6GXHPAU_mbRect@@@ZA		; mbGetCaretRect
PUBLIC	?wkeFireKeyDownEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA ; wkeFireKeyDownEvent
PUBLIC	?jsIsJsValueValid@@3P6A_NPAUJsExecStateInfo@@_J@ZA ; jsIsJsValueValid
PUBLIC	?wkeRunJS@@3P6A_JPAVCWebView@wke@@PBD@ZA	; wkeRunJS
PUBLIC	_IID_IPrintDialogServices
PUBLIC	?wkeGetWebViewForCurrentContext@@3P6APAVCWebView@wke@@XZA ; wkeGetWebViewForCurrentContext
PUBLIC	?mbOnDocumentReady@@3P6GXHP6GXHPAX0@Z0@ZA	; mbOnDocumentReady
PUBLIC	?wkeCanGoBack@@3P6A_NPAVCWebView@wke@@@ZA	; wkeCanGoBack
PUBLIC	?mbGetWebViewForCurrentContext@@3P6GHXZA	; mbGetWebViewForCurrentContext
PUBLIC	?mbNetSetMIMEType@@3P6GXPAXPBD@ZA		; mbNetSetMIMEType
PUBLIC	?wkeSetCookieJarFullPath@@3P6AXPAVCWebView@wke@@PB_W@ZA ; wkeSetCookieJarFullPath
PUBLIC	?wkeCreateWebCustomWindow@@3P6APAVCWebView@wke@@PBU_wkeWindowCreateInfo@@@ZA ; wkeCreateWebCustomWindow
PUBLIC	?wkeOnURLChanged2@@3P6AXPAVCWebView@wke@@P6AX0PAX1QAVCString@2@@Z1@ZA ; wkeOnURLChanged2
PUBLIC	?mbNetHoldJobToAsynCommit@@3P6GHPAX@ZA		; mbNetHoldJobToAsynCommit
PUBLIC	?mbGoToIndex@@3P6GXHH@ZA			; mbGoToIndex
PUBLIC	?wkeMouseWheel@@3P6A_NPAVCWebView@wke@@HHHI@ZA	; wkeMouseWheel
PUBLIC	?wkeOnDownload2@@3P6AXPAVCWebView@wke@@P6A?AW4_wkeDownloadOpt@@0PAXIPBD221PAU_wkeNetJobDataBind@@@Z1@ZA ; wkeOnDownload2
PUBLIC	?jsGetCallstack@@3P6APBDPAUJsExecStateInfo@@@ZA	; jsGetCallstack
PUBLIC	?wkeSetUserAgentW@@3P6AXPAVCWebView@wke@@PB_W@ZA ; wkeSetUserAgentW
PUBLIC	?wkeConfigure@@3P6AXPBU_wkeSettings@@@ZA	; wkeConfigure
PUBLIC	?mbOnAlertBox@@3P6GXHP6GXHPAXPBD@Z0@ZA		; mbOnAlertBox
PUBLIC	?mbEditorDelete@@3P6GXH@ZA			; mbEditorDelete
PUBLIC	?wkeTitleW@@3P6APB_WPAVCWebView@wke@@@ZA	; wkeTitleW
PUBLIC	?mbDeleteString@@3P6GXPAUmbString@@@ZA		; mbDeleteString
PUBLIC	?mbSetCookieJarFullPath@@3P6GXHPB_W@ZA		; mbSetCookieJarFullPath
PUBLIC	?wkeNetCancelWebUrlRequest@@3P6AXH@ZA		; wkeNetCancelWebUrlRequest
PUBLIC	?mbSetCookieEnabled@@3P6GXHH@ZA			; mbSetCookieEnabled
PUBLIC	?mbSetInitSettings@@3P6GXPAU_mbSettings@@PBD1@ZA ; mbSetInitSettings
PUBLIC	?jsIsFalse@@3P6A_N_J@ZA				; jsIsFalse
PUBLIC	?mbUtilPrint@@3P6GHHPAXPBU_mbPrintSettings@@@ZA	; mbUtilPrint
PUBLIC	?mbClearCookie@@3P6GXH@ZA			; mbClearCookie
PUBLIC	?mbUtilEncodeURLEscape@@3P6GPBDPBD@ZA		; mbUtilEncodeURLEscape
PUBLIC	?mbReload@@3P6GXH@ZA				; mbReload
PUBLIC	?mbWebFrameGetMainFrame@@3P6GPAXH@ZA		; mbWebFrameGetMainFrame
PUBLIC	?mbSetResourceGc@@3P6GXHH@ZA			; mbSetResourceGc
PUBLIC	?mbCallBlinkThreadSync@@3P6GXP6GXPAX0@Z00@ZA	; mbCallBlinkThreadSync
PUBLIC	?mbDestroyWebView@@3P6GXH@ZA			; mbDestroyWebView
PUBLIC	?mbOnJsQuery@@3P6GXHP6GXHPAX0_JHPBD@Z0@ZA	; mbOnJsQuery
PUBLIC	?jsIsBoolean@@3P6A_N_J@ZA			; jsIsBoolean
PUBLIC	?wkeVisitAllCookie@@3P6AXPAVCWebView@wke@@PAXP6A_N1PBD222HHPAH@Z@ZA ; wkeVisitAllCookie
PUBLIC	?mbSetDiskCacheEnabled@@3P6GXHH@ZA		; mbSetDiskCacheEnabled
PUBLIC	?wkeOnNavigation@@3P6AXPAVCWebView@wke@@P6A_N0PAXW4_wkeNavigationType@@QAVCString@2@@Z1@ZA ; wkeOnNavigation
PUBLIC	_IID_IAxWinHostWindowLic
PUBLIC	?wkeNetCreatePostBodyElements@@3P6APAU_wkePostBodyElements@@PAVCWebView@wke@@I@ZA ; wkeNetCreatePostBodyElements
PUBLIC	?wkeGetWindowHandle@@3P6APAUHWND__@@PAVCWebView@wke@@@ZA ; wkeGetWindowHandle
PUBLIC	?wkeSetContextMenuEnabled@@3P6AXPAVCWebView@wke@@_N@ZA ; wkeSetContextMenuEnabled
PUBLIC	?mbGetBlinkMainThreadIsolate@@3P6GPAXXZA	; mbGetBlinkMainThreadIsolate
PUBLIC	?wkeAwaken@@3P6AXPAVCWebView@wke@@@ZA		; wkeAwaken
PUBLIC	?jsGetGlobal@@3P6A_JPAUJsExecStateInfo@@PBD@ZA	; jsGetGlobal
PUBLIC	?wkeGetContentWidth@@3P6AHPAVCWebView@wke@@@ZA	; wkeGetContentWidth
PUBLIC	?mbFireMouseEvent@@3P6GHHIHHI@ZA		; mbFireMouseEvent
EXTRN	_memset:PROC
EXTRN	_GetProcessHeap@0:PROC
EXTRN	__imp__GetProcessHeap@0:PROC
EXTRN	__imp__DeleteCriticalSection@4:PROC
EXTRN	_free:PROC
EXTRN	__imp__UnregisterClassW@8:PROC
EXTRN	_HeapDestroy@4:PROC
EXTRN	__imp__HeapDestroy@4:PROC
EXTRN	___ImageBase:BYTE
EXTRN	__imp__DecodePointer@4:PROC
EXTRN	__Init_thread_abort:PROC
EXTRN	__imp__SysFreeString@4:PROC
EXTRN	_HeapAlloc@12:PROC
EXTRN	__imp__HeapAlloc@12:PROC
EXTRN	__imp__RaiseException@16:PROC
EXTRN	_HeapReAlloc@16:PROC
EXTRN	__imp__HeapReAlloc@16:PROC
EXTRN	__Init_thread_header:PROC
EXTRN	_GetLastError@0:PROC
EXTRN	__imp__GetLastError@0:PROC
EXTRN	??3@YAXPAXI@Z:PROC				; operator delete
EXTRN	_atexit:PROC
EXTRN	_HeapSize@12:PROC
EXTRN	__imp__HeapSize@12:PROC
EXTRN	__Init_thread_footer:PROC
EXTRN	??_M@YGXPAXIIP6EX0@Z@Z:PROC			; `eh vector destructor iterator'
EXTRN	?_AtlBaseModule@ATL@@3VCAtlBaseModule@1@A:BYTE	; ATL::_AtlBaseModule
EXTRN	__imp__InitializeCriticalSectionAndSpinCount@8:PROC
EXTRN	__Init_thread_epoch:DWORD
EXTRN	_HeapFree@12:PROC
EXTRN	__imp__HeapFree@12:PROC
EXTRN	??_V@YAXPAXI@Z:PROC				; operator delete[]
EXTRN	___CxxFrameHandler3:PROC
;	COMDAT ?wkeNetFreePostBodyElements@@3P6AXPAU_wkePostBodyElements@@@ZA
_BSS	SEGMENT
?wkeNetFreePostBodyElements@@3P6AXPAU_wkePostBodyElements@@@ZA DD 01H DUP (?) ; wkeNetFreePostBodyElements
_BSS	ENDS
;	COMDAT ?mbGetCursorInfoType@@3P6GHH@ZA
_BSS	SEGMENT
?mbGetCursorInfoType@@3P6GHH@ZA DD 01H DUP (?)		; mbGetCursorInfoType
_BSS	ENDS
;	COMDAT ?wkeUtilEncodeURLEscape@@3P6APBDPBD@ZA
_BSS	SEGMENT
?wkeUtilEncodeURLEscape@@3P6APBDPBD@ZA DD 01H DUP (?)	; wkeUtilEncodeURLEscape
_BSS	ENDS
;	COMDAT ?mbUtilsSilentPrint@@3P6GHHPBD@ZA
_BSS	SEGMENT
?mbUtilsSilentPrint@@3P6GHHPBD@ZA DD 01H DUP (?)	; mbUtilsSilentPrint
_BSS	ENDS
;	COMDAT ?mbFireContextMenuEvent@@3P6GHHHHI@ZA
_BSS	SEGMENT
?mbFireContextMenuEvent@@3P6GHHHHI@ZA DD 01H DUP (?)	; mbFireContextMenuEvent
_BSS	ENDS
;	COMDAT ?mbCreateInitSettings@@3P6GPAU_mbSettings@@XZA
_BSS	SEGMENT
?mbCreateInitSettings@@3P6GPAU_mbSettings@@XZA DD 01H DUP (?) ; mbCreateInitSettings
_BSS	ENDS
;	COMDAT ?mbGetContentAsMarkup@@3P6GXHP6GXHPAXPBDI@Z00@ZA
_BSS	SEGMENT
?mbGetContentAsMarkup@@3P6GXHP6GXHPAXPBDI@Z00@ZA DD 01H DUP (?) ; mbGetContentAsMarkup
_BSS	ENDS
;	COMDAT ?mbJsToV8Value@@3P6GPAXPAX_J@ZA
_BSS	SEGMENT
?mbJsToV8Value@@3P6GPAXPAX_J@ZA DD 01H DUP (?)		; mbJsToV8Value
_BSS	ENDS
;	COMDAT ?wkeWebFrameGetMainFrame@@3P6APAXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeWebFrameGetMainFrame@@3P6APAXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeWebFrameGetMainFrame
_BSS	ENDS
;	COMDAT ?wkeCanGoForward@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeCanGoForward@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeCanGoForward
_BSS	ENDS
;	COMDAT ?mbGetTitle@@3P6GPBDH@ZA
_BSS	SEGMENT
?mbGetTitle@@3P6GPBDH@ZA DD 01H DUP (?)			; mbGetTitle
_BSS	ENDS
;	COMDAT ?wkeNetSetMIMEType@@3P6AXPAXPBD@ZA
_BSS	SEGMENT
?wkeNetSetMIMEType@@3P6AXPAXPBD@ZA DD 01H DUP (?)	; wkeNetSetMIMEType
_BSS	ENDS
;	COMDAT ?wkeNetCreateWebUrlRequest2@@3P6APAUwkeWebUrlRequest@@QAVWebURLRequest@blink@@@ZA
_BSS	SEGMENT
?wkeNetCreateWebUrlRequest2@@3P6APAUwkeWebUrlRequest@@QAVWebURLRequest@blink@@@ZA DD 01H DUP (?) ; wkeNetCreateWebUrlRequest2
_BSS	ENDS
;	COMDAT ?mbFireWindowsMessage@@3P6GHHPAUHWND__@@IIJPAJ@ZA
_BSS	SEGMENT
?mbFireWindowsMessage@@3P6GHHPAUHWND__@@IIJPAJ@ZA DD 01H DUP (?) ; mbFireWindowsMessage
_BSS	ENDS
;	COMDAT ?wkeToString@@3P6APBDQAVCString@wke@@@ZA
_BSS	SEGMENT
?wkeToString@@3P6APBDQAVCString@wke@@@ZA DD 01H DUP (?)	; wkeToString
_BSS	ENDS
;	COMDAT ?mbNetSendWsText@@3P6GXPAXPBDI@ZA
_BSS	SEGMENT
?mbNetSendWsText@@3P6GXPAXPBDI@ZA DD 01H DUP (?)	; mbNetSendWsText
_BSS	ENDS
;	COMDAT ?wkePrintToBitmap@@3P6APBU_wkeMemBuf@@PAVCWebView@wke@@PAXPBU_wkeScreenshotSettings@@@ZA
_BSS	SEGMENT
?wkePrintToBitmap@@3P6APBU_wkeMemBuf@@PAVCWebView@wke@@PAXPBU_wkeScreenshotSettings@@@ZA DD 01H DUP (?) ; wkePrintToBitmap
_BSS	ENDS
;	COMDAT ?wkeCreateMemBuf@@3P6APAU_wkeMemBuf@@PAVCWebView@wke@@PAXI@ZA
_BSS	SEGMENT
?wkeCreateMemBuf@@3P6APAU_wkeMemBuf@@PAVCWebView@wke@@PAXI@ZA DD 01H DUP (?) ; wkeCreateMemBuf
_BSS	ENDS
;	COMDAT ?wkeNetSetData@@3P6AXPAX0H@ZA
_BSS	SEGMENT
?wkeNetSetData@@3P6AXPAX0H@ZA DD 01H DUP (?)		; wkeNetSetData
_BSS	ENDS
;	COMDAT ?mbFireMouseWheelEvent@@3P6GHHHHHI@ZA
_BSS	SEGMENT
?mbFireMouseWheelEvent@@3P6GHHHHHI@ZA DD 01H DUP (?)	; mbFireMouseWheelEvent
_BSS	ENDS
;	COMDAT ?wkeDestroyWebWindow@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeDestroyWebWindow@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeDestroyWebWindow
_BSS	ENDS
;	COMDAT ?wkeNetGetHTTPHeaderFieldFromResponse@@3P6APBDPAXPBD@ZA
_BSS	SEGMENT
?wkeNetGetHTTPHeaderFieldFromResponse@@3P6APBDPAXPBD@ZA DD 01H DUP (?) ; wkeNetGetHTTPHeaderFieldFromResponse
_BSS	ENDS
;	COMDAT ?wkeGetHeight@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetHeight@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeGetHeight
_BSS	ENDS
;	COMDAT ?wkeUtilBase64Decode@@3P6APBDPBD@ZA
_BSS	SEGMENT
?wkeUtilBase64Decode@@3P6APBDPBD@ZA DD 01H DUP (?)	; wkeUtilBase64Decode
_BSS	ENDS
;	COMDAT ?wkeGetTitleW@@3P6APB_WPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetTitleW@@3P6APB_WPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetTitleW
_BSS	ENDS
;	COMDAT ?wkeIsLoading@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsLoading@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsLoading
_BSS	ENDS
;	COMDAT ?wkeShowWindow@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeShowWindow@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeShowWindow
_BSS	ENDS
;	COMDAT ?wkeKeyDown@@3P6A_NPAVCWebView@wke@@II_N@ZA
_BSS	SEGMENT
?wkeKeyDown@@3P6A_NPAVCWebView@wke@@II_N@ZA DD 01H DUP (?) ; wkeKeyDown
_BSS	ENDS
;	COMDAT ?wkeUtilDecodeURLEscape@@3P6APBDPBD@ZA
_BSS	SEGMENT
?wkeUtilDecodeURLEscape@@3P6APBDPBD@ZA DD 01H DUP (?)	; wkeUtilDecodeURLEscape
_BSS	ENDS
;	COMDAT ?wkeFreeMemBuf@@3P6AXPAU_wkeMemBuf@@@ZA
_BSS	SEGMENT
?wkeFreeMemBuf@@3P6AXPAU_wkeMemBuf@@@ZA DD 01H DUP (?)	; wkeFreeMemBuf
_BSS	ENDS
;	COMDAT ?jsIsString@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsString@@3P6A_N_J@ZA DD 01H DUP (?)			; jsIsString
_BSS	ENDS
;	COMDAT ?jsBoolean@@3P6A_J_N@ZA
_BSS	SEGMENT
?jsBoolean@@3P6A_J_N@ZA DD 01H DUP (?)			; jsBoolean
_BSS	ENDS
;	COMDAT ?mbOnDownloadInBlinkThread@@3P6GXHP6G?AW4_mbDownloadOpt@@HPAXIPBD110PAU_mbNetJobDataBind@@@Z0@ZA
_BSS	SEGMENT
?mbOnDownloadInBlinkThread@@3P6GXHP6G?AW4_mbDownloadOpt@@HPAXIPBD110PAU_mbNetJobDataBind@@@Z0@ZA DD 01H DUP (?) ; mbOnDownloadInBlinkThread
_BSS	ENDS
;	COMDAT ?wkeShowDevtools@@3P6AXPAVCWebView@wke@@PB_WP6AX0PAXPAUHWND__@@@Z2@ZA
_BSS	SEGMENT
?wkeShowDevtools@@3P6AXPAVCWebView@wke@@PB_WP6AX0PAXPAUHWND__@@@Z2@ZA DD 01H DUP (?) ; wkeShowDevtools
_BSS	ENDS
;	COMDAT ?wkeGetBlinkMainThreadIsolate@@3P6APAXXZA
_BSS	SEGMENT
?wkeGetBlinkMainThreadIsolate@@3P6APAXXZA DD 01H DUP (?) ; wkeGetBlinkMainThreadIsolate
_BSS	ENDS
;	COMDAT ?wkeDeleteString@@3P6AXPAVCString@wke@@@ZA
_BSS	SEGMENT
?wkeDeleteString@@3P6AXPAVCString@wke@@@ZA DD 01H DUP (?) ; wkeDeleteString
_BSS	ENDS
;	COMDAT ?mbGetContentWidth@@3P6GHH@ZA
_BSS	SEGMENT
?mbGetContentWidth@@3P6GHH@ZA DD 01H DUP (?)		; mbGetContentWidth
_BSS	ENDS
;	COMDAT ?wkeSetFileSystem@@3P6AXP6APAXPBD@ZP6AXPAX@ZP6AI2@ZP6AH22I@ZP6AH2HH@Z@ZA
_BSS	SEGMENT
?wkeSetFileSystem@@3P6AXP6APAXPBD@ZP6AXPAX@ZP6AI2@ZP6AH22I@ZP6AH2HH@Z@ZA DD 01H DUP (?) ; wkeSetFileSystem
_BSS	ENDS
;	COMDAT ?mbNetGetRawResponseHeadInBlinkThread@@3P6GPBU_mbSlist@@PAX@ZA
_BSS	SEGMENT
?mbNetGetRawResponseHeadInBlinkThread@@3P6GPBU_mbSlist@@PAX@ZA DD 01H DUP (?) ; mbNetGetRawResponseHeadInBlinkThread
_BSS	ENDS
;	COMDAT ?wkeStopLoading@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeStopLoading@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeStopLoading
_BSS	ENDS
;	COMDAT ?wkeNetCreatePostBodyElement@@3P6APAU_wkePostBodyElement@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeNetCreatePostBodyElement@@3P6APAU_wkePostBodyElement@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeNetCreatePostBodyElement
_BSS	ENDS
;	COMDAT ?wkeIsInitialize@@3P6A_NXZA
_BSS	SEGMENT
?wkeIsInitialize@@3P6A_NXZA DD 01H DUP (?)		; wkeIsInitialize
_BSS	ENDS
;	COMDAT ?mbEnableHighDPISupport@@3P6GXXZA
_BSS	SEGMENT
?mbEnableHighDPISupport@@3P6GXXZA DD 01H DUP (?)	; mbEnableHighDPISupport
_BSS	ENDS
;	COMDAT ?wkeSetCookieEnabled@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetCookieEnabled@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetCookieEnabled
_BSS	ENDS
;	COMDAT ?wkeCut@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeCut@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeCut
_BSS	ENDS
;	COMDAT ?mbSetDragDropEnable@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetDragDropEnable@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetDragDropEnable
_BSS	ENDS
;	COMDAT ?jsToInt@@3P6AHPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToInt@@3P6AHPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?)	; jsToInt
_BSS	ENDS
;	COMDAT ?wkeFireKeyUpEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA
_BSS	SEGMENT
?wkeFireKeyUpEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA DD 01H DUP (?) ; wkeFireKeyUpEvent
_BSS	ENDS
;	COMDAT ?mbOnConsole@@3P6GXHP6GXHPAXW4mbConsoleLevel@@PBD2I2@Z0@ZA
_BSS	SEGMENT
?mbOnConsole@@3P6GXHP6GXHPAXW4mbConsoleLevel@@PBD2I2@Z0@ZA DD 01H DUP (?) ; mbOnConsole
_BSS	ENDS
;	COMDAT ?mbSetHandle@@3P6GXHPAUHWND__@@@ZA
_BSS	SEGMENT
?mbSetHandle@@3P6GXHPAUHWND__@@@ZA DD 01H DUP (?)	; mbSetHandle
_BSS	ENDS
;	COMDAT ?mbNetGetReferrer@@3P6GPBDPAX@ZA
_BSS	SEGMENT
?mbNetGetReferrer@@3P6GPBDPAX@ZA DD 01H DUP (?)		; mbNetGetReferrer
_BSS	ENDS
;	COMDAT ?mbNetGetHttpStatusCode@@3P6GHPAUmbWebUrlResponse@@@ZA
_BSS	SEGMENT
?mbNetGetHttpStatusCode@@3P6GHPAUmbWebUrlResponse@@@ZA DD 01H DUP (?) ; mbNetGetHttpStatusCode
_BSS	ENDS
;	COMDAT ?mbCanGoBack@@**********************************@@H@Z0@ZA
_BSS	SEGMENT
?mbCanGoBack@@**********************************@@H@Z0@ZA DD 01H DUP (?) ; mbCanGoBack
_BSS	ENDS
;	COMDAT ?wkeEditorSelectAll@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorSelectAll@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeEditorSelectAll
_BSS	ENDS
;	COMDAT ?mbUtilScreenshot@@3P6GXHPBU_mbScreenshotSettings@@P6GXHPAXPBDI@Z1@ZA
_BSS	SEGMENT
?mbUtilScreenshot@@3P6GXHPBU_mbScreenshotSettings@@P6GXHPAXPBDI@Z1@ZA DD 01H DUP (?) ; mbUtilScreenshot
_BSS	ENDS
;	COMDAT ?wkeNodeOnCreateProcess@@3P6AXPAVCWebView@wke@@P6AX0PAXPB_W2PAU_STARTUPINFOW@@@Z1@ZA
_BSS	SEGMENT
?wkeNodeOnCreateProcess@@3P6AXPAVCWebView@wke@@P6AX0PAXPB_W2PAU_STARTUPINFOW@@@Z1@ZA DD 01H DUP (?) ; wkeNodeOnCreateProcess
_BSS	ENDS
;	COMDAT ?wkeNetAddHTTPHeaderFieldToUrlRequest@@3P6AXPAUwkeWebUrlRequest@@PBD1@ZA
_BSS	SEGMENT
?wkeNetAddHTTPHeaderFieldToUrlRequest@@3P6AXPAUwkeWebUrlRequest@@PBD1@ZA DD 01H DUP (?) ; wkeNetAddHTTPHeaderFieldToUrlRequest
_BSS	ENDS
;	COMDAT ?wkeSetCspCheckEnable@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetCspCheckEnable@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetCspCheckEnable
_BSS	ENDS
;	COMDAT ?wkeIsLoadingSucceeded@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsLoadingSucceeded@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsLoadingSucceeded
_BSS	ENDS
;	COMDAT ?mbPerformCookieCommand@@3P6GXHW4mbCookieCommand@@@ZA
_BSS	SEGMENT
?mbPerformCookieCommand@@3P6GXHW4mbCookieCommand@@@ZA DD 01H DUP (?) ; mbPerformCookieCommand
_BSS	ENDS
;	COMDAT ?jsGetLength@@3P6AHPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsGetLength@@3P6AHPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsGetLength
_BSS	ENDS
;	COMDAT ?mbEditorSelectAll@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorSelectAll@@3P6GXH@ZA DD 01H DUP (?)		; mbEditorSelectAll
_BSS	ENDS
;	COMDAT ?wkeNetGetResponseUrl@@3P6APBDPAUwkeWebUrlResponse@@@ZA
_BSS	SEGMENT
?wkeNetGetResponseUrl@@3P6APBDPAUwkeWebUrlResponse@@@ZA DD 01H DUP (?) ; wkeNetGetResponseUrl
_BSS	ENDS
;	COMDAT ?wkeFireMouseWheelEvent@@3P6A_NPAVCWebView@wke@@HHHI@ZA
_BSS	SEGMENT
?wkeFireMouseWheelEvent@@3P6A_NPAVCWebView@wke@@HHHI@ZA DD 01H DUP (?) ; wkeFireMouseWheelEvent
_BSS	ENDS
;	COMDAT ?wkeNetSetHTTPHeaderField@@3P6AXPAXPB_W1_N@ZA
_BSS	SEGMENT
?wkeNetSetHTTPHeaderField@@3P6AXPAXPB_W1_N@ZA DD 01H DUP (?) ; wkeNetSetHTTPHeaderField
_BSS	ENDS
;	COMDAT ?wkeGoForward@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGoForward@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGoForward
_BSS	ENDS
;	COMDAT ?wkeSetDeviceParameter@@3P6AXPAVCWebView@wke@@PBD1HM@ZA
_BSS	SEGMENT
?wkeSetDeviceParameter@@3P6AXPAVCWebView@wke@@PBD1HM@ZA DD 01H DUP (?) ; wkeSetDeviceParameter
_BSS	ENDS
;	COMDAT ?jsArg@@3P6A_JPAUJsExecStateInfo@@H@ZA
_BSS	SEGMENT
?jsArg@@3P6A_JPAUJsExecStateInfo@@H@ZA DD 01H DUP (?)	; jsArg
_BSS	ENDS
;	COMDAT ?mbSetCspCheckEnable@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetCspCheckEnable@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetCspCheckEnable
_BSS	ENDS
;	COMDAT ?wkeSetHeadlessEnabled@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetHeadlessEnabled@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetHeadlessEnabled
_BSS	ENDS
;	COMDAT ?jsToBoolean@@3P6A_NPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToBoolean@@3P6A_NPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToBoolean
_BSS	ENDS
;	COMDAT ?mbGetUrl@@3P6GPBDH@ZA
_BSS	SEGMENT
?mbGetUrl@@3P6GPBDH@ZA DD 01H DUP (?)			; mbGetUrl
_BSS	ENDS
;	COMDAT ?mbNetCreatePostBodyElement@@3P6GPAU_mbPostBodyElement@@H@ZA
_BSS	SEGMENT
?mbNetCreatePostBodyElement@@3P6GPAU_mbPostBodyElement@@H@ZA DD 01H DUP (?) ; mbNetCreatePostBodyElement
_BSS	ENDS
;	COMDAT ?wkeRunJsByFrame@@3P6A_JPAVCWebView@wke@@PAXPBD_N@ZA
_BSS	SEGMENT
?wkeRunJsByFrame@@3P6A_JPAVCWebView@wke@@PAXPBD_N@ZA DD 01H DUP (?) ; wkeRunJsByFrame
_BSS	ENDS
;	COMDAT ?wkeNetOnResponse@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD1@Z1@ZA
_BSS	SEGMENT
?wkeNetOnResponse@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD1@Z1@ZA DD 01H DUP (?) ; wkeNetOnResponse
_BSS	ENDS
;	COMDAT ?wkeCopy@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeCopy@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeCopy
_BSS	ENDS
;	COMDAT ?mbWebFrameGetMainWorldScriptContext@@3P6GXHPAX0@ZA
_BSS	SEGMENT
?mbWebFrameGetMainWorldScriptContext@@3P6GXHPAX0@ZA DD 01H DUP (?) ; mbWebFrameGetMainWorldScriptContext
_BSS	ENDS
;	COMDAT ?jsBindGetter@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@Z@ZA
_BSS	SEGMENT
?jsBindGetter@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@Z@ZA DD 01H DUP (?) ; jsBindGetter
_BSS	ENDS
;	COMDAT ?jsFunction@@3P6A_JPAUJsExecStateInfo@@PAUtagjsData@@@ZA
_BSS	SEGMENT
?jsFunction@@3P6A_JPAUJsExecStateInfo@@PAUtagjsData@@@ZA DD 01H DUP (?) ; jsFunction
_BSS	ENDS
;	COMDAT ?wkeIsWebviewAlive@@3P6A_NH@ZA
_BSS	SEGMENT
?wkeIsWebviewAlive@@3P6A_NH@ZA DD 01H DUP (?)		; wkeIsWebviewAlive
_BSS	ENDS
;	COMDAT ?wkeNetGetHTTPHeaderField@@3P6APBDPAXPBD@ZA
_BSS	SEGMENT
?wkeNetGetHTTPHeaderField@@3P6APBDPAXPBD@ZA DD 01H DUP (?) ; wkeNetGetHTTPHeaderField
_BSS	ENDS
;	COMDAT ?wkeNetStartUrlRequest@@3P6AHPAVCWebView@wke@@PAUwkeWebUrlRequest@@PAXPBU_wkeUrlRequestCallbacks@@@ZA
_BSS	SEGMENT
?wkeNetStartUrlRequest@@3P6AHPAVCWebView@wke@@PAUwkeWebUrlRequest@@PAXPBU_wkeUrlRequestCallbacks@@@ZA DD 01H DUP (?) ; wkeNetStartUrlRequest
_BSS	ENDS
;	COMDAT ?mbSetDiskCacheLimitDisk@@3P6GXHI@ZA
_BSS	SEGMENT
?mbSetDiskCacheLimitDisk@@3P6GXHI@ZA DD 01H DUP (?)	; mbSetDiskCacheLimitDisk
_BSS	ENDS
;	COMDAT ?wkeHeight@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeHeight@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeHeight
_BSS	ENDS
;	COMDAT ?wkeEditorUndo@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorUndo@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeEditorUndo
_BSS	ENDS
;	COMDAT ?mbSetDiskCachePath@@3P6GXHPB_W@ZA
_BSS	SEGMENT
?mbSetDiskCachePath@@3P6GXHPB_W@ZA DD 01H DUP (?)	; mbSetDiskCachePath
_BSS	ENDS
;	COMDAT ?wkeSetViewNetInterface@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeSetViewNetInterface@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeSetViewNetInterface
_BSS	ENDS
;	COMDAT ?wkeUnfocus@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeUnfocus@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeUnfocus
_BSS	ENDS
;	COMDAT ?wkeWebFrameGetMainWorldScriptContext@@3P6AXPAVCWebView@wke@@PAX1@ZA
_BSS	SEGMENT
?wkeWebFrameGetMainWorldScriptContext@@3P6AXPAVCWebView@wke@@PAX1@ZA DD 01H DUP (?) ; wkeWebFrameGetMainWorldScriptContext
_BSS	ENDS
;	COMDAT ?mbGetProcAddr@@3P6GPAXPBD@ZA
_BSS	SEGMENT
?mbGetProcAddr@@3P6GPAXPBD@ZA DD 01H DUP (?)		; mbGetProcAddr
_BSS	ENDS
;	COMDAT ?wkeOnConsole@@3P6AXPAVCWebView@wke@@P6AX0PAXW4_wkeConsoleLevel@@QAVCString@2@3I3@Z1@ZA
_BSS	SEGMENT
?wkeOnConsole@@3P6AXPAVCWebView@wke@@P6AX0PAXW4_wkeConsoleLevel@@QAVCString@2@3I3@Z1@ZA DD 01H DUP (?) ; wkeOnConsole
_BSS	ENDS
;	COMDAT ?jsString@@3P6A_JPAUJsExecStateInfo@@PBD@ZA
_BSS	SEGMENT
?jsString@@3P6A_JPAUJsExecStateInfo@@PBD@ZA DD 01H DUP (?) ; jsString
_BSS	ENDS
;	COMDAT ?wkeOnWillReleaseScriptContext@@3P6AXPAVCWebView@wke@@P6AX0PAX11H@Z1@ZA
_BSS	SEGMENT
?wkeOnWillReleaseScriptContext@@3P6AXPAVCWebView@wke@@P6AX0PAX11H@Z1@ZA DD 01H DUP (?) ; wkeOnWillReleaseScriptContext
_BSS	ENDS
;	COMDAT ?mbSetContextMenuEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetContextMenuEnabled@@3P6GXHH@ZA DD 01H DUP (?)	; mbSetContextMenuEnabled
_BSS	ENDS
;	COMDAT ?jsIsUndefined@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsUndefined@@3P6A_N_J@ZA DD 01H DUP (?)		; jsIsUndefined
_BSS	ENDS
;	COMDAT ?jsArrayBuffer@@3P6A_JPAUJsExecStateInfo@@PADI@ZA
_BSS	SEGMENT
?jsArrayBuffer@@3P6A_JPAUJsExecStateInfo@@PADI@ZA DD 01H DUP (?) ; jsArrayBuffer
_BSS	ENDS
;	COMDAT ?wkeLoadHTMLW@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeLoadHTMLW@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeLoadHTMLW
_BSS	ENDS
;	COMDAT ?mbOnPrinting@@3P6GHHP6GHHPAXW4_mbPrintintStep@@PAUHDC__@@PBU_mbPrintintSettings@@H@Z0@ZA
_BSS	SEGMENT
?mbOnPrinting@@3P6GHHP6GHHPAXW4_mbPrintintStep@@PAUHDC__@@PBU_mbPrintintSettings@@H@Z0@ZA DD 01H DUP (?) ; mbOnPrinting
_BSS	ENDS
;	COMDAT ?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA
_BSS	SEGMENT
?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA DD 01H DUP (?) ; TSS1<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
_BSS	ENDS
;	COMDAT ?wkeCreateWebView@@3P6APAVCWebView@wke@@XZA
_BSS	SEGMENT
?wkeCreateWebView@@3P6APAVCWebView@wke@@XZA DD 01H DUP (?) ; wkeCreateWebView
_BSS	ENDS
;	COMDAT ?wkeNetCancelRequest@@3P6AXPAX@ZA
_BSS	SEGMENT
?wkeNetCancelRequest@@3P6AXPAX@ZA DD 01H DUP (?)	; wkeNetCancelRequest
_BSS	ENDS
;	COMDAT ?mbNetGetHTTPHeaderField@@3P6GPBDPAXPBDH@ZA
_BSS	SEGMENT
?mbNetGetHTTPHeaderField@@3P6GPBDPAXPBDH@ZA DD 01H DUP (?) ; mbNetGetHTTPHeaderField
_BSS	ENDS
;	COMDAT ?wkeOnDownload@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD@Z1@ZA
_BSS	SEGMENT
?wkeOnDownload@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD@Z1@ZA DD 01H DUP (?) ; wkeOnDownload
_BSS	ENDS
;	COMDAT ?wkeCreateWebWindow@@3P6APAVCWebView@wke@@W4_wkeWindowType@@PAUHWND__@@HHHH@ZA
_BSS	SEGMENT
?wkeCreateWebWindow@@3P6APAVCWebView@wke@@W4_wkeWindowType@@PAUHWND__@@HHHH@ZA DD 01H DUP (?) ; wkeCreateWebWindow
_BSS	ENDS
;	COMDAT ?mbOnLoadUrlFinish@@3P6GXHP6GXHPAXPBD0H@Z0@ZA
_BSS	SEGMENT
?mbOnLoadUrlFinish@@3P6GXHP6GXHPAXPBD0H@Z0@ZA DD 01H DUP (?) ; mbOnLoadUrlFinish
_BSS	ENDS
;	COMDAT ?mbFastReload@@3P6GXH@ZA
_BSS	SEGMENT
?mbFastReload@@3P6GXH@ZA DD 01H DUP (?)			; mbFastReload
_BSS	ENDS
;	COMDAT ?jsReleaseRef@@3P6A_NPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsReleaseRef@@3P6A_NPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsReleaseRef
_BSS	ENDS
;	COMDAT ?wkeSetHandleOffset@@3P6AXPAVCWebView@wke@@HH@ZA
_BSS	SEGMENT
?wkeSetHandleOffset@@3P6AXPAVCWebView@wke@@HH@ZA DD 01H DUP (?) ; wkeSetHandleOffset
_BSS	ENDS
;	COMDAT ?mbSetNpapiPluginsEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetNpapiPluginsEnabled@@3P6GXHH@ZA DD 01H DUP (?)	; mbSetNpapiPluginsEnabled
_BSS	ENDS
;	COMDAT ?wkeResize@@3P6AXPAVCWebView@wke@@HH@ZA
_BSS	SEGMENT
?wkeResize@@3P6AXPAVCWebView@wke@@HH@ZA DD 01H DUP (?)	; wkeResize
_BSS	ENDS
;	COMDAT ?jsToDouble@@3P6ANPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToDouble@@3P6ANPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToDouble
_BSS	ENDS
;	COMDAT ?mbUtilDecodeURLEscape@@3P6GPBDPBD@ZA
_BSS	SEGMENT
?mbUtilDecodeURLEscape@@3P6GPBDPBD@ZA DD 01H DUP (?)	; mbUtilDecodeURLEscape
_BSS	ENDS
;	COMDAT ?mbSetAudioMuted@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetAudioMuted@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetAudioMuted
_BSS	ENDS
;	COMDAT ?mbCreateString@@3P6GPAUmbString@@PBDI@ZA
_BSS	SEGMENT
?mbCreateString@@3P6GPAUmbString@@PBDI@ZA DD 01H DUP (?) ; mbCreateString
_BSS	ENDS
;	COMDAT ?wkeDragTargetDrop@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1H@ZA
_BSS	SEGMENT
?wkeDragTargetDrop@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1H@ZA DD 01H DUP (?) ; wkeDragTargetDrop
_BSS	ENDS
;	COMDAT ?mbOnBlinkThreadInit@@3P6GXP6GXPAX0@Z00@ZA
_BSS	SEGMENT
?mbOnBlinkThreadInit@@3P6GXP6GXPAX0@Z00@ZA DD 01H DUP (?) ; mbOnBlinkThreadInit
_BSS	ENDS
;	COMDAT ?mbSetWindowTitle@@3P6GXHPBD@ZA
_BSS	SEGMENT
?mbSetWindowTitle@@3P6GXHPBD@ZA DD 01H DUP (?)		; mbSetWindowTitle
_BSS	ENDS
;	COMDAT ?jsIsValidExecState@@3P6A_NPAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsIsValidExecState@@3P6A_NPAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsIsValidExecState
_BSS	ENDS
;	COMDAT ?wkeNetFreePostBodyElement@@3P6AXPAU_wkePostBodyElement@@@ZA
_BSS	SEGMENT
?wkeNetFreePostBodyElement@@3P6AXPAU_wkePostBodyElement@@@ZA DD 01H DUP (?) ; wkeNetFreePostBodyElement
_BSS	ENDS
;	COMDAT ?wkeNetGetExpectedContentLength@@3P6A_JPAUwkeWebUrlResponse@@@ZA
_BSS	SEGMENT
?wkeNetGetExpectedContentLength@@3P6A_JPAUwkeWebUrlResponse@@@ZA DD 01H DUP (?) ; wkeNetGetExpectedContentLength
_BSS	ENDS
;	COMDAT ?wkeSetViewSettings@@3P6AXPAVCWebView@wke@@PBU_wkeViewSettings@@@ZA
_BSS	SEGMENT
?wkeSetViewSettings@@3P6AXPAVCWebView@wke@@PBU_wkeViewSettings@@@ZA DD 01H DUP (?) ; wkeSetViewSettings
_BSS	ENDS
;	COMDAT ?wkeSetClientHandler@@3P6AXPAVCWebView@wke@@PBU_wkeClientHandler@@@ZA
_BSS	SEGMENT
?wkeSetClientHandler@@3P6AXPAVCWebView@wke@@PBU_wkeClientHandler@@@ZA DD 01H DUP (?) ; wkeSetClientHandler
_BSS	ENDS
;	COMDAT ?mbGetStringLen@@3P6GIPAUmbString@@@ZA
_BSS	SEGMENT
?mbGetStringLen@@3P6GIPAUmbString@@@ZA DD 01H DUP (?)	; mbGetStringLen
_BSS	ENDS
;	COMDAT ?wkeNetGetMIMEType@@3P6APBDPAXPAVCString@wke@@@ZA
_BSS	SEGMENT
?wkeNetGetMIMEType@@3P6APBDPAXPAVCString@wke@@@ZA DD 01H DUP (?) ; wkeNetGetMIMEType
_BSS	ENDS
;	COMDAT ?wkeKeyUp@@3P6A_NPAVCWebView@wke@@II_N@ZA
_BSS	SEGMENT
?wkeKeyUp@@3P6A_NPAVCWebView@wke@@II_N@ZA DD 01H DUP (?) ; wkeKeyUp
_BSS	ENDS
;	COMDAT ?mbOnClose@@3P6GHHP6GHHPAX0@Z0@ZA
_BSS	SEGMENT
?mbOnClose@@3P6GHHP6GHHPAX0@Z0@ZA DD 01H DUP (?)	; mbOnClose
_BSS	ENDS
;	COMDAT ?wkeAddNpapiPlugin@@3P6AXPAVCWebView@wke@@PAX11@ZA
_BSS	SEGMENT
?wkeAddNpapiPlugin@@3P6AXPAVCWebView@wke@@PAX11@ZA DD 01H DUP (?) ; wkeAddNpapiPlugin
_BSS	ENDS
;	COMDAT ?jsEmptyArray@@3P6A_JPAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsEmptyArray@@3P6A_JPAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsEmptyArray
_BSS	ENDS
;	COMDAT ?jsIsArray@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsArray@@3P6A_N_J@ZA DD 01H DUP (?)			; jsIsArray
_BSS	ENDS
;	COMDAT ?mbLoadURL@@3P6GXHPBD@ZA
_BSS	SEGMENT
?mbLoadURL@@3P6GXHPBD@ZA DD 01H DUP (?)			; mbLoadURL
_BSS	ENDS
;	COMDAT ?mbSetZoomFactor@@3P6GXHM@ZA
_BSS	SEGMENT
?mbSetZoomFactor@@3P6GXHM@ZA DD 01H DUP (?)		; mbSetZoomFactor
_BSS	ENDS
;	COMDAT ?g_hMiniblinkMod@@3PAUHINSTANCE__@@A
_BSS	SEGMENT
?g_hMiniblinkMod@@3PAUHINSTANCE__@@A DD 01H DUP (?)	; g_hMiniblinkMod
_BSS	ENDS
;	COMDAT ?wkeSetCurlHookUrl@@3P6AXP6APAXPBD@Z@ZA
_BSS	SEGMENT
?wkeSetCurlHookUrl@@3P6AXP6APAXPBD@Z@ZA DD 01H DUP (?)	; wkeSetCurlHookUrl
_BSS	ENDS
;	COMDAT ?wkeRegisterEmbedderCustomElement@@3P6A_NPAVCWebView@wke@@PAXPBD11@ZA
_BSS	SEGMENT
?wkeRegisterEmbedderCustomElement@@3P6A_NPAVCWebView@wke@@PAXPBD11@ZA DD 01H DUP (?) ; wkeRegisterEmbedderCustomElement
_BSS	ENDS
;	COMDAT ?mbSetContextMenuItemShow@@3P6GXHW4_mbMenuItemId@@H@ZA
_BSS	SEGMENT
?mbSetContextMenuItemShow@@3P6GXHW4_mbMenuItemId@@H@ZA DD 01H DUP (?) ; mbSetContextMenuItemShow
_BSS	ENDS
;	COMDAT ?wkeSetZoomFactor@@3P6AXPAVCWebView@wke@@M@ZA
_BSS	SEGMENT
?wkeSetZoomFactor@@3P6AXPAVCWebView@wke@@M@ZA DD 01H DUP (?) ; wkeSetZoomFactor
_BSS	ENDS
;	COMDAT ?jsStringW@@3P6A_JPAUJsExecStateInfo@@PB_W@ZA
_BSS	SEGMENT
?jsStringW@@3P6A_JPAUJsExecStateInfo@@PB_W@ZA DD 01H DUP (?) ; jsStringW
_BSS	ENDS
;	COMDAT ?wkeSetMouseEnabled@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetMouseEnabled@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetMouseEnabled
_BSS	ENDS
;	COMDAT ?wkeGlobalExec@@3P6APAUJsExecStateInfo@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGlobalExec@@3P6APAUJsExecStateInfo@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGlobalExec
_BSS	ENDS
;	COMDAT ?wkeSetDirty@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetDirty@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetDirty
_BSS	ENDS
;	COMDAT ?jsSetAt@@3P6AXPAUJsExecStateInfo@@_JH1@ZA
_BSS	SEGMENT
?jsSetAt@@3P6AXPAUJsExecStateInfo@@_JH1@ZA DD 01H DUP (?) ; jsSetAt
_BSS	ENDS
;	COMDAT ?wkeJsBindFunction@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2I@ZA
_BSS	SEGMENT
?wkeJsBindFunction@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2I@ZA DD 01H DUP (?) ; wkeJsBindFunction
_BSS	ENDS
;	COMDAT ?jsArgType@@3P6A?AW4_jsType@@PAUJsExecStateInfo@@H@ZA
_BSS	SEGMENT
?jsArgType@@3P6A?AW4_jsType@@PAUJsExecStateInfo@@H@ZA DD 01H DUP (?) ; jsArgType
_BSS	ENDS
;	COMDAT ?mbRegisterEmbedderCustomElement@@3P6GHHPAXPBD00@ZA
_BSS	SEGMENT
?mbRegisterEmbedderCustomElement@@3P6GHHPAXPBD00@ZA DD 01H DUP (?) ; mbRegisterEmbedderCustomElement
_BSS	ENDS
;	COMDAT ?mbCreateMemBuf@@3P6GPAU_mbMemBuf@@HPAXI@ZA
_BSS	SEGMENT
?mbCreateMemBuf@@3P6GPAU_mbMemBuf@@HPAXI@ZA DD 01H DUP (?) ; mbCreateMemBuf
_BSS	ENDS
;	COMDAT ?mbOnLoadUrlFail@@3P6GXHP6GXHPAXPBD0@Z0@ZA
_BSS	SEGMENT
?mbOnLoadUrlFail@@3P6GXHP6GXHPAXPBD0@Z0@ZA DD 01H DUP (?) ; mbOnLoadUrlFail
_BSS	ENDS
;	COMDAT ?wkeCreateStringW@@3P6APAVCString@wke@@PB_WI@ZA
_BSS	SEGMENT
?wkeCreateStringW@@3P6APAVCString@wke@@PB_WI@ZA DD 01H DUP (?) ; wkeCreateStringW
_BSS	ENDS
;	COMDAT ?mbMoveWindow@@3P6GXHHHHH@ZA
_BSS	SEGMENT
?mbMoveWindow@@3P6GXHHHHH@ZA DD 01H DUP (?)		; mbMoveWindow
_BSS	ENDS
;	COMDAT ?jsAddRef@@3P6A_NPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsAddRef@@3P6A_NPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsAddRef
_BSS	ENDS
;	COMDAT ?wkeVersionString@@3P6APBDXZA
_BSS	SEGMENT
?wkeVersionString@@3P6APBDXZA DD 01H DUP (?)		; wkeVersionString
_BSS	ENDS
;	COMDAT ?mbSetDebugConfig@@3P6GXHPBD0@ZA
_BSS	SEGMENT
?mbSetDebugConfig@@3P6GXHPBD0@ZA DD 01H DUP (?)		; mbSetDebugConfig
_BSS	ENDS
;	COMDAT ?wkeGetStringW@@3P6APB_WQAVCString@wke@@@ZA
_BSS	SEGMENT
?wkeGetStringW@@3P6APB_WQAVCString@wke@@@ZA DD 01H DUP (?) ; wkeGetStringW
_BSS	ENDS
;	COMDAT ?wkeOnLoadUrlFail@@3P6AXPAVCWebView@wke@@P6AX0PAXPBD1@Z1@ZA
_BSS	SEGMENT
?wkeOnLoadUrlFail@@3P6AXPAVCWebView@wke@@P6AX0PAXPBD1@Z1@ZA DD 01H DUP (?) ; wkeOnLoadUrlFail
_BSS	ENDS
;	COMDAT ?mbCallBlinkThreadAsync@@3P6GXP6GXPAX0@Z00@ZA
_BSS	SEGMENT
?mbCallBlinkThreadAsync@@3P6GXP6GXPAX0@Z00@ZA DD 01H DUP (?) ; mbCallBlinkThreadAsync
_BSS	ENDS
;	COMDAT ?wkePaint2@@3P6AXPAVCWebView@wke@@PAXHHHHHHHH_N@ZA
_BSS	SEGMENT
?wkePaint2@@3P6AXPAVCWebView@wke@@PAXHHHHHHHH_N@ZA DD 01H DUP (?) ; wkePaint2
_BSS	ENDS
;	COMDAT ?mbNetCreateWebUrlRequest@@3P6GPAUmbWebUrlRequest@@PBD00@ZA
_BSS	SEGMENT
?mbNetCreateWebUrlRequest@@3P6GPAUmbWebUrlRequest@@PBD00@ZA DD 01H DUP (?) ; mbNetCreateWebUrlRequest
_BSS	ENDS
;	COMDAT ?wkeIsMainFrame@@3P6A_NPAVCWebView@wke@@PAX@ZA
_BSS	SEGMENT
?wkeIsMainFrame@@3P6A_NPAVCWebView@wke@@PAX@ZA DD 01H DUP (?) ; wkeIsMainFrame
_BSS	ENDS
;	COMDAT ?wkeGetTempCallbackInfo@@3P6APAU_wkeTempCallbackInfo@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetTempCallbackInfo@@3P6APAU_wkeTempCallbackInfo@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetTempCallbackInfo
_BSS	ENDS
;	COMDAT ?mbJsToBoolean@@3P6GHPAX_J@ZA
_BSS	SEGMENT
?mbJsToBoolean@@3P6GHPAX_J@ZA DD 01H DUP (?)		; mbJsToBoolean
_BSS	ENDS
;	COMDAT ?mbUtilPrintToBitmap@@3P6GXHPAXPBU_mbScreenshotSettings@@P6GXH0PBDI@Z0@ZA
_BSS	SEGMENT
?mbUtilPrintToBitmap@@3P6GXHPAXPBU_mbScreenshotSettings@@P6GXH0PBDI@Z0@ZA DD 01H DUP (?) ; mbUtilPrintToBitmap
_BSS	ENDS
;	COMDAT ?wkeGetDocumentCompleteURL@@3P6APBDPAVCWebView@wke@@PAXPBD@ZA
_BSS	SEGMENT
?wkeGetDocumentCompleteURL@@3P6APBDPAVCWebView@wke@@PAXPBD@ZA DD 01H DUP (?) ; wkeGetDocumentCompleteURL
_BSS	ENDS
;	COMDAT ?mbFireKeyDownEvent@@3P6GHHIIH@ZA
_BSS	SEGMENT
?mbFireKeyDownEvent@@3P6GHHIIH@ZA DD 01H DUP (?)	; mbFireKeyDownEvent
_BSS	ENDS
;	COMDAT ?jsToTempString@@3P6APBDPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToTempString@@3P6APBDPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToTempString
_BSS	ENDS
;	COMDAT ?mbNavigateAtIndex@@3P6GXHH@ZA
_BSS	SEGMENT
?mbNavigateAtIndex@@3P6GXHH@ZA DD 01H DUP (?)		; mbNavigateAtIndex
_BSS	ENDS
;	COMDAT ?mbOnThreadIdle@@3P6GXP6GXPAX0@Z00@ZA
_BSS	SEGMENT
?mbOnThreadIdle@@3P6GXP6GXPAX0@Z00@ZA DD 01H DUP (?)	; mbOnThreadIdle
_BSS	ENDS
;	COMDAT ?jsGetLastErrorIfException@@3P6APAU_jsExceptionInfo@@PAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsGetLastErrorIfException@@3P6APAU_jsExceptionInfo@@PAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsGetLastErrorIfException
_BSS	ENDS
;	COMDAT ?wkeGetWidth@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetWidth@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeGetWidth
_BSS	ENDS
;	COMDAT ?mbOnNetGetFavicon@@3P6GXHP6GXHPAXPBDPAU_mbMemBuf@@@Z0@ZA
_BSS	SEGMENT
?mbOnNetGetFavicon@@3P6GXHP6GXHPAXPBDPAU_mbMemBuf@@@Z0@ZA DD 01H DUP (?) ; mbOnNetGetFavicon
_BSS	ENDS
;	COMDAT ?mbFreeMemBuf@@3P6GXPAU_mbMemBuf@@@ZA
_BSS	SEGMENT
?mbFreeMemBuf@@3P6GXPAU_mbMemBuf@@@ZA DD 01H DUP (?)	; mbFreeMemBuf
_BSS	ENDS
;	COMDAT ?mbUtilSerializeToMHTML@@3P6GXHP6GXHPAXPBD@Z0@ZA
_BSS	SEGMENT
?mbUtilSerializeToMHTML@@3P6GXHP6GXHPAXPBD@Z0@ZA DD 01H DUP (?) ; mbUtilSerializeToMHTML
_BSS	ENDS
;	COMDAT ?wkeGetURL@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetURL@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeGetURL
_BSS	ENDS
;	COMDAT ?mbSetMouseEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetMouseEnabled@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetMouseEnabled
_BSS	ENDS
;	COMDAT ?jsBindSetter@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@Z@ZA
_BSS	SEGMENT
?jsBindSetter@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@Z@ZA DD 01H DUP (?) ; jsBindSetter
_BSS	ENDS
;	COMDAT ?mbGetJsValueType@@3P6G?AW4mbJsType@@PAX_J@ZA
_BSS	SEGMENT
?mbGetJsValueType@@3P6G?AW4mbJsType@@PAX_J@ZA DD 01H DUP (?) ; mbGetJsValueType
_BSS	ENDS
;	COMDAT ?wkeWebViewName@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeWebViewName@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeWebViewName
_BSS	ENDS
;	COMDAT ?wkeOnURLChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA
_BSS	SEGMENT
?wkeOnURLChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA DD 01H DUP (?) ; wkeOnURLChanged
_BSS	ENDS
;	COMDAT ?mbUtilIsRegistered@@3P6GHPB_W@ZA
_BSS	SEGMENT
?mbUtilIsRegistered@@3P6GHPB_W@ZA DD 01H DUP (?)	; mbUtilIsRegistered
_BSS	ENDS
;	COMDAT ?wkeSetTouchEnabled@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetTouchEnabled@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetTouchEnabled
_BSS	ENDS
;	COMDAT ?mbOnConfirmBox@@3P6GXHP6GHHPAXPBD@Z0@ZA
_BSS	SEGMENT
?mbOnConfirmBox@@3P6GXHP6GHHPAXPBD@Z0@ZA DD 01H DUP (?)	; mbOnConfirmBox
_BSS	ENDS
;	COMDAT ?wkeFireContextMenuEvent@@3P6A_NPAVCWebView@wke@@HHI@ZA
_BSS	SEGMENT
?wkeFireContextMenuEvent@@3P6A_NPAVCWebView@wke@@HHI@ZA DD 01H DUP (?) ; wkeFireContextMenuEvent
_BSS	ENDS
;	COMDAT ?mbUtilCreateRequestCode@@3P6GPBDPBD@ZA
_BSS	SEGMENT
?mbUtilCreateRequestCode@@3P6GPBDPBD@ZA DD 01H DUP (?)	; mbUtilCreateRequestCode
_BSS	ENDS
;	COMDAT ?jsGetKeys@@3P6APAU_jsKeys@@PAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsGetKeys@@3P6APAU_jsKeys@@PAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsGetKeys
_BSS	ENDS
;	COMDAT ?wkeCookieEnabled@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeCookieEnabled@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeCookieEnabled
_BSS	ENDS
;	COMDAT ?wkeDragTargetDragOver@@3P6A?AW4_wkeWebDragOperation@@PAVCWebView@wke@@PBUtagPOINT@@1W41@H@ZA
_BSS	SEGMENT
?wkeDragTargetDragOver@@3P6A?AW4_wkeWebDragOperation@@PAVCWebView@wke@@PBUtagPOINT@@1W41@H@ZA DD 01H DUP (?) ; wkeDragTargetDragOver
_BSS	ENDS
;	COMDAT ?mbGetwkeView@@3P6GPAXH@ZA
_BSS	SEGMENT
?mbGetwkeView@@3P6GPAXH@ZA DD 01H DUP (?)		; mbGetwkeView
_BSS	ENDS
;	COMDAT ?mbOnLoadUrlEnd@@3P6GXHP6GXHPAXPBD00H@Z0@ZA
_BSS	SEGMENT
?mbOnLoadUrlEnd@@3P6GXHP6GXHPAXPBD00H@Z0@ZA DD 01H DUP (?) ; mbOnLoadUrlEnd
_BSS	ENDS
;	COMDAT ?mbPopupDownloadMgr@@3P6GHHPBDPAX@ZA
_BSS	SEGMENT
?mbPopupDownloadMgr@@3P6GHHPBDPAX@ZA DD 01H DUP (?)	; mbPopupDownloadMgr
_BSS	ENDS
;	COMDAT ?wkeLayoutIfNeeded@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeLayoutIfNeeded@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeLayoutIfNeeded
_BSS	ENDS
;	COMDAT ?jsFalse@@3P6A_JXZA
_BSS	SEGMENT
?jsFalse@@3P6A_JXZA DD 01H DUP (?)			; jsFalse
_BSS	ENDS
;	COMDAT ?wkeGetContentHeight@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetContentHeight@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetContentHeight
_BSS	ENDS
;	COMDAT ?wkeGetContentAsMarkup@@3P6APBDPAVCWebView@wke@@PAXPAI@ZA
_BSS	SEGMENT
?wkeGetContentAsMarkup@@3P6APBDPAVCWebView@wke@@PAXPAI@ZA DD 01H DUP (?) ; wkeGetContentAsMarkup
_BSS	ENDS
;	COMDAT ?wkeSetMemoryCacheEnable@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetMemoryCacheEnable@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetMemoryCacheEnable
_BSS	ENDS
;	COMDAT ?jsGet@@3P6A_JPAUJsExecStateInfo@@_JPBD@ZA
_BSS	SEGMENT
?jsGet@@3P6A_JPAUJsExecStateInfo@@_JPBD@ZA DD 01H DUP (?) ; jsGet
_BSS	ENDS
;	COMDAT ?wkeSetUserKeyValue@@3P6AXPAVCWebView@wke@@PBDPAX@ZA
_BSS	SEGMENT
?wkeSetUserKeyValue@@3P6AXPAVCWebView@wke@@PBDPAX@ZA DD 01H DUP (?) ; wkeSetUserKeyValue
_BSS	ENDS
;	COMDAT ?mbNetEnableResPacket@@3P6GXHPB_W@ZA
_BSS	SEGMENT
?mbNetEnableResPacket@@3P6GXHPB_W@ZA DD 01H DUP (?)	; mbNetEnableResPacket
_BSS	ENDS
;	COMDAT ?wkeSaveMemoryCache@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeSaveMemoryCache@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeSaveMemoryCache
_BSS	ENDS
;	COMDAT ?mbGetCookieOnBlinkThread@@3P6GPBDH@ZA
_BSS	SEGMENT
?mbGetCookieOnBlinkThread@@3P6GPBDH@ZA DD 01H DUP (?)	; mbGetCookieOnBlinkThread
_BSS	ENDS
;	COMDAT ?wkeGetWebViewByNData@@3P6APAVCWebView@wke@@PAX@ZA
_BSS	SEGMENT
?wkeGetWebViewByNData@@3P6APAVCWebView@wke@@PAX@ZA DD 01H DUP (?) ; wkeGetWebViewByNData
_BSS	ENDS
;	COMDAT ?wkePostURL@@3P6AXPAVCWebView@wke@@PBD1H@ZA
_BSS	SEGMENT
?wkePostURL@@3P6AXPAVCWebView@wke@@PBD1H@ZA DD 01H DUP (?) ; wkePostURL
_BSS	ENDS
;	COMDAT ?wkeReload@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeReload@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeReload
_BSS	ENDS
;	COMDAT ?mbSetDiskCacheLimit@@3P6GXHI@ZA
_BSS	SEGMENT
?mbSetDiskCacheLimit@@3P6GXHI@ZA DD 01H DUP (?)		; mbSetDiskCacheLimit
_BSS	ENDS
;	COMDAT ?mbGetHostHWND@@3P6GPAUHWND__@@H@ZA
_BSS	SEGMENT
?mbGetHostHWND@@3P6GPAUHWND__@@H@ZA DD 01H DUP (?)	; mbGetHostHWND
_BSS	ENDS
;	COMDAT ?wkeOnAlertBox@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA
_BSS	SEGMENT
?wkeOnAlertBox@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA DD 01H DUP (?) ; wkeOnAlertBox
_BSS	ENDS
;	COMDAT ?wkeRunMessageLoop@@3P6AXXZA
_BSS	SEGMENT
?wkeRunMessageLoop@@3P6AXXZA DD 01H DUP (?)		; wkeRunMessageLoop
_BSS	ENDS
;	COMDAT ?mbSetCookie@@3P6GXHPBD0@ZA
_BSS	SEGMENT
?mbSetCookie@@3P6GXHPBD0@ZA DD 01H DUP (?)		; mbSetCookie
_BSS	ENDS
;	COMDAT ?mbOnDocumentReadyInBlinkThread@@3P6GXHP6GXHPAX0@Z0@ZA
_BSS	SEGMENT
?mbOnDocumentReadyInBlinkThread@@3P6GXHP6GXHPAX0@Z0@ZA DD 01H DUP (?) ; mbOnDocumentReadyInBlinkThread
_BSS	ENDS
;	COMDAT ?wkeGetTitle@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetTitle@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetTitle
_BSS	ENDS
;	COMDAT ?mbSetHandleOffset@@3P6GXHHH@ZA
_BSS	SEGMENT
?mbSetHandleOffset@@3P6GXHHH@ZA DD 01H DUP (?)		; mbSetHandleOffset
_BSS	ENDS
;	COMDAT ?wkeDragTargetDragEnter@@3P6A?AW4_wkeWebDragOperation@@PAVCWebView@wke@@PBU_wkeWebDragData@@PBUtagPOINT@@2W41@H@ZA
_BSS	SEGMENT
?wkeDragTargetDragEnter@@3P6A?AW4_wkeWebDragOperation@@PAVCWebView@wke@@PBU_wkeWebDragData@@PBUtagPOINT@@2W41@H@ZA DD 01H DUP (?) ; wkeDragTargetDragEnter
_BSS	ENDS
;	COMDAT ?wkeIsLoadFailed@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsLoadFailed@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsLoadFailed
_BSS	ENDS
;	COMDAT ?wkeZoomFactor@@3P6AMPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeZoomFactor@@3P6AMPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeZoomFactor
_BSS	ENDS
;	COMDAT ?mbEditorCut@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorCut@@3P6GXH@ZA DD 01H DUP (?)			; mbEditorCut
_BSS	ENDS
;	COMDAT ?mbOnLoadUrlHeadersReceived@@3P6GXHP6GXHPAXPBD0@Z0@ZA
_BSS	SEGMENT
?mbOnLoadUrlHeadersReceived@@3P6GXHP6GXHPAXPBD0@Z0@ZA DD 01H DUP (?) ; mbOnLoadUrlHeadersReceived
_BSS	ENDS
;	COMDAT ?mbAddPluginDirectory@@3P6GXHPB_W@ZA
_BSS	SEGMENT
?mbAddPluginDirectory@@3P6GXHPB_W@ZA DD 01H DUP (?)	; mbAddPluginDirectory
_BSS	ENDS
;	COMDAT ?wkeFocus@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeFocus@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeFocus
_BSS	ENDS
;	COMDAT ?mbUnlockViewDC@@3P6GXH@ZA
_BSS	SEGMENT
?mbUnlockViewDC@@3P6GXH@ZA DD 01H DUP (?)		; mbUnlockViewDC
_BSS	ENDS
;	COMDAT ?mbGetNavigateIndex@@3P6GHH@ZA
_BSS	SEGMENT
?mbGetNavigateIndex@@3P6GHH@ZA DD 01H DUP (?)		; mbGetNavigateIndex
_BSS	ENDS
;	COMDAT ?wkeNetHoldJobToAsynCommit@@3P6AHPAX@ZA
_BSS	SEGMENT
?wkeNetHoldJobToAsynCommit@@3P6AHPAX@ZA DD 01H DUP (?)	; wkeNetHoldJobToAsynCommit
_BSS	ENDS
;	COMDAT ?wkeSetLanguage@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeSetLanguage@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeSetLanguage
_BSS	ENDS
;	COMDAT ?wkeMoveToCenter@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeMoveToCenter@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeMoveToCenter
_BSS	ENDS
;	COMDAT ?wkeTitle@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeTitle@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeTitle
_BSS	ENDS
;	COMDAT ?wkeJsBindSetter@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2@ZA
_BSS	SEGMENT
?wkeJsBindSetter@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2@ZA DD 01H DUP (?) ; wkeJsBindSetter
_BSS	ENDS
;	COMDAT ?wkeSetDragEnable@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetDragEnable@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetDragEnable
_BSS	ENDS
;	COMDAT ?wkeEditorPaste@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorPaste@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeEditorPaste
_BSS	ENDS
;	COMDAT ?mbEditorRedo@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorRedo@@3P6GXH@ZA DD 01H DUP (?)			; mbEditorRedo
_BSS	ENDS
;	COMDAT ?mbFireKeyUpEvent@@3P6GHHIIH@ZA
_BSS	SEGMENT
?mbFireKeyUpEvent@@3P6GHHIIH@ZA DD 01H DUP (?)		; mbFireKeyUpEvent
_BSS	ENDS
;	COMDAT ?mbOnURLChanged@@3P6GXHP6GXHPAXPBDHH@Z0@ZA
_BSS	SEGMENT
?mbOnURLChanged@@3P6GXHP6GXHPAXPBDHH@Z0@ZA DD 01H DUP (?) ; mbOnURLChanged
_BSS	ENDS
;	COMDAT ?jsIsFunction@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsFunction@@3P6A_N_J@ZA DD 01H DUP (?)		; jsIsFunction
_BSS	ENDS
;	COMDAT ?mbSetCookieJarPath@@3P6GXHPB_W@ZA
_BSS	SEGMENT
?mbSetCookieJarPath@@3P6GXHPB_W@ZA DD 01H DUP (?)	; mbSetCookieJarPath
_BSS	ENDS
;	COMDAT ?mbGetCookie@@**********************************@@PBD@Z0@ZA
_BSS	SEGMENT
?mbGetCookie@@**********************************@@PBD@Z0@ZA DD 01H DUP (?) ; mbGetCookie
_BSS	ENDS
;	COMDAT ?jsSetLength@@3P6AXPAUJsExecStateInfo@@_JH@ZA
_BSS	SEGMENT
?jsSetLength@@3P6AXPAUJsExecStateInfo@@_JH@ZA DD 01H DUP (?) ; jsSetLength
_BSS	ENDS
;	COMDAT ?wkeDelete@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeDelete@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeDelete
_BSS	ENDS
;	COMDAT ?mbUtilSetDefaultPrinterSettings@@3P6GXHPBU_mbDefaultPrinterSettings@@@ZA
_BSS	SEGMENT
?mbUtilSetDefaultPrinterSettings@@3P6GXHPBU_mbDefaultPrinterSettings@@@ZA DD 01H DUP (?) ; mbUtilSetDefaultPrinterSettings
_BSS	ENDS
;	COMDAT ?wkeSelectAll@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeSelectAll@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeSelectAll
_BSS	ENDS
;	COMDAT ?jsToV8Value@@3P6APAXPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToV8Value@@3P6APAXPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToV8Value
_BSS	ENDS
;	COMDAT ?mbOnPluginList@@3P6GXHP6GHHPAX0@Z0@ZA
_BSS	SEGMENT
?mbOnPluginList@@3P6GXHP6GHHPAX0@Z0@ZA DD 01H DUP (?)	; mbOnPluginList
_BSS	ENDS
;	COMDAT ?mbGetString@@3P6GPBDPAUmbString@@@ZA
_BSS	SEGMENT
?mbGetString@@3P6GPBDPAUmbString@@@ZA DD 01H DUP (?)	; mbGetString
_BSS	ENDS
;	COMDAT ?wkeOnDocumentReady2@@3P6AXPAVCWebView@wke@@P6AX0PAX1@Z1@ZA
_BSS	SEGMENT
?wkeOnDocumentReady2@@3P6AXPAVCWebView@wke@@P6AX0PAX1@Z1@ZA DD 01H DUP (?) ; wkeOnDocumentReady2
_BSS	ENDS
;	COMDAT ?mbShowWindow@@3P6GXHH@ZA
_BSS	SEGMENT
?mbShowWindow@@3P6GXHH@ZA DD 01H DUP (?)		; mbShowWindow
_BSS	ENDS
;	COMDAT ?jsEval@@3P6A_JPAUJsExecStateInfo@@PBD@ZA
_BSS	SEGMENT
?jsEval@@3P6A_JPAUJsExecStateInfo@@PBD@ZA DD 01H DUP (?) ; jsEval
_BSS	ENDS
;	COMDAT ?jsEmptyObject@@3P6A_JPAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsEmptyObject@@3P6A_JPAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsEmptyObject
_BSS	ENDS
;	COMDAT ?jsToTempStringW@@3P6APB_WPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToTempStringW@@3P6APB_WPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToTempStringW
_BSS	ENDS
;	COMDAT ?jsTypeOf@@3P6A?AW4_jsType@@_J@ZA
_BSS	SEGMENT
?jsTypeOf@@3P6A?AW4_jsType@@_J@ZA DD 01H DUP (?)	; jsTypeOf
_BSS	ENDS
;	COMDAT ?mbNetSetWebsocketCallback@@3P6GXHPBU_mbWebsocketHookCallbacks@@PAX@ZA
_BSS	SEGMENT
?mbNetSetWebsocketCallback@@3P6GXHPBU_mbWebsocketHookCallbacks@@PAX@ZA DD 01H DUP (?) ; mbNetSetWebsocketCallback
_BSS	ENDS
;	COMDAT ?mbNetSendWsBlob@@3P6GXPAXPBDI@ZA
_BSS	SEGMENT
?mbNetSendWsBlob@@3P6GXPAXPBDI@ZA DD 01H DUP (?)	; mbNetSendWsBlob
_BSS	ENDS
;	COMDAT ?jsSetGlobal@@3P6AXPAUJsExecStateInfo@@PBD_J@ZA
_BSS	SEGMENT
?jsSetGlobal@@3P6AXPAUJsExecStateInfo@@PBD_J@ZA DD 01H DUP (?) ; jsSetGlobal
_BSS	ENDS
;	COMDAT ?wkeInsertCSSByFrame@@3P6AXPAVCWebView@wke@@PAXPBD@ZA
_BSS	SEGMENT
?wkeInsertCSSByFrame@@3P6AXPAVCWebView@wke@@PAXPBD@ZA DD 01H DUP (?) ; wkeInsertCSSByFrame
_BSS	ENDS
;	COMDAT ?mbIsAudioMuted@@3P6GHH@ZA
_BSS	SEGMENT
?mbIsAudioMuted@@3P6GHH@ZA DD 01H DUP (?)		; mbIsAudioMuted
_BSS	ENDS
;	COMDAT ?wkeIsWebRemoteFrame@@3P6A_NPAVCWebView@wke@@PAX@ZA
_BSS	SEGMENT
?wkeIsWebRemoteFrame@@3P6A_NPAVCWebView@wke@@PAX@ZA DD 01H DUP (?) ; wkeIsWebRemoteFrame
_BSS	ENDS
;	COMDAT ?jsInt@@3P6A_JH@ZA
_BSS	SEGMENT
?jsInt@@3P6A_JH@ZA DD 01H DUP (?)			; jsInt
_BSS	ENDS
;	COMDAT ?wkeCreateString@@3P6APAVCString@wke@@PBDI@ZA
_BSS	SEGMENT
?wkeCreateString@@3P6APAVCString@wke@@PBDI@ZA DD 01H DUP (?) ; wkeCreateString
_BSS	ENDS
;	COMDAT ?mbUtilPrintToPdf@@3P6GXHPAXPBU_mbPrintSettings@@P6GXH0PBU_mbPdfDatas@@@Z0@ZA
_BSS	SEGMENT
?mbUtilPrintToPdf@@3P6GXHPAXPBU_mbPrintSettings@@P6GXH0PBU_mbPdfDatas@@@Z0@ZA DD 01H DUP (?) ; mbUtilPrintToPdf
_BSS	ENDS
;	COMDAT ?jsGetData@@3P6APAUtagjsData@@PAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsGetData@@3P6APAUtagjsData@@PAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsGetData
_BSS	ENDS
;	COMDAT ?mbSetDeviceParameter@@3P6GXHPBD0HM@ZA
_BSS	SEGMENT
?mbSetDeviceParameter@@3P6GXHPBD0HM@ZA DD 01H DUP (?)	; mbSetDeviceParameter
_BSS	ENDS
;	COMDAT ?jsThrowException@@3P6A_JPAUJsExecStateInfo@@PBD@ZA
_BSS	SEGMENT
?jsThrowException@@3P6A_JPAUJsExecStateInfo@@PBD@ZA DD 01H DUP (?) ; jsThrowException
_BSS	ENDS
;	COMDAT ?mbFireKeyPressEvent@@3P6GHHIIH@ZA
_BSS	SEGMENT
?mbFireKeyPressEvent@@3P6GHHIIH@ZA DD 01H DUP (?)	; mbFireKeyPressEvent
_BSS	ENDS
;	COMDAT ?mbOnDestroy@@3P6GHHP6GHHPAX0@Z0@ZA
_BSS	SEGMENT
?mbOnDestroy@@3P6GHHP6GHHPAX0@Z0@ZA DD 01H DUP (?)	; mbOnDestroy
_BSS	ENDS
;	COMDAT ?mbNetHookRequest@@3P6GXPAX@ZA
_BSS	SEGMENT
?mbNetHookRequest@@3P6GXPAX@ZA DD 01H DUP (?)		; mbNetHookRequest
_BSS	ENDS
;	COMDAT ?wkeUtilCreateV8Snapshot@@3P6APBU_wkeMemBuf@@PBD@ZA
_BSS	SEGMENT
?wkeUtilCreateV8Snapshot@@3P6APBU_wkeMemBuf@@PBD@ZA DD 01H DUP (?) ; wkeUtilCreateV8Snapshot
_BSS	ENDS
;	COMDAT ?mbOnPromptBox@@3P6GXHP6GPAUmbString@@HPAXPBD1@Z0@ZA
_BSS	SEGMENT
?mbOnPromptBox@@3P6GXHP6GPAUmbString@@HPAXPBD1@Z0@ZA DD 01H DUP (?) ; mbOnPromptBox
_BSS	ENDS
;	COMDAT ?mbSetUserAgent@@3P6GXHPBD@ZA
_BSS	SEGMENT
?mbSetUserAgent@@3P6GXHPBD@ZA DD 01H DUP (?)		; mbSetUserAgent
_BSS	ENDS
;	COMDAT ?mbNetCreatePostBodyElements@@3P6GPAU_mbPostBodyElements@@HI@ZA
_BSS	SEGMENT
?mbNetCreatePostBodyElements@@3P6GPAU_mbPostBodyElements@@HI@ZA DD 01H DUP (?) ; mbNetCreatePostBodyElements
_BSS	ENDS
;	COMDAT ?jsEvalW@@3P6A_JPAUJsExecStateInfo@@PB_W@ZA
_BSS	SEGMENT
?jsEvalW@@3P6A_JPAUJsExecStateInfo@@PB_W@ZA DD 01H DUP (?) ; jsEvalW
_BSS	ENDS
;	COMDAT ?wkeContentsHeight@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeContentsHeight@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeContentsHeight
_BSS	ENDS
;	COMDAT ?jsIsObject@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsObject@@3P6A_N_J@ZA DD 01H DUP (?)			; jsIsObject
_BSS	ENDS
;	COMDAT ?wkeLoadHTML@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeLoadHTML@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeLoadHTML
_BSS	ENDS
;	COMDAT ?jsToString@@3P6APBDPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToString@@3P6APBDPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToString
_BSS	ENDS
;	COMDAT ?wkeEditorDelete@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorDelete@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeEditorDelete
_BSS	ENDS
;	COMDAT ?wkeMoveWindow@@3P6AXPAVCWebView@wke@@HHHH@ZA
_BSS	SEGMENT
?wkeMoveWindow@@3P6AXPAVCWebView@wke@@HHHH@ZA DD 01H DUP (?) ; wkeMoveWindow
_BSS	ENDS
;	COMDAT ?mbGoForward@@3P6GXH@ZA
_BSS	SEGMENT
?mbGoForward@@3P6GXH@ZA DD 01H DUP (?)			; mbGoForward
_BSS	ENDS
;	COMDAT ?wkeGetCursorInfoType@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetCursorInfoType@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetCursorInfoType
_BSS	ENDS
;	COMDAT ?jsBindFunction@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@ZI@ZA
_BSS	SEGMENT
?jsBindFunction@@3P6AXPBDP6I_JPAUJsExecStateInfo@@@ZI@ZA DD 01H DUP (?) ; jsBindFunction
_BSS	ENDS
;	COMDAT ?wkeWidth@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeWidth@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeWidth
_BSS	ENDS
;	COMDAT ?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA
_BSS	SEGMENT
?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA DD 01H DUP (?) ; TSS0<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
_BSS	ENDS
;	COMDAT ?mbContextMenuItemClick@@3P6GXHP6G_NHPAXW4_wkeOnContextMenuItemClickType@@W4_wkeOnContextMenuItemClickStep@@00@Z0@ZA
_BSS	SEGMENT
?mbContextMenuItemClick@@3P6GXHP6G_NHPAXW4_wkeOnContextMenuItemClickType@@W4_wkeOnContextMenuItemClickStep@@00@Z0@ZA DD 01H DUP (?) ; mbContextMenuItemClick
_BSS	ENDS
;	COMDAT ?mbUtilCreateV8Snapshot@@3P6GPBU_mbMemBuf@@PBD@ZA
_BSS	SEGMENT
?mbUtilCreateV8Snapshot@@3P6GPBU_mbMemBuf@@PBD@ZA DD 01H DUP (?) ; mbUtilCreateV8Snapshot
_BSS	ENDS
;	COMDAT ?wkeIsDocumentReady@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsDocumentReady@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsDocumentReady
_BSS	ENDS
;	COMDAT ?mbGetZoomFactor@@3P6GMH@ZA
_BSS	SEGMENT
?mbGetZoomFactor@@3P6GMH@ZA DD 01H DUP (?)		; mbGetZoomFactor
_BSS	ENDS
;	COMDAT ?wkeRepaintIfNeeded@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeRepaintIfNeeded@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeRepaintIfNeeded
_BSS	ENDS
;	COMDAT ?mbIsMainFrame@@3P6GHHPAX@ZA
_BSS	SEGMENT
?mbIsMainFrame@@3P6GHHPAX@ZA DD 01H DUP (?)		; mbIsMainFrame
_BSS	ENDS
;	COMDAT ?wkeGetCookieW@@3P6APB_WPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetCookieW@@3P6APB_WPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetCookieW
_BSS	ENDS
;	COMDAT ?wkeOnCreateView@@3P6AXPAVCWebView@wke@@P6APAV12@0PAXW4_wkeNavigationType@@QAVCString@2@PBU_wkeWindowFeatures@@@Z1@ZA
_BSS	SEGMENT
?wkeOnCreateView@@3P6AXPAVCWebView@wke@@P6APAV12@0PAXW4_wkeNavigationType@@QAVCString@2@PBU_wkeWindowFeatures@@@Z1@ZA DD 01H DUP (?) ; wkeOnCreateView
_BSS	ENDS
;	COMDAT ?wkeGetUserKeyValue@@3P6APAXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeGetUserKeyValue@@3P6APAXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeGetUserKeyValue
_BSS	ENDS
;	COMDAT ?mbDownloadByPath@@3P6G?AW4_mbDownloadOpt@@HPBU_mbDownloadOptions@@PB_WIPBD22PAXPAU_mbNetJobDataBind@@PAU_mbDownloadBind@@@ZA
_BSS	SEGMENT
?mbDownloadByPath@@3P6G?AW4_mbDownloadOpt@@HPBU_mbDownloadOptions@@PB_WIPBD22PAXPAU_mbNetJobDataBind@@PAU_mbDownloadBind@@@ZA DD 01H DUP (?) ; mbDownloadByPath
_BSS	ENDS
;	COMDAT ?wkeNetDeleteBlinkWebURLRequestPtr@@3P6AXPAVWebURLRequest@blink@@@ZA
_BSS	SEGMENT
?wkeNetDeleteBlinkWebURLRequestPtr@@3P6AXPAVWebURLRequest@blink@@@ZA DD 01H DUP (?) ; wkeNetDeleteBlinkWebURLRequestPtr
_BSS	ENDS
;	COMDAT ?wkeSleep@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeSleep@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeSleep
_BSS	ENDS
;	COMDAT ?strHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4VCWin32Heap@3@A
_BSS	SEGMENT
?strHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4VCWin32Heap@3@A DB 0cH DUP (?) ; `ATL::CAtlStringMgr::GetInstance'::`2'::strHeap
_BSS	ENDS
;	COMDAT ?wkeNetCopyWebUrlRequest@@3P6APAVWebURLRequest@blink@@PAX_N@ZA
_BSS	SEGMENT
?wkeNetCopyWebUrlRequest@@3P6APAVWebURLRequest@blink@@PAX_N@ZA DD 01H DUP (?) ; wkeNetCopyWebUrlRequest
_BSS	ENDS
;	COMDAT ?wkeOnDocumentReady@@3P6AXPAVCWebView@wke@@P6AX0PAX@Z1@ZA
_BSS	SEGMENT
?wkeOnDocumentReady@@3P6AXPAVCWebView@wke@@P6AX0PAX@Z1@ZA DD 01H DUP (?) ; wkeOnDocumentReady
_BSS	ENDS
;	COMDAT ?jsFloat@@3P6A_JM@ZA
_BSS	SEGMENT
?jsFloat@@3P6A_JM@ZA DD 01H DUP (?)			; jsFloat
_BSS	ENDS
;	COMDAT ?wkeGetViewDC@@3P6APAUHDC__@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetViewDC@@3P6APAUHDC__@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetViewDC
_BSS	ENDS
;	COMDAT ?mbGoBack@@3P6GXH@ZA
_BSS	SEGMENT
?mbGoBack@@3P6GXH@ZA DD 01H DUP (?)			; mbGoBack
_BSS	ENDS
;	COMDAT ?wkeUtilRelasePrintPdfDatas@@3P6AXPBU_wkePdfDatas@@@ZA
_BSS	SEGMENT
?wkeUtilRelasePrintPdfDatas@@3P6AXPBU_wkePdfDatas@@@ZA DD 01H DUP (?) ; wkeUtilRelasePrintPdfDatas
_BSS	ENDS
;	COMDAT ?wkeSetName@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeSetName@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeSetName
_BSS	ENDS
;	COMDAT ?wkeOnStartDragging@@3P6AXPAVCWebView@wke@@P6AX0PAX1PBU_wkeWebDragData@@W4_wkeWebDragOperation@@PBXPBU_wkePoint@@@Z1@ZA
_BSS	SEGMENT
?wkeOnStartDragging@@3P6AXPAVCWebView@wke@@P6AX0PAX1PBU_wkeWebDragData@@W4_wkeWebDragOperation@@PBXPBU_wkePoint@@@Z1@ZA DD 01H DUP (?) ; wkeOnStartDragging
_BSS	ENDS
;	COMDAT ?mbCanGoForward@@**********************************@@H@Z0@ZA
_BSS	SEGMENT
?mbCanGoForward@@**********************************@@H@Z0@ZA DD 01H DUP (?) ; mbCanGoForward
_BSS	ENDS
;	COMDAT ?mbCreateWebCustomWindow@@3P6GHPAUHWND__@@KKHHHH@ZA
_BSS	SEGMENT
?mbCreateWebCustomWindow@@3P6GHPAUHWND__@@KKHHHH@ZA DD 01H DUP (?) ; mbCreateWebCustomWindow
_BSS	ENDS
;	COMDAT ?wkeSetNpapiPluginsEnabled@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetNpapiPluginsEnabled@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetNpapiPluginsEnabled
_BSS	ENDS
;	COMDAT ?wkeGetMediaVolume@@3P6AMPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetMediaVolume@@3P6AMPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetMediaVolume
_BSS	ENDS
;	COMDAT ?wkePerformCookieCommand@@3P6AXPAVCWebView@wke@@W4_wkeCookieCommand@@@ZA
_BSS	SEGMENT
?wkePerformCookieCommand@@3P6AXPAVCWebView@wke@@W4_wkeCookieCommand@@@ZA DD 01H DUP (?) ; wkePerformCookieCommand
_BSS	ENDS
;	COMDAT ?mbNetGetExpectedContentLength@@3P6G_JPAUmbWebUrlResponse@@@ZA
_BSS	SEGMENT
?mbNetGetExpectedContentLength@@3P6G_JPAUmbWebUrlResponse@@@ZA DD 01H DUP (?) ; mbNetGetExpectedContentLength
_BSS	ENDS
;	COMDAT ?wkeIsAwake@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsAwake@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeIsAwake
_BSS	ENDS
;	COMDAT ?wkeEditorCopy@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorCopy@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeEditorCopy
_BSS	ENDS
;	COMDAT ?mbNetAddHTTPHeaderFieldToUrlRequest@@3P6GXPAUmbWebUrlRequest@@PBD1@ZA
_BSS	SEGMENT
?mbNetAddHTTPHeaderFieldToUrlRequest@@3P6GXPAUmbWebUrlRequest@@PBD1@ZA DD 01H DUP (?) ; mbNetAddHTTPHeaderFieldToUrlRequest
_BSS	ENDS
;	COMDAT ?mbOnAcceleratedPaint@@3P6GXHP6GXHPAXHPBU_mbRect@@I0@Z0@ZA
_BSS	SEGMENT
?mbOnAcceleratedPaint@@3P6GXHP6GXHPAXHPBU_mbRect@@I0@Z0@ZA DD 01H DUP (?) ; mbOnAcceleratedPaint
_BSS	ENDS
;	COMDAT ?wkeOnPluginFind@@3P6AXPAVCWebView@wke@@PBDP6AX0PAX1222@Z2@ZA
_BSS	SEGMENT
?wkeOnPluginFind@@3P6AXPAVCWebView@wke@@PBDP6AX0PAX1222@Z2@ZA DD 01H DUP (?) ; wkeOnPluginFind
_BSS	ENDS
;	COMDAT ?wkeNetGetRequestMethod@@3P6A?AW4_wkeRequestType@@PAX@ZA
_BSS	SEGMENT
?wkeNetGetRequestMethod@@3P6A?AW4_wkeRequestType@@PAX@ZA DD 01H DUP (?) ; wkeNetGetRequestMethod
_BSS	ENDS
;	COMDAT ?wkeGetWebviewId@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetWebviewId@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetWebviewId
_BSS	ENDS
;	COMDAT ?jsToStringW@@3P6APB_WPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToStringW@@3P6APB_WPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToStringW
_BSS	ENDS
;	COMDAT ?wkeOnWillMediaLoad@@3P6AXPAVCWebView@wke@@P6AX0PAXPBDPAU_wkeMediaLoadInfo@@@Z1@ZA
_BSS	SEGMENT
?wkeOnWillMediaLoad@@3P6AXPAVCWebView@wke@@P6AX0PAXPBDPAU_wkeMediaLoadInfo@@@Z1@ZA DD 01H DUP (?) ; wkeOnWillMediaLoad
_BSS	ENDS
;	COMDAT ?wkeEditorRedo@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorRedo@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeEditorRedo
_BSS	ENDS
;	COMDAT ?wkeNetGetFavicon@@3P6AHPAVCWebView@wke@@P6AX0PAXPBDPAU_wkeMemBuf@@@Z1@ZA
_BSS	SEGMENT
?wkeNetGetFavicon@@3P6AHPAVCWebView@wke@@P6AX0PAXPBDPAU_wkeMemBuf@@@Z1@ZA DD 01H DUP (?) ; wkeNetGetFavicon
_BSS	ENDS
;	COMDAT ?wkeGetVersion@@3P6AIXZA
_BSS	SEGMENT
?wkeGetVersion@@3P6AIXZA DD 01H DUP (?)			; wkeGetVersion
_BSS	ENDS
;	COMDAT ?wkeShutdown@@3P6AXXZA
_BSS	SEGMENT
?wkeShutdown@@3P6AXXZA DD 01H DUP (?)			; wkeShutdown
_BSS	ENDS
;	COMDAT ?jsSet@@3P6AXPAUJsExecStateInfo@@_JPBD1@ZA
_BSS	SEGMENT
?jsSet@@3P6AXPAUJsExecStateInfo@@_JPBD1@ZA DD 01H DUP (?) ; jsSet
_BSS	ENDS
;	COMDAT ?jsArgCount@@3P6AHPAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsArgCount@@3P6AHPAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsArgCount
_BSS	ENDS
;	COMDAT ?wkeOnLoadingFinish@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@W4_wkeLoadingResult@@2@Z1@ZA
_BSS	SEGMENT
?wkeOnLoadingFinish@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@W4_wkeLoadingResult@@2@Z1@ZA DD 01H DUP (?) ; wkeOnLoadingFinish
_BSS	ENDS
;	COMDAT ?mbNetGetResponseUrl@@3P6GPBDPAUmbWebUrlResponse@@@ZA
_BSS	SEGMENT
?mbNetGetResponseUrl@@3P6GPBDPAUmbWebUrlResponse@@@ZA DD 01H DUP (?) ; mbNetGetResponseUrl
_BSS	ENDS
;	COMDAT ?wkeSetViewProxy@@3P6AXPAVCWebView@wke@@PAU_wkeProxy@@@ZA
_BSS	SEGMENT
?wkeSetViewProxy@@3P6AXPAVCWebView@wke@@PAU_wkeProxy@@@ZA DD 01H DUP (?) ; wkeSetViewProxy
_BSS	ENDS
;	COMDAT ?wkeOnTitleChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA
_BSS	SEGMENT
?wkeOnTitleChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA DD 01H DUP (?) ; wkeOnTitleChanged
_BSS	ENDS
;	COMDAT ?mbOnImageBufferToDataURL@@3P6GXHP6GPAUmbString@@HPAXPBDI@Z0@ZA
_BSS	SEGMENT
?mbOnImageBufferToDataURL@@3P6GXHP6GPAUmbString@@HPAXPBDI@Z0@ZA DD 01H DUP (?) ; mbOnImageBufferToDataURL
_BSS	ENDS
;	COMDAT ?wkeEditorCut@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorCut@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeEditorCut
_BSS	ENDS
;	COMDAT ?wkeIsProcessingUserGesture@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsProcessingUserGesture@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsProcessingUserGesture
_BSS	ENDS
;	COMDAT ?jsUndefined@@3P6A_JXZA
_BSS	SEGMENT
?jsUndefined@@3P6A_JXZA DD 01H DUP (?)			; jsUndefined
_BSS	ENDS
;	COMDAT ?wkeGoBack@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGoBack@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeGoBack
_BSS	ENDS
;	COMDAT ?wkeVersion@@3P6AIXZA
_BSS	SEGMENT
?wkeVersion@@3P6AIXZA DD 01H DUP (?)			; wkeVersion
_BSS	ENDS
;	COMDAT ?wkeOnPrint@@3P6AXPAVCWebView@wke@@P6AX0PAX11@Z1@ZA
_BSS	SEGMENT
?wkeOnPrint@@3P6AXPAVCWebView@wke@@P6AX0PAX11@Z1@ZA DD 01H DUP (?) ; wkeOnPrint
_BSS	ENDS
;	COMDAT ?jsIsNull@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsNull@@3P6A_N_J@ZA DD 01H DUP (?)			; jsIsNull
_BSS	ENDS
;	COMDAT ?mbJsToDouble@@3P6GNPAX_J@ZA
_BSS	SEGMENT
?mbJsToDouble@@3P6GNPAX_J@ZA DD 01H DUP (?)		; mbJsToDouble
_BSS	ENDS
;	COMDAT ?wkeAddPluginDirectory@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeAddPluginDirectory@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeAddPluginDirectory
_BSS	ENDS
;	COMDAT ?jsDeleteObjectProp@@3P6AXPAUJsExecStateInfo@@_JPBD@ZA
_BSS	SEGMENT
?jsDeleteObjectProp@@3P6AXPAUJsExecStateInfo@@_JPBD@ZA DD 01H DUP (?) ; jsDeleteObjectProp
_BSS	ENDS
;	COMDAT ?wkeMouseEvent@@3P6A_NPAVCWebView@wke@@IHHI@ZA
_BSS	SEGMENT
?wkeMouseEvent@@3P6A_NPAVCWebView@wke@@IHHI@ZA DD 01H DUP (?) ; wkeMouseEvent
_BSS	ENDS
;	COMDAT ?wkeAddDirtyArea@@3P6AXPAVCWebView@wke@@HHHH@ZA
_BSS	SEGMENT
?wkeAddDirtyArea@@3P6AXPAVCWebView@wke@@HHHH@ZA DD 01H DUP (?) ; wkeAddDirtyArea
_BSS	ENDS
;	COMDAT ?mbNetGetPostBody@@3P6GPAU_mbPostBodyElements@@PAX@ZA
_BSS	SEGMENT
?mbNetGetPostBody@@3P6GPAU_mbPostBodyElements@@PAX@ZA DD 01H DUP (?) ; mbNetGetPostBody
_BSS	ENDS
;	COMDAT ?wkeGetCaretRect@@3P6A?AU_wkeRect@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetCaretRect@@3P6A?AU_wkeRect@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetCaretRect
_BSS	ENDS
;	COMDAT ?jsEvalExW@@3P6A_JPAUJsExecStateInfo@@PB_W_N@ZA
_BSS	SEGMENT
?jsEvalExW@@3P6A_JPAUJsExecStateInfo@@PB_W_N@ZA DD 01H DUP (?) ; jsEvalExW
_BSS	ENDS
;	COMDAT ?mbSetTouchEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetTouchEnabled@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetTouchEnabled
_BSS	ENDS
;	COMDAT ?mbWake@@3P6GXH@ZA
_BSS	SEGMENT
?mbWake@@3P6GXH@ZA DD 01H DUP (?)			; mbWake
_BSS	ENDS
;	COMDAT ?mbCallUiThreadSync@@3P6GXP6GXPAX0@Z00@ZA
_BSS	SEGMENT
?mbCallUiThreadSync@@3P6GXP6GXPAX0@Z00@ZA DD 01H DUP (?) ; mbCallUiThreadSync
_BSS	ENDS
;	COMDAT ?mbNetCancelRequest@@3P6GXPAX@ZA
_BSS	SEGMENT
?mbNetCancelRequest@@3P6GXPAX@ZA DD 01H DUP (?)		; mbNetCancelRequest
_BSS	ENDS
;	COMDAT ?wkeSetContextMenuItemShow@@3P6AXPAVCWebView@wke@@W4_wkeMenuItemId@@_N@ZA
_BSS	SEGMENT
?wkeSetContextMenuItemShow@@3P6AXPAVCWebView@wke@@W4_wkeMenuItemId@@_N@ZA DD 01H DUP (?) ; wkeSetContextMenuItemShow
_BSS	ENDS
;	COMDAT ?mbStopLoading@@3P6GXH@ZA
_BSS	SEGMENT
?mbStopLoading@@3P6GXH@ZA DD 01H DUP (?)		; mbStopLoading
_BSS	ENDS
;	COMDAT ?mbNetFreePostBodyElement@@3P6GXPAU_mbPostBodyElement@@@ZA
_BSS	SEGMENT
?mbNetFreePostBodyElement@@3P6GXPAU_mbPostBodyElement@@@ZA DD 01H DUP (?) ; mbNetFreePostBodyElement
_BSS	ENDS
;	COMDAT ?wkeSetNavigationToNewWindowEnable@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetNavigationToNewWindowEnable@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetNavigationToNewWindowEnable
_BSS	ENDS
;	COMDAT ?jsGlobalObject@@3P6A_JPAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsGlobalObject@@3P6A_JPAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsGlobalObject
_BSS	ENDS
;	COMDAT ?wkeResizeWindow@@3P6AXPAVCWebView@wke@@HH@ZA
_BSS	SEGMENT
?wkeResizeWindow@@3P6AXPAVCWebView@wke@@HH@ZA DD 01H DUP (?) ; wkeResizeWindow
_BSS	ENDS
;	COMDAT ?wkeIsLoadingFailed@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsLoadingFailed@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsLoadingFailed
_BSS	ENDS
;	COMDAT ?mbNetSetData@@3P6GXPAX0H@ZA
_BSS	SEGMENT
?mbNetSetData@@3P6GXPAX0H@ZA DD 01H DUP (?)		; mbNetSetData
_BSS	ENDS
;	COMDAT ?wkeLoadURL@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeLoadURL@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeLoadURL
_BSS	ENDS
;	COMDAT ?mbUninit@@3P6GXXZA
_BSS	SEGMENT
?mbUninit@@3P6GXXZA DD 01H DUP (?)			; mbUninit
_BSS	ENDS
;	COMDAT ?wkeGetDebugConfig@@3P6APAXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeGetDebugConfig@@3P6APAXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeGetDebugConfig
_BSS	ENDS
;	COMDAT ?mbSetDiskCacheLevel@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetDiskCacheLevel@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetDiskCacheLevel
_BSS	ENDS
;	COMDAT ?mbRunJsSync@@3P6G_JHPAXPBDH@ZA
_BSS	SEGMENT
?mbRunJsSync@@3P6G_JHPAXPBDH@ZA DD 01H DUP (?)		; mbRunJsSync
_BSS	ENDS
;	COMDAT ?mbGoToOffset@@3P6GXHH@ZA
_BSS	SEGMENT
?mbGoToOffset@@3P6GXHH@ZA DD 01H DUP (?)		; mbGoToOffset
_BSS	ENDS
;	COMDAT ?wkeFireMouseEvent@@3P6A_NPAVCWebView@wke@@IHHI@ZA
_BSS	SEGMENT
?wkeFireMouseEvent@@3P6A_NPAVCWebView@wke@@IHHI@ZA DD 01H DUP (?) ; wkeFireMouseEvent
_BSS	ENDS
;	COMDAT ?wkeGoToOffset@@3P6AXPAVCWebView@wke@@H@ZA
_BSS	SEGMENT
?wkeGoToOffset@@3P6AXPAVCWebView@wke@@H@ZA DD 01H DUP (?) ; wkeGoToOffset
_BSS	ENDS
;	COMDAT ?wkeIsLoaded@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsLoaded@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeIsLoaded
_BSS	ENDS
;	COMDAT ?wkeLoadFile@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeLoadFile@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeLoadFile
_BSS	ENDS
;	COMDAT ?wkeContentsWidth@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeContentsWidth@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeContentsWidth
_BSS	ENDS
;	COMDAT ?mbSetEditable@@3P6GXH_N@ZA
_BSS	SEGMENT
?mbSetEditable@@3P6GXH_N@ZA DD 01H DUP (?)		; mbSetEditable
_BSS	ENDS
;	COMDAT ?wkeGetCaret@@3P6A?AU_wkeRect@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetCaret@@3P6A?AU_wkeRect@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetCaret
_BSS	ENDS
;	COMDAT ?wkeOnLoadUrlBegin@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD1@Z1@ZA
_BSS	SEGMENT
?wkeOnLoadUrlBegin@@3P6AXPAVCWebView@wke@@P6A_N0PAXPBD1@Z1@ZA DD 01H DUP (?) ; wkeOnLoadUrlBegin
_BSS	ENDS
;	COMDAT ?wkeGetGlobalExecByFrame@@3P6APAUJsExecStateInfo@@PAVCWebView@wke@@PAX@ZA
_BSS	SEGMENT
?wkeGetGlobalExecByFrame@@3P6APAUJsExecStateInfo@@PAVCWebView@wke@@PAX@ZA DD 01H DUP (?) ; wkeGetGlobalExecByFrame
_BSS	ENDS
;	COMDAT ?wkeSetWebViewName@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeSetWebViewName@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeSetWebViewName
_BSS	ENDS
;	COMDAT ?mbPluginListBuilderAddMediaTypeToLastPlugin@@3P6GXPAXPBD1@ZA
_BSS	SEGMENT
?mbPluginListBuilderAddMediaTypeToLastPlugin@@3P6GXPAXPBD1@ZA DD 01H DUP (?) ; mbPluginListBuilderAddMediaTypeToLastPlugin
_BSS	ENDS
;	COMDAT ?wkeGetVersionString@@3P6APBDXZA
_BSS	SEGMENT
?wkeGetVersionString@@3P6APBDXZA DD 01H DUP (?)		; wkeGetVersionString
_BSS	ENDS
;	COMDAT ?jsCallGlobal@@3P6A_JPAUJsExecStateInfo@@_JPA_JH@ZA
_BSS	SEGMENT
?jsCallGlobal@@3P6A_JPAUJsExecStateInfo@@_JPA_JH@ZA DD 01H DUP (?) ; jsCallGlobal
_BSS	ENDS
;	COMDAT ?mbOnDidCreateScriptContext@@3P6GXHP6GXHPAX00HH@Z0@ZA
_BSS	SEGMENT
?mbOnDidCreateScriptContext@@3P6GXHP6GXHPAX00HH@Z0@ZA DD 01H DUP (?) ; mbOnDidCreateScriptContext
_BSS	ENDS
;	COMDAT ?wkeFinalize@@3P6AXXZA
_BSS	SEGMENT
?wkeFinalize@@3P6AXXZA DD 01H DUP (?)			; wkeFinalize
_BSS	ENDS
;	COMDAT ?wkeOnPromptBox@@3P6AXPAVCWebView@wke@@P6A_N0PAXQAVCString@2@2PAV32@@Z1@ZA
_BSS	SEGMENT
?wkeOnPromptBox@@3P6AXPAVCWebView@wke@@P6A_N0PAXQAVCString@2@2PAV32@@Z1@ZA DD 01H DUP (?) ; wkeOnPromptBox
_BSS	ENDS
;	COMDAT ?jsGetAt@@3P6A_JPAUJsExecStateInfo@@_JH@ZA
_BSS	SEGMENT
?jsGetAt@@3P6A_JPAUJsExecStateInfo@@_JH@ZA DD 01H DUP (?) ; jsGetAt
_BSS	ENDS
;	COMDAT ?mbOnCreateView@@********************************@@PBDPBUmbWindowFeatures@@@Z0@ZA
_BSS	SEGMENT
?mbOnCreateView@@********************************@@PBDPBUmbWindowFeatures@@@Z0@ZA DD 01H DUP (?) ; mbOnCreateView
_BSS	ENDS
;	COMDAT ?mbCreateStringWithoutNullTermination@@3P6GPAUmbString@@PBDI@ZA
_BSS	SEGMENT
?mbCreateStringWithoutNullTermination@@3P6GPAUmbString@@PBDI@ZA DD 01H DUP (?) ; mbCreateStringWithoutNullTermination
_BSS	ENDS
;	COMDAT ?mbEditorUndo@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorUndo@@3P6GXH@ZA DD 01H DUP (?)			; mbEditorUndo
_BSS	ENDS
;	COMDAT ?jsIsNumber@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsNumber@@3P6A_N_J@ZA DD 01H DUP (?)			; jsIsNumber
_BSS	ENDS
;	COMDAT ?jsGetWebView@@3P6APAVCWebView@wke@@PAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsGetWebView@@3P6APAVCWebView@wke@@PAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsGetWebView
_BSS	ENDS
;	COMDAT ?mbResponseQuery@@3P6GXH_JHPBD@ZA
_BSS	SEGMENT
?mbResponseQuery@@3P6GXH_JHPBD@ZA DD 01H DUP (?)	; mbResponseQuery
_BSS	ENDS
;	COMDAT ?mbNetCancelWebUrlRequest@@3P6GXH@ZA
_BSS	SEGMENT
?mbNetCancelWebUrlRequest@@3P6GXH@ZA DD 01H DUP (?)	; mbNetCancelWebUrlRequest
_BSS	ENDS
;	COMDAT ?wkeUtilBase64Encode@@3P6APBDPBD@ZA
_BSS	SEGMENT
?wkeUtilBase64Encode@@3P6APBDPBD@ZA DD 01H DUP (?)	; wkeUtilBase64Encode
_BSS	ENDS
;	COMDAT ?wkeNetChangeRequestUrl@@3P6AXPAXPBD@ZA
_BSS	SEGMENT
?wkeNetChangeRequestUrl@@3P6AXPAXPBD@ZA DD 01H DUP (?)	; wkeNetChangeRequestUrl
_BSS	ENDS
;	COMDAT ?wkeSetFocus@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeSetFocus@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeSetFocus
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
_BSS	ENDS
;	COMDAT ?mbOnDownload@@3P6GXHP6GHHPAX0PBD0@Z0@ZA
_BSS	SEGMENT
?mbOnDownload@@3P6GXHP6GHHPAX0PBD0@Z0@ZA DD 01H DUP (?)	; mbOnDownload
_BSS	ENDS
;	COMDAT ?mbSetLocalStorageFullPath@@3P6GXHPB_W@ZA
_BSS	SEGMENT
?mbSetLocalStorageFullPath@@3P6GXHPB_W@ZA DD 01H DUP (?) ; mbSetLocalStorageFullPath
_BSS	ENDS
;	COMDAT ?wkePaint@@3P6AXPAVCWebView@wke@@PAXH@ZA
_BSS	SEGMENT
?wkePaint@@3P6AXPAVCWebView@wke@@PAXH@ZA DD 01H DUP (?)	; wkePaint
_BSS	ENDS
;	COMDAT ?mbOnLoadingFinish@@3P6GXHP6GXHPAX0PBDW4mbLoadingResult@@1@Z0@ZA
_BSS	SEGMENT
?mbOnLoadingFinish@@3P6GXHP6GXHPAX0PBDW4mbLoadingResult@@1@Z0@ZA DD 01H DUP (?) ; mbOnLoadingFinish
_BSS	ENDS
;	COMDAT ?mbGetUserKeyValue@@3P6GPAXHPBD@ZA
_BSS	SEGMENT
?mbGetUserKeyValue@@3P6GPAXHPBD@ZA DD 01H DUP (?)	; mbGetUserKeyValue
_BSS	ENDS
;	COMDAT ?wkeOnConfirmBox@@3P6AXPAVCWebView@wke@@P6A_N0PAXQAVCString@2@@Z1@ZA
_BSS	SEGMENT
?wkeOnConfirmBox@@3P6AXPAVCWebView@wke@@P6A_N0PAXQAVCString@2@@Z1@ZA DD 01H DUP (?) ; wkeOnConfirmBox
_BSS	ENDS
;	COMDAT ?wkeContextMenuEvent@@3P6A_NPAVCWebView@wke@@HHI@ZA
_BSS	SEGMENT
?wkeContextMenuEvent@@3P6A_NPAVCWebView@wke@@HHI@ZA DD 01H DUP (?) ; wkeContextMenuEvent
_BSS	ENDS
;	COMDAT ?wkeIsCookieEnabled@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsCookieEnabled@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsCookieEnabled
_BSS	ENDS
;	COMDAT ?mbSetSystemTouchEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetSystemTouchEnabled@@3P6GXHH@ZA DD 01H DUP (?)	; mbSetSystemTouchEnabled
_BSS	ENDS
;	COMDAT ?wkeDragTargetEnd@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1W4_wkeWebDragOperation@@@ZA
_BSS	SEGMENT
?wkeDragTargetEnd@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1W4_wkeWebDragOperation@@@ZA DD 01H DUP (?) ; wkeDragTargetEnd
_BSS	ENDS
;	COMDAT ?wkeGetFrameUrl@@3P6APBDPAVCWebView@wke@@PAX@ZA
_BSS	SEGMENT
?wkeGetFrameUrl@@3P6APBDPAVCWebView@wke@@PAX@ZA DD 01H DUP (?) ; wkeGetFrameUrl
_BSS	ENDS
;	COMDAT ?wkeOnWindowClosing@@3P6AXPAVCWebView@wke@@P6A_N0PAX@Z1@ZA
_BSS	SEGMENT
?wkeOnWindowClosing@@3P6AXPAVCWebView@wke@@P6A_N0PAX@Z1@ZA DD 01H DUP (?) ; wkeOnWindowClosing
_BSS	ENDS
;	COMDAT ?wkeSetHandle@@3P6AXPAVCWebView@wke@@PAUHWND__@@@ZA
_BSS	SEGMENT
?wkeSetHandle@@3P6AXPAVCWebView@wke@@PAUHWND__@@@ZA DD 01H DUP (?) ; wkeSetHandle
_BSS	ENDS
;	COMDAT ?jsTrue@@3P6A_JXZA
_BSS	SEGMENT
?jsTrue@@3P6A_JXZA DD 01H DUP (?)			; jsTrue
_BSS	ENDS
;	COMDAT ?mbOnLoadUrlBegin@@3P6GXHP6GHHPAXPBD0@Z0@ZA
_BSS	SEGMENT
?mbOnLoadUrlBegin@@3P6GXHP6GHHPAXPBD0@Z0@ZA DD 01H DUP (?) ; mbOnLoadUrlBegin
_BSS	ENDS
;	COMDAT ?wkeSetWindowTitleW@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeSetWindowTitleW@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeSetWindowTitleW
_BSS	ENDS
;	COMDAT ?wkePaste@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkePaste@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkePaste
_BSS	ENDS
;	COMDAT ?mbNetOnResponse@@3P6GXHP6GHHPAXPBD0@Z0@ZA
_BSS	SEGMENT
?mbNetOnResponse@@3P6GXHP6GHHPAXPBD0@Z0@ZA DD 01H DUP (?) ; mbNetOnResponse
_BSS	ENDS
;	COMDAT ?mbGetPdfPageData@@3P6GXHP6GXHPAX0I@Z0@ZA
_BSS	SEGMENT
?mbGetPdfPageData@@3P6GXHP6GXHPAX0I@Z0@ZA DD 01H DUP (?) ; mbGetPdfPageData
_BSS	ENDS
;	COMDAT ?mbLoadHtmlWithBaseUrl@@3P6GXHPBD0@ZA
_BSS	SEGMENT
?mbLoadHtmlWithBaseUrl@@3P6GXHPBD0@ZA DD 01H DUP (?)	; mbLoadHtmlWithBaseUrl
_BSS	ENDS
;	COMDAT ?mbSetAutoDrawToHwnd@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetAutoDrawToHwnd@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetAutoDrawToHwnd
_BSS	ENDS
;	COMDAT ?wkeLoadW@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeLoadW@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?)	; wkeLoadW
_BSS	ENDS
;	COMDAT ?mbUtilBase64Decode@@3P6GPBDPBD@ZA
_BSS	SEGMENT
?mbUtilBase64Decode@@3P6GPBDPBD@ZA DD 01H DUP (?)	; mbUtilBase64Decode
_BSS	ENDS
;	COMDAT ?jsToFloat@@3P6AMPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsToFloat@@3P6AMPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsToFloat
_BSS	ENDS
;	COMDAT ?mbRunMessageLoop@@3P6GXXZA
_BSS	SEGMENT
?mbRunMessageLoop@@3P6GXXZA DD 01H DUP (?)		; mbRunMessageLoop
_BSS	ENDS
;	COMDAT ?wkeSetResourceGc@@3P6AXPAVCWebView@wke@@J@ZA
_BSS	SEGMENT
?wkeSetResourceGc@@3P6AXPAVCWebView@wke@@J@ZA DD 01H DUP (?) ; wkeSetResourceGc
_BSS	ENDS
;	COMDAT ?wkeSetString@@3P6AXPAVCString@wke@@PBDI@ZA
_BSS	SEGMENT
?wkeSetString@@3P6AXPAVCString@wke@@PBDI@ZA DD 01H DUP (?) ; wkeSetString
_BSS	ENDS
;	COMDAT ?mbNetGetMIMEType@@3P6GPBDPAX@ZA
_BSS	SEGMENT
?mbNetGetMIMEType@@3P6GPBDPAX@ZA DD 01H DUP (?)		; mbNetGetMIMEType
_BSS	ENDS
;	COMDAT ?wkeIsDirty@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsDirty@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeIsDirty
_BSS	ENDS
;	COMDAT ?mbPopupDialogAndDownload@@3P6G?AW4_mbDownloadOpt@@HPBU_mbDialogOptions@@IPBD11PAXPAU_mbNetJobDataBind@@PAU_mbDownloadBind@@@ZA
_BSS	SEGMENT
?mbPopupDialogAndDownload@@3P6G?AW4_mbDownloadOpt@@HPBU_mbDialogOptions@@IPBD11PAXPAU_mbNetJobDataBind@@PAU_mbDownloadBind@@@ZA DD 01H DUP (?) ; mbPopupDialogAndDownload
_BSS	ENDS
;	COMDAT ?wkeNetGetPostBody@@3P6APAU_wkePostBodyElements@@PAX@ZA
_BSS	SEGMENT
?wkeNetGetPostBody@@3P6APAU_wkePostBodyElements@@PAX@ZA DD 01H DUP (?) ; wkeNetGetPostBody
_BSS	ENDS
;	COMDAT ?mbGetLockedViewDC@@3P6GPAUHDC__@@H@ZA
_BSS	SEGMENT
?mbGetLockedViewDC@@3P6GPAUHDC__@@H@ZA DD 01H DUP (?)	; mbGetLockedViewDC
_BSS	ENDS
;	COMDAT ?WM_ATLGETHOST@ATL@@3IA
_BSS	SEGMENT
?WM_ATLGETHOST@ATL@@3IA DD 01H DUP (?)			; ATL::WM_ATLGETHOST
_BSS	ENDS
;	COMDAT ?WM_ATLGETCONTROL@ATL@@3IA
_BSS	SEGMENT
?WM_ATLGETCONTROL@ATL@@3IA DD 01H DUP (?)		; ATL::WM_ATLGETCONTROL
_BSS	ENDS
;	COMDAT ?_pAtlAutoThreadModule@ATL@@3PAUIAtlAutoThreadModule@1@A
_BSS	SEGMENT
?_pAtlAutoThreadModule@ATL@@3PAUIAtlAutoThreadModule@1@A DD 01H DUP (?) ; ATL::_pAtlAutoThreadModule
_BSS	ENDS
;	COMDAT ?_pAtlModule@ATL@@3PAVCAtlModule@1@A
_BSS	SEGMENT
?_pAtlModule@ATL@@3PAVCAtlModule@1@A DD 01H DUP (?)	; ATL::_pAtlModule
_BSS	ENDS
;	COMDAT ?_AtlRegisterPerUser@ATL@@3_NA
_BSS	SEGMENT
?_AtlRegisterPerUser@ATL@@3_NA DB 01H DUP (?)		; ATL::_AtlRegisterPerUser
_BSS	ENDS
;	COMDAT ?m_bInitFailed@CAtlBaseModule@ATL@@2_NA
_BSS	SEGMENT
?m_bInitFailed@CAtlBaseModule@ATL@@2_NA DB 01H DUP (?)	; ATL::CAtlBaseModule::m_bInitFailed
_BSS	ENDS
;	COMDAT ?_pPerfRegFunc@ATL@@3P6AJPAUHINSTANCE__@@@ZA
_BSS	SEGMENT
?_pPerfRegFunc@ATL@@3P6AJPAUHINSTANCE__@@@ZA DD 01H DUP (?) ; ATL::_pPerfRegFunc
_BSS	ENDS
;	COMDAT ?_pModule@ATL@@3PAVCComModule@1@A
_BSS	SEGMENT
?_pModule@ATL@@3PAVCComModule@1@A DD 01H DUP (?)	; ATL::_pModule
_BSS	ENDS
;	COMDAT ?_pPerfUnRegFunc@ATL@@3P6AJXZA
_BSS	SEGMENT
?_pPerfUnRegFunc@ATL@@3P6AJXZA DD 01H DUP (?)		; ATL::_pPerfUnRegFunc
_BSS	ENDS
;	COMDAT ?wkeSetMediaPlayerFactory@@3P6AXPAVCWebView@wke@@P6APAVWkeMediaPlayer@2@0PAVWkeMediaPlayerClient@2@PAX2@ZP6A_NPBD@Z@ZA
_BSS	SEGMENT
?wkeSetMediaPlayerFactory@@3P6AXPAVCWebView@wke@@P6APAVWkeMediaPlayer@2@0PAVWkeMediaPlayerClient@2@PAX2@ZP6A_NPBD@Z@ZA DD 01H DUP (?) ; wkeSetMediaPlayerFactory
_BSS	ENDS
;	COMDAT ?wkeIsTransparent@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsTransparent@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsTransparent
_BSS	ENDS
;	COMDAT ?mbSetDragEnable@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetDragEnable@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetDragEnable
_BSS	ENDS
;	COMDAT ?wkeSetDragFiles@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1QAPAVCString@2@H@ZA
_BSS	SEGMENT
?wkeSetDragFiles@@3P6AXPAVCWebView@wke@@PBUtagPOINT@@1QAPAVCString@2@H@ZA DD 01H DUP (?) ; wkeSetDragFiles
_BSS	ENDS
;	COMDAT ?mbGetGlobalExecByFrame@@3P6GPAXHPAX@ZA
_BSS	SEGMENT
?mbGetGlobalExecByFrame@@3P6GPAXHPAX@ZA DD 01H DUP (?)	; mbGetGlobalExecByFrame
_BSS	ENDS
;	COMDAT ?mbCallUiThreadAsync@@3P6GXP6GXPAX0@Z00@ZA
_BSS	SEGMENT
?mbCallUiThreadAsync@@3P6GXP6GXPAX0@Z00@ZA DD 01H DUP (?) ; mbCallUiThreadAsync
_BSS	ENDS
;	COMDAT ?wkeKillFocus@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeKillFocus@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeKillFocus
_BSS	ENDS
;	COMDAT ?wkeFireWindowsMessage@@3P6A_NPAVCWebView@wke@@PAUHWND__@@IIJPAJ@ZA
_BSS	SEGMENT
?wkeFireWindowsMessage@@3P6A_NPAVCWebView@wke@@PAUHWND__@@IIJPAJ@ZA DD 01H DUP (?) ; wkeFireWindowsMessage
_BSS	ENDS
;	COMDAT ?wkeUtilSetUiCallback@@3P6AXP6AHPAUHWND__@@P6AX0PAX@Z1@Z@ZA
_BSS	SEGMENT
?wkeUtilSetUiCallback@@3P6AXP6AHPAUHWND__@@P6AX0PAX@Z1@Z@ZA DD 01H DUP (?) ; wkeUtilSetUiCallback
_BSS	ENDS
;	COMDAT ?jsIsTrue@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsTrue@@3P6A_N_J@ZA DD 01H DUP (?)			; jsIsTrue
_BSS	ENDS
;	COMDAT ?wkeNetGetUrlByJob@@3P6APBDPAX@ZA
_BSS	SEGMENT
?wkeNetGetUrlByJob@@3P6APBDPAX@ZA DD 01H DUP (?)	; wkeNetGetUrlByJob
_BSS	ENDS
;	COMDAT ?mbSetViewProxy@@3P6GXHPBUmbProxy@@@ZA
_BSS	SEGMENT
?mbSetViewProxy@@3P6GXHPBUmbProxy@@@ZA DD 01H DUP (?)	; mbSetViewProxy
_BSS	ENDS
;	COMDAT ?wkeEnableWindow@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeEnableWindow@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeEnableWindow
_BSS	ENDS
;	COMDAT ?wkeSetTransparent@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetTransparent@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetTransparent
_BSS	ENDS
;	COMDAT ?wkeKeyPress@@3P6A_NPAVCWebView@wke@@II_N@ZA
_BSS	SEGMENT
?wkeKeyPress@@3P6A_NPAVCWebView@wke@@II_N@ZA DD 01H DUP (?) ; wkeKeyPress
_BSS	ENDS
;	COMDAT ?wkeGetClientHandler@@3P6APBU_wkeClientHandler@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetClientHandler@@3P6APBU_wkeClientHandler@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetClientHandler
_BSS	ENDS
;	COMDAT ?mbNetContinueJob@@3P6GXPAX@ZA
_BSS	SEGMENT
?mbNetContinueJob@@3P6GXPAX@ZA DD 01H DUP (?)		; mbNetContinueJob
_BSS	ENDS
;	COMDAT ?mbSetTransparent@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetTransparent@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetTransparent
_BSS	ENDS
;	COMDAT ?mbOnTitleChanged@@3P6GXHP6GXHPAXPBD@Z0@ZA
_BSS	SEGMENT
?mbOnTitleChanged@@3P6GXHP6GXHPAXPBD@Z0@ZA DD 01H DUP (?) ; mbOnTitleChanged
_BSS	ENDS
;	COMDAT ?mbPluginListBuilderAddPlugin@@3P6GXPAXPBD11@ZA
_BSS	SEGMENT
?mbPluginListBuilderAddPlugin@@3P6GXPAXPBD11@ZA DD 01H DUP (?) ; mbPluginListBuilderAddPlugin
_BSS	ENDS
;	COMDAT ?wkePostURLW@@3P6AXPAVCWebView@wke@@PB_WPBDH@ZA
_BSS	SEGMENT
?wkePostURLW@@3P6AXPAVCWebView@wke@@PB_WPBDH@ZA DD 01H DUP (?) ; wkePostURLW
_BSS	ENDS
;	COMDAT ?wkeGetString@@3P6APBDQAVCString@wke@@@ZA
_BSS	SEGMENT
?wkeGetString@@3P6APBDQAVCString@wke@@@ZA DD 01H DUP (?) ; wkeGetString
_BSS	ENDS
;	COMDAT ?mbKillFocus@@3P6GXH@ZA
_BSS	SEGMENT
?mbKillFocus@@3P6GXH@ZA DD 01H DUP (?)			; mbKillFocus
_BSS	ENDS
;	COMDAT ?wkeSetCookie@@3P6AXPAVCWebView@wke@@PBD1@ZA
_BSS	SEGMENT
?wkeSetCookie@@3P6AXPAVCWebView@wke@@PBD1@ZA DD 01H DUP (?) ; wkeSetCookie
_BSS	ENDS
;	COMDAT ?mbSetHeadlessEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetHeadlessEnabled@@3P6GXHH@ZA DD 01H DUP (?)	; mbSetHeadlessEnabled
_BSS	ENDS
;	COMDAT ?mbSetFocus@@3P6GXH@ZA
_BSS	SEGMENT
?mbSetFocus@@3P6GXH@ZA DD 01H DUP (?)			; mbSetFocus
_BSS	ENDS
;	COMDAT ?wkeRunJSW@@3P6A_JPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeRunJSW@@3P6A_JPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeRunJSW
_BSS	ENDS
;	COMDAT ?wkeDestroyWebView@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeDestroyWebView@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeDestroyWebView
_BSS	ENDS
;	COMDAT ?mbNetSetHTTPHeaderField@@3P6GXPAXPB_W1H@ZA
_BSS	SEGMENT
?mbNetSetHTTPHeaderField@@3P6GXPAXPB_W1H@ZA DD 01H DUP (?) ; mbNetSetHTTPHeaderField
_BSS	ENDS
;	COMDAT ?jsObject@@3P6A_JPAUJsExecStateInfo@@PAUtagjsData@@@ZA
_BSS	SEGMENT
?jsObject@@3P6A_JPAUJsExecStateInfo@@PAUtagjsData@@@ZA DD 01H DUP (?) ; jsObject
_BSS	ENDS
;	COMDAT ?mbOnNavigationSync@@********************************@@PBD@Z0@ZA
_BSS	SEGMENT
?mbOnNavigationSync@@********************************@@PBD@Z0@ZA DD 01H DUP (?) ; mbOnNavigationSync
_BSS	ENDS
;	COMDAT ?mbEditorPaste@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorPaste@@3P6GXH@ZA DD 01H DUP (?)		; mbEditorPaste
_BSS	ENDS
;	COMDAT ?wkeIsLoadComplete@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsLoadComplete@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsLoadComplete
_BSS	ENDS
;	COMDAT ?wkeSetCookieJarPath@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeSetCookieJarPath@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeSetCookieJarPath
_BSS	ENDS
;	COMDAT ?wkeJsBindGetter@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2@ZA
_BSS	SEGMENT
?wkeJsBindGetter@@3P6AXPBDP6A_JPAUJsExecStateInfo@@PAX@Z2@ZA DD 01H DUP (?) ; wkeJsBindGetter
_BSS	ENDS
;	COMDAT ?wkeGetUserAgent@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetUserAgent@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetUserAgent
_BSS	ENDS
;	COMDAT ?wkeUtilSerializeToMHTML@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeUtilSerializeToMHTML@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeUtilSerializeToMHTML
_BSS	ENDS
;	COMDAT ?wkeLoadFileW@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeLoadFileW@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeLoadFileW
_BSS	ENDS
;	COMDAT ?jsNull@@3P6A_JXZA
_BSS	SEGMENT
?jsNull@@3P6A_JXZA DD 01H DUP (?)			; jsNull
_BSS	ENDS
;	COMDAT ?mbPostURL@@3P6GXHPBD0H@ZA
_BSS	SEGMENT
?mbPostURL@@3P6GXHPBD0H@ZA DD 01H DUP (?)		; mbPostURL
_BSS	ENDS
;	COMDAT ?mbSetWindowTitleW@@3P6GXHPB_W@ZA
_BSS	SEGMENT
?mbSetWindowTitleW@@3P6GXHPB_W@ZA DD 01H DUP (?)	; mbSetWindowTitleW
_BSS	ENDS
;	COMDAT ?wkeGetSource@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetSource@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetSource
_BSS	ENDS
;	COMDAT ?wkeSetWindowTitle@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeSetWindowTitle@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeSetWindowTitle
_BSS	ENDS
;	COMDAT ?mbUtilBase64Encode@@3P6GPBDPBD@ZA
_BSS	SEGMENT
?mbUtilBase64Encode@@3P6GPBDPBD@ZA DD 01H DUP (?)	; mbUtilBase64Encode
_BSS	ENDS
;	COMDAT ?wkeNetHookRequest@@3P6AXPAX@ZA
_BSS	SEGMENT
?wkeNetHookRequest@@3P6AXPAX@ZA DD 01H DUP (?)		; wkeNetHookRequest
_BSS	ENDS
;	COMDAT ?mbCreateWebWindow@@3P6GHW4_mbWindowType@@PAUHWND__@@HHHH@ZA
_BSS	SEGMENT
?mbCreateWebWindow@@3P6GHW4_mbWindowType@@PAUHWND__@@HHHH@ZA DD 01H DUP (?) ; mbCreateWebWindow
_BSS	ENDS
;	COMDAT ?mbSetViewSettings@@3P6GXHPBU_mbViewSettings@@@ZA
_BSS	SEGMENT
?mbSetViewSettings@@3P6GXHPBU_mbViewSettings@@@ZA DD 01H DUP (?) ; mbSetViewSettings
_BSS	ENDS
;	COMDAT ?wkeOnPaintBitUpdated@@3P6AXPAVCWebView@wke@@P6AX0PAXPBXPBU_wkeRect@@HH@Z1@ZA
_BSS	SEGMENT
?wkeOnPaintBitUpdated@@3P6AXPAVCWebView@wke@@P6AX0PAXPBXPBU_wkeRect@@HH@Z1@ZA DD 01H DUP (?) ; wkeOnPaintBitUpdated
_BSS	ENDS
;	COMDAT ?mbSetNavigationToNewWindowEnable@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetNavigationToNewWindowEnable@@3P6GXHH@ZA DD 01H DUP (?) ; mbSetNavigationToNewWindowEnable
_BSS	ENDS
;	COMDAT ?wkeIsLoadingCompleted@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeIsLoadingCompleted@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeIsLoadingCompleted
_BSS	ENDS
;	COMDAT ?wkeSetEditable@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetEditable@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetEditable
_BSS	ENDS
;	COMDAT ?mbOnWillReleaseScriptContext@@3P6GXHP6GXHPAX00H@Z0@ZA
_BSS	SEGMENT
?mbOnWillReleaseScriptContext@@3P6GXHP6GXHPAX00H@Z0@ZA DD 01H DUP (?) ; mbOnWillReleaseScriptContext
_BSS	ENDS
;	COMDAT ?wkeOnContextMenuItemClick@@3P6AXPAVCWebView@wke@@P6A_N0PAXW4_wkeOnContextMenuItemClickType@@W4_wkeOnContextMenuItemClickStep@@11@Z1@ZA
_BSS	SEGMENT
?wkeOnContextMenuItemClick@@3P6AXPAVCWebView@wke@@P6A_N0PAXW4_wkeOnContextMenuItemClickType@@W4_wkeOnContextMenuItemClickStep@@11@Z1@ZA DD 01H DUP (?) ; wkeOnContextMenuItemClick
_BSS	ENDS
;	COMDAT ?wkeGetHostHWND@@3P6APAUHWND__@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetHostHWND@@3P6APAUHWND__@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetHostHWND
_BSS	ENDS
;	COMDAT ?mbOnPaintUpdated@@3P6GXHP6GXHPAXQAUHDC__@@HHHH@Z0@ZA
_BSS	SEGMENT
?mbOnPaintUpdated@@3P6GXHP6GXHPAXQAUHDC__@@HHHH@Z0@ZA DD 01H DUP (?) ; mbOnPaintUpdated
_BSS	ENDS
;	COMDAT ?jsDouble@@3P6A_JN@ZA
_BSS	SEGMENT
?jsDouble@@3P6A_JN@ZA DD 01H DUP (?)			; jsDouble
_BSS	ENDS
;	COMDAT ?wkeSetProxy@@3P6AXPBU_wkeProxy@@@ZA
_BSS	SEGMENT
?wkeSetProxy@@3P6AXPBU_wkeProxy@@@ZA DD 01H DUP (?)	; wkeSetProxy
_BSS	ENDS
;	COMDAT ?mbSetNodeJsEnable@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetNodeJsEnable@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetNodeJsEnable
_BSS	ENDS
;	COMDAT ?mbGetSource@@3P6GXHP6GXHPAXPBD@Z0@ZA
_BSS	SEGMENT
?mbGetSource@@3P6GXHP6GXHPAXPBD@Z0@ZA DD 01H DUP (?)	; mbGetSource
_BSS	ENDS
;	COMDAT ?mbMoveToCenter@@3P6GXH@ZA
_BSS	SEGMENT
?mbMoveToCenter@@3P6GXH@ZA DD 01H DUP (?)		; mbMoveToCenter
_BSS	ENDS
;	COMDAT ?wkeLoadURLW@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeLoadURLW@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeLoadURLW
_BSS	ENDS
;	COMDAT ?mbInsertCSSByFrame@@3P6GXHPAXPBD@ZA
_BSS	SEGMENT
?mbInsertCSSByFrame@@3P6GXHPAXPBD@ZA DD 01H DUP (?)	; mbInsertCSSByFrame
_BSS	ENDS
;	COMDAT ?mbSetUserKeyValue@@3P6GXHPBDPAX@ZA
_BSS	SEGMENT
?mbSetUserKeyValue@@3P6GXHPBDPAX@ZA DD 01H DUP (?)	; mbSetUserKeyValue
_BSS	ENDS
;	COMDAT ?mbCreateWebView@@3P6GHXZA
_BSS	SEGMENT
?mbCreateWebView@@3P6GHXZA DD 01H DUP (?)		; mbCreateWebView
_BSS	ENDS
;	COMDAT ?mbEditorCopy@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorCopy@@3P6GXH@ZA DD 01H DUP (?)			; mbEditorCopy
_BSS	ENDS
;	COMDAT ?wkeSetUIThreadCallback@@3P6AXPAVCWebView@wke@@P6AX0P6AX0PAX@Z1@Z1@ZA
_BSS	SEGMENT
?wkeSetUIThreadCallback@@3P6AXPAVCWebView@wke@@P6AX0P6AX0PAX@Z1@Z1@ZA DD 01H DUP (?) ; wkeSetUIThreadCallback
_BSS	ENDS
;	COMDAT ?wkeOnWindowDestroy@@3P6AXPAVCWebView@wke@@P6AX0PAX@Z1@ZA
_BSS	SEGMENT
?wkeOnWindowDestroy@@3P6AXPAVCWebView@wke@@P6AX0PAX@Z1@ZA DD 01H DUP (?) ; wkeOnWindowDestroy
_BSS	ENDS
;	COMDAT ?wkeSetCursorInfoType@@3P6AXPAVCWebView@wke@@H@ZA
_BSS	SEGMENT
?wkeSetCursorInfoType@@3P6AXPAVCWebView@wke@@H@ZA DD 01H DUP (?) ; wkeSetCursorInfoType
_BSS	ENDS
;	COMDAT ?wkeGC@@3P6AXPAVCWebView@wke@@J@ZA
_BSS	SEGMENT
?wkeGC@@3P6AXPAVCWebView@wke@@J@ZA DD 01H DUP (?)	; wkeGC
_BSS	ENDS
;	COMDAT ?wkeOnDraggableRegionsChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXPBUwkeDraggableRegion@@H@Z1@ZA
_BSS	SEGMENT
?wkeOnDraggableRegionsChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXPBUwkeDraggableRegion@@H@Z1@ZA DD 01H DUP (?) ; wkeOnDraggableRegionsChanged
_BSS	ENDS
;	COMDAT ?wkeLoadHtmlWithBaseUrl@@3P6AXPAVCWebView@wke@@PBD1@ZA
_BSS	SEGMENT
?wkeLoadHtmlWithBaseUrl@@3P6AXPAVCWebView@wke@@PBD1@ZA DD 01H DUP (?) ; wkeLoadHtmlWithBaseUrl
_BSS	ENDS
;	COMDAT ?wkeUtilPrintToPdf@@3P6APBU_wkePdfDatas@@PAVCWebView@wke@@PAXPBU_wkePrintSettings@@@ZA
_BSS	SEGMENT
?wkeUtilPrintToPdf@@3P6APBU_wkePdfDatas@@PAVCWebView@wke@@PAXPBU_wkePrintSettings@@@ZA DD 01H DUP (?) ; wkeUtilPrintToPdf
_BSS	ENDS
;	COMDAT ?wkeGetName@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetName@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeGetName
_BSS	ENDS
;	COMDAT ?wkeUpdate@@3P6AXXZA
_BSS	SEGMENT
?wkeUpdate@@3P6AXXZA DD 01H DUP (?)			; wkeUpdate
_BSS	ENDS
;	COMDAT ?wkeGetZoomFactor@@3P6AMPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetZoomFactor@@3P6AMPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetZoomFactor
_BSS	ENDS
;	COMDAT ?wkeMediaVolume@@3P6AMPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeMediaVolume@@3P6AMPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeMediaVolume
_BSS	ENDS
;	COMDAT ?wkeSetMediaVolume@@3P6AXPAVCWebView@wke@@M@ZA
_BSS	SEGMENT
?wkeSetMediaVolume@@3P6AXPAVCWebView@wke@@M@ZA DD 01H DUP (?) ; wkeSetMediaVolume
_BSS	ENDS
;	COMDAT ?jsGC@@3P6AXXZA
_BSS	SEGMENT
?jsGC@@3P6AXXZA DD 01H DUP (?)				; jsGC
_BSS	ENDS
;	COMDAT ?mbNetGetRequestMethod@@3P6G?AW4_mbRequestType@@PAX@ZA
_BSS	SEGMENT
?mbNetGetRequestMethod@@3P6G?AW4_mbRequestType@@PAX@ZA DD 01H DUP (?) ; mbNetGetRequestMethod
_BSS	ENDS
;	COMDAT ?wkeSetUserAgent@@3P6AXPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeSetUserAgent@@3P6AXPAVCWebView@wke@@PBD@ZA DD 01H DUP (?) ; wkeSetUserAgent
_BSS	ENDS
;	COMDAT ?wkeSetDebugConfig@@3P6AXPAVCWebView@wke@@PBD1PBX2@ZA
_BSS	SEGMENT
?wkeSetDebugConfig@@3P6AXPAVCWebView@wke@@PBD1PBX2@ZA DD 01H DUP (?) ; wkeSetDebugConfig
_BSS	ENDS
;	COMDAT ?mbNetStartUrlRequest@@3P6GHHPAUmbWebUrlRequest@@PAXPBU_mbUrlRequestCallbacks@@@ZA
_BSS	SEGMENT
?mbNetStartUrlRequest@@3P6GHHPAUmbWebUrlRequest@@PAXPBU_mbUrlRequestCallbacks@@@ZA DD 01H DUP (?) ; mbNetStartUrlRequest
_BSS	ENDS
;	COMDAT ?mbResize@@3P6GXHHH@ZA
_BSS	SEGMENT
?mbResize@@3P6GXHHH@ZA DD 01H DUP (?)			; mbResize
_BSS	ENDS
;	COMDAT ?mbNetFreePostBodyElements@@3P6GXPAU_mbPostBodyElements@@@ZA
_BSS	SEGMENT
?mbNetFreePostBodyElements@@3P6GXPAU_mbPostBodyElements@@@ZA DD 01H DUP (?) ; mbNetFreePostBodyElements
_BSS	ENDS
;	COMDAT ?wkeOnMouseOverUrlChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA
_BSS	SEGMENT
?wkeOnMouseOverUrlChanged@@3P6AXPAVCWebView@wke@@P6AX0PAXQAVCString@2@@Z1@ZA DD 01H DUP (?) ; wkeOnMouseOverUrlChanged
_BSS	ENDS
;	COMDAT ?wkeGoToIndex@@3P6AXPAVCWebView@wke@@H@ZA
_BSS	SEGMENT
?wkeGoToIndex@@3P6AXPAVCWebView@wke@@H@ZA DD 01H DUP (?) ; wkeGoToIndex
_BSS	ENDS
;	COMDAT ?wkeToStringW@@3P6APB_WQAVCString@wke@@@ZA
_BSS	SEGMENT
?wkeToStringW@@3P6APB_WQAVCString@wke@@@ZA DD 01H DUP (?) ; wkeToStringW
_BSS	ENDS
;	COMDAT ?wkeNetCreateWebUrlRequest@@3P6APAUwkeWebUrlRequest@@PBD00@ZA
_BSS	SEGMENT
?wkeNetCreateWebUrlRequest@@3P6APAUwkeWebUrlRequest@@PBD00@ZA DD 01H DUP (?) ; wkeNetCreateWebUrlRequest
_BSS	ENDS
;	COMDAT ?mbSetMemoryCacheEnable@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetMemoryCacheEnable@@3P6GXHH@ZA DD 01H DUP (?)	; mbSetMemoryCacheEnable
_BSS	ENDS
;	COMDAT ?wkeGetCookie@@3P6APBDPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetCookie@@3P6APBDPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetCookie
_BSS	ENDS
;	COMDAT ?jsCall@@3P6A_JPAUJsExecStateInfo@@_J1PA_JH@ZA
_BSS	SEGMENT
?jsCall@@3P6A_JPAUJsExecStateInfo@@_J1PA_JH@ZA DD 01H DUP (?) ; jsCall
_BSS	ENDS
;	COMDAT ?mbGetContentHeight@@3P6GHH@ZA
_BSS	SEGMENT
?mbGetContentHeight@@3P6GHH@ZA DD 01H DUP (?)		; mbGetContentHeight
_BSS	ENDS
;	COMDAT ?mbJsToString@@3P6GPBDPAX_J@ZA
_BSS	SEGMENT
?mbJsToString@@3P6GPBDPAX_J@ZA DD 01H DUP (?)		; mbJsToString
_BSS	ENDS
;	COMDAT ?mbNetGetRawHttpHeadInBlinkThread@@3P6GPBU_mbSlist@@PAX@ZA
_BSS	SEGMENT
?mbNetGetRawHttpHeadInBlinkThread@@3P6GPBU_mbSlist@@PAX@ZA DD 01H DUP (?) ; mbNetGetRawHttpHeadInBlinkThread
_BSS	ENDS
;	COMDAT ?mbSetProxy@@3P6GXHPBUmbProxy@@@ZA
_BSS	SEGMENT
?mbSetProxy@@3P6GXHPBUmbProxy@@@ZA DD 01H DUP (?)	; mbSetProxy
_BSS	ENDS
;	COMDAT ?wkeOnDidCreateScriptContext@@3P6AXPAVCWebView@wke@@P6AX0PAX11HH@Z1@ZA
_BSS	SEGMENT
?wkeOnDidCreateScriptContext@@3P6AXPAVCWebView@wke@@P6AX0PAX11HH@Z1@ZA DD 01H DUP (?) ; wkeOnDidCreateScriptContext
_BSS	ENDS
;	COMDAT ?mbEditorUnSelect@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorUnSelect@@3P6GXH@ZA DD 01H DUP (?)		; mbEditorUnSelect
_BSS	ENDS
;	COMDAT ?wkeOnOtherLoad@@3P6AXPAVCWebView@wke@@P6AX0PAXW4_wkeOtherLoadType@@PAU_wkeTempCallbackInfo@@@Z1@ZA
_BSS	SEGMENT
?wkeOnOtherLoad@@3P6AXPAVCWebView@wke@@P6AX0PAXW4_wkeOtherLoadType@@PAU_wkeTempCallbackInfo@@@Z1@ZA DD 01H DUP (?) ; wkeOnOtherLoad
_BSS	ENDS
;	COMDAT ?wkeFireKeyPressEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA
_BSS	SEGMENT
?wkeFireKeyPressEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA DD 01H DUP (?) ; wkeFireKeyPressEvent
_BSS	ENDS
;	COMDAT ?mbPluginListBuilderAddFileExtensionToLastMediaType@@3P6GXPAXPBD@ZA
_BSS	SEGMENT
?mbPluginListBuilderAddFileExtensionToLastMediaType@@3P6GXPAXPBD@ZA DD 01H DUP (?) ; mbPluginListBuilderAddFileExtensionToLastMediaType
_BSS	ENDS
;	COMDAT ?mbRunJs@@3P6GXHPAXPBDHP6GXH00_J@Z00@ZA
_BSS	SEGMENT
?mbRunJs@@3P6GXHPAXPBDHP6GXH00_J@Z00@ZA DD 01H DUP (?)	; mbRunJs
_BSS	ENDS
;	COMDAT ?mbOnNavigation@@********************************@@PBD@Z0@ZA
_BSS	SEGMENT
?mbOnNavigation@@********************************@@PBD@Z0@ZA DD 01H DUP (?) ; mbOnNavigation
_BSS	ENDS
;	COMDAT ?wkeNetContinueJob@@3P6AXPAX@ZA
_BSS	SEGMENT
?wkeNetContinueJob@@3P6AXPAX@ZA DD 01H DUP (?)		; wkeNetContinueJob
_BSS	ENDS
;	COMDAT ?jsGetArrayBuffer@@3P6APAU_wkeMemBuf@@PAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsGetArrayBuffer@@3P6APAU_wkeMemBuf@@PAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsGetArrayBuffer
_BSS	ENDS
;	COMDAT ?wkeSetDragDropEnable@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetDragDropEnable@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetDragDropEnable
_BSS	ENDS
;	COMDAT ?mbOnPaintBitUpdated@@3P6GXHP6GXHPAXPBXPBU_mbRect@@HH@Z0@ZA
_BSS	SEGMENT
?mbOnPaintBitUpdated@@3P6GXHP6GXHPAXPBXPBU_mbRect@@HH@Z0@ZA DD 01H DUP (?) ; mbOnPaintBitUpdated
_BSS	ENDS
;	COMDAT ?mbOnNodeCreateProcess@@3P6GXHP6GXHPAXPB_W1PAU_STARTUPINFOW@@@Z0@ZA
_BSS	SEGMENT
?mbOnNodeCreateProcess@@3P6GXHP6GXHPAXPB_W1PAU_STARTUPINFOW@@@Z0@ZA DD 01H DUP (?) ; mbOnNodeCreateProcess
_BSS	ENDS
;	COMDAT ?wkeEditorUnSelect@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeEditorUnSelect@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeEditorUnSelect
_BSS	ENDS
;	COMDAT ?wkeWake@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeWake@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeWake
_BSS	ENDS
;	COMDAT ?strMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4V23@A
_BSS	SEGMENT
?strMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4V23@A DB 01cH DUP (?) ; `ATL::CAtlStringMgr::GetInstance'::`2'::strMgr
_BSS	ENDS
;	COMDAT ?wkeSetStringW@@3P6AXPAVCString@wke@@PB_WI@ZA
_BSS	SEGMENT
?wkeSetStringW@@3P6AXPAVCString@wke@@PB_WI@ZA DD 01H DUP (?) ; wkeSetStringW
_BSS	ENDS
;	COMDAT ?wkeDragTargetDragLeave@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeDragTargetDragLeave@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeDragTargetDragLeave
_BSS	ENDS
;	COMDAT ?mbNetChangeRequestUrl@@3P6GXPAXPBD@ZA
_BSS	SEGMENT
?mbNetChangeRequestUrl@@3P6GXPAXPBD@ZA DD 01H DUP (?)	; mbNetChangeRequestUrl
_BSS	ENDS
;	COMDAT ?wkeOnPaintUpdated@@3P6AXPAVCWebView@wke@@P6AX0PAXQAUHDC__@@HHHH@Z1@ZA
_BSS	SEGMENT
?wkeOnPaintUpdated@@3P6AXPAVCWebView@wke@@P6AX0PAXQAUHDC__@@HHHH@Z1@ZA DD 01H DUP (?) ; wkeOnPaintUpdated
_BSS	ENDS
;	COMDAT ?wkeSetLocalStorageFullPath@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeSetLocalStorageFullPath@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeSetLocalStorageFullPath
_BSS	ENDS
;	COMDAT ?wkeNetGetHttpStatusCode@@3P6AHPAUwkeWebUrlResponse@@@ZA
_BSS	SEGMENT
?wkeNetGetHttpStatusCode@@3P6AHPAUwkeWebUrlResponse@@@ZA DD 01H DUP (?) ; wkeNetGetHttpStatusCode
_BSS	ENDS
;	COMDAT ?wkeOnLoadUrlEnd@@3P6AXPAVCWebView@wke@@P6AX0PAXPBD11H@Z1@ZA
_BSS	SEGMENT
?wkeOnLoadUrlEnd@@3P6AXPAVCWebView@wke@@P6AX0PAXPBD11H@Z1@ZA DD 01H DUP (?) ; wkeOnLoadUrlEnd
_BSS	ENDS
;	COMDAT ?mbGetCaretRect@@3P6GXHPAU_mbRect@@@ZA
_BSS	SEGMENT
?mbGetCaretRect@@3P6GXHPAU_mbRect@@@ZA DD 01H DUP (?)	; mbGetCaretRect
_BSS	ENDS
;	COMDAT ?wkeFireKeyDownEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA
_BSS	SEGMENT
?wkeFireKeyDownEvent@@3P6A_NPAVCWebView@wke@@II_N@ZA DD 01H DUP (?) ; wkeFireKeyDownEvent
_BSS	ENDS
;	COMDAT ?jsIsJsValueValid@@3P6A_NPAUJsExecStateInfo@@_J@ZA
_BSS	SEGMENT
?jsIsJsValueValid@@3P6A_NPAUJsExecStateInfo@@_J@ZA DD 01H DUP (?) ; jsIsJsValueValid
_BSS	ENDS
;	COMDAT ?wkeRunJS@@3P6A_JPAVCWebView@wke@@PBD@ZA
_BSS	SEGMENT
?wkeRunJS@@3P6A_JPAVCWebView@wke@@PBD@ZA DD 01H DUP (?)	; wkeRunJS
_BSS	ENDS
;	COMDAT ?wkeGetWebViewForCurrentContext@@3P6APAVCWebView@wke@@XZA
_BSS	SEGMENT
?wkeGetWebViewForCurrentContext@@3P6APAVCWebView@wke@@XZA DD 01H DUP (?) ; wkeGetWebViewForCurrentContext
_BSS	ENDS
;	COMDAT ?mbOnDocumentReady@@3P6GXHP6GXHPAX0@Z0@ZA
_BSS	SEGMENT
?mbOnDocumentReady@@3P6GXHP6GXHPAX0@Z0@ZA DD 01H DUP (?) ; mbOnDocumentReady
_BSS	ENDS
;	COMDAT ?wkeCanGoBack@@3P6A_NPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeCanGoBack@@3P6A_NPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeCanGoBack
_BSS	ENDS
;	COMDAT ?mbGetWebViewForCurrentContext@@3P6GHXZA
_BSS	SEGMENT
?mbGetWebViewForCurrentContext@@3P6GHXZA DD 01H DUP (?)	; mbGetWebViewForCurrentContext
_BSS	ENDS
;	COMDAT ?mbNetSetMIMEType@@3P6GXPAXPBD@ZA
_BSS	SEGMENT
?mbNetSetMIMEType@@3P6GXPAXPBD@ZA DD 01H DUP (?)	; mbNetSetMIMEType
_BSS	ENDS
;	COMDAT ?wkeSetCookieJarFullPath@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeSetCookieJarFullPath@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeSetCookieJarFullPath
_BSS	ENDS
;	COMDAT ?wkeCreateWebCustomWindow@@3P6APAVCWebView@wke@@PBU_wkeWindowCreateInfo@@@ZA
_BSS	SEGMENT
?wkeCreateWebCustomWindow@@3P6APAVCWebView@wke@@PBU_wkeWindowCreateInfo@@@ZA DD 01H DUP (?) ; wkeCreateWebCustomWindow
_BSS	ENDS
;	COMDAT ?wkeOnURLChanged2@@3P6AXPAVCWebView@wke@@P6AX0PAX1QAVCString@2@@Z1@ZA
_BSS	SEGMENT
?wkeOnURLChanged2@@3P6AXPAVCWebView@wke@@P6AX0PAX1QAVCString@2@@Z1@ZA DD 01H DUP (?) ; wkeOnURLChanged2
_BSS	ENDS
;	COMDAT ?mbNetHoldJobToAsynCommit@@3P6GHPAX@ZA
_BSS	SEGMENT
?mbNetHoldJobToAsynCommit@@3P6GHPAX@ZA DD 01H DUP (?)	; mbNetHoldJobToAsynCommit
_BSS	ENDS
;	COMDAT ?mbGoToIndex@@3P6GXHH@ZA
_BSS	SEGMENT
?mbGoToIndex@@3P6GXHH@ZA DD 01H DUP (?)			; mbGoToIndex
_BSS	ENDS
;	COMDAT ?wkeMouseWheel@@3P6A_NPAVCWebView@wke@@HHHI@ZA
_BSS	SEGMENT
?wkeMouseWheel@@3P6A_NPAVCWebView@wke@@HHHI@ZA DD 01H DUP (?) ; wkeMouseWheel
_BSS	ENDS
;	COMDAT ?wkeOnDownload2@@3P6AXPAVCWebView@wke@@P6A?AW4_wkeDownloadOpt@@0PAXIPBD221PAU_wkeNetJobDataBind@@@Z1@ZA
_BSS	SEGMENT
?wkeOnDownload2@@3P6AXPAVCWebView@wke@@P6A?AW4_wkeDownloadOpt@@0PAXIPBD221PAU_wkeNetJobDataBind@@@Z1@ZA DD 01H DUP (?) ; wkeOnDownload2
_BSS	ENDS
;	COMDAT ?jsGetCallstack@@3P6APBDPAUJsExecStateInfo@@@ZA
_BSS	SEGMENT
?jsGetCallstack@@3P6APBDPAUJsExecStateInfo@@@ZA DD 01H DUP (?) ; jsGetCallstack
_BSS	ENDS
;	COMDAT ?wkeSetUserAgentW@@3P6AXPAVCWebView@wke@@PB_W@ZA
_BSS	SEGMENT
?wkeSetUserAgentW@@3P6AXPAVCWebView@wke@@PB_W@ZA DD 01H DUP (?) ; wkeSetUserAgentW
_BSS	ENDS
;	COMDAT ?wkeConfigure@@3P6AXPBU_wkeSettings@@@ZA
_BSS	SEGMENT
?wkeConfigure@@3P6AXPBU_wkeSettings@@@ZA DD 01H DUP (?)	; wkeConfigure
_BSS	ENDS
;	COMDAT ?mbOnAlertBox@@3P6GXHP6GXHPAXPBD@Z0@ZA
_BSS	SEGMENT
?mbOnAlertBox@@3P6GXHP6GXHPAXPBD@Z0@ZA DD 01H DUP (?)	; mbOnAlertBox
_BSS	ENDS
;	COMDAT ?mbEditorDelete@@3P6GXH@ZA
_BSS	SEGMENT
?mbEditorDelete@@3P6GXH@ZA DD 01H DUP (?)		; mbEditorDelete
_BSS	ENDS
;	COMDAT ?wkeTitleW@@3P6APB_WPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeTitleW@@3P6APB_WPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeTitleW
_BSS	ENDS
;	COMDAT ?mbDeleteString@@3P6GXPAUmbString@@@ZA
_BSS	SEGMENT
?mbDeleteString@@3P6GXPAUmbString@@@ZA DD 01H DUP (?)	; mbDeleteString
_BSS	ENDS
;	COMDAT ?mbSetCookieJarFullPath@@3P6GXHPB_W@ZA
_BSS	SEGMENT
?mbSetCookieJarFullPath@@3P6GXHPB_W@ZA DD 01H DUP (?)	; mbSetCookieJarFullPath
_BSS	ENDS
;	COMDAT ?wkeNetCancelWebUrlRequest@@3P6AXH@ZA
_BSS	SEGMENT
?wkeNetCancelWebUrlRequest@@3P6AXH@ZA DD 01H DUP (?)	; wkeNetCancelWebUrlRequest
_BSS	ENDS
;	COMDAT ?mbSetCookieEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetCookieEnabled@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetCookieEnabled
_BSS	ENDS
;	COMDAT ?mbSetInitSettings@@3P6GXPAU_mbSettings@@PBD1@ZA
_BSS	SEGMENT
?mbSetInitSettings@@3P6GXPAU_mbSettings@@PBD1@ZA DD 01H DUP (?) ; mbSetInitSettings
_BSS	ENDS
;	COMDAT ?jsIsFalse@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsFalse@@3P6A_N_J@ZA DD 01H DUP (?)			; jsIsFalse
_BSS	ENDS
;	COMDAT ?mbUtilPrint@@3P6GHHPAXPBU_mbPrintSettings@@@ZA
_BSS	SEGMENT
?mbUtilPrint@@3P6GHHPAXPBU_mbPrintSettings@@@ZA DD 01H DUP (?) ; mbUtilPrint
_BSS	ENDS
;	COMDAT ?mbClearCookie@@3P6GXH@ZA
_BSS	SEGMENT
?mbClearCookie@@3P6GXH@ZA DD 01H DUP (?)		; mbClearCookie
_BSS	ENDS
;	COMDAT ?mbUtilEncodeURLEscape@@3P6GPBDPBD@ZA
_BSS	SEGMENT
?mbUtilEncodeURLEscape@@3P6GPBDPBD@ZA DD 01H DUP (?)	; mbUtilEncodeURLEscape
_BSS	ENDS
;	COMDAT ?mbReload@@3P6GXH@ZA
_BSS	SEGMENT
?mbReload@@3P6GXH@ZA DD 01H DUP (?)			; mbReload
_BSS	ENDS
;	COMDAT ?mbWebFrameGetMainFrame@@3P6GPAXH@ZA
_BSS	SEGMENT
?mbWebFrameGetMainFrame@@3P6GPAXH@ZA DD 01H DUP (?)	; mbWebFrameGetMainFrame
_BSS	ENDS
;	COMDAT ?mbSetResourceGc@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetResourceGc@@3P6GXHH@ZA DD 01H DUP (?)		; mbSetResourceGc
_BSS	ENDS
;	COMDAT ?mbCallBlinkThreadSync@@3P6GXP6GXPAX0@Z00@ZA
_BSS	SEGMENT
?mbCallBlinkThreadSync@@3P6GXP6GXPAX0@Z00@ZA DD 01H DUP (?) ; mbCallBlinkThreadSync
_BSS	ENDS
;	COMDAT ?mbDestroyWebView@@3P6GXH@ZA
_BSS	SEGMENT
?mbDestroyWebView@@3P6GXH@ZA DD 01H DUP (?)		; mbDestroyWebView
_BSS	ENDS
;	COMDAT ?mbOnJsQuery@@3P6GXHP6GXHPAX0_JHPBD@Z0@ZA
_BSS	SEGMENT
?mbOnJsQuery@@3P6GXHP6GXHPAX0_JHPBD@Z0@ZA DD 01H DUP (?) ; mbOnJsQuery
_BSS	ENDS
;	COMDAT ?jsIsBoolean@@3P6A_N_J@ZA
_BSS	SEGMENT
?jsIsBoolean@@3P6A_N_J@ZA DD 01H DUP (?)		; jsIsBoolean
_BSS	ENDS
;	COMDAT ?wkeVisitAllCookie@@3P6AXPAVCWebView@wke@@PAXP6A_N1PBD222HHPAH@Z@ZA
_BSS	SEGMENT
?wkeVisitAllCookie@@3P6AXPAVCWebView@wke@@PAXP6A_N1PBD222HHPAH@Z@ZA DD 01H DUP (?) ; wkeVisitAllCookie
_BSS	ENDS
;	COMDAT ?mbSetDiskCacheEnabled@@3P6GXHH@ZA
_BSS	SEGMENT
?mbSetDiskCacheEnabled@@3P6GXHH@ZA DD 01H DUP (?)	; mbSetDiskCacheEnabled
_BSS	ENDS
;	COMDAT ?wkeOnNavigation@@3P6AXPAVCWebView@wke@@P6A_N0PAXW4_wkeNavigationType@@QAVCString@2@@Z1@ZA
_BSS	SEGMENT
?wkeOnNavigation@@3P6AXPAVCWebView@wke@@P6A_N0PAXW4_wkeNavigationType@@QAVCString@2@@Z1@ZA DD 01H DUP (?) ; wkeOnNavigation
_BSS	ENDS
;	COMDAT ?wkeNetCreatePostBodyElements@@3P6APAU_wkePostBodyElements@@PAVCWebView@wke@@I@ZA
_BSS	SEGMENT
?wkeNetCreatePostBodyElements@@3P6APAU_wkePostBodyElements@@PAVCWebView@wke@@I@ZA DD 01H DUP (?) ; wkeNetCreatePostBodyElements
_BSS	ENDS
;	COMDAT ?wkeGetWindowHandle@@3P6APAUHWND__@@PAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetWindowHandle@@3P6APAUHWND__@@PAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetWindowHandle
_BSS	ENDS
;	COMDAT ?wkeSetContextMenuEnabled@@3P6AXPAVCWebView@wke@@_N@ZA
_BSS	SEGMENT
?wkeSetContextMenuEnabled@@3P6AXPAVCWebView@wke@@_N@ZA DD 01H DUP (?) ; wkeSetContextMenuEnabled
_BSS	ENDS
;	COMDAT ?mbGetBlinkMainThreadIsolate@@3P6GPAXXZA
_BSS	SEGMENT
?mbGetBlinkMainThreadIsolate@@3P6GPAXXZA DD 01H DUP (?)	; mbGetBlinkMainThreadIsolate
_BSS	ENDS
;	COMDAT ?wkeAwaken@@3P6AXPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeAwaken@@3P6AXPAVCWebView@wke@@@ZA DD 01H DUP (?)	; wkeAwaken
_BSS	ENDS
;	COMDAT ?jsGetGlobal@@3P6A_JPAUJsExecStateInfo@@PBD@ZA
_BSS	SEGMENT
?jsGetGlobal@@3P6A_JPAUJsExecStateInfo@@PBD@ZA DD 01H DUP (?) ; jsGetGlobal
_BSS	ENDS
;	COMDAT ?wkeGetContentWidth@@3P6AHPAVCWebView@wke@@@ZA
_BSS	SEGMENT
?wkeGetContentWidth@@3P6AHPAVCWebView@wke@@@ZA DD 01H DUP (?) ; wkeGetContentWidth
_BSS	ENDS
;	COMDAT ?mbFireMouseEvent@@3P6GHHIHHI@ZA
_BSS	SEGMENT
?mbFireMouseEvent@@3P6GHHIHHI@ZA DD 01H DUP (?)		; mbFireMouseEvent
_BSS	ENDS
_DATA	ENDS
;	COMDAT _IID_IAxWinHostWindowLic
CONST	SEGMENT
_IID_IAxWinHostWindowLic DD 03935bda8H
	DW	04ed9H
	DW	0495cH
	DB	086H
	DB	050H
	DB	0e0H
	DB	01fH
	DB	0c1H
	DB	0e3H
	DB	08aH
	DB	04bH
CONST	ENDS
;	COMDAT _IID_IPrintDialogServices
CONST	SEGMENT
_IID_IPrintDialogServices DD 0509aaedaH
	DW	05639H
	DW	011d1H
	DB	0b6H
	DB	0a1H
	DB	00H
	DB	00H
	DB	0f8H
	DB	075H
	DB	07bH
	DB	0f9H
CONST	ENDS
;	COMDAT _IID_IAxWinAmbientDispatch
CONST	SEGMENT
_IID_IAxWinAmbientDispatch DD 0b6ea2051H
	DW	048aH
	DW	011d1H
	DB	082H
	DB	0b9H
	DB	00H
	DB	0c0H
	DB	04fH
	DB	0b9H
	DB	094H
	DB	02eH
CONST	ENDS
;	COMDAT _CLSID_Registrar
CONST	SEGMENT
_CLSID_Registrar DD 044ec053aH
	DW	0400fH
	DW	011d0H
	DB	09dH
	DB	0cdH
	DB	00H
	DB	0a0H
	DB	0c9H
	DB	03H
	DB	091H
	DB	0d3H
CONST	ENDS
;	COMDAT ?chLeftBracket@ATL@@3_WB
CONST	SEGMENT
?chLeftBracket@ATL@@3_WB DW 07bH			; ATL::chLeftBracket
CONST	ENDS
;	COMDAT ?szDwordVal@ATL@@3QB_WB
CONST	SEGMENT
?szDwordVal@ATL@@3QB_WB DD FLAT:$SG4294966768		; ATL::szDwordVal
CONST	ENDS
;	COMDAT ?chEquals@ATL@@3_WB
CONST	SEGMENT
?chEquals@ATL@@3_WB DW 03dH				; ATL::chEquals
CONST	ENDS
;	COMDAT __pAtlLcidToLocaleNameTable
CONST	SEGMENT
__pAtlLcidToLocaleNameTable DD 01H
	DD	FLAT:$SG4294967268
	DD	02H
	DD	FLAT:$SG4294967267
	DD	03H
	DD	FLAT:$SG4294967266
	DD	04H
	DD	FLAT:$SG4294967265
	DD	05H
	DD	FLAT:$SG4294967264
	DD	06H
	DD	FLAT:$SG4294967263
	DD	07H
	DD	FLAT:$SG4294967262
	DD	08H
	DD	FLAT:$SG4294967261
	DD	09H
	DD	FLAT:$SG4294967260
	DD	0aH
	DD	FLAT:$SG4294967259
	DD	0bH
	DD	FLAT:$SG4294967258
	DD	0cH
	DD	FLAT:$SG4294967257
	DD	0dH
	DD	FLAT:$SG4294967256
	DD	0eH
	DD	FLAT:$SG4294967255
	DD	0fH
	DD	FLAT:$SG4294967254
	DD	010H
	DD	FLAT:$SG4294967253
	DD	011H
	DD	FLAT:$SG4294967252
	DD	012H
	DD	FLAT:$SG4294967251
	DD	013H
	DD	FLAT:$SG4294967250
	DD	014H
	DD	FLAT:$SG4294967249
	DD	015H
	DD	FLAT:$SG4294967248
	DD	016H
	DD	FLAT:$SG4294967247
	DD	018H
	DD	FLAT:$SG4294967246
	DD	019H
	DD	FLAT:$SG4294967245
	DD	01aH
	DD	FLAT:$SG4294967244
	DD	01bH
	DD	FLAT:$SG4294967243
	DD	01cH
	DD	FLAT:$SG4294967242
	DD	01dH
	DD	FLAT:$SG4294967241
	DD	01eH
	DD	FLAT:$SG4294967240
	DD	01fH
	DD	FLAT:$SG4294967239
	DD	020H
	DD	FLAT:$SG4294967238
	DD	021H
	DD	FLAT:$SG4294967237
	DD	022H
	DD	FLAT:$SG4294967236
	DD	023H
	DD	FLAT:$SG4294967235
	DD	024H
	DD	FLAT:$SG4294967234
	DD	025H
	DD	FLAT:$SG4294967233
	DD	026H
	DD	FLAT:$SG4294967232
	DD	027H
	DD	FLAT:$SG4294967231
	DD	029H
	DD	FLAT:$SG4294967230
	DD	02aH
	DD	FLAT:$SG4294967229
	DD	02bH
	DD	FLAT:$SG4294967228
	DD	02cH
	DD	FLAT:$SG4294967227
	DD	02dH
	DD	FLAT:$SG4294967226
	DD	02fH
	DD	FLAT:$SG4294967225
	DD	036H
	DD	FLAT:$SG4294967224
	DD	037H
	DD	FLAT:$SG4294967223
	DD	038H
	DD	FLAT:$SG4294967222
	DD	039H
	DD	FLAT:$SG4294967221
	DD	03eH
	DD	FLAT:$SG4294967220
	DD	03fH
	DD	FLAT:$SG4294967219
	DD	040H
	DD	FLAT:$SG4294967218
	DD	041H
	DD	FLAT:$SG4294967217
	DD	043H
	DD	FLAT:$SG4294967216
	DD	044H
	DD	FLAT:$SG4294967215
	DD	046H
	DD	FLAT:$SG4294967214
	DD	047H
	DD	FLAT:$SG4294967213
	DD	049H
	DD	FLAT:$SG4294967212
	DD	04aH
	DD	FLAT:$SG4294967211
	DD	04bH
	DD	FLAT:$SG4294967210
	DD	04eH
	DD	FLAT:$SG4294967209
	DD	04fH
	DD	FLAT:$SG4294967208
	DD	050H
	DD	FLAT:$SG4294967207
	DD	056H
	DD	FLAT:$SG4294967206
	DD	057H
	DD	FLAT:$SG4294967205
	DD	05aH
	DD	FLAT:$SG4294967204
	DD	065H
	DD	FLAT:$SG4294967203
	DD	07fH
	DD	FLAT:$SG4294967202
	DD	0401H
	DD	FLAT:$SG4294967201
	DD	0402H
	DD	FLAT:$SG4294967200
	DD	0403H
	DD	FLAT:$SG4294967199
	DD	0404H
	DD	FLAT:$SG4294967198
	DD	0405H
	DD	FLAT:$SG4294967197
	DD	0406H
	DD	FLAT:$SG4294967196
	DD	0407H
	DD	FLAT:$SG4294967195
	DD	0408H
	DD	FLAT:$SG4294967194
	DD	0409H
	DD	FLAT:$SG4294967193
	DD	040bH
	DD	FLAT:$SG4294967192
	DD	040cH
	DD	FLAT:$SG4294967191
	DD	040dH
	DD	FLAT:$SG4294967190
	DD	040eH
	DD	FLAT:$SG4294967189
	DD	040fH
	DD	FLAT:$SG4294967188
	DD	0410H
	DD	FLAT:$SG4294967187
	DD	0411H
	DD	FLAT:$SG4294967186
	DD	0412H
	DD	FLAT:$SG4294967185
	DD	0413H
	DD	FLAT:$SG4294967184
	DD	0414H
	DD	FLAT:$SG4294967183
	DD	0415H
	DD	FLAT:$SG4294967182
	DD	0416H
	DD	FLAT:$SG4294967181
	DD	0418H
	DD	FLAT:$SG4294967180
	DD	0419H
	DD	FLAT:$SG4294967179
	DD	041aH
	DD	FLAT:$SG4294967178
	DD	041bH
	DD	FLAT:$SG4294967177
	DD	041cH
	DD	FLAT:$SG4294967176
	DD	041dH
	DD	FLAT:$SG4294967175
	DD	041eH
	DD	FLAT:$SG4294967174
	DD	041fH
	DD	FLAT:$SG4294967173
	DD	0420H
	DD	FLAT:$SG4294967172
	DD	0421H
	DD	FLAT:$SG4294967171
	DD	0422H
	DD	FLAT:$SG4294967170
	DD	0423H
	DD	FLAT:$SG4294967169
	DD	0424H
	DD	FLAT:$SG4294967168
	DD	0425H
	DD	FLAT:$SG4294967167
	DD	0426H
	DD	FLAT:$SG4294967166
	DD	0427H
	DD	FLAT:$SG4294967165
	DD	0429H
	DD	FLAT:$SG4294967164
	DD	042aH
	DD	FLAT:$SG4294967163
	DD	042bH
	DD	FLAT:$SG4294967162
	DD	042cH
	DD	FLAT:$SG4294967161
	DD	042dH
	DD	FLAT:$SG4294967160
	DD	042fH
	DD	FLAT:$SG4294967159
	DD	0432H
	DD	FLAT:$SG4294967158
	DD	0434H
	DD	FLAT:$SG4294967157
	DD	0435H
	DD	FLAT:$SG4294967156
	DD	0436H
	DD	FLAT:$SG4294967155
	DD	0437H
	DD	FLAT:$SG4294967154
	DD	0438H
	DD	FLAT:$SG4294967153
	DD	0439H
	DD	FLAT:$SG4294967152
	DD	043aH
	DD	FLAT:$SG4294967151
	DD	043bH
	DD	FLAT:$SG4294967150
	DD	043eH
	DD	FLAT:$SG4294967149
	DD	043fH
	DD	FLAT:$SG4294967148
	DD	0440H
	DD	FLAT:$SG4294967147
	DD	0441H
	DD	FLAT:$SG4294967146
	DD	0443H
	DD	FLAT:$SG4294967145
	DD	0444H
	DD	FLAT:$SG4294967144
	DD	0445H
	DD	FLAT:$SG4294967143
	DD	0446H
	DD	FLAT:$SG4294967142
	DD	0447H
	DD	FLAT:$SG4294967141
	DD	0449H
	DD	FLAT:$SG4294967140
	DD	044aH
	DD	FLAT:$SG4294967139
	DD	044bH
	DD	FLAT:$SG4294967138
	DD	044cH
	DD	FLAT:$SG4294967137
	DD	044eH
	DD	FLAT:$SG4294967136
	DD	044fH
	DD	FLAT:$SG4294967135
	DD	0450H
	DD	FLAT:$SG4294967134
	DD	0452H
	DD	FLAT:$SG4294967133
	DD	0456H
	DD	FLAT:$SG4294967132
	DD	0457H
	DD	FLAT:$SG4294967131
	DD	045aH
	DD	FLAT:$SG4294967130
	DD	0465H
	DD	FLAT:$SG4294967129
	DD	046bH
	DD	FLAT:$SG4294967128
	DD	046cH
	DD	FLAT:$SG4294967127
	DD	0481H
	DD	FLAT:$SG4294967126
	DD	0801H
	DD	FLAT:$SG4294967125
	DD	0804H
	DD	FLAT:$SG4294967124
	DD	0807H
	DD	FLAT:$SG4294967123
	DD	0809H
	DD	FLAT:$SG4294967122
	DD	080aH
	DD	FLAT:$SG4294967121
	DD	080cH
	DD	FLAT:$SG4294967120
	DD	0810H
	DD	FLAT:$SG4294967119
	DD	0813H
	DD	FLAT:$SG4294967118
	DD	0814H
	DD	FLAT:$SG4294967117
	DD	0816H
	DD	FLAT:$SG4294967116
	DD	081aH
	DD	FLAT:$SG4294967115
	DD	081dH
	DD	FLAT:$SG4294967114
	DD	082cH
	DD	FLAT:$SG4294967113
	DD	083bH
	DD	FLAT:$SG4294967112
	DD	083eH
	DD	FLAT:$SG4294967111
	DD	0843H
	DD	FLAT:$SG4294967110
	DD	086bH
	DD	FLAT:$SG4294967109
	DD	0c01H
	DD	FLAT:$SG4294967108
	DD	0c04H
	DD	FLAT:$SG4294967107
	DD	0c07H
	DD	FLAT:$SG4294967106
	DD	0c09H
	DD	FLAT:$SG4294967105
	DD	0c0aH
	DD	FLAT:$SG4294967104
	DD	0c0cH
	DD	FLAT:$SG4294967103
	DD	0c1aH
	DD	FLAT:$SG4294967102
	DD	0c3bH
	DD	FLAT:$SG4294967101
	DD	0c6bH
	DD	FLAT:$SG4294967100
	DD	01001H
	DD	FLAT:$SG4294967099
	DD	01004H
	DD	FLAT:$SG4294967098
	DD	01007H
	DD	FLAT:$SG4294967097
	DD	01009H
	DD	FLAT:$SG4294967096
	DD	0100aH
	DD	FLAT:$SG4294967095
	DD	0100cH
	DD	FLAT:$SG4294967094
	DD	0101aH
	DD	FLAT:$SG4294967093
	DD	0103bH
	DD	FLAT:$SG4294967092
	DD	01401H
	DD	FLAT:$SG4294967091
	DD	01404H
	DD	FLAT:$SG4294967090
	DD	01407H
	DD	FLAT:$SG4294967089
	DD	01409H
	DD	FLAT:$SG4294967088
	DD	0140aH
	DD	FLAT:$SG4294967087
	DD	0140cH
	DD	FLAT:$SG4294967086
	DD	0141aH
	DD	FLAT:$SG4294967085
	DD	0143bH
	DD	FLAT:$SG4294967084
	DD	01801H
	DD	FLAT:$SG4294967083
	DD	01809H
	DD	FLAT:$SG4294967082
	DD	0180aH
	DD	FLAT:$SG4294967081
	DD	0180cH
	DD	FLAT:$SG4294967080
	DD	0181aH
	DD	FLAT:$SG4294967079
	DD	0183bH
	DD	FLAT:$SG4294967078
	DD	01c01H
	DD	FLAT:$SG4294967077
	DD	01c09H
	DD	FLAT:$SG4294967076
	DD	01c0aH
	DD	FLAT:$SG4294967075
	DD	01c1aH
	DD	FLAT:$SG4294967074
	DD	01c3bH
	DD	FLAT:$SG4294967073
	DD	02001H
	DD	FLAT:$SG4294967072
	DD	02009H
	DD	FLAT:$SG4294967071
	DD	0200aH
	DD	FLAT:$SG4294967070
	DD	0203bH
	DD	FLAT:$SG4294967069
	DD	02401H
	DD	FLAT:$SG4294967068
	DD	02409H
	DD	FLAT:$SG4294967067
	DD	0240aH
	DD	FLAT:$SG4294967066
	DD	0243bH
	DD	FLAT:$SG4294967065
	DD	02801H
	DD	FLAT:$SG4294967064
	DD	02809H
	DD	FLAT:$SG4294967063
	DD	0280aH
	DD	FLAT:$SG4294967062
	DD	02c01H
	DD	FLAT:$SG4294967061
	DD	02c09H
	DD	FLAT:$SG4294967060
	DD	02c0aH
	DD	FLAT:$SG4294967059
	DD	03001H
	DD	FLAT:$SG4294967058
	DD	03009H
	DD	FLAT:$SG4294967057
	DD	0300aH
	DD	FLAT:$SG4294967056
	DD	03401H
	DD	FLAT:$SG4294967055
	DD	03409H
	DD	FLAT:$SG4294967054
	DD	0340aH
	DD	FLAT:$SG4294967053
	DD	03801H
	DD	FLAT:$SG4294967052
	DD	0380aH
	DD	FLAT:$SG4294967051
	DD	03c01H
	DD	FLAT:$SG4294967050
	DD	03c0aH
	DD	FLAT:$SG4294967049
	DD	04001H
	DD	FLAT:$SG4294967048
	DD	0400aH
	DD	FLAT:$SG4294967047
	DD	0440aH
	DD	FLAT:$SG4294967046
	DD	0480aH
	DD	FLAT:$SG4294967045
	DD	04c0aH
	DD	FLAT:$SG4294967044
	DD	0500aH
	DD	FLAT:$SG4294967043
	DD	07c04H
	DD	FLAT:$SG4294967042
	DD	07c1aH
	DD	FLAT:$SG4294967041
CONST	ENDS
;	COMDAT ?chRightBracket@ATL@@3_WB
CONST	SEGMENT
?chRightBracket@ATL@@3_WB DW 07dH			; ATL::chRightBracket
CONST	ENDS
;	COMDAT __pAtlLocaleNameToIndexTable
CONST	SEGMENT
__pAtlLocaleNameToIndexTable DD FLAT:$SG4294967040
	DD	042H
	DD	FLAT:$SG4294967039
	DD	02cH
	DD	FLAT:$SG4294967038
	DD	071H
	DD	FLAT:$SG4294967037
	DD	00H
	DD	FLAT:$SG4294967036
	DD	0d8H
	DD	FLAT:$SG4294967035
	DD	0daH
	DD	FLAT:$SG4294967034
	DD	0b1H
	DD	FLAT:$SG4294967033
	DD	0a0H
	DD	FLAT:$SG4294967032
	DD	08fH
	DD	FLAT:$SG4294967031
	DD	0cfH
	DD	FLAT:$SG4294967030
	DD	0d5H
	DD	FLAT:$SG4294967029
	DD	0d2H
	DD	FLAT:$SG4294967028
	DD	0a9H
	DD	FLAT:$SG4294967027
	DD	0b9H
	DD	FLAT:$SG4294967026
	DD	0c4H
	DD	FLAT:$SG4294967025
	DD	0dcH
	DD	FLAT:$SG4294967024
	DD	043H
	DD	FLAT:$SG4294967023
	DD	0ccH
	DD	FLAT:$SG4294967022
	DD	0bfH
	DD	FLAT:$SG4294967021
	DD	0c8H
	DD	FLAT:$SG4294967020
	DD	029H
	DD	FLAT:$SG4294967019
	DD	09bH
	DD	FLAT:$SG4294967018
	DD	06bH
	DD	FLAT:$SG4294967017
	DD	021H
	DD	FLAT:$SG4294967016
	DD	063H
	DD	FLAT:$SG4294967015
	DD	01H
	DD	FLAT:$SG4294967014
	DD	044H
	DD	FLAT:$SG4294967013
	DD	07dH
	DD	FLAT:$SG4294967012
	DD	0b7H
	DD	FLAT:$SG4294967011
	DD	02H
	DD	FLAT:$SG4294967010
	DD	045H
	DD	FLAT:$SG4294967009
	DD	04H
	DD	FLAT:$SG4294967008
	DD	047H
	DD	FLAT:$SG4294967007
	DD	087H
	DD	FLAT:$SG4294967006
	DD	05H
	DD	FLAT:$SG4294967005
	DD	048H
	DD	FLAT:$SG4294967004
	DD	06H
	DD	FLAT:$SG4294967003
	DD	0a2H
	DD	FLAT:$SG4294967002
	DD	091H
	DD	FLAT:$SG4294967001
	DD	049H
	DD	FLAT:$SG4294967000
	DD	0b3H
	DD	FLAT:$SG4294966999
	DD	0abH
	DD	FLAT:$SG4294966998
	DD	041H
	DD	FLAT:$SG4294966997
	DD	08bH
	DD	FLAT:$SG4294966996
	DD	07H
	DD	FLAT:$SG4294966995
	DD	04aH
	DD	FLAT:$SG4294966994
	DD	08H
	DD	FLAT:$SG4294966993
	DD	0a3H
	DD	FLAT:$SG4294966992
	DD	0cdH
	DD	FLAT:$SG4294966991
	DD	0acH
	DD	FLAT:$SG4294966990
	DD	0c9H
	DD	FLAT:$SG4294966989
	DD	092H
	DD	FLAT:$SG4294966988
	DD	0baH
	DD	FLAT:$SG4294966987
	DD	0c5H
	DD	FLAT:$SG4294966986
	DD	0b4H
	DD	FLAT:$SG4294966985
	DD	0d6H
	DD	FLAT:$SG4294966984
	DD	0d0H
	DD	FLAT:$SG4294966983
	DD	04bH
	DD	FLAT:$SG4294966982
	DD	0c0H
	DD	FLAT:$SG4294966981
	DD	0d3H
	DD	FLAT:$SG4294966980
	DD	09H
	DD	FLAT:$SG4294966979
	DD	0d1H
	DD	FLAT:$SG4294966978
	DD	0ddH
	DD	FLAT:$SG4294966977
	DD	0d7H
	DD	FLAT:$SG4294966976
	DD	0caH
	DD	FLAT:$SG4294966975
	DD	0b5H
	DD	FLAT:$SG4294966974
	DD	0c1H
	DD	FLAT:$SG4294966973
	DD	0d4H
	DD	FLAT:$SG4294966972
	DD	0a4H
	DD	FLAT:$SG4294966971
	DD	0adH
	DD	FLAT:$SG4294966970
	DD	0dfH
	DD	FLAT:$SG4294966969
	DD	093H
	DD	FLAT:$SG4294966968
	DD	0e0H
	DD	FLAT:$SG4294966967
	DD	0bbH
	DD	FLAT:$SG4294966966
	DD	0ceH
	DD	FLAT:$SG4294966965
	DD	0e1H
	DD	FLAT:$SG4294966964
	DD	0dbH
	DD	FLAT:$SG4294966963
	DD	0deH
	DD	FLAT:$SG4294966962
	DD	0d9H
	DD	FLAT:$SG4294966961
	DD	0c6H
	DD	FLAT:$SG4294966960
	DD	023H
	DD	FLAT:$SG4294966959
	DD	065H
	DD	FLAT:$SG4294966958
	DD	02aH
	DD	FLAT:$SG4294966957
	DD	06cH
	DD	FLAT:$SG4294966956
	DD	026H
	DD	FLAT:$SG4294966955
	DD	068H
	DD	FLAT:$SG4294966954
	DD	0aH
	DD	FLAT:$SG4294966953
	DD	04cH
	DD	FLAT:$SG4294966952
	DD	02eH
	DD	FLAT:$SG4294966951
	DD	073H
	DD	FLAT:$SG4294966950
	DD	0bH
	DD	FLAT:$SG4294966949
	DD	094H
	DD	FLAT:$SG4294966948
	DD	0a5H
	DD	FLAT:$SG4294966947
	DD	0aeH
	DD	FLAT:$SG4294966946
	DD	04dH
	DD	FLAT:$SG4294966945
	DD	0b6H
	DD	FLAT:$SG4294966944
	DD	0bcH
	DD	FLAT:$SG4294966943
	DD	03eH
	DD	FLAT:$SG4294966942
	DD	088H
	DD	FLAT:$SG4294966941
	DD	037H
	DD	FLAT:$SG4294966940
	DD	07fH
	DD	FLAT:$SG4294966939
	DD	0cH
	DD	FLAT:$SG4294966938
	DD	04eH
	DD	FLAT:$SG4294966937
	DD	02fH
	DD	FLAT:$SG4294966936
	DD	074H
	DD	FLAT:$SG4294966935
	DD	018H
	DD	FLAT:$SG4294966934
	DD	0afH
	DD	FLAT:$SG4294966933
	DD	05aH
	DD	FLAT:$SG4294966932
	DD	0dH
	DD	FLAT:$SG4294966931
	DD	04fH
	DD	FLAT:$SG4294966930
	DD	028H
	DD	FLAT:$SG4294966929
	DD	06aH
	DD	FLAT:$SG4294966928
	DD	01fH
	DD	FLAT:$SG4294966927
	DD	061H
	DD	FLAT:$SG4294966926
	DD	0eH
	DD	FLAT:$SG4294966925
	DD	050H
	DD	FLAT:$SG4294966924
	DD	0fH
	DD	FLAT:$SG4294966923
	DD	095H
	DD	FLAT:$SG4294966922
	DD	051H
	DD	FLAT:$SG4294966921
	DD	010H
	DD	FLAT:$SG4294966920
	DD	052H
	DD	FLAT:$SG4294966919
	DD	02dH
	DD	FLAT:$SG4294966918
	DD	072H
	DD	FLAT:$SG4294966917
	DD	031H
	DD	FLAT:$SG4294966916
	DD	078H
	DD	FLAT:$SG4294966915
	DD	03aH
	DD	FLAT:$SG4294966914
	DD	082H
	DD	FLAT:$SG4294966913
	DD	011H
	DD	FLAT:$SG4294966912
	DD	03fH
	DD	FLAT:$SG4294966911
	DD	089H
	DD	FLAT:$SG4294966910
	DD	053H
	DD	FLAT:$SG4294966909
	DD	032H
	DD	FLAT:$SG4294966908
	DD	079H
	DD	FLAT:$SG4294966907
	DD	025H
	DD	FLAT:$SG4294966906
	DD	067H
	DD	FLAT:$SG4294966905
	DD	024H
	DD	FLAT:$SG4294966904
	DD	066H
	DD	FLAT:$SG4294966903
	DD	08eH
	DD	FLAT:$SG4294966902
	DD	02bH
	DD	FLAT:$SG4294966901
	DD	06dH
	DD	FLAT:$SG4294966900
	DD	083H
	DD	FLAT:$SG4294966899
	DD	03dH
	DD	FLAT:$SG4294966898
	DD	086H
	DD	FLAT:$SG4294966897
	DD	03bH
	DD	FLAT:$SG4294966896
	DD	084H
	DD	FLAT:$SG4294966895
	DD	030H
	DD	FLAT:$SG4294966894
	DD	09dH
	DD	FLAT:$SG4294966893
	DD	077H
	DD	FLAT:$SG4294966892
	DD	075H
	DD	FLAT:$SG4294966891
	DD	055H
	DD	FLAT:$SG4294966890
	DD	012H
	DD	FLAT:$SG4294966889
	DD	096H
	DD	FLAT:$SG4294966888
	DD	054H
	DD	FLAT:$SG4294966887
	DD	097H
	DD	FLAT:$SG4294966886
	DD	013H
	DD	FLAT:$SG4294966885
	DD	08dH
	DD	FLAT:$SG4294966884
	DD	036H
	DD	FLAT:$SG4294966883
	DD	07eH
	DD	FLAT:$SG4294966882
	DD	014H
	DD	FLAT:$SG4294966881
	DD	056H
	DD	FLAT:$SG4294966880
	DD	015H
	DD	FLAT:$SG4294966879
	DD	057H
	DD	FLAT:$SG4294966878
	DD	098H
	DD	FLAT:$SG4294966877
	DD	08cH
	DD	FLAT:$SG4294966876
	DD	09fH
	DD	FLAT:$SG4294966875
	DD	0a8H
	DD	FLAT:$SG4294966874
	DD	016H
	DD	FLAT:$SG4294966873
	DD	058H
	DD	FLAT:$SG4294966872
	DD	017H
	DD	FLAT:$SG4294966871
	DD	059H
	DD	FLAT:$SG4294966870
	DD	03cH
	DD	FLAT:$SG4294966869
	DD	085H
	DD	FLAT:$SG4294966868
	DD	0a7H
	DD	FLAT:$SG4294966867
	DD	076H
	DD	FLAT:$SG4294966866
	DD	09cH
	DD	FLAT:$SG4294966865
	DD	019H
	DD	FLAT:$SG4294966864
	DD	05bH
	DD	FLAT:$SG4294966863
	DD	022H
	DD	FLAT:$SG4294966862
	DD	064H
	DD	FLAT:$SG4294966861
	DD	0beH
	DD	FLAT:$SG4294966860
	DD	0c3H
	DD	FLAT:$SG4294966859
	DD	0b0H
	DD	FLAT:$SG4294966858
	DD	0b8H
	DD	FLAT:$SG4294966857
	DD	0cbH
	DD	FLAT:$SG4294966856
	DD	0c7H
	DD	FLAT:$SG4294966855
	DD	01aH
	DD	FLAT:$SG4294966854
	DD	05cH
	DD	FLAT:$SG4294966853
	DD	0e3H
	DD	FLAT:$SG4294966852
	DD	0c2H
	DD	FLAT:$SG4294966851
	DD	0bdH
	DD	FLAT:$SG4294966850
	DD	0a6H
	DD	FLAT:$SG4294966849
	DD	099H
	DD	FLAT:$SG4294966848
	DD	01bH
	DD	FLAT:$SG4294966847
	DD	09aH
	DD	FLAT:$SG4294966846
	DD	05dH
	DD	FLAT:$SG4294966845
	DD	033H
	DD	FLAT:$SG4294966844
	DD	07aH
	DD	FLAT:$SG4294966843
	DD	040H
	DD	FLAT:$SG4294966842
	DD	08aH
	DD	FLAT:$SG4294966841
	DD	038H
	DD	FLAT:$SG4294966840
	DD	080H
	DD	FLAT:$SG4294966839
	DD	039H
	DD	FLAT:$SG4294966838
	DD	081H
	DD	FLAT:$SG4294966837
	DD	01cH
	DD	FLAT:$SG4294966836
	DD	05eH
	DD	FLAT:$SG4294966835
	DD	06eH
	DD	FLAT:$SG4294966834
	DD	01dH
	DD	FLAT:$SG4294966833
	DD	05fH
	DD	FLAT:$SG4294966832
	DD	035H
	DD	FLAT:$SG4294966831
	DD	07cH
	DD	FLAT:$SG4294966830
	DD	020H
	DD	FLAT:$SG4294966829
	DD	062H
	DD	FLAT:$SG4294966828
	DD	01eH
	DD	FLAT:$SG4294966827
	DD	060H
	DD	FLAT:$SG4294966826
	DD	034H
	DD	FLAT:$SG4294966825
	DD	09eH
	DD	FLAT:$SG4294966824
	DD	07bH
	DD	FLAT:$SG4294966823
	DD	027H
	DD	FLAT:$SG4294966822
	DD	069H
	DD	FLAT:$SG4294966821
	DD	06fH
	DD	FLAT:$SG4294966820
	DD	03H
	DD	FLAT:$SG4294966819
	DD	0e2H
	DD	FLAT:$SG4294966818
	DD	090H
	DD	FLAT:$SG4294966817
	DD	0a1H
	DD	FLAT:$SG4294966816
	DD	0b2H
	DD	FLAT:$SG4294966815
	DD	0aaH
	DD	FLAT:$SG4294966814
	DD	046H
	DD	FLAT:$SG4294966813
	DD	070H
CONST	ENDS
;	COMDAT ?szNoRemove@ATL@@3QB_WB
CONST	SEGMENT
?szNoRemove@ATL@@3QB_WB DD FLAT:$SG4294966764		; ATL::szNoRemove
CONST	ENDS
;	COMDAT ?chDirSep@ATL@@3_WB
CONST	SEGMENT
?chDirSep@ATL@@3_WB DW 05cH				; ATL::chDirSep
CONST	ENDS
;	COMDAT ?multiszStringVal@ATL@@3QB_WB
CONST	SEGMENT
?multiszStringVal@ATL@@3QB_WB DD FLAT:$SG4294966769	; ATL::multiszStringVal
CONST	ENDS
;	COMDAT ?szStringVal@ATL@@3QB_WB
CONST	SEGMENT
?szStringVal@ATL@@3QB_WB DD FLAT:$SG4294966770		; ATL::szStringVal
CONST	ENDS
;	COMDAT ?szForceRemove@ATL@@3QB_WB
CONST	SEGMENT
?szForceRemove@ATL@@3QB_WB DD FLAT:$SG4294966765	; ATL::szForceRemove
CONST	ENDS
;	COMDAT ___pobjMapEntryFirst
ATL$__a	SEGMENT
___pobjMapEntryFirst DD 00H
ATL$__a	ENDS
;	COMDAT ?szBinaryVal@ATL@@3QB_WB
CONST	SEGMENT
?szBinaryVal@ATL@@3QB_WB DD FLAT:$SG4294966767		; ATL::szBinaryVal
CONST	ENDS
;	COMDAT ___pobjMapEntryLast
ATL$__z	SEGMENT
___pobjMapEntryLast DD 00H
ATL$__z	ENDS
;	COMDAT ?szDelete@ATL@@3QB_WB
CONST	SEGMENT
?szDelete@ATL@@3QB_WB DD FLAT:$SG4294966763		; ATL::szDelete
CONST	ENDS
;	COMDAT ?chQuote@ATL@@3_WB
CONST	SEGMENT
?chQuote@ATL@@3_WB DW 027H				; ATL::chQuote
CONST	ENDS
;	COMDAT ?szValToken@ATL@@3QB_WB
CONST	SEGMENT
?szValToken@ATL@@3QB_WB DD FLAT:$SG4294966766		; ATL::szValToken
CONST	ENDS
;	COMDAT _IID_IInternalConnection
CONST	SEGMENT
_IID_IInternalConnection DD 072ad0770H
	DW	06a9fH
	DW	011d1H
	DB	0bcH
	DB	0ecH
	DB	00H
	DB	060H
	DB	08H
	DB	08fH
	DB	044H
	DB	04eH
CONST	ENDS
;	COMDAT ?kMbDllPath@@3PB_WB
_DATA	SEGMENT
?kMbDllPath@@3PB_WB DD FLAT:$SG4294965828		; kMbDllPath
_DATA	ENDS
;	COMDAT ?kWkeDllPath@@3PB_WB
CONST	SEGMENT
?kWkeDllPath@@3PB_WB DD FLAT:$SG4294966541		; kWkeDllPath
CONST	ENDS
;	COMDAT ??_7CAtlStringMgr@ATL@@6B@
CONST	SEGMENT
??_7CAtlStringMgr@ATL@@6B@ DD FLAT:?Allocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@HH@Z ; ATL::CAtlStringMgr::`vftable'
	DD	FLAT:?Free@CAtlStringMgr@ATL@@UAEXPAUCStringData@2@@Z
	DD	FLAT:?Reallocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@PAU32@HH@Z
	DD	FLAT:?GetNilString@CAtlStringMgr@ATL@@UAEPAUCStringData@2@XZ
	DD	FLAT:?Clone@CAtlStringMgr@ATL@@UAEPAUIAtlStringMgr@2@XZ
	DD	FLAT:??_ECAtlStringMgr@ATL@@UAEPAXI@Z
CONST	ENDS
;	COMDAT _LIBID_ATLLib
CONST	SEGMENT
_LIBID_ATLLib DD 044ec0535H
	DW	0400fH
	DW	011d0H
	DB	09dH
	DB	0cdH
	DB	00H
	DB	0a0H
	DB	0c9H
	DB	03H
	DB	091H
	DB	0d3H
CONST	ENDS
;	COMDAT _IID_IDocHostUIHandlerDispatch
CONST	SEGMENT
_IID_IDocHostUIHandlerDispatch DD 0425b5af0H
	DW	065f1H
	DW	011d1H
	DB	096H
	DB	011H
	DB	00H
	DB	00H
	DB	0f8H
	DB	01eH
	DB	0dH
	DB	0dH
CONST	ENDS
;	COMDAT _IID_IPrintDialogCallback
CONST	SEGMENT
_IID_IPrintDialogCallback DD 05852a2c3H
	DW	06530H
	DW	011d1H
	DB	0b6H
	DB	0a3H
	DB	00H
	DB	00H
	DB	0f8H
	DB	075H
	DB	07bH
	DB	0f9H
CONST	ENDS
;	COMDAT _IID_IAxWinHostWindow
CONST	SEGMENT
_IID_IAxWinHostWindow DD 0b6ea2050H
	DW	048aH
	DW	011d1H
	DB	082H
	DB	0b9H
	DB	00H
	DB	0c0H
	DB	04fH
	DB	0b9H
	DB	094H
	DB	02eH
CONST	ENDS
;	COMDAT ?kMbMainDllPath@@3PB_WB
_DATA	SEGMENT
?kMbMainDllPath@@3PB_WB DD FLAT:$SG4294965827		; kMbMainDllPath
_DATA	ENDS
;	COMDAT _IID_IAxWinAmbientDispatchEx
CONST	SEGMENT
_IID_IAxWinAmbientDispatchEx DD 0b2d0778bH
	DW	0ac99H
	DW	04c58H
	DB	0a5H
	DB	0c8H
	DB	0e7H
	DB	072H
	DB	04eH
	DB	053H
	DB	016H
	DB	0b5H
CONST	ENDS
;	COMDAT _IID_IRegistrar
CONST	SEGMENT
_IID_IRegistrar DD 044ec053bH
	DW	0400fH
	DW	011d0H
	DB	09dH
	DB	0cdH
	DB	00H
	DB	0a0H
	DB	0c9H
	DB	03H
	DB	091H
	DB	0d3H
CONST	ENDS
;	COMDAT ??_7CWin32Heap@ATL@@6B@
CONST	SEGMENT
??_7CWin32Heap@ATL@@6B@ DD FLAT:?Allocate@CWin32Heap@ATL@@UAEPAXI@Z ; ATL::CWin32Heap::`vftable'
	DD	FLAT:?Free@CWin32Heap@ATL@@UAEXPAX@Z
	DD	FLAT:?Reallocate@CWin32Heap@ATL@@UAEPAXPAXI@Z
	DD	FLAT:?GetSize@CWin32Heap@ATL@@UAEIPAX@Z
	DD	FLAT:??_ECWin32Heap@ATL@@UAEPAXI@Z
$SG4294967291 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294967290 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294967289 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
$SG4294965370 DB 00H
$SG4294967288 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294967287 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
$SG4294967286 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294967285 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
$SG4294967284 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294967283 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
$SG4294967282 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
$SG4294967281 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
$SG4294967280 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294967279 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294967278 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294967277 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
$SG4294967276 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294967275 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294967274 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
$SG4294967273 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294967272 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
$SG4294967271 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
$SG4294967270 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
$SG4294967269 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294967268 DB 'a', 00H, 'r', 00H, 00H, 00H
$SG4294967267 DB 'b', 00H, 'g', 00H, 00H, 00H
$SG4294967266 DB 'c', 00H, 'a', 00H, 00H, 00H
$SG4294967265 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
$SG4294967264 DB 'c', 00H, 's', 00H, 00H, 00H
$SG4294967263 DB 'd', 00H, 'a', 00H, 00H, 00H
$SG4294967262 DB 'd', 00H, 'e', 00H, 00H, 00H
$SG4294967261 DB 'e', 00H, 'l', 00H, 00H, 00H
$SG4294967260 DB 'e', 00H, 'n', 00H, 00H, 00H
$SG4294967259 DB 'e', 00H, 's', 00H, 00H, 00H
$SG4294967258 DB 'f', 00H, 'i', 00H, 00H, 00H
$SG4294967257 DB 'f', 00H, 'r', 00H, 00H, 00H
$SG4294967256 DB 'h', 00H, 'e', 00H, 00H, 00H
$SG4294967255 DB 'h', 00H, 'u', 00H, 00H, 00H
$SG4294967254 DB 'i', 00H, 's', 00H, 00H, 00H
$SG4294967253 DB 'i', 00H, 't', 00H, 00H, 00H
$SG4294967252 DB 'j', 00H, 'a', 00H, 00H, 00H
$SG4294967251 DB 'k', 00H, 'o', 00H, 00H, 00H
$SG4294967250 DB 'n', 00H, 'l', 00H, 00H, 00H
$SG4294967249 DB 'n', 00H, 'o', 00H, 00H, 00H
$SG4294967248 DB 'p', 00H, 'l', 00H, 00H, 00H
$SG4294967247 DB 'p', 00H, 't', 00H, 00H, 00H
$SG4294967246 DB 'r', 00H, 'o', 00H, 00H, 00H
$SG4294967245 DB 'r', 00H, 'u', 00H, 00H, 00H
$SG4294967244 DB 'h', 00H, 'r', 00H, 00H, 00H
$SG4294967243 DB 's', 00H, 'k', 00H, 00H, 00H
$SG4294967242 DB 's', 00H, 'q', 00H, 00H, 00H
$SG4294967241 DB 's', 00H, 'v', 00H, 00H, 00H
$SG4294967240 DB 't', 00H, 'h', 00H, 00H, 00H
$SG4294967239 DB 't', 00H, 'r', 00H, 00H, 00H
$SG4294967238 DB 'u', 00H, 'r', 00H, 00H, 00H
$SG4294967237 DB 'i', 00H, 'd', 00H, 00H, 00H
$SG4294967236 DB 'u', 00H, 'k', 00H, 00H, 00H
$SG4294967235 DB 'b', 00H, 'e', 00H, 00H, 00H
$SG4294967234 DB 's', 00H, 'l', 00H, 00H, 00H
$SG4294967233 DB 'e', 00H, 't', 00H, 00H, 00H
$SG4294967232 DB 'l', 00H, 'v', 00H, 00H, 00H
$SG4294967231 DB 'l', 00H, 't', 00H, 00H, 00H
$SG4294967230 DB 'f', 00H, 'a', 00H, 00H, 00H
$SG4294967229 DB 'v', 00H, 'i', 00H, 00H, 00H
$SG4294967228 DB 'h', 00H, 'y', 00H, 00H, 00H
$SG4294967227 DB 'a', 00H, 'z', 00H, 00H, 00H
$SG4294967226 DB 'e', 00H, 'u', 00H, 00H, 00H
$SG4294967225 DB 'm', 00H, 'k', 00H, 00H, 00H
$SG4294967224 DB 'a', 00H, 'f', 00H, 00H, 00H
$SG4294967223 DB 'k', 00H, 'a', 00H, 00H, 00H
$SG4294967222 DB 'f', 00H, 'o', 00H, 00H, 00H
$SG4294967221 DB 'h', 00H, 'i', 00H, 00H, 00H
$SG4294967220 DB 'm', 00H, 's', 00H, 00H, 00H
$SG4294967219 DB 'k', 00H, 'k', 00H, 00H, 00H
$SG4294967218 DB 'k', 00H, 'y', 00H, 00H, 00H
$SG4294967217 DB 's', 00H, 'w', 00H, 00H, 00H
$SG4294967216 DB 'u', 00H, 'z', 00H, 00H, 00H
$SG4294967215 DB 't', 00H, 't', 00H, 00H, 00H
$SG4294967214 DB 'p', 00H, 'a', 00H, 00H, 00H
$SG4294967213 DB 'g', 00H, 'u', 00H, 00H, 00H
$SG4294967212 DB 't', 00H, 'a', 00H, 00H, 00H
$SG4294967211 DB 't', 00H, 'e', 00H, 00H, 00H
$SG4294967210 DB 'k', 00H, 'n', 00H, 00H, 00H
$SG4294967209 DB 'm', 00H, 'r', 00H, 00H, 00H
$SG4294967208 DB 's', 00H, 'a', 00H, 00H, 00H
$SG4294967207 DB 'm', 00H, 'n', 00H, 00H, 00H
$SG4294967206 DB 'g', 00H, 'l', 00H, 00H, 00H
$SG4294967205 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294967204 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294967203 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294967202 DB 00H, 00H
$SG4294967201 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294967200 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294967199 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294967198 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294967197 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294967196 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294967195 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294967194 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294967193 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294967192 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294967191 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294967190 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294967189 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294967188 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294967187 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294967186 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294967185 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294967184 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294967183 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294967182 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294967181 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294967180 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294967179 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294967178 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294967177 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294967176 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294967175 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294967174 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294967173 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294967172 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294967171 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294967170 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294967169 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294967168 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294967167 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294967166 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294967165 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294967164 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294967163 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294967162 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294967161 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967160 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294967159 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294967158 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294967157 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294967156 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294967155 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294967154 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294967153 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294967152 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967151 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294967150 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294967149 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294967148 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294967147 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294967146 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294967145 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967144 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294967143 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967142 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967141 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967140 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967139 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967138 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967137 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967136 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967135 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294967134 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294967133 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294967132 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294967131 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
$SG4294967130 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
$SG4294967129 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
$SG4294967128 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
$SG4294967127 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294967126 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294967125 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294967124 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294967123 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294967122 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294967121 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294967120 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294967119 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294967118 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294967117 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294967116 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294967115 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967114 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294967113 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
$SG4294967112 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294967111 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294967110 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
$SG4294967109 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
$SG4294967108 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294967107 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294967106 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294967105 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294967104 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294967103 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294967102 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
$SG4294967101 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294967100 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
$SG4294967099 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294967098 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294967097 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294967096 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294967095 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294967094 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294967093 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294967092 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
$SG4294967091 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294967090 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294967089 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294967088 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294967087 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294967086 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294967085 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967084 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
$SG4294967083 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294967082 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294967081 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294967080 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294967079 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967078 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
$SG4294967077 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294967076 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294967075 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294967074 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
$SG4294967073 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
$SG4294967072 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294967071 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294967070 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294967069 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
$SG4294967068 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294967067 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294967066 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294967065 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
$SG4294967064 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294967063 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294967062 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294967061 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294967060 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294967059 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294967058 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294967057 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294967056 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294967055 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294967054 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294967053 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294967052 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294967051 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294967050 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294967049 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294967048 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294967047 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294967046 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294967045 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294967044 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294967043 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294967042 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
$SG4294967041 DB 's', 00H, 'r', 00H, 00H, 00H
$SG4294967040 DB 00H, 00H
$SG4294967039 DB 'a', 00H, 'f', 00H, 00H, 00H
$SG4294967038 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294967037 DB 'a', 00H, 'r', 00H, 00H, 00H
$SG4294967036 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294967035 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294967034 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294967033 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294967032 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294967031 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294967030 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294967029 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294967028 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294967027 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294967026 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294967025 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294967024 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294967023 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294967022 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967021 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294967020 DB 'a', 00H, 'z', 00H, 00H, 00H
$SG4294967019 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
$SG4294967018 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967017 DB 'b', 00H, 'e', 00H, 00H, 00H
$SG4294967016 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294967015 DB 'b', 00H, 'g', 00H, 00H, 00H
$SG4294967014 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294967013 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294967012 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294967011 DB 'c', 00H, 'a', 00H, 00H, 00H
$SG4294967010 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294967009 DB 'c', 00H, 's', 00H, 00H, 00H
$SG4294967008 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294967007 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294967006 DB 'd', 00H, 'a', 00H, 00H, 00H
$SG4294967005 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294967004 DB 'd', 00H, 'e', 00H, 00H, 00H
$SG4294967003 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294967002 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294967001 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294967000 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294966999 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294966998 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294966997 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
$SG4294966996 DB 'e', 00H, 'l', 00H, 00H, 00H
$SG4294966995 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294966994 DB 'e', 00H, 'n', 00H, 00H, 00H
$SG4294966993 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294966992 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294966991 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294966990 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294966989 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294966988 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294966987 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294966986 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294966985 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294966984 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294966983 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294966982 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294966981 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294966980 DB 'e', 00H, 's', 00H, 00H, 00H
$SG4294966979 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294966978 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294966977 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294966976 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294966975 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294966974 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294966973 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294966972 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294966971 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294966970 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294966969 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294966968 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294966967 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294966966 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294966965 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294966964 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294966963 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294966962 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294966961 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294966960 DB 'e', 00H, 't', 00H, 00H, 00H
$SG4294966959 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294966958 DB 'e', 00H, 'u', 00H, 00H, 00H
$SG4294966957 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294966956 DB 'f', 00H, 'a', 00H, 00H, 00H
$SG4294966955 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294966954 DB 'f', 00H, 'i', 00H, 00H, 00H
$SG4294966953 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294966952 DB 'f', 00H, 'o', 00H, 00H, 00H
$SG4294966951 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294966950 DB 'f', 00H, 'r', 00H, 00H, 00H
$SG4294966949 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294966948 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294966947 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294966946 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294966945 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294966944 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294966943 DB 'g', 00H, 'l', 00H, 00H, 00H
$SG4294966942 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294966941 DB 'g', 00H, 'u', 00H, 00H, 00H
$SG4294966940 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966939 DB 'h', 00H, 'e', 00H, 00H, 00H
$SG4294966938 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294966937 DB 'h', 00H, 'i', 00H, 00H, 00H
$SG4294966936 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966935 DB 'h', 00H, 'r', 00H, 00H, 00H
$SG4294966934 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294966933 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294966932 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+1
$SG4294966931 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294966930 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294966929 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294966928 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294966927 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294966926 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294966925 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294966924 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294966923 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294966922 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294966921 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294966920 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294966919 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294966918 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294966917 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294966916 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294966915 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294966914 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966913 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294966912 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294966911 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966910 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294966909 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294966908 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294966907 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294966906 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294966905 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294966904 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294966903 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294966902 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294966901 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294966900 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966899 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294966898 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294966897 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294966896 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966895 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294966894 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294966893 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294966892 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294966891 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294966890 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294966889 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294966888 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294966887 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294966886 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294966885 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294966884 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294966883 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966882 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294966881 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294966880 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294966879 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294966878 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294966877 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966876 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966875 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966874 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294966873 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294966872 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294966871 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294966870 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294966869 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966868 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294966867 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294966866 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294966865 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294966864 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294966863 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294966862 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294966861 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966860 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966859 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966858 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966857 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966856 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966855 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294966854 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294966853 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294966852 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294966851 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294966850 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294966849 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294966848 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294966847 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294966846 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294966845 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294966844 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294966843 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294966842 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966841 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294966840 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966839 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294966838 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294966837 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294966836 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294966835 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294966834 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294966833 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294966832 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294966831 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294966830 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294966829 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294966828 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294966827 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294966826 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294966825 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294966824 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294966823 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294966822 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294966821 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294966820 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966819 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966818 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294966817 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294966816 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294966815 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294966814 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294966813 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294966770 DB 'S', 00H, 00H, 00H
$SG4294966769 DB 'M', 00H, 00H, 00H
$SG4294966768 DB 'D', 00H, 00H, 00H
$SG4294966767 DB 'B', 00H, 00H, 00H
$SG4294966766 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294966765 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294966764 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294966763 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966762 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294966761 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294966760 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294966759 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294966758 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294966757 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294966756 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294966755 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294966754 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294966753 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294966752 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294966751 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294966678 DB ':', 00H, 00H, 00H
$SG4294966669 DB 00H, 00H
	ORG $+2
$SG4294966668 DB 00H, 00H
	ORG $+2
$SG4294966541 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294965828 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294965827 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294965369 DB '\', 00H, 'z', 00H, 'h', 00H, 'u', 00H, 'a', 00H, 'n', 00H
	DB	'k', 00H, 'L', 00H, 'o', 00H, 'g', 00H, '.', 00H, 't', 00H, 'x'
	DB	00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294965368 DB 'C', 00H
	ORG $+2
$SG4294965367 DB '%', 00H, '0', 00H, '2', 00H, 'd', 00H, '-', 00H, '%', 00H
	DB	'0', 00H, '2', 00H, 'd', 00H, ' ', 00H, '%', 00H, '0', 00H, '2'
	DB	00H, 'd', 00H, ':', 00H, '%', 00H, '0', 00H, '2', 00H, 'd', 00H
	DB	':', 00H, '%', 00H, '0', 00H, '2', 00H, 'd', 00H, ' ', 00H, '%'
	DB	00H, 's', 00H, 0aH, 00H, 00H, 00H
	ORG $+2
$SG4294965366 DB 0aH, 00H, 00H, 00H
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
CONST	ENDS
PUBLIC	??$AtlAlignUp@H@ATL@@YGHHK@Z			; ATL::AtlAlignUp<int>
PUBLIC	??$AtlAdd@H@ATL@@YAJPAHHH@Z			; ATL::AtlAdd<int>
PUBLIC	??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::~CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
PUBLIC	??$AtlAdd@I@ATL@@YAJPAIII@Z			; ATL::AtlAdd<unsigned int>
PUBLIC	??0?$CTraceCategoryEx@$0EAAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<4194304,0>::CTraceCategoryEx<4194304,0>
PUBLIC	??0?$CTraceCategoryEx@$0CAAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<2097152,0>::CTraceCategoryEx<2097152,0>
PUBLIC	??0?$CTraceCategoryEx@$0BAAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<1048576,0>::CTraceCategoryEx<1048576,0>
PUBLIC	??0?$CTraceCategoryEx@$0EAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<262144,0>::CTraceCategoryEx<262144,0>
PUBLIC	??0?$CTraceCategoryEx@$0CAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<131072,0>::CTraceCategoryEx<131072,0>
PUBLIC	??0?$CTraceCategoryEx@$0BAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<65536,0>::CTraceCategoryEx<65536,0>
PUBLIC	??0?$CTraceCategoryEx@$0IAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<32768,0>::CTraceCategoryEx<32768,0>
PUBLIC	??0?$CTraceCategoryEx@$0EAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<16384,0>::CTraceCategoryEx<16384,0>
PUBLIC	??0?$CTraceCategoryEx@$0CAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<8192,0>::CTraceCategoryEx<8192,0>
PUBLIC	??0?$CTraceCategoryEx@$0BAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<4096,0>::CTraceCategoryEx<4096,0>
PUBLIC	??0?$CTraceCategoryEx@$0IAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<2048,0>::CTraceCategoryEx<2048,0>
PUBLIC	??0?$CTraceCategoryEx@$0EAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<1024,0>::CTraceCategoryEx<1024,0>
PUBLIC	??0?$CTraceCategoryEx@$0CAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<512,0>::CTraceCategoryEx<512,0>
PUBLIC	??0?$CTraceCategoryEx@$0BAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<256,0>::CTraceCategoryEx<256,0>
PUBLIC	??0?$CTraceCategoryEx@$0IA@$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<128,0>::CTraceCategoryEx<128,0>
PUBLIC	??0?$CTraceCategoryEx@$0EA@$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<64,0>::CTraceCategoryEx<64,0>
PUBLIC	??0?$CTraceCategoryEx@$0CA@$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<32,0>::CTraceCategoryEx<32,0>
PUBLIC	??0?$CTraceCategoryEx@$0BA@$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<16,0>::CTraceCategoryEx<16,0>
PUBLIC	??0?$CTraceCategoryEx@$07$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<8,0>::CTraceCategoryEx<8,0>
PUBLIC	??0?$CTraceCategoryEx@$03$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<4,0>::CTraceCategoryEx<4,0>
PUBLIC	??0?$CTraceCategoryEx@$01$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<2,0>::CTraceCategoryEx<2,0>
PUBLIC	??0?$CTraceCategoryEx@$00$0A@@ATL@@QAE@PB_W@Z	; ATL::CTraceCategoryEx<1,0>::CTraceCategoryEx<1,0>
PUBLIC	??0?$CTraceCategoryEx@$0IAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<524288,0>::CTraceCategoryEx<524288,0>
PUBLIC	??$AtlAdd@K@ATL@@YAJPAKKK@Z			; ATL::AtlAdd<unsigned long>
PUBLIC	??0?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
PUBLIC	?GetSize@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QBEHXZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::GetSize
PUBLIC	?RemoveAll@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEXXZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::RemoveAll
PUBLIC	??A?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEAAGH@Z ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::operator[]
PUBLIC	?__empty_global_delete@@YAXPAXI@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPAX@Z		; __empty_global_delete
PUBLIC	?OnStereo@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnStereo
PUBLIC	?OnRightChannel@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnRightChannel
PUBLIC	?OnLeftChannel@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnLeftChannel
PUBLIC	?OnDefRate@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnDefRate
PUBLIC	?OnRateDown@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnRateDown
PUBLIC	?OnRateUp@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnRateUp
PUBLIC	?On5Rewind@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::On5Rewind
PUBLIC	?On5Forward@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::On5Forward
PUBLIC	?OnAudioZero@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnAudioZero
PUBLIC	?OnAudioDown@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnAudioDown
PUBLIC	?OnAudioUp@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnAudioUp
PUBLIC	?OnExitFullScreen@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnExitFullScreen
PUBLIC	?OnFullScreen@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnFullScreen
PUBLIC	?OnStop@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnStop
PUBLIC	?OnBossKey@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ; CAcceleratorlImp::OnBossKey
PUBLIC	??0id@locale@std@@QAE@I@Z			; std::locale::id::id
PUBLIC	??_H@YGXPAXIIP6EPAX0@Z@Z			; `vector constructor iterator'
PUBLIC	??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ	; ATL::CComTypeInfoHolder::stringdispid::~stringdispid
PUBLIC	??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z ; ATL::CComTypeInfoHolder::stringdispid::`vector deleting destructor'
PUBLIC	??0CAtlStringMgrStaticInitializer@ATLImplementationDetails@ATL@@QAE@XZ ; ATL::ATLImplementationDetails::CAtlStringMgrStaticInitializer::CAtlStringMgrStaticInitializer
PUBLIC	??1CAtlStringMgr@ATL@@UAE@XZ			; ATL::CAtlStringMgr::~CAtlStringMgr
PUBLIC	??_GCAtlStringMgr@ATL@@UAEPAXI@Z		; ATL::CAtlStringMgr::`scalar deleting destructor'
PUBLIC	??__FstrHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strHeap''
PUBLIC	??__FstrMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strMgr''
PUBLIC	?Clone@CAtlStringMgr@ATL@@UAEPAUIAtlStringMgr@2@XZ ; ATL::CAtlStringMgr::Clone
PUBLIC	?GetNilString@CAtlStringMgr@ATL@@UAEPAUCStringData@2@XZ ; ATL::CAtlStringMgr::GetNilString
PUBLIC	?Reallocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@PAU32@HH@Z ; ATL::CAtlStringMgr::Reallocate
PUBLIC	?Free@CAtlStringMgr@ATL@@UAEXPAUCStringData@2@@Z ; ATL::CAtlStringMgr::Free
PUBLIC	?Allocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@HH@Z ; ATL::CAtlStringMgr::Allocate
PUBLIC	?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ ; ATL::CAtlStringMgr::GetInstance
PUBLIC	??0CAtlStringMgr@ATL@@QAE@PAUIAtlMemMgr@1@@Z	; ATL::CAtlStringMgr::CAtlStringMgr
PUBLIC	?SetManager@CNilStringData@ATL@@QAEXPAUIAtlStringMgr@2@@Z ; ATL::CNilStringData::SetManager
PUBLIC	??0CNilStringData@ATL@@QAE@XZ			; ATL::CNilStringData::CNilStringData
PUBLIC	?AddRef@CStringData@ATL@@QAEXXZ			; ATL::CStringData::AddRef
PUBLIC	??_GCWin32Heap@ATL@@UAEPAXI@Z			; ATL::CWin32Heap::`scalar deleting destructor'
PUBLIC	?GetSize@CWin32Heap@ATL@@UAEIPAX@Z		; ATL::CWin32Heap::GetSize
PUBLIC	?Reallocate@CWin32Heap@ATL@@UAEPAXPAXI@Z	; ATL::CWin32Heap::Reallocate
PUBLIC	?Free@CWin32Heap@ATL@@UAEXPAX@Z			; ATL::CWin32Heap::Free
PUBLIC	?Allocate@CWin32Heap@ATL@@UAEPAXI@Z		; ATL::CWin32Heap::Allocate
PUBLIC	??1CWin32Heap@ATL@@UAE@XZ			; ATL::CWin32Heap::~CWin32Heap
PUBLIC	??0CWin32Heap@ATL@@QAE@PAX@Z			; ATL::CWin32Heap::CWin32Heap
PUBLIC	?AtlWinModuleInit@ATL@@YGJPAU_ATL_WIN_MODULE70@1@@Z ; ATL::AtlWinModuleInit
PUBLIC	?AtlWinModuleTerm@ATL@@YGJPAU_ATL_WIN_MODULE70@1@PAUHINSTANCE__@@@Z ; ATL::AtlWinModuleTerm
PUBLIC	??1_ATL_WIN_MODULE70@ATL@@QAE@XZ		; ATL::_ATL_WIN_MODULE70::~_ATL_WIN_MODULE70
PUBLIC	??0_ATL_WIN_MODULE70@ATL@@QAE@XZ		; ATL::_ATL_WIN_MODULE70::_ATL_WIN_MODULE70
PUBLIC	?Term@CAtlWinModule@ATL@@QAEXXZ			; ATL::CAtlWinModule::Term
PUBLIC	??1CAtlWinModule@ATL@@QAE@XZ			; ATL::CAtlWinModule::~CAtlWinModule
PUBLIC	??0CAtlWinModule@ATL@@QAE@XZ			; ATL::CAtlWinModule::CAtlWinModule
PUBLIC	??1_ATL_COM_MODULE70@ATL@@QAE@XZ		; ATL::_ATL_COM_MODULE70::~_ATL_COM_MODULE70
PUBLIC	??0_ATL_COM_MODULE70@ATL@@QAE@XZ		; ATL::_ATL_COM_MODULE70::_ATL_COM_MODULE70
PUBLIC	?Term@CAtlComModule@ATL@@QAEXXZ			; ATL::CAtlComModule::Term
PUBLIC	??1CAtlComModule@ATL@@QAE@XZ			; ATL::CAtlComModule::~CAtlComModule
PUBLIC	??0CAtlComModule@ATL@@QAE@XZ			; ATL::CAtlComModule::CAtlComModule
PUBLIC	??1CComBSTR@ATL@@QAE@XZ				; ATL::CComBSTR::~CComBSTR
PUBLIC	?GetModuleInstance@CAtlBaseModule@ATL@@QAEPAUHINSTANCE__@@XZ ; ATL::CAtlBaseModule::GetModuleInstance
PUBLIC	?Term@CComCriticalSection@ATL@@QAEJXZ		; ATL::CComCriticalSection::Term
PUBLIC	?Init@CComCriticalSection@ATL@@QAEJXZ		; ATL::CComCriticalSection::Init
PUBLIC	??1CComCriticalSection@ATL@@QAE@XZ		; ATL::CComCriticalSection::~CComCriticalSection
PUBLIC	??0CComCriticalSection@ATL@@QAE@XZ		; ATL::CComCriticalSection::CComCriticalSection
PUBLIC	?_AtlInitializeCriticalSectionEx@ATL@@YAHPAU_RTL_CRITICAL_SECTION@@KK@Z ; ATL::_AtlInitializeCriticalSectionEx
PUBLIC	?_AtlRaiseException@ATL@@YAXKK@Z		; ATL::_AtlRaiseException
PUBLIC	??0CTraceCategory@ATL@@QAE@PB_W@Z		; ATL::CTraceCategory::CTraceCategory
PUBLIC	??$AtlMultiply@K@ATL@@YAJPAKKK@Z		; ATL::AtlMultiply<unsigned long>
PUBLIC	??$AtlMultiply@I@ATL@@YAJPAIII@Z		; ATL::AtlMultiply<unsigned int>
PUBLIC	_HRESULT_FROM_WIN32
PUBLIC	?atlTraceDBProvider@ATL@@3V?$CTraceCategoryEx@$0CAA@$0A@@1@A ; ATL::atlTraceDBProvider
PUBLIC	?atlTraceGeneral@ATL@@3V?$CTraceCategoryEx@$00$0A@@1@A ; ATL::atlTraceGeneral
PUBLIC	?atlTraceDBClient@ATL@@3V?$CTraceCategoryEx@$0BAA@$0A@@1@A ; ATL::atlTraceDBClient
PUBLIC	?atlTraceSnapin@ATL@@3V?$CTraceCategoryEx@$0EAA@$0A@@1@A ; ATL::atlTraceSnapin
PUBLIC	?_AtlWinModule@ATL@@3VCAtlWinModule@1@A		; ATL::_AtlWinModule
PUBLIC	?atlTraceQI@ATL@@3V?$CTraceCategoryEx@$03$0A@@1@A ; ATL::atlTraceQI
PUBLIC	?atlTraceException@ATL@@3V?$CTraceCategoryEx@$0CAAA@$0A@@1@A ; ATL::atlTraceException
PUBLIC	?atlTraceString@ATL@@3V?$CTraceCategoryEx@$0CAAAA@$0A@@1@A ; ATL::atlTraceString
PUBLIC	?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A	; std::codecvt<char,char,_Mbstatet>::id
PUBLIC	?atlTraceWindowing@ATL@@3V?$CTraceCategoryEx@$0CA@$0A@@1@A ; ATL::atlTraceWindowing
PUBLIC	?atlTraceStencil@ATL@@3V?$CTraceCategoryEx@$0BAAAA@$0A@@1@A ; ATL::atlTraceStencil
PUBLIC	?atlTraceCache@ATL@@3V?$CTraceCategoryEx@$0IAAA@$0A@@1@A ; ATL::atlTraceCache
PUBLIC	?atlTraceUtil@ATL@@3V?$CTraceCategoryEx@$0IAAAA@$0A@@1@A ; ATL::atlTraceUtil
PUBLIC	?atlTraceTime@ATL@@3V?$CTraceCategoryEx@$0EAAA@$0A@@1@A ; ATL::atlTraceTime
PUBLIC	?_AtlComModule@ATL@@3VCAtlComModule@1@A		; ATL::_AtlComModule
PUBLIC	?atlTraceISAPI@ATL@@3V?$CTraceCategoryEx@$0EAAAAA@$0A@@1@A ; ATL::atlTraceISAPI
PUBLIC	?atlTraceSync@ATL@@3V?$CTraceCategoryEx@$0CAAAAA@$0A@@1@A ; ATL::atlTraceSync
PUBLIC	?atlTraceMap@ATL@@3V?$CTraceCategoryEx@$0EAAAA@$0A@@1@A ; ATL::atlTraceMap
PUBLIC	?atlTraceNotImpl@ATL@@3V?$CTraceCategoryEx@$0IAA@$0A@@1@A ; ATL::atlTraceNotImpl
PUBLIC	?atlTraceSecurity@ATL@@3V?$CTraceCategoryEx@$0BAAAAA@$0A@@1@A ; ATL::atlTraceSecurity
PUBLIC	?atlTraceRefcount@ATL@@3V?$CTraceCategoryEx@$0BA@$0A@@1@A ; ATL::atlTraceRefcount
PUBLIC	?atlTraceHosting@ATL@@3V?$CTraceCategoryEx@$0IA@$0A@@1@A ; ATL::atlTraceHosting
PUBLIC	?InitializeCAtlStringMgr@ATLImplementationDetails@ATL@@3UCAtlStringMgrStaticInitializer@12@A ; ATL::ATLImplementationDetails::InitializeCAtlStringMgr
PUBLIC	?atlTraceRegistrar@ATL@@3V?$CTraceCategoryEx@$07$0A@@1@A ; ATL::atlTraceRegistrar
PUBLIC	?atlTraceAllocation@ATL@@3V?$CTraceCategoryEx@$0BAAA@$0A@@1@A ; ATL::atlTraceAllocation
PUBLIC	?atlTraceControls@ATL@@3V?$CTraceCategoryEx@$0EA@$0A@@1@A ; ATL::atlTraceControls
PUBLIC	?atlTraceCOM@ATL@@3V?$CTraceCategoryEx@$01$0A@@1@A ; ATL::atlTraceCOM
PUBLIC	__real@0000000000000000
PUBLIC	__real@3e45798ee2308c3a
PUBLIC	__real@3f000000
PUBLIC	__real@3f800000
PUBLIC	__real@3fb999999999999a
PUBLIC	__real@3fe0000000000000
PUBLIC	__real@3ff0000000000000
PUBLIC	__real@40000000
PUBLIC	__real@401c000000000000
PUBLIC	__real@4024000000000000
PUBLIC	__real@4034000000000000
PUBLIC	__real@403e99a027525461
PUBLIC	__real@4048000000000000
PUBLIC	__real@404dffdf3b645a1d
PUBLIC	__real@4059000000000000
PUBLIC	__real@405e866666666666
PUBLIC	__real@4076d40000000000
PUBLIC	__real@408f400000000000
PUBLIC	__real@4097d20000000000
PUBLIC	__real@40c3880000000000
PUBLIC	__real@40d8f84000000000
PUBLIC	__real@40e1d58800000000
PUBLIC	__real@413c7dd040000000
PUBLIC	__real@41547f8b20000000
PUBLIC	__real@4194997000000000
PUBLIC	__real@4197d78400000000
PUBLIC	__real@41a0000000000000
PUBLIC	__real@41ddcd6500000000
PUBLIC	__real@41dfffffffc00000
PUBLIC	__real@41efffffffe00000
PUBLIC	__real@4202a05f20000000
PUBLIC	__real@42540000
PUBLIC	__real@42b40000
PUBLIC	__real@42e7f907ca644000
PUBLIC	__real@42fa6401072fe000
PUBLIC	__real@43340000
PUBLIC	__real@43870000
PUBLIC	__real@43e0000000000000
PUBLIC	__real@546d42aea2879f2e
PUBLIC	__real@54b249ad2594c37d
PUBLIC	__real@7f800000
PUBLIC	__real@7fe1ccf385ebc8a0
PUBLIC	__real@8000000000000000
PUBLIC	__real@bf000000
PUBLIC	__real@bfe0000000000000
PUBLIC	__real@bff0000000000000
PUBLIC	__real@c1e0000000000000
PUBLIC	__real@c3e0000000000000
PUBLIC	__xmm@41f00000000000000000000000000000
PUBLIC	__xmm@80000000000000008000000000000000
EXTRN	??_ECWin32Heap@ATL@@UAEPAXI@Z:PROC		; ATL::CWin32Heap::`vector deleting destructor'
EXTRN	??_ECAtlStringMgr@ATL@@UAEPAXI@Z:PROC		; ATL::CAtlStringMgr::`vector deleting destructor'
EXTRN	@_RTC_AllocaHelper@12:PROC
EXTRN	@_RTC_CheckStackVars2@12:PROC
EXTRN	@_RTC_CheckStackVars@8:PROC
EXTRN	__CxxThrowException@8:PROC
EXTRN	__RTC_CheckEsp:PROC
EXTRN	__RTC_UninitUse:PROC
EXTRN	___RTDynamicCast:PROC
EXTRN	__alldiv:PROC
EXTRN	__allmul:PROC
EXTRN	__alloca_probe_16:PROC
EXTRN	__allrem:PROC
EXTRN	__allshl:PROC
EXTRN	__allshr:PROC
EXTRN	__aulldiv:PROC
EXTRN	__aulldvrm:PROC
EXTRN	__aullrem:PROC
EXTRN	__aullshr:PROC
EXTRN	__chkstk:PROC
EXTRN	__dtol3:PROC
EXTRN	__dtoui3:PROC
EXTRN	__dtoul3:PROC
EXTRN	__except_handler3:PROC
EXTRN	__ftoui3:PROC
EXTRN	__local_unwind2:PROC
EXTRN	__ltod3:PROC
EXTRN	__setjmp3:PROC
EXTRN	___security_cookie:DWORD
EXTRN	__fltused:DWORD
EXTRN	__tls_array:DWORD
EXTRN	__tls_index:DWORD
;	COMDAT ?atlTraceDBProvider@ATL@@3V?$CTraceCategoryEx@$0CAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceDBProvider@ATL@@3V?$CTraceCategoryEx@$0CAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceDBProvider
_BSS	ENDS
;	COMDAT ?atlTraceGeneral@ATL@@3V?$CTraceCategoryEx@$00$0A@@1@A
_BSS	SEGMENT
?atlTraceGeneral@ATL@@3V?$CTraceCategoryEx@$00$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceGeneral
_BSS	ENDS
;	COMDAT ?atlTraceDBClient@ATL@@3V?$CTraceCategoryEx@$0BAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceDBClient@ATL@@3V?$CTraceCategoryEx@$0BAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceDBClient
_BSS	ENDS
;	COMDAT ?atlTraceSnapin@ATL@@3V?$CTraceCategoryEx@$0EAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceSnapin@ATL@@3V?$CTraceCategoryEx@$0EAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceSnapin
_BSS	ENDS
;	COMDAT ?_AtlWinModule@ATL@@3VCAtlWinModule@1@A
_BSS	SEGMENT
?_AtlWinModule@ATL@@3VCAtlWinModule@1@A DB 02cH DUP (?)	; ATL::_AtlWinModule
_BSS	ENDS
;	COMDAT ?atlTraceQI@ATL@@3V?$CTraceCategoryEx@$03$0A@@1@A
_BSS	SEGMENT
?atlTraceQI@ATL@@3V?$CTraceCategoryEx@$03$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceQI
_BSS	ENDS
;	COMDAT ?atlTraceException@ATL@@3V?$CTraceCategoryEx@$0CAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceException@ATL@@3V?$CTraceCategoryEx@$0CAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceException
_BSS	ENDS
;	COMDAT ?atlTraceString@ATL@@3V?$CTraceCategoryEx@$0CAAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceString@ATL@@3V?$CTraceCategoryEx@$0CAAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceString
_BSS	ENDS
;	COMDAT ?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A
_BSS	SEGMENT
?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A DD 01H DUP (?) ; std::codecvt<char,char,_Mbstatet>::id
_BSS	ENDS
;	COMDAT ?atlTraceWindowing@ATL@@3V?$CTraceCategoryEx@$0CA@$0A@@1@A
_BSS	SEGMENT
?atlTraceWindowing@ATL@@3V?$CTraceCategoryEx@$0CA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceWindowing
_BSS	ENDS
;	COMDAT ?atlTraceStencil@ATL@@3V?$CTraceCategoryEx@$0BAAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceStencil@ATL@@3V?$CTraceCategoryEx@$0BAAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceStencil
_BSS	ENDS
;	COMDAT ?atlTraceCache@ATL@@3V?$CTraceCategoryEx@$0IAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceCache@ATL@@3V?$CTraceCategoryEx@$0IAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceCache
_BSS	ENDS
;	COMDAT ?atlTraceUtil@ATL@@3V?$CTraceCategoryEx@$0IAAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceUtil@ATL@@3V?$CTraceCategoryEx@$0IAAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceUtil
_BSS	ENDS
;	COMDAT ?atlTraceTime@ATL@@3V?$CTraceCategoryEx@$0EAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceTime@ATL@@3V?$CTraceCategoryEx@$0EAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceTime
_BSS	ENDS
;	COMDAT ?_AtlComModule@ATL@@3VCAtlComModule@1@A
_BSS	SEGMENT
?_AtlComModule@ATL@@3VCAtlComModule@1@A DB 028H DUP (?)	; ATL::_AtlComModule
_BSS	ENDS
;	COMDAT ?atlTraceISAPI@ATL@@3V?$CTraceCategoryEx@$0EAAAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceISAPI@ATL@@3V?$CTraceCategoryEx@$0EAAAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceISAPI
_BSS	ENDS
;	COMDAT ?atlTraceSync@ATL@@3V?$CTraceCategoryEx@$0CAAAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceSync@ATL@@3V?$CTraceCategoryEx@$0CAAAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceSync
_BSS	ENDS
;	COMDAT ?atlTraceMap@ATL@@3V?$CTraceCategoryEx@$0EAAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceMap@ATL@@3V?$CTraceCategoryEx@$0EAAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceMap
_BSS	ENDS
;	COMDAT ?atlTraceNotImpl@ATL@@3V?$CTraceCategoryEx@$0IAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceNotImpl@ATL@@3V?$CTraceCategoryEx@$0IAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceNotImpl
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT ?atlTraceSecurity@ATL@@3V?$CTraceCategoryEx@$0BAAAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceSecurity@ATL@@3V?$CTraceCategoryEx@$0BAAAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceSecurity
_BSS	ENDS
;	COMDAT ?atlTraceRefcount@ATL@@3V?$CTraceCategoryEx@$0BA@$0A@@1@A
_BSS	SEGMENT
?atlTraceRefcount@ATL@@3V?$CTraceCategoryEx@$0BA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceRefcount
_BSS	ENDS
;	COMDAT ?atlTraceHosting@ATL@@3V?$CTraceCategoryEx@$0IA@$0A@@1@A
_BSS	SEGMENT
?atlTraceHosting@ATL@@3V?$CTraceCategoryEx@$0IA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceHosting
_BSS	ENDS
;	COMDAT ?InitializeCAtlStringMgr@ATLImplementationDetails@ATL@@3UCAtlStringMgrStaticInitializer@12@A
_BSS	SEGMENT
?InitializeCAtlStringMgr@ATLImplementationDetails@ATL@@3UCAtlStringMgrStaticInitializer@12@A DB 01H DUP (?) ; ATL::ATLImplementationDetails::InitializeCAtlStringMgr
_BSS	ENDS
;	COMDAT ?atlTraceRegistrar@ATL@@3V?$CTraceCategoryEx@$07$0A@@1@A
_BSS	SEGMENT
?atlTraceRegistrar@ATL@@3V?$CTraceCategoryEx@$07$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceRegistrar
_BSS	ENDS
;	COMDAT ?atlTraceAllocation@ATL@@3V?$CTraceCategoryEx@$0BAAA@$0A@@1@A
_BSS	SEGMENT
?atlTraceAllocation@ATL@@3V?$CTraceCategoryEx@$0BAAA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceAllocation
_BSS	ENDS
;	COMDAT ?atlTraceControls@ATL@@3V?$CTraceCategoryEx@$0EA@$0A@@1@A
_BSS	SEGMENT
?atlTraceControls@ATL@@3V?$CTraceCategoryEx@$0EA@$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceControls
_BSS	ENDS
;	COMDAT ?atlTraceCOM@ATL@@3V?$CTraceCategoryEx@$01$0A@@1@A
_BSS	SEGMENT
?atlTraceCOM@ATL@@3V?$CTraceCategoryEx@$01$0A@@1@A DD 01H DUP (?) ; ATL::atlTraceCOM
_BSS	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceGeneral$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceGeneral@ATL@@YAXXZ ; ATL::atlTraceGeneral$initializer$
;	COMDAT __xmm@80000000000000008000000000000000
CONST	SEGMENT
__xmm@80000000000000008000000000000000 DB 00H, 00H, 00H, 00H, 00H, 00H, 00H
	DB	080H, 00H, 00H, 00H, 00H, 00H, 00H, 00H, 080H
CONST	ENDS
;	COMDAT __xmm@41f00000000000000000000000000000
CONST	SEGMENT
__xmm@41f00000000000000000000000000000 DB 00H, 00H, 00H, 00H, 00H, 00H, 00H
	DB	00H, 00H, 00H, 00H, 00H, 00H, 00H, 0f0H, 'A'
CONST	ENDS
;	COMDAT __real@c3e0000000000000
CONST	SEGMENT
__real@c3e0000000000000 DQ 0c3e0000000000000r	; -9.22337e+18
CONST	ENDS
;	COMDAT __real@c1e0000000000000
CONST	SEGMENT
__real@c1e0000000000000 DQ 0c1e0000000000000r	; -2.14748e+09
CONST	ENDS
;	COMDAT __real@bff0000000000000
CONST	SEGMENT
__real@bff0000000000000 DQ 0bff0000000000000r	; -1
CONST	ENDS
;	COMDAT __real@bfe0000000000000
CONST	SEGMENT
__real@bfe0000000000000 DQ 0bfe0000000000000r	; -0.5
CONST	ENDS
;	COMDAT __real@bf000000
CONST	SEGMENT
__real@bf000000 DD 0bf000000r			; -0.5
CONST	ENDS
;	COMDAT __real@8000000000000000
CONST	SEGMENT
__real@8000000000000000 DQ 08000000000000000r	; -0
CONST	ENDS
;	COMDAT __real@7fe1ccf385ebc8a0
CONST	SEGMENT
__real@7fe1ccf385ebc8a0 DQ 07fe1ccf385ebc8a0r	; 1e+308
CONST	ENDS
;	COMDAT __real@7f800000
CONST	SEGMENT
__real@7f800000 DD 07f800000r			; inf
CONST	ENDS
;	COMDAT __real@54b249ad2594c37d
CONST	SEGMENT
__real@54b249ad2594c37d DQ 054b249ad2594c37dr	; 1e+100
CONST	ENDS
;	COMDAT __real@546d42aea2879f2e
CONST	SEGMENT
__real@546d42aea2879f2e DQ 0546d42aea2879f2er	; 5e+98
CONST	ENDS
;	COMDAT __real@43e0000000000000
CONST	SEGMENT
__real@43e0000000000000 DQ 043e0000000000000r	; 9.22337e+18
CONST	ENDS
;	COMDAT __real@43870000
CONST	SEGMENT
__real@43870000 DD 043870000r			; 270
CONST	ENDS
;	COMDAT __real@43340000
CONST	SEGMENT
__real@43340000 DD 043340000r			; 180
CONST	ENDS
;	COMDAT __real@42fa6401072fe000
CONST	SEGMENT
__real@42fa6401072fe000 DQ 042fa6401072fe000r	; 4.64269e+14
CONST	ENDS
;	COMDAT __real@42e7f907ca644000
CONST	SEGMENT
__real@42e7f907ca644000 DQ 042e7f907ca644000r	; 2.10867e+14
CONST	ENDS
;	COMDAT __real@42b40000
CONST	SEGMENT
__real@42b40000 DD 042b40000r			; 90
CONST	ENDS
;	COMDAT __real@42540000
CONST	SEGMENT
__real@42540000 DD 042540000r			; 53
CONST	ENDS
;	COMDAT __real@4202a05f20000000
CONST	SEGMENT
__real@4202a05f20000000 DQ 04202a05f20000000r	; 1e+10
CONST	ENDS
;	COMDAT __real@41efffffffe00000
CONST	SEGMENT
__real@41efffffffe00000 DQ 041efffffffe00000r	; 4.29497e+09
CONST	ENDS
;	COMDAT __real@41dfffffffc00000
CONST	SEGMENT
__real@41dfffffffc00000 DQ 041dfffffffc00000r	; 2.14748e+09
CONST	ENDS
;	COMDAT __real@41ddcd6500000000
CONST	SEGMENT
__real@41ddcd6500000000 DQ 041ddcd6500000000r	; 2e+09
CONST	ENDS
;	COMDAT __real@41a0000000000000
CONST	SEGMENT
__real@41a0000000000000 DQ 041a0000000000000r	; 1.34218e+08
CONST	ENDS
;	COMDAT __real@4197d78400000000
CONST	SEGMENT
__real@4197d78400000000 DQ 04197d78400000000r	; 1e+08
CONST	ENDS
;	COMDAT __real@4194997000000000
CONST	SEGMENT
__real@4194997000000000 DQ 04194997000000000r	; 8.64e+07
CONST	ENDS
;	COMDAT __real@41547f8b20000000
CONST	SEGMENT
__real@41547f8b20000000 DQ 041547f8b20000000r	; 5.37348e+06
CONST	ENDS
;	COMDAT __real@413c7dd040000000
CONST	SEGMENT
__real@413c7dd040000000 DQ 0413c7dd040000000r	; 1.86722e+06
CONST	ENDS
;	COMDAT __real@40e1d58800000000
CONST	SEGMENT
__real@40e1d58800000000 DQ 040e1d58800000000r	; 36524.3
CONST	ENDS
;	COMDAT __real@40d8f84000000000
CONST	SEGMENT
__real@40d8f84000000000 DQ 040d8f84000000000r	; 25569
CONST	ENDS
;	COMDAT __real@40c3880000000000
CONST	SEGMENT
__real@40c3880000000000 DQ 040c3880000000000r	; 10000
CONST	ENDS
;	COMDAT __real@4097d20000000000
CONST	SEGMENT
__real@4097d20000000000 DQ 04097d20000000000r	; 1524.5
CONST	ENDS
;	COMDAT __real@408f400000000000
CONST	SEGMENT
__real@408f400000000000 DQ 0408f400000000000r	; 1000
CONST	ENDS
;	COMDAT __real@4076d40000000000
CONST	SEGMENT
__real@4076d40000000000 DQ 04076d40000000000r	; 365.25
CONST	ENDS
;	COMDAT __real@405e866666666666
CONST	SEGMENT
__real@405e866666666666 DQ 0405e866666666666r	; 122.1
CONST	ENDS
;	COMDAT __real@4059000000000000
CONST	SEGMENT
__real@4059000000000000 DQ 04059000000000000r	; 100
CONST	ENDS
;	COMDAT __real@404dffdf3b645a1d
CONST	SEGMENT
__real@404dffdf3b645a1d DQ 0404dffdf3b645a1dr	; 59.999
CONST	ENDS
;	COMDAT __real@4048000000000000
CONST	SEGMENT
__real@4048000000000000 DQ 04048000000000000r	; 48
CONST	ENDS
;	COMDAT __real@403e99a027525461
CONST	SEGMENT
__real@403e99a027525461 DQ 0403e99a027525461r	; 30.6001
CONST	ENDS
;	COMDAT __real@4034000000000000
CONST	SEGMENT
__real@4034000000000000 DQ 04034000000000000r	; 20
CONST	ENDS
;	COMDAT __real@4024000000000000
CONST	SEGMENT
__real@4024000000000000 DQ 04024000000000000r	; 10
CONST	ENDS
;	COMDAT __real@401c000000000000
CONST	SEGMENT
__real@401c000000000000 DQ 0401c000000000000r	; 7
CONST	ENDS
;	COMDAT __real@40000000
CONST	SEGMENT
__real@40000000 DD 040000000r			; 2
CONST	ENDS
;	COMDAT __real@3ff0000000000000
CONST	SEGMENT
__real@3ff0000000000000 DQ 03ff0000000000000r	; 1
CONST	ENDS
;	COMDAT __real@3fe0000000000000
CONST	SEGMENT
__real@3fe0000000000000 DQ 03fe0000000000000r	; 0.5
CONST	ENDS
;	COMDAT __real@3fb999999999999a
CONST	SEGMENT
__real@3fb999999999999a DQ 03fb999999999999ar	; 0.1
CONST	ENDS
;	COMDAT __real@3f800000
CONST	SEGMENT
__real@3f800000 DD 03f800000r			; 1
CONST	ENDS
;	COMDAT __real@3f000000
CONST	SEGMENT
__real@3f000000 DD 03f000000r			; 0.5
CONST	ENDS
;	COMDAT __real@3e45798ee2308c3a
CONST	SEGMENT
__real@3e45798ee2308c3a DQ 03e45798ee2308c3ar	; 1e-08
CONST	ENDS
;	COMDAT __real@0000000000000000
CONST	SEGMENT
__real@0000000000000000 DQ 00000000000000000r	; 0
CONST	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0CAtlComModule@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0CAtlComModule@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0CAtlComModule@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0CAtlComModule@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1CAtlComModule@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1CAtlComModule@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1CAtlComModule@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1CAtlComModule@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0_ATL_COM_MODULE70@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0_ATL_COM_MODULE70@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??0_ATL_COM_MODULE70@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0_ATL_COM_MODULE70@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1_ATL_COM_MODULE70@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1_ATL_COM_MODULE70@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1_ATL_COM_MODULE70@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1_ATL_COM_MODULE70@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0CAtlWinModule@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0CAtlWinModule@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0CAtlWinModule@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0CAtlWinModule@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1CAtlWinModule@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1CAtlWinModule@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1CAtlWinModule@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1CAtlWinModule@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ$1
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ$1
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ$0
	DD	0ffffffffH
	DD	FLAT:__unwindfunclet$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ$1
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ DD 019930522H
	DD	00H
	DD	00H
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
xdata$x	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceCOM$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceCOM@ATL@@YAXXZ ; ATL::atlTraceCOM$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceQI$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceQI@ATL@@YAXXZ ; ATL::atlTraceQI$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceRegistrar$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceRegistrar@ATL@@YAXXZ ; ATL::atlTraceRegistrar$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceRefcount$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceRefcount@ATL@@YAXXZ ; ATL::atlTraceRefcount$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceWindowing$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceWindowing@ATL@@YAXXZ ; ATL::atlTraceWindowing$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceControls$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceControls@ATL@@YAXXZ ; ATL::atlTraceControls$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceHosting$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceHosting@ATL@@YAXXZ ; ATL::atlTraceHosting$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceDBClient$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceDBClient@ATL@@YAXXZ ; ATL::atlTraceDBClient$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceDBProvider$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceDBProvider@ATL@@YAXXZ ; ATL::atlTraceDBProvider$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceSnapin$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceSnapin@ATL@@YAXXZ ; ATL::atlTraceSnapin$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceNotImpl$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceNotImpl@ATL@@YAXXZ ; ATL::atlTraceNotImpl$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceAllocation$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceAllocation@ATL@@YAXXZ ; ATL::atlTraceAllocation$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceException$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceException@ATL@@YAXXZ ; ATL::atlTraceException$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceTime$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceTime@ATL@@YAXXZ ; ATL::atlTraceTime$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceCache$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceCache@ATL@@YAXXZ ; ATL::atlTraceCache$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceStencil$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceStencil@ATL@@YAXXZ ; ATL::atlTraceStencil$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceString$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceString@ATL@@YAXXZ ; ATL::atlTraceString$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceMap$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceMap@ATL@@YAXXZ ; ATL::atlTraceMap$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceUtil$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceUtil@ATL@@YAXXZ ; ATL::atlTraceUtil$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceSecurity$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceSecurity@ATL@@YAXXZ ; ATL::atlTraceSecurity$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceSync$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceSync@ATL@@YAXXZ ; ATL::atlTraceSync$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?atlTraceISAPI$initializer$@ATL@@3P6AXXZA DD FLAT:??__EatlTraceISAPI@ATL@@YAXXZ ; ATL::atlTraceISAPI$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?_AtlComModule$initializer$@ATL@@3P6AXXZA DD FLAT:??__E_AtlComModule@ATL@@YAXXZ ; ATL::_AtlComModule$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?_AtlWinModule$initializer$@ATL@@3P6AXXZA DD FLAT:??__E_AtlWinModule@ATL@@YAXXZ ; ATL::_AtlWinModule$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
?InitializeCAtlStringMgr$initializer$@ATLImplementationDetails@ATL@@3P6AXXZA DD FLAT:??__EInitializeCAtlStringMgr@ATLImplementationDetails@ATL@@YAXXZ ; ATL::ATLImplementationDetails::InitializeCAtlStringMgr$initializer$
CRT$XCU	ENDS
CRT$XCU	SEGMENT
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??id$initializer$@?$codecvt@DDU_Mbstatet@@@std@@2P6AXXZA@@3P6AXXZA DD FLAT:??__E?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A@@YAXXZ ; ??id$initializer$@?$codecvt@DDU_Mbstatet@@@std@@2P6AXXZA@@3P6AXXZA
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
CRT$XCU	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft sdks\windows\v7.1a\include\winerror.h
;	COMDAT _HRESULT_FROM_WIN32
_TEXT	SEGMENT
tv68 = -4						; size = 4
_x$ = 8							; size = 4
_HRESULT_FROM_WIN32 PROC				; COMDAT

; 23841: FORCEINLINE HRESULT HRESULT_FROM_WIN32(unsigned long x) { return (HRESULT)(x) <= 0 ? (HRESULT)(x) : (HRESULT) (((x) & 0x0000FFFF) | (FACILITY_WIN32 << 16) | 0x80000000);}

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	83 7d 08 00	 cmp	 DWORD PTR _x$[ebp], 0
  0000f	7f 08		 jg	 SHORT $LN3@HRESULT_FR
  00011	8b 45 08	 mov	 eax, DWORD PTR _x$[ebp]
  00014	89 45 fc	 mov	 DWORD PTR tv68[ebp], eax
  00017	eb 18		 jmp	 SHORT $LN4@HRESULT_FR
$LN3@HRESULT_FR:
  00019	8b 4d 08	 mov	 ecx, DWORD PTR _x$[ebp]
  0001c	81 e1 ff ff 00
	00		 and	 ecx, 65535		; 0000ffffH
  00022	81 c9 00 00 07
	00		 or	 ecx, 458752		; 00070000H
  00028	81 c9 00 00 00
	80		 or	 ecx, -2147483648	; 80000000H
  0002e	89 4d fc	 mov	 DWORD PTR tv68[ebp], ecx
$LN4@HRESULT_FR:
  00031	8b 45 fc	 mov	 eax, DWORD PTR tv68[ebp]
  00034	8b e5		 mov	 esp, ebp
  00036	5d		 pop	 ebp
  00037	c3		 ret	 0
_HRESULT_FROM_WIN32 ENDP
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlalloc.h
;	COMDAT ??$AtlMultiply@I@ATL@@YAJPAIII@Z
_TEXT	SEGMENT
_i64Result$ = -8					; size = 8
_piResult$ = 8						; size = 4
_iLeft$ = 12						; size = 4
_iRight$ = 16						; size = 4
??$AtlMultiply@I@ATL@@YAJPAIII@Z PROC			; ATL::AtlMultiply<unsigned int>, COMDAT

; 135  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 136  : 	unsigned __int64 i64Result=static_cast<unsigned __int64>(iLeft) * static_cast<unsigned __int64>(iRight);

  00014	8b 45 0c	 mov	 eax, DWORD PTR _iLeft$[ebp]
  00017	f7 65 10	 mul	 DWORD PTR _iRight$[ebp]
  0001a	89 45 f8	 mov	 DWORD PTR _i64Result$[ebp], eax
  0001d	89 55 fc	 mov	 DWORD PTR _i64Result$[ebp+4], edx

; 137  : 	if(i64Result>UINT_MAX)

  00020	83 7d fc 00	 cmp	 DWORD PTR _i64Result$[ebp+4], 0
  00024	77 06		 ja	 SHORT $LN4@AtlMultipl
  00026	83 7d f8 ff	 cmp	 DWORD PTR _i64Result$[ebp], -1
  0002a	76 0f		 jbe	 SHORT $LN2@AtlMultipl
$LN4@AtlMultipl:

; 138  : 	{
; 139  : 		return HRESULT_FROM_WIN32(ERROR_ARITHMETIC_OVERFLOW);

  0002c	68 16 02 00 00	 push	 534			; 00000216H
  00031	e8 00 00 00 00	 call	 _HRESULT_FROM_WIN32
  00036	83 c4 04	 add	 esp, 4
  00039	eb 0a		 jmp	 SHORT $LN1@AtlMultipl
$LN2@AtlMultipl:

; 140  : 	}
; 141  : 	*piResult=static_cast<unsigned int>(i64Result);

  0003b	8b 45 f8	 mov	 eax, DWORD PTR _i64Result$[ebp]
  0003e	8b 4d 08	 mov	 ecx, DWORD PTR _piResult$[ebp]
  00041	89 01		 mov	 DWORD PTR [ecx], eax

; 142  : 	return S_OK;

  00043	33 c0		 xor	 eax, eax
$LN1@AtlMultipl:

; 143  : }

  00045	83 c4 08	 add	 esp, 8
  00048	3b ec		 cmp	 ebp, esp
  0004a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004f	8b e5		 mov	 esp, ebp
  00051	5d		 pop	 ebp
  00052	c3		 ret	 0
??$AtlMultiply@I@ATL@@YAJPAIII@Z ENDP			; ATL::AtlMultiply<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlalloc.h
;	COMDAT ??$AtlMultiply@K@ATL@@YAJPAKKK@Z
_TEXT	SEGMENT
_i64Result$ = -8					; size = 8
_piResult$ = 8						; size = 4
_iLeft$ = 12						; size = 4
_iRight$ = 16						; size = 4
??$AtlMultiply@K@ATL@@YAJPAKKK@Z PROC			; ATL::AtlMultiply<unsigned long>, COMDAT

; 165  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 166  : 	unsigned __int64 i64Result=static_cast<unsigned __int64>(iLeft) * static_cast<unsigned __int64>(iRight);

  00014	8b 45 0c	 mov	 eax, DWORD PTR _iLeft$[ebp]
  00017	f7 65 10	 mul	 DWORD PTR _iRight$[ebp]
  0001a	89 45 f8	 mov	 DWORD PTR _i64Result$[ebp], eax
  0001d	89 55 fc	 mov	 DWORD PTR _i64Result$[ebp+4], edx

; 167  : 	if(i64Result>ULONG_MAX)

  00020	83 7d fc 00	 cmp	 DWORD PTR _i64Result$[ebp+4], 0
  00024	77 06		 ja	 SHORT $LN4@AtlMultipl
  00026	83 7d f8 ff	 cmp	 DWORD PTR _i64Result$[ebp], -1
  0002a	76 0f		 jbe	 SHORT $LN2@AtlMultipl
$LN4@AtlMultipl:

; 168  : 	{
; 169  : 		return HRESULT_FROM_WIN32(ERROR_ARITHMETIC_OVERFLOW);

  0002c	68 16 02 00 00	 push	 534			; 00000216H
  00031	e8 00 00 00 00	 call	 _HRESULT_FROM_WIN32
  00036	83 c4 04	 add	 esp, 4
  00039	eb 0a		 jmp	 SHORT $LN1@AtlMultipl
$LN2@AtlMultipl:

; 170  : 	}
; 171  : 	*piResult=static_cast<unsigned long>(i64Result);

  0003b	8b 45 f8	 mov	 eax, DWORD PTR _i64Result$[ebp]
  0003e	8b 4d 08	 mov	 ecx, DWORD PTR _piResult$[ebp]
  00041	89 01		 mov	 DWORD PTR [ecx], eax

; 172  : 	return S_OK;

  00043	33 c0		 xor	 eax, eax
$LN1@AtlMultipl:

; 173  : }

  00045	83 c4 08	 add	 esp, 8
  00048	3b ec		 cmp	 ebp, esp
  0004a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004f	8b e5		 mov	 esp, ebp
  00051	5d		 pop	 ebp
  00052	c3		 ret	 0
??$AtlMultiply@K@ATL@@YAJPAKKK@Z ENDP			; ATL::AtlMultiply<unsigned long>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0CTraceCategory@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0CTraceCategory@ATL@@QAE@PB_W@Z PROC			; ATL::CTraceCategory::CTraceCategory, COMDAT
; _this$ = ecx

; 93   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 92   : 	CTraceCategory(_In_z_ LPCTSTR pszCategoryName = nullptr) : CTraceCategoryEx(pszCategoryName)

  0000e	8b 45 08	 mov	 eax, DWORD PTR _pszCategoryName$[ebp]
  00011	50		 push	 eax
  00012	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00015	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0IAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<524288,0>::CTraceCategoryEx<524288,0>

; 94   : 	}

  0001a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001d	83 c4 04	 add	 esp, 4
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	8b e5		 mov	 esp, ebp
  00029	5d		 pop	 ebp
  0002a	c2 04 00	 ret	 4
??0CTraceCategory@ATL@@QAE@PB_W@Z ENDP			; ATL::CTraceCategory::CTraceCategory
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceGeneral@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceGeneral@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceGeneral'', COMDAT

; 97   : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceGeneral> atlTraceGeneral(_T("atlTraceGeneral"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967291
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceGeneral@ATL@@3V?$CTraceCategoryEx@$00$0A@@1@A ; ATL::atlTraceGeneral
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$00$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<1,0>::CTraceCategoryEx<1,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceGeneral@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceGeneral''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceCOM@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceCOM@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceCOM'', COMDAT

; 98   : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceCom> atlTraceCOM(_T("atlTraceCOM"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967290
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceCOM@ATL@@3V?$CTraceCategoryEx@$01$0A@@1@A ; ATL::atlTraceCOM
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$01$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<2,0>::CTraceCategoryEx<2,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceCOM@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceCOM''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceQI@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceQI@ATL@@YAXXZ PROC				; ATL::`dynamic initializer for 'atlTraceQI'', COMDAT

; 99   : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceQI> atlTraceQI(_T("atlTraceQI"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967289
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceQI@ATL@@3V?$CTraceCategoryEx@$03$0A@@1@A ; ATL::atlTraceQI
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$03$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<4,0>::CTraceCategoryEx<4,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceQI@ATL@@YAXXZ ENDP				; ATL::`dynamic initializer for 'atlTraceQI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceRegistrar@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceRegistrar@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceRegistrar'', COMDAT

; 100  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceRegistrar> atlTraceRegistrar(_T("atlTraceRegistrar"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967288
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceRegistrar@ATL@@3V?$CTraceCategoryEx@$07$0A@@1@A ; ATL::atlTraceRegistrar
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$07$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<8,0>::CTraceCategoryEx<8,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceRegistrar@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceRegistrar''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceRefcount@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceRefcount@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceRefcount'', COMDAT

; 101  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceRefcount> atlTraceRefcount(_T("atlTraceRefcount"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967287
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceRefcount@ATL@@3V?$CTraceCategoryEx@$0BA@$0A@@1@A ; ATL::atlTraceRefcount
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0BA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<16,0>::CTraceCategoryEx<16,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceRefcount@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceRefcount''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceWindowing@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceWindowing@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceWindowing'', COMDAT

; 102  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceWindowing> atlTraceWindowing(_T("atlTraceWindowing"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967286
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceWindowing@ATL@@3V?$CTraceCategoryEx@$0CA@$0A@@1@A ; ATL::atlTraceWindowing
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0CA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<32,0>::CTraceCategoryEx<32,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceWindowing@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceWindowing''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceControls@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceControls@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceControls'', COMDAT

; 103  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceControls> atlTraceControls(_T("atlTraceControls"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967285
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceControls@ATL@@3V?$CTraceCategoryEx@$0EA@$0A@@1@A ; ATL::atlTraceControls
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0EA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<64,0>::CTraceCategoryEx<64,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceControls@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceControls''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceHosting@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceHosting@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceHosting'', COMDAT

; 104  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceHosting> atlTraceHosting(_T("atlTraceHosting"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967284
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceHosting@ATL@@3V?$CTraceCategoryEx@$0IA@$0A@@1@A ; ATL::atlTraceHosting
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0IA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<128,0>::CTraceCategoryEx<128,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceHosting@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceHosting''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceDBClient@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceDBClient@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceDBClient'', COMDAT

; 105  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceDBClient> atlTraceDBClient(_T("atlTraceDBClient"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967283
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceDBClient@ATL@@3V?$CTraceCategoryEx@$0BAA@$0A@@1@A ; ATL::atlTraceDBClient
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0BAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<256,0>::CTraceCategoryEx<256,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceDBClient@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceDBClient''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceDBProvider@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceDBProvider@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceDBProvider'', COMDAT

; 106  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceDBProvider> atlTraceDBProvider(_T("atlTraceDBProvider"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967282
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceDBProvider@ATL@@3V?$CTraceCategoryEx@$0CAA@$0A@@1@A ; ATL::atlTraceDBProvider
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0CAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<512,0>::CTraceCategoryEx<512,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceDBProvider@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceDBProvider''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceSnapin@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceSnapin@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceSnapin'', COMDAT

; 107  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceSnapin> atlTraceSnapin(_T("atlTraceSnapin"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967281
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceSnapin@ATL@@3V?$CTraceCategoryEx@$0EAA@$0A@@1@A ; ATL::atlTraceSnapin
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0EAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<1024,0>::CTraceCategoryEx<1024,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceSnapin@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceSnapin''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceNotImpl@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceNotImpl@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceNotImpl'', COMDAT

; 108  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceNotImpl> atlTraceNotImpl(_T("atlTraceNotImpl"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967280
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceNotImpl@ATL@@3V?$CTraceCategoryEx@$0IAA@$0A@@1@A ; ATL::atlTraceNotImpl
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0IAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<2048,0>::CTraceCategoryEx<2048,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceNotImpl@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceNotImpl''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceAllocation@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceAllocation@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceAllocation'', COMDAT

; 109  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceAllocation> atlTraceAllocation(_T("atlTraceAllocation"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967279
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceAllocation@ATL@@3V?$CTraceCategoryEx@$0BAAA@$0A@@1@A ; ATL::atlTraceAllocation
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0BAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<4096,0>::CTraceCategoryEx<4096,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceAllocation@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceAllocation''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceException@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceException@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceException'', COMDAT

; 110  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceException> atlTraceException(_T("atlTraceException"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967278
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceException@ATL@@3V?$CTraceCategoryEx@$0CAAA@$0A@@1@A ; ATL::atlTraceException
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0CAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<8192,0>::CTraceCategoryEx<8192,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceException@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceException''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceTime@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceTime@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceTime'', COMDAT

; 111  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceTime> atlTraceTime(_T("atlTraceTime"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967277
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceTime@ATL@@3V?$CTraceCategoryEx@$0EAAA@$0A@@1@A ; ATL::atlTraceTime
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0EAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<16384,0>::CTraceCategoryEx<16384,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceTime@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceTime''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceCache@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceCache@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceCache'', COMDAT

; 112  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceCache> atlTraceCache(_T("atlTraceCache"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967276
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceCache@ATL@@3V?$CTraceCategoryEx@$0IAAA@$0A@@1@A ; ATL::atlTraceCache
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0IAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<32768,0>::CTraceCategoryEx<32768,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceCache@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceCache''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceStencil@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceStencil@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceStencil'', COMDAT

; 113  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceStencil> atlTraceStencil(_T("atlTraceStencil"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967275
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceStencil@ATL@@3V?$CTraceCategoryEx@$0BAAAA@$0A@@1@A ; ATL::atlTraceStencil
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0BAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<65536,0>::CTraceCategoryEx<65536,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceStencil@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceStencil''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceString@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceString@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceString'', COMDAT

; 114  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceString> atlTraceString(_T("atlTraceString"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967274
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceString@ATL@@3V?$CTraceCategoryEx@$0CAAAA@$0A@@1@A ; ATL::atlTraceString
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0CAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<131072,0>::CTraceCategoryEx<131072,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceString@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceString''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceMap@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceMap@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceMap'', COMDAT

; 115  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceMap> atlTraceMap(_T("atlTraceMap"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967273
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceMap@ATL@@3V?$CTraceCategoryEx@$0EAAAA@$0A@@1@A ; ATL::atlTraceMap
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0EAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<262144,0>::CTraceCategoryEx<262144,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceMap@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceMap''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceUtil@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUtil@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceUtil'', COMDAT

; 116  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceUtil> atlTraceUtil(_T("atlTraceUtil"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967272
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUtil@ATL@@3V?$CTraceCategoryEx@$0IAAAA@$0A@@1@A ; ATL::atlTraceUtil
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0IAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<524288,0>::CTraceCategoryEx<524288,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceUtil@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceUtil''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceSecurity@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceSecurity@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceSecurity'', COMDAT

; 117  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceSecurity> atlTraceSecurity(_T("atlTraceSecurity"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967271
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceSecurity@ATL@@3V?$CTraceCategoryEx@$0BAAAAA@$0A@@1@A ; ATL::atlTraceSecurity
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0BAAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<1048576,0>::CTraceCategoryEx<1048576,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceSecurity@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceSecurity''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceSync@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceSync@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceSync'', COMDAT

; 118  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceSync> atlTraceSync(_T("atlTraceSync"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967270
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceSync@ATL@@3V?$CTraceCategoryEx@$0CAAAAA@$0A@@1@A ; ATL::atlTraceSync
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0CAAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<2097152,0>::CTraceCategoryEx<2097152,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceSync@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceSync''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??__EatlTraceISAPI@ATL@@YAXXZ
text$di	SEGMENT
??__EatlTraceISAPI@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for 'atlTraceISAPI'', COMDAT

; 119  : __declspec(selectany) CTraceCategoryEx<CTraceCategoryEx<>::TraceISAPI> atlTraceISAPI(_T("atlTraceISAPI"));

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	68 00 00 00 00	 push	 OFFSET $SG4294967269
  00008	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceISAPI@ATL@@3V?$CTraceCategoryEx@$0EAAAAA@$0A@@1@A ; ATL::atlTraceISAPI
  0000d	e8 00 00 00 00	 call	 ??0?$CTraceCategoryEx@$0EAAAAA@$0A@@ATL@@QAE@PB_W@Z ; ATL::CTraceCategoryEx<4194304,0>::CTraceCategoryEx<4194304,0>
  00012	3b ec		 cmp	 ebp, esp
  00014	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
??__EatlTraceISAPI@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for 'atlTraceISAPI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlexcept.h
;	COMDAT ?_AtlRaiseException@ATL@@YAXKK@Z
_TEXT	SEGMENT
_dwExceptionCode$ = 8					; size = 4
_dwExceptionFlags$ = 12					; size = 4
?_AtlRaiseException@ATL@@YAXKK@Z PROC			; ATL::_AtlRaiseException, COMDAT

; 30   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 31   : 	RaiseException( dwExceptionCode, dwExceptionFlags, 0, NULL );

  00004	8b f4		 mov	 esi, esp
  00006	6a 00		 push	 0
  00008	6a 00		 push	 0
  0000a	8b 45 0c	 mov	 eax, DWORD PTR _dwExceptionFlags$[ebp]
  0000d	50		 push	 eax
  0000e	8b 4d 08	 mov	 ecx, DWORD PTR _dwExceptionCode$[ebp]
  00011	51		 push	 ecx
  00012	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__RaiseException@16
  00018	3b f4		 cmp	 esi, esp
  0001a	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 32   : }

  0001f	5e		 pop	 esi
  00020	3b ec		 cmp	 ebp, esp
  00022	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00027	5d		 pop	 ebp
  00028	c3		 ret	 0
?_AtlRaiseException@ATL@@YAXKK@Z ENDP			; ATL::_AtlRaiseException
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlwinverapi.h
;	COMDAT ?_AtlInitializeCriticalSectionEx@ATL@@YAHPAU_RTL_CRITICAL_SECTION@@KK@Z
_TEXT	SEGMENT
_lpCriticalSection$ = 8					; size = 4
_dwSpinCount$ = 12					; size = 4
_Flags$ = 16						; size = 4
?_AtlInitializeCriticalSectionEx@ATL@@YAHPAU_RTL_CRITICAL_SECTION@@KK@Z PROC ; ATL::_AtlInitializeCriticalSectionEx, COMDAT

; 727  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	56		 push	 esi

; 728  : #if (NTDDI_VERSION >= NTDDI_VISTA) && !defined(_USING_V110_SDK71_) && !defined(_ATL_XP_TARGETING)
; 729  : 	// InitializeCriticalSectionEx is available in Vista or later, desktop or store apps
; 730  : 	return ::InitializeCriticalSectionEx(lpCriticalSection, dwSpinCount, Flags);
; 731  : #else
; 732  : 	UNREFERENCED_PARAMETER(Flags);
; 733  : 
; 734  : 	// ...otherwise fall back to using InitializeCriticalSectionAndSpinCount.
; 735  : 	return ::InitializeCriticalSectionAndSpinCount(lpCriticalSection, dwSpinCount);

  00004	8b f4		 mov	 esi, esp
  00006	8b 45 0c	 mov	 eax, DWORD PTR _dwSpinCount$[ebp]
  00009	50		 push	 eax
  0000a	8b 4d 08	 mov	 ecx, DWORD PTR _lpCriticalSection$[ebp]
  0000d	51		 push	 ecx
  0000e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__InitializeCriticalSectionAndSpinCount@8
  00014	3b f4		 cmp	 esi, esp
  00016	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 736  : #endif
; 737  : }

  0001b	5e		 pop	 esi
  0001c	3b ec		 cmp	 ebp, esp
  0001e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00023	5d		 pop	 ebp
  00024	c3		 ret	 0
?_AtlInitializeCriticalSectionEx@ATL@@YAHPAU_RTL_CRITICAL_SECTION@@KK@Z ENDP ; ATL::_AtlInitializeCriticalSectionEx
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlcore.h
;	COMDAT ??0CComCriticalSection@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0CComCriticalSection@ATL@@QAE@XZ PROC			; ATL::CComCriticalSection::CComCriticalSection, COMDAT
; _this$ = ecx

; 123  : 	CComCriticalSection() throw()

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 124  : 	{
; 125  : 		memset(&m_sec, 0, sizeof(CRITICAL_SECTION));

  0000e	6a 18		 push	 24			; 00000018H
  00010	6a 00		 push	 0
  00012	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00015	50		 push	 eax
  00016	e8 00 00 00 00	 call	 _memset
  0001b	83 c4 0c	 add	 esp, 12			; 0000000cH

; 126  : 	}

  0001e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00021	83 c4 04	 add	 esp, 4
  00024	3b ec		 cmp	 ebp, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002b	8b e5		 mov	 esp, ebp
  0002d	5d		 pop	 ebp
  0002e	c3		 ret	 0
??0CComCriticalSection@ATL@@QAE@XZ ENDP			; ATL::CComCriticalSection::CComCriticalSection
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlcore.h
;	COMDAT ??1CComCriticalSection@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??1CComCriticalSection@ATL@@QAE@XZ PROC			; ATL::CComCriticalSection::~CComCriticalSection, COMDAT
; _this$ = ecx

; 129  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 130  : 	}

  0000e	8b e5		 mov	 esp, ebp
  00010	5d		 pop	 ebp
  00011	c3		 ret	 0
??1CComCriticalSection@ATL@@QAE@XZ ENDP			; ATL::CComCriticalSection::~CComCriticalSection
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlcore.h
;	COMDAT ?Init@CComCriticalSection@ATL@@QAEJXZ
_TEXT	SEGMENT
_hRes$ = -8						; size = 4
_this$ = -4						; size = 4
?Init@CComCriticalSection@ATL@@QAEJXZ PROC		; ATL::CComCriticalSection::Init, COMDAT
; _this$ = ecx

; 143  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 144  : 		HRESULT hRes = S_OK;

  00018	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR _hRes$[ebp], 0

; 145  : 		if (!_AtlInitializeCriticalSectionEx(&m_sec, 0, 0))

  0001f	6a 00		 push	 0
  00021	6a 00		 push	 0
  00023	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00026	50		 push	 eax
  00027	e8 00 00 00 00	 call	 ?_AtlInitializeCriticalSectionEx@ATL@@YAHPAU_RTL_CRITICAL_SECTION@@KK@Z ; ATL::_AtlInitializeCriticalSectionEx
  0002c	83 c4 0c	 add	 esp, 12			; 0000000cH
  0002f	85 c0		 test	 eax, eax
  00031	75 1b		 jne	 SHORT $LN2@Init

; 146  : 		{
; 147  : 			hRes = HRESULT_FROM_WIN32(GetLastError());

  00033	8b f4		 mov	 esi, esp
  00035	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetLastError@0
  0003b	3b f4		 cmp	 esi, esp
  0003d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00042	50		 push	 eax
  00043	e8 00 00 00 00	 call	 _HRESULT_FROM_WIN32
  00048	83 c4 04	 add	 esp, 4
  0004b	89 45 f8	 mov	 DWORD PTR _hRes$[ebp], eax
$LN2@Init:

; 148  : 		}
; 149  : 
; 150  : 		return hRes;

  0004e	8b 45 f8	 mov	 eax, DWORD PTR _hRes$[ebp]

; 151  : 	}

  00051	5e		 pop	 esi
  00052	83 c4 08	 add	 esp, 8
  00055	3b ec		 cmp	 ebp, esp
  00057	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005c	8b e5		 mov	 esp, ebp
  0005e	5d		 pop	 ebp
  0005f	c3		 ret	 0
?Init@CComCriticalSection@ATL@@QAEJXZ ENDP		; ATL::CComCriticalSection::Init
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlcore.h
;	COMDAT ?Term@CComCriticalSection@ATL@@QAEJXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?Term@CComCriticalSection@ATL@@QAEJXZ PROC		; ATL::CComCriticalSection::Term, COMDAT
; _this$ = ecx

; 154  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 155  : 		DeleteCriticalSection(&m_sec);

  0000f	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00012	8b f4		 mov	 esi, esp
  00014	50		 push	 eax
  00015	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__DeleteCriticalSection@4
  0001b	3b f4		 cmp	 esi, esp
  0001d	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 156  : 		return S_OK;

  00022	33 c0		 xor	 eax, eax

; 157  : 	}

  00024	5e		 pop	 esi
  00025	83 c4 04	 add	 esp, 4
  00028	3b ec		 cmp	 ebp, esp
  0002a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002f	8b e5		 mov	 esp, ebp
  00031	5d		 pop	 ebp
  00032	c3		 ret	 0
?Term@CComCriticalSection@ATL@@QAEJXZ ENDP		; ATL::CComCriticalSection::Term
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlcore.h
;	COMDAT ?GetModuleInstance@CAtlBaseModule@ATL@@QAEPAUHINSTANCE__@@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetModuleInstance@CAtlBaseModule@ATL@@QAEPAUHINSTANCE__@@XZ PROC ; ATL::CAtlBaseModule::GetModuleInstance, COMDAT
; _this$ = ecx

; 289  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 290  : 		return m_hInst;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 40 04	 mov	 eax, DWORD PTR [eax+4]

; 291  : 	}

  00014	8b e5		 mov	 esp, ebp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?GetModuleInstance@CAtlBaseModule@ATL@@QAEPAUHINSTANCE__@@XZ ENDP ; ATL::CAtlBaseModule::GetModuleInstance
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlcomcli.h
;	COMDAT ??1CComBSTR@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??1CComBSTR@ATL@@QAE@XZ PROC				; ATL::CComBSTR::~CComBSTR, COMDAT
; _this$ = ecx

; 1671 : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1672 :     ::SysFreeString(m_str);

  0000f	8b f4		 mov	 esi, esp
  00011	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00014	8b 08		 mov	 ecx, DWORD PTR [eax]
  00016	51		 push	 ecx
  00017	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__SysFreeString@4
  0001d	3b f4		 cmp	 esi, esp
  0001f	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 1673 : }

  00024	5e		 pop	 esi
  00025	83 c4 04	 add	 esp, 4
  00028	3b ec		 cmp	 ebp, esp
  0002a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002f	8b e5		 mov	 esp, ebp
  00031	5d		 pop	 ebp
  00032	c3		 ret	 0
??1CComBSTR@ATL@@QAE@XZ ENDP				; ATL::CComBSTR::~CComBSTR
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ??0CAtlComModule@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0CAtlComModule@ATL@@QAE@XZ PROC			; ATL::CAtlComModule::CAtlComModule, COMDAT
; _this$ = ecx

; 2586 : 	CAtlComModule() throw()

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0CAtlComModule@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ??0_ATL_COM_MODULE70@ATL@@QAE@XZ
  0002b	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 2587 : 	{
; 2588 : 		cbSize = 0;

  00032	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00035	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 2589 : 
; 2590 : 		m_hInstTypeLib = reinterpret_cast<HINSTANCE>(&__ImageBase);

  0003b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003e	c7 41 04 00 00
	00 00		 mov	 DWORD PTR [ecx+4], OFFSET ___ImageBase

; 2591 : 
; 2592 : 		m_ppAutoObjMapFirst = &__pobjMapEntryFirst + 1;

  00045	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00048	c7 42 08 04 00
	00 00		 mov	 DWORD PTR [edx+8], OFFSET ___pobjMapEntryFirst+4

; 2593 : 		m_ppAutoObjMapLast = &__pobjMapEntryLast;

  0004f	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00052	c7 40 0c 00 00
	00 00		 mov	 DWORD PTR [eax+12], OFFSET ___pobjMapEntryLast

; 2594 : 
; 2595 : 		if (FAILED(m_csObjMap.Init()))

  00059	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0005c	83 c1 10	 add	 ecx, 16			; 00000010H
  0005f	e8 00 00 00 00	 call	 ?Init@CComCriticalSection@ATL@@QAEJXZ ; ATL::CComCriticalSection::Init
  00064	85 c0		 test	 eax, eax
  00066	7d 09		 jge	 SHORT $LN2@CAtlComMod

; 2596 : 		{
; 2597 : 			ATLTRACE(atlTraceCOM, 0, _T("ERROR : Unable to initialize critical section in CAtlComModule\n"));
; 2598 : 			ATLASSERT(0);
; 2599 : 			CAtlBaseModule::m_bInitFailed = true;

  00068	c6 05 00 00 00
	00 01		 mov	 BYTE PTR ?m_bInitFailed@CAtlBaseModule@ATL@@2_NA, 1 ; ATL::CAtlBaseModule::m_bInitFailed

; 2600 : 			return;

  0006f	eb 09		 jmp	 SHORT $LN1@CAtlComMod
$LN2@CAtlComMod:

; 2601 : 		}
; 2602 : 		// Set cbSize on success.
; 2603 : 		cbSize = sizeof(_ATL_COM_MODULE);

  00071	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00074	c7 01 28 00 00
	00		 mov	 DWORD PTR [ecx], 40	; 00000028H
$LN1@CAtlComMod:

; 2604 : 	}

  0007a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00081	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00084	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00087	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0008e	83 c4 10	 add	 esp, 16			; 00000010H
  00091	3b ec		 cmp	 ebp, esp
  00093	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00098	8b e5		 mov	 esp, ebp
  0009a	5d		 pop	 ebp
  0009b	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0CAtlComModule@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1_ATL_COM_MODULE70@ATL@@QAE@XZ
__ehhandler$??0CAtlComModule@ATL@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0CAtlComModule@ATL@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0CAtlComModule@ATL@@QAE@XZ ENDP			; ATL::CAtlComModule::CAtlComModule
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ??1CAtlComModule@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1CAtlComModule@ATL@@QAE@XZ PROC			; ATL::CAtlComModule::~CAtlComModule, COMDAT
; _this$ = ecx

; 2607 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1CAtlComModule@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 2608 : 		Term();

  0002a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002d	e8 00 00 00 00	 call	 ?Term@CAtlComModule@ATL@@QAEXXZ ; ATL::CAtlComModule::Term

; 2609 : 	}

  00032	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00039	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003c	e8 00 00 00 00	 call	 ??1_ATL_COM_MODULE70@ATL@@QAE@XZ
  00041	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00044	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0004b	83 c4 10	 add	 esp, 16			; 00000010H
  0004e	3b ec		 cmp	 ebp, esp
  00050	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00055	8b e5		 mov	 esp, ebp
  00057	5d		 pop	 ebp
  00058	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1CAtlComModule@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1_ATL_COM_MODULE70@ATL@@QAE@XZ
__ehhandler$??1CAtlComModule@ATL@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1CAtlComModule@ATL@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1CAtlComModule@ATL@@QAE@XZ ENDP			; ATL::CAtlComModule::~CAtlComModule
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ?Term@CAtlComModule@ATL@@QAEXXZ
_TEXT	SEGMENT
_factory$1 = -16					; size = 4
_pCache$2 = -12						; size = 4
_ppEntry$3 = -8						; size = 4
_this$ = -4						; size = 4
?Term@CAtlComModule@ATL@@QAEXXZ PROC			; ATL::CAtlComModule::Term, COMDAT
; _this$ = ecx

; 2613 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 10	 sub	 esp, 16			; 00000010H
  00006	56		 push	 esi
  00007	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  0000c	89 45 f0	 mov	 DWORD PTR [ebp-16], eax
  0000f	89 45 f4	 mov	 DWORD PTR [ebp-12], eax
  00012	89 45 f8	 mov	 DWORD PTR [ebp-8], eax
  00015	89 45 fc	 mov	 DWORD PTR [ebp-4], eax
  00018	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 2614 : 		if (cbSize == 0)

  0001b	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001e	83 38 00	 cmp	 DWORD PTR [eax], 0
  00021	75 05		 jne	 SHORT $LN5@Term

; 2615 : 			return;

  00023	e9 88 00 00 00	 jmp	 $LN1@Term
$LN5@Term:

; 2616 : 
; 2617 : 		for (_ATL_OBJMAP_ENTRY_EX** ppEntry = m_ppAutoObjMapFirst; ppEntry < m_ppAutoObjMapLast; ppEntry++)

  00028	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0002b	8b 51 08	 mov	 edx, DWORD PTR [ecx+8]
  0002e	89 55 f8	 mov	 DWORD PTR _ppEntry$3[ebp], edx
  00031	eb 09		 jmp	 SHORT $LN4@Term
$LN2@Term:
  00033	8b 45 f8	 mov	 eax, DWORD PTR _ppEntry$3[ebp]
  00036	83 c0 04	 add	 eax, 4
  00039	89 45 f8	 mov	 DWORD PTR _ppEntry$3[ebp], eax
$LN4@Term:
  0003c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0003f	8b 55 f8	 mov	 edx, DWORD PTR _ppEntry$3[ebp]
  00042	3b 51 0c	 cmp	 edx, DWORD PTR [ecx+12]
  00045	73 55		 jae	 SHORT $LN3@Term

; 2618 : 		{
; 2619 : 			if (*ppEntry != NULL)

  00047	8b 45 f8	 mov	 eax, DWORD PTR _ppEntry$3[ebp]
  0004a	83 38 00	 cmp	 DWORD PTR [eax], 0
  0004d	74 4b		 je	 SHORT $LN6@Term

; 2620 : 			{
; 2621 : 				_ATL_OBJMAP_CACHE* pCache = (**ppEntry).pCache;

  0004f	8b 4d f8	 mov	 ecx, DWORD PTR _ppEntry$3[ebp]
  00052	8b 11		 mov	 edx, DWORD PTR [ecx]
  00054	8b 42 10	 mov	 eax, DWORD PTR [edx+16]
  00057	89 45 f4	 mov	 DWORD PTR _pCache$2[ebp], eax

; 2622 : 
; 2623 : 				if (pCache->pCF != NULL)

  0005a	8b 4d f4	 mov	 ecx, DWORD PTR _pCache$2[ebp]
  0005d	83 39 00	 cmp	 DWORD PTR [ecx], 0
  00060	74 38		 je	 SHORT $LN6@Term

; 2624 : 				{
; 2625 : 					// Decode factory pointer if it's not null
; 2626 : 					IUnknown *factory = reinterpret_cast<IUnknown*>(::DecodePointer(pCache->pCF));

  00062	8b f4		 mov	 esi, esp
  00064	8b 55 f4	 mov	 edx, DWORD PTR _pCache$2[ebp]
  00067	8b 02		 mov	 eax, DWORD PTR [edx]
  00069	50		 push	 eax
  0006a	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__DecodePointer@4
  00070	3b f4		 cmp	 esi, esp
  00072	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00077	89 45 f0	 mov	 DWORD PTR _factory$1[ebp], eax

; 2627 : 					_Analysis_assume_(factory != nullptr);
; 2628 : 					factory->Release();

  0007a	8b 4d f0	 mov	 ecx, DWORD PTR _factory$1[ebp]
  0007d	8b 11		 mov	 edx, DWORD PTR [ecx]
  0007f	8b f4		 mov	 esi, esp
  00081	8b 45 f0	 mov	 eax, DWORD PTR _factory$1[ebp]
  00084	50		 push	 eax
  00085	8b 4a 08	 mov	 ecx, DWORD PTR [edx+8]
  00088	ff d1		 call	 ecx
  0008a	3b f4		 cmp	 esi, esp
  0008c	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 2629 : 					pCache->pCF = NULL;

  00091	8b 55 f4	 mov	 edx, DWORD PTR _pCache$2[ebp]
  00094	c7 02 00 00 00
	00		 mov	 DWORD PTR [edx], 0
$LN6@Term:

; 2630 : 				}
; 2631 : 			}
; 2632 : 		}

  0009a	eb 97		 jmp	 SHORT $LN2@Term
$LN3@Term:

; 2633 : 		m_csObjMap.Term();

  0009c	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0009f	83 c1 10	 add	 ecx, 16			; 00000010H
  000a2	e8 00 00 00 00	 call	 ?Term@CComCriticalSection@ATL@@QAEJXZ ; ATL::CComCriticalSection::Term

; 2634 : 		// Set to 0 to indicate that this function has been called
; 2635 : 		// At this point no one should be concerned about cbsize
; 2636 : 		// having the correct value
; 2637 : 		cbSize = 0;

  000a7	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  000aa	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0
$LN1@Term:

; 2638 : 	}

  000b0	5e		 pop	 esi
  000b1	83 c4 10	 add	 esp, 16			; 00000010H
  000b4	3b ec		 cmp	 ebp, esp
  000b6	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000bb	8b e5		 mov	 esp, ebp
  000bd	5d		 pop	 ebp
  000be	c3		 ret	 0
?Term@CAtlComModule@ATL@@QAEXXZ ENDP			; ATL::CAtlComModule::Term
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??0_ATL_COM_MODULE70@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0_ATL_COM_MODULE70@ATL@@QAE@XZ PROC			; ATL::_ATL_COM_MODULE70::_ATL_COM_MODULE70, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0_ATL_COM_MODULE70@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	83 c1 10	 add	 ecx, 16			; 00000010H
  00029	e8 00 00 00 00	 call	 ??0CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::CComCriticalSection
  0002e	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00035	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0003c	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003f	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00042	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00049	83 c4 10	 add	 esp, 16			; 00000010H
  0004c	3b ec		 cmp	 ebp, esp
  0004e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00053	8b e5		 mov	 esp, ebp
  00055	5d		 pop	 ebp
  00056	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0_ATL_COM_MODULE70@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 10	 add	 ecx, 16			; 00000010H
  00006	e9 00 00 00 00	 jmp	 ??1CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::~CComCriticalSection
__ehhandler$??0_ATL_COM_MODULE70@ATL@@QAE@XZ:
  0000b	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0_ATL_COM_MODULE70@ATL@@QAE@XZ
  00010	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0_ATL_COM_MODULE70@ATL@@QAE@XZ ENDP			; ATL::_ATL_COM_MODULE70::_ATL_COM_MODULE70
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1_ATL_COM_MODULE70@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1_ATL_COM_MODULE70@ATL@@QAE@XZ PROC			; ATL::_ATL_COM_MODULE70::~_ATL_COM_MODULE70, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1_ATL_COM_MODULE70@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0002a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	83 c1 10	 add	 ecx, 16			; 00000010H
  00037	e8 00 00 00 00	 call	 ??1CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::~CComCriticalSection
  0003c	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003f	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00046	83 c4 10	 add	 esp, 16			; 00000010H
  00049	3b ec		 cmp	 ebp, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00050	8b e5		 mov	 esp, ebp
  00052	5d		 pop	 ebp
  00053	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1_ATL_COM_MODULE70@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 10	 add	 ecx, 16			; 00000010H
  00006	e9 00 00 00 00	 jmp	 ??1CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::~CComCriticalSection
__ehhandler$??1_ATL_COM_MODULE70@ATL@@QAE@XZ:
  0000b	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1_ATL_COM_MODULE70@ATL@@QAE@XZ
  00010	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1_ATL_COM_MODULE70@ATL@@QAE@XZ ENDP			; ATL::_ATL_COM_MODULE70::~_ATL_COM_MODULE70
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ??__E_AtlComModule@ATL@@YAXXZ
text$di	SEGMENT
??__E_AtlComModule@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for '_AtlComModule'', COMDAT

; 2716 : __declspec(selectany) CAtlComModule _AtlComModule;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlComModule@ATL@@3VCAtlComModule@1@A ; ATL::_AtlComModule
  00008	e8 00 00 00 00	 call	 ??0CAtlComModule@ATL@@QAE@XZ ; ATL::CAtlComModule::CAtlComModule
  0000d	68 00 00 00 00	 push	 OFFSET ??__F_AtlComModule@ATL@@YAXXZ ; ATL::`dynamic atexit destructor for '_AtlComModule''
  00012	e8 00 00 00 00	 call	 _atexit
  00017	83 c4 04	 add	 esp, 4
  0001a	3b ec		 cmp	 ebp, esp
  0001c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00021	5d		 pop	 ebp
  00022	c3		 ret	 0
??__E_AtlComModule@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for '_AtlComModule''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__F_AtlComModule@ATL@@YAXXZ
text$yd	SEGMENT
??__F_AtlComModule@ATL@@YAXXZ PROC			; ATL::`dynamic atexit destructor for '_AtlComModule'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlComModule@ATL@@3VCAtlComModule@1@A ; ATL::_AtlComModule
  00008	e8 00 00 00 00	 call	 ??1CAtlComModule@ATL@@QAE@XZ ; ATL::CAtlComModule::~CAtlComModule
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__F_AtlComModule@ATL@@YAXXZ ENDP			; ATL::`dynamic atexit destructor for '_AtlComModule''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ??0CAtlWinModule@ATL@@QAE@XZ
_TEXT	SEGMENT
_hr$ = -20						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0CAtlWinModule@ATL@@QAE@XZ PROC			; ATL::CAtlWinModule::CAtlWinModule, COMDAT
; _this$ = ecx

; 2937 : 	CAtlWinModule()

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0CAtlWinModule@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 08	 sub	 esp, 8
  0001b	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00022	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00029	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  0002c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002f	e8 00 00 00 00	 call	 ??0_ATL_WIN_MODULE70@ATL@@QAE@XZ
  00034	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 2938 : 	{
; 2939 : 		cbSize = sizeof(_ATL_WIN_MODULE);

  0003b	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003e	c7 00 2c 00 00
	00		 mov	 DWORD PTR [eax], 44	; 0000002cH

; 2940 : 		HRESULT hr = AtlWinModuleInit(this);

  00044	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00047	51		 push	 ecx
  00048	e8 00 00 00 00	 call	 ?AtlWinModuleInit@ATL@@YGJPAU_ATL_WIN_MODULE70@1@@Z ; ATL::AtlWinModuleInit
  0004d	89 45 ec	 mov	 DWORD PTR _hr$[ebp], eax

; 2941 : 		if (FAILED(hr))

  00050	83 7d ec 00	 cmp	 DWORD PTR _hr$[ebp], 0
  00054	7d 10		 jge	 SHORT $LN1@CAtlWinMod

; 2942 : 		{
; 2943 : 			ATLASSERT(0);
; 2944 : 			CAtlBaseModule::m_bInitFailed = true;

  00056	c6 05 00 00 00
	00 01		 mov	 BYTE PTR ?m_bInitFailed@CAtlBaseModule@ATL@@2_NA, 1 ; ATL::CAtlBaseModule::m_bInitFailed

; 2945 : 			cbSize = 0;

  0005d	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00060	c7 02 00 00 00
	00		 mov	 DWORD PTR [edx], 0
$LN1@CAtlWinMod:

; 2946 : 			return;
; 2947 : 		}
; 2948 : 	}

  00066	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0006d	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00070	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00073	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0007a	83 c4 14	 add	 esp, 20			; 00000014H
  0007d	3b ec		 cmp	 ebp, esp
  0007f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00084	8b e5		 mov	 esp, ebp
  00086	5d		 pop	 ebp
  00087	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0CAtlWinModule@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1_ATL_WIN_MODULE70@ATL@@QAE@XZ
__ehhandler$??0CAtlWinModule@ATL@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0CAtlWinModule@ATL@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0CAtlWinModule@ATL@@QAE@XZ ENDP			; ATL::CAtlWinModule::CAtlWinModule
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ??1CAtlWinModule@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1CAtlWinModule@ATL@@QAE@XZ PROC			; ATL::CAtlWinModule::~CAtlWinModule, COMDAT
; _this$ = ecx

; 2951 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1CAtlWinModule@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 2952 : 		Term();

  0002a	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002d	e8 00 00 00 00	 call	 ?Term@CAtlWinModule@ATL@@QAEXXZ ; ATL::CAtlWinModule::Term

; 2953 : 	}

  00032	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00039	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003c	e8 00 00 00 00	 call	 ??1_ATL_WIN_MODULE70@ATL@@QAE@XZ
  00041	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00044	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  0004b	83 c4 10	 add	 esp, 16			; 00000010H
  0004e	3b ec		 cmp	 ebp, esp
  00050	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00055	8b e5		 mov	 esp, ebp
  00057	5d		 pop	 ebp
  00058	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1CAtlWinModule@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1_ATL_WIN_MODULE70@ATL@@QAE@XZ
__ehhandler$??1CAtlWinModule@ATL@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1CAtlWinModule@ATL@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1CAtlWinModule@ATL@@QAE@XZ ENDP			; ATL::CAtlWinModule::~CAtlWinModule
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ?Term@CAtlWinModule@ATL@@QAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?Term@CAtlWinModule@ATL@@QAEXXZ PROC			; ATL::CAtlWinModule::Term, COMDAT
; _this$ = ecx

; 2956 : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 2957 : 		AtlWinModuleTerm(this, _AtlBaseModule.GetModuleInstance());

  0000e	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlBaseModule@ATL@@3VCAtlBaseModule@1@A ; ATL::_AtlBaseModule
  00013	e8 00 00 00 00	 call	 ?GetModuleInstance@CAtlBaseModule@ATL@@QAEPAUHINSTANCE__@@XZ ; ATL::CAtlBaseModule::GetModuleInstance
  00018	50		 push	 eax
  00019	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001c	50		 push	 eax
  0001d	e8 00 00 00 00	 call	 ?AtlWinModuleTerm@ATL@@YGJPAU_ATL_WIN_MODULE70@1@PAUHINSTANCE__@@@Z ; ATL::AtlWinModuleTerm

; 2958 : 	}

  00022	83 c4 04	 add	 esp, 4
  00025	3b ec		 cmp	 ebp, esp
  00027	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002c	8b e5		 mov	 esp, ebp
  0002e	5d		 pop	 ebp
  0002f	c3		 ret	 0
?Term@CAtlWinModule@ATL@@QAEXXZ ENDP			; ATL::CAtlWinModule::Term
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??0_ATL_WIN_MODULE70@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??0_ATL_WIN_MODULE70@ATL@@QAE@XZ PROC			; ATL::_ATL_WIN_MODULE70::_ATL_WIN_MODULE70, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	83 c1 04	 add	 ecx, 4
  00029	e8 00 00 00 00	 call	 ??0CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::CComCriticalSection
  0002e	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  00035	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00038	83 c1 20	 add	 ecx, 32			; 00000020H
  0003b	e8 00 00 00 00	 call	 ??0?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
  00040	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00044	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0004b	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0004e	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00051	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00058	83 c4 10	 add	 esp, 16			; 00000010H
  0005b	3b ec		 cmp	 ebp, esp
  0005d	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00062	8b e5		 mov	 esp, ebp
  00064	5d		 pop	 ebp
  00065	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 04	 add	 ecx, 4
  00006	e9 00 00 00 00	 jmp	 ??1CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::~CComCriticalSection
__unwindfunclet$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ$1:
  0000b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000e	83 c1 20	 add	 ecx, 32			; 00000020H
  00011	e9 00 00 00 00	 jmp	 ??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::~CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
__ehhandler$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ:
  00016	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0_ATL_WIN_MODULE70@ATL@@QAE@XZ
  0001b	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0_ATL_WIN_MODULE70@ATL@@QAE@XZ ENDP			; ATL::_ATL_WIN_MODULE70::_ATL_WIN_MODULE70
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1_ATL_WIN_MODULE70@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1_ATL_WIN_MODULE70@ATL@@QAE@XZ PROC			; ATL::_ATL_WIN_MODULE70::~_ATL_WIN_MODULE70, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
  0002a	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  0002e	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00031	83 c1 20	 add	 ecx, 32			; 00000020H
  00034	e8 00 00 00 00	 call	 ??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::~CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
  00039	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00040	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00043	83 c1 04	 add	 ecx, 4
  00046	e8 00 00 00 00	 call	 ??1CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::~CComCriticalSection
  0004b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0004e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00055	83 c4 10	 add	 esp, 16			; 00000010H
  00058	3b ec		 cmp	 ebp, esp
  0005a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005f	8b e5		 mov	 esp, ebp
  00061	5d		 pop	 ebp
  00062	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 c1 04	 add	 ecx, 4
  00006	e9 00 00 00 00	 jmp	 ??1CComCriticalSection@ATL@@QAE@XZ ; ATL::CComCriticalSection::~CComCriticalSection
__unwindfunclet$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ$1:
  0000b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000e	83 c1 20	 add	 ecx, 32			; 00000020H
  00011	e9 00 00 00 00	 jmp	 ??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::~CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
__ehhandler$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ:
  00016	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1_ATL_WIN_MODULE70@ATL@@QAE@XZ
  0001b	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1_ATL_WIN_MODULE70@ATL@@QAE@XZ ENDP			; ATL::_ATL_WIN_MODULE70::~_ATL_WIN_MODULE70
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ??__E_AtlWinModule@ATL@@YAXXZ
text$di	SEGMENT
??__E_AtlWinModule@ATL@@YAXXZ PROC			; ATL::`dynamic initializer for '_AtlWinModule'', COMDAT

; 2972 : __declspec(selectany) CAtlWinModule _AtlWinModule;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlWinModule@ATL@@3VCAtlWinModule@1@A ; ATL::_AtlWinModule
  00008	e8 00 00 00 00	 call	 ??0CAtlWinModule@ATL@@QAE@XZ ; ATL::CAtlWinModule::CAtlWinModule
  0000d	68 00 00 00 00	 push	 OFFSET ??__F_AtlWinModule@ATL@@YAXXZ ; ATL::`dynamic atexit destructor for '_AtlWinModule''
  00012	e8 00 00 00 00	 call	 _atexit
  00017	83 c4 04	 add	 esp, 4
  0001a	3b ec		 cmp	 ebp, esp
  0001c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00021	5d		 pop	 ebp
  00022	c3		 ret	 0
??__E_AtlWinModule@ATL@@YAXXZ ENDP			; ATL::`dynamic initializer for '_AtlWinModule''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__F_AtlWinModule@ATL@@YAXXZ
text$yd	SEGMENT
??__F_AtlWinModule@ATL@@YAXXZ PROC			; ATL::`dynamic atexit destructor for '_AtlWinModule'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?_AtlWinModule@ATL@@3VCAtlWinModule@1@A ; ATL::_AtlWinModule
  00008	e8 00 00 00 00	 call	 ??1CAtlWinModule@ATL@@QAE@XZ ; ATL::CAtlWinModule::~CAtlWinModule
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__F_AtlWinModule@ATL@@YAXXZ ENDP			; ATL::`dynamic atexit destructor for '_AtlWinModule''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ?AtlWinModuleTerm@ATL@@YGJPAU_ATL_WIN_MODULE70@1@PAUHINSTANCE__@@@Z
_TEXT	SEGMENT
_i$1 = -4						; size = 4
_pWinModule$ = 8					; size = 4
_hInst$ = 12						; size = 4
?AtlWinModuleTerm@ATL@@YGJPAU_ATL_WIN_MODULE70@1@PAUHINSTANCE__@@@Z PROC ; ATL::AtlWinModuleTerm, COMDAT

; 7652 : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 7653 : 	if (pWinModule == NULL)

  0000c	83 7d 08 00	 cmp	 DWORD PTR _pWinModule$[ebp], 0
  00010	75 0a		 jne	 SHORT $LN5@AtlWinModu

; 7654 : 		return E_INVALIDARG;

  00012	b8 57 00 07 80	 mov	 eax, -2147024809	; 80070057H
  00017	e9 86 00 00 00	 jmp	 $LN1@AtlWinModu
$LN5@AtlWinModu:

; 7655 : 	if (pWinModule->cbSize == 0)

  0001c	8b 45 08	 mov	 eax, DWORD PTR _pWinModule$[ebp]
  0001f	83 38 00	 cmp	 DWORD PTR [eax], 0
  00022	75 04		 jne	 SHORT $LN6@AtlWinModu

; 7656 : 		return S_OK;

  00024	33 c0		 xor	 eax, eax
  00026	eb 7a		 jmp	 SHORT $LN1@AtlWinModu
$LN6@AtlWinModu:

; 7657 : 	if (pWinModule->cbSize != sizeof(_ATL_WIN_MODULE))

  00028	8b 4d 08	 mov	 ecx, DWORD PTR _pWinModule$[ebp]
  0002b	83 39 2c	 cmp	 DWORD PTR [ecx], 44	; 0000002cH
  0002e	74 07		 je	 SHORT $LN7@AtlWinModu

; 7658 : 		return E_INVALIDARG;

  00030	b8 57 00 07 80	 mov	 eax, -2147024809	; 80070057H
  00035	eb 6b		 jmp	 SHORT $LN1@AtlWinModu
$LN7@AtlWinModu:

; 7659 : 
; 7660 : 	for (int i = 0; i < pWinModule->m_rgWindowClassAtoms.GetSize(); i++)

  00037	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR _i$1[ebp], 0
  0003e	eb 09		 jmp	 SHORT $LN4@AtlWinModu
$LN2@AtlWinModu:
  00040	8b 55 fc	 mov	 edx, DWORD PTR _i$1[ebp]
  00043	83 c2 01	 add	 edx, 1
  00046	89 55 fc	 mov	 DWORD PTR _i$1[ebp], edx
$LN4@AtlWinModu:
  00049	8b 4d 08	 mov	 ecx, DWORD PTR _pWinModule$[ebp]
  0004c	83 c1 20	 add	 ecx, 32			; 00000020H
  0004f	e8 00 00 00 00	 call	 ?GetSize@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QBEHXZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::GetSize
  00054	39 45 fc	 cmp	 DWORD PTR _i$1[ebp], eax
  00057	7d 28		 jge	 SHORT $LN3@AtlWinModu

; 7661 : 		UnregisterClass((LPCTSTR)pWinModule->m_rgWindowClassAtoms[i], hInst);

  00059	8b f4		 mov	 esi, esp
  0005b	8b 45 0c	 mov	 eax, DWORD PTR _hInst$[ebp]
  0005e	50		 push	 eax
  0005f	8b 4d fc	 mov	 ecx, DWORD PTR _i$1[ebp]
  00062	51		 push	 ecx
  00063	8b 4d 08	 mov	 ecx, DWORD PTR _pWinModule$[ebp]
  00066	83 c1 20	 add	 ecx, 32			; 00000020H
  00069	e8 00 00 00 00	 call	 ??A?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEAAGH@Z ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::operator[]
  0006e	0f b7 10	 movzx	 edx, WORD PTR [eax]
  00071	52		 push	 edx
  00072	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__UnregisterClassW@8
  00078	3b f4		 cmp	 esi, esp
  0007a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0007f	eb bf		 jmp	 SHORT $LN2@AtlWinModu
$LN3@AtlWinModu:

; 7662 : 	pWinModule->m_rgWindowClassAtoms.RemoveAll();

  00081	8b 4d 08	 mov	 ecx, DWORD PTR _pWinModule$[ebp]
  00084	83 c1 20	 add	 ecx, 32			; 00000020H
  00087	e8 00 00 00 00	 call	 ?RemoveAll@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEXXZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::RemoveAll

; 7663 : 	pWinModule->m_csWindowCreate.Term();

  0008c	8b 4d 08	 mov	 ecx, DWORD PTR _pWinModule$[ebp]
  0008f	83 c1 04	 add	 ecx, 4
  00092	e8 00 00 00 00	 call	 ?Term@CComCriticalSection@ATL@@QAEJXZ ; ATL::CComCriticalSection::Term

; 7664 : 	pWinModule->cbSize = 0;

  00097	8b 45 08	 mov	 eax, DWORD PTR _pWinModule$[ebp]
  0009a	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 7665 : 	return S_OK;

  000a0	33 c0		 xor	 eax, eax
$LN1@AtlWinModu:

; 7666 : }

  000a2	5e		 pop	 esi
  000a3	83 c4 04	 add	 esp, 4
  000a6	3b ec		 cmp	 ebp, esp
  000a8	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000ad	8b e5		 mov	 esp, ebp
  000af	5d		 pop	 ebp
  000b0	c2 08 00	 ret	 8
?AtlWinModuleTerm@ATL@@YGJPAU_ATL_WIN_MODULE70@1@PAUHINSTANCE__@@@Z ENDP ; ATL::AtlWinModuleTerm
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlbase.h
;	COMDAT ?AtlWinModuleInit@ATL@@YGJPAU_ATL_WIN_MODULE70@1@@Z
_TEXT	SEGMENT
_hr$ = -4						; size = 4
_pWinModule$ = 8					; size = 4
?AtlWinModuleInit@ATL@@YGJPAU_ATL_WIN_MODULE70@1@@Z PROC ; ATL::AtlWinModuleInit, COMDAT

; 8396 : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH

; 8397 : 	if (pWinModule == NULL)

  0000b	83 7d 08 00	 cmp	 DWORD PTR _pWinModule$[ebp], 0
  0000f	75 07		 jne	 SHORT $LN2@AtlWinModu

; 8398 : 		return E_INVALIDARG;

  00011	b8 57 00 07 80	 mov	 eax, -2147024809	; 80070057H
  00016	eb 2a		 jmp	 SHORT $LN1@AtlWinModu
$LN2@AtlWinModu:

; 8399 : 
; 8400 : 	// check only in the DLL
; 8401 : 	if (pWinModule->cbSize != sizeof(_ATL_WIN_MODULE))

  00018	8b 45 08	 mov	 eax, DWORD PTR _pWinModule$[ebp]
  0001b	83 38 2c	 cmp	 DWORD PTR [eax], 44	; 0000002cH
  0001e	74 07		 je	 SHORT $LN3@AtlWinModu

; 8402 : 		return E_INVALIDARG;

  00020	b8 57 00 07 80	 mov	 eax, -2147024809	; 80070057H
  00025	eb 1b		 jmp	 SHORT $LN1@AtlWinModu
$LN3@AtlWinModu:

; 8403 : 
; 8404 : 	pWinModule->m_pCreateWndList = NULL;

  00027	8b 4d 08	 mov	 ecx, DWORD PTR _pWinModule$[ebp]
  0002a	c7 41 1c 00 00
	00 00		 mov	 DWORD PTR [ecx+28], 0

; 8405 : 
; 8406 : 	HRESULT hr = pWinModule->m_csWindowCreate.Init();

  00031	8b 4d 08	 mov	 ecx, DWORD PTR _pWinModule$[ebp]
  00034	83 c1 04	 add	 ecx, 4
  00037	e8 00 00 00 00	 call	 ?Init@CComCriticalSection@ATL@@QAEJXZ ; ATL::CComCriticalSection::Init
  0003c	89 45 fc	 mov	 DWORD PTR _hr$[ebp], eax

; 8407 : 	if (FAILED(hr))
; 8408 : 	{
; 8409 : 		ATLTRACE(atlTraceWindowing, 0, _T("ERROR : Unable to initialize critical section in AtlWinModuleInit\n"));
; 8410 : 		ATLASSERT(0);
; 8411 : 	}
; 8412 : 	return hr;

  0003f	8b 45 fc	 mov	 eax, DWORD PTR _hr$[ebp]
$LN1@AtlWinModu:

; 8413 : }

  00042	83 c4 04	 add	 esp, 4
  00045	3b ec		 cmp	 ebp, esp
  00047	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004c	8b e5		 mov	 esp, ebp
  0004e	5d		 pop	 ebp
  0004f	c2 04 00	 ret	 4
?AtlWinModuleInit@ATL@@YGJPAU_ATL_WIN_MODULE70@1@@Z ENDP ; ATL::AtlWinModuleInit
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlmem.h
;	COMDAT ??0CWin32Heap@ATL@@QAE@PAX@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_hHeap$ = 8						; size = 4
??0CWin32Heap@ATL@@QAE@PAX@Z PROC			; ATL::CWin32Heap::CWin32Heap, COMDAT
; _this$ = ecx

; 89   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CWin32Heap@ATL@@6B@

; 87   : 		m_hHeap( hHeap ),

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	8b 55 08	 mov	 edx, DWORD PTR _hHeap$[ebp]
  0001d	89 51 04	 mov	 DWORD PTR [ecx+4], edx

; 88   : 		m_bOwnHeap( false )

  00020	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00023	c6 40 08 00	 mov	 BYTE PTR [eax+8], 0

; 90   : 		ATLASSERT( hHeap != NULL );
; 91   : 	}

  00027	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002a	8b e5		 mov	 esp, ebp
  0002c	5d		 pop	 ebp
  0002d	c2 04 00	 ret	 4
??0CWin32Heap@ATL@@QAE@PAX@Z ENDP			; ATL::CWin32Heap::CWin32Heap
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlmem.h
;	COMDAT ??1CWin32Heap@ATL@@UAE@XZ
_TEXT	SEGMENT
_bSuccess$1 = -8					; size = 4
_this$ = -4						; size = 4
??1CWin32Heap@ATL@@UAE@XZ PROC				; ATL::CWin32Heap::~CWin32Heap, COMDAT
; _this$ = ecx

; 109  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  00018	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001b	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CWin32Heap@ATL@@6B@

; 110  : #ifdef _ATL_USE_WINAPI_FAMILY_DESKTOP_APP
; 111  : 		if( m_bOwnHeap && (m_hHeap != NULL) )

  00021	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00024	0f b6 51 08	 movzx	 edx, BYTE PTR [ecx+8]
  00028	85 d2		 test	 edx, edx
  0002a	74 22		 je	 SHORT $LN1@CWin32Heap
  0002c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002f	83 78 04 00	 cmp	 DWORD PTR [eax+4], 0
  00033	74 19		 je	 SHORT $LN1@CWin32Heap

; 112  : 		{
; 113  : 			BOOL bSuccess;
; 114  : 
; 115  : 			bSuccess = ::HeapDestroy( m_hHeap );

  00035	8b f4		 mov	 esi, esp
  00037	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0003a	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0003d	52		 push	 edx
  0003e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__HeapDestroy@4
  00044	3b f4		 cmp	 esi, esp
  00046	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004b	89 45 f8	 mov	 DWORD PTR _bSuccess$1[ebp], eax
$LN1@CWin32Heap:

; 116  : 			ATLASSERT( bSuccess );
; 117  : 		}
; 118  : #endif
; 119  : 	}

  0004e	5e		 pop	 esi
  0004f	83 c4 08	 add	 esp, 8
  00052	3b ec		 cmp	 ebp, esp
  00054	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00059	8b e5		 mov	 esp, ebp
  0005b	5d		 pop	 ebp
  0005c	c3		 ret	 0
??1CWin32Heap@ATL@@UAE@XZ ENDP				; ATL::CWin32Heap::~CWin32Heap
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlmem.h
;	COMDAT ?Allocate@CWin32Heap@ATL@@UAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_nBytes$ = 8						; size = 4
?Allocate@CWin32Heap@ATL@@UAEPAXI@Z PROC		; ATL::CWin32Heap::Allocate, COMDAT
; _this$ = ecx

; 144  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 145  : 		return( ::HeapAlloc( m_hHeap, 0, nBytes ) );

  0000f	8b f4		 mov	 esi, esp
  00011	8b 45 08	 mov	 eax, DWORD PTR _nBytes$[ebp]
  00014	50		 push	 eax
  00015	6a 00		 push	 0
  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0001d	52		 push	 edx
  0001e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__HeapAlloc@12
  00024	3b f4		 cmp	 esi, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 146  : 	}

  0002b	5e		 pop	 esi
  0002c	83 c4 04	 add	 esp, 4
  0002f	3b ec		 cmp	 ebp, esp
  00031	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00036	8b e5		 mov	 esp, ebp
  00038	5d		 pop	 ebp
  00039	c2 04 00	 ret	 4
?Allocate@CWin32Heap@ATL@@UAEPAXI@Z ENDP		; ATL::CWin32Heap::Allocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlmem.h
;	COMDAT ?Free@CWin32Heap@ATL@@UAEXPAX@Z
_TEXT	SEGMENT
_bSuccess$1 = -8					; size = 4
_this$ = -4						; size = 4
_p$ = 8							; size = 4
?Free@CWin32Heap@ATL@@UAEXPAX@Z PROC			; ATL::CWin32Heap::Free, COMDAT
; _this$ = ecx

; 148  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00015	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 149  : 		if( p != NULL )

  00018	83 7d 08 00	 cmp	 DWORD PTR _p$[ebp], 0
  0001c	74 1f		 je	 SHORT $LN1@Free

; 150  : 		{
; 151  : 			BOOL bSuccess;
; 152  : 
; 153  : 			bSuccess = ::HeapFree( m_hHeap, 0, p );

  0001e	8b f4		 mov	 esi, esp
  00020	8b 45 08	 mov	 eax, DWORD PTR _p$[ebp]
  00023	50		 push	 eax
  00024	6a 00		 push	 0
  00026	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00029	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0002c	52		 push	 edx
  0002d	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__HeapFree@12
  00033	3b f4		 cmp	 esi, esp
  00035	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003a	89 45 f8	 mov	 DWORD PTR _bSuccess$1[ebp], eax
$LN1@Free:

; 154  : 			ATLASSERT( bSuccess );
; 155  : 		}
; 156  : 	}

  0003d	5e		 pop	 esi
  0003e	83 c4 08	 add	 esp, 8
  00041	3b ec		 cmp	 ebp, esp
  00043	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00048	8b e5		 mov	 esp, ebp
  0004a	5d		 pop	 ebp
  0004b	c2 04 00	 ret	 4
?Free@CWin32Heap@ATL@@UAEXPAX@Z ENDP			; ATL::CWin32Heap::Free
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlmem.h
;	COMDAT ?Reallocate@CWin32Heap@ATL@@UAEPAXPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_p$ = 8							; size = 4
_nBytes$ = 12						; size = 4
?Reallocate@CWin32Heap@ATL@@UAEPAXPAXI@Z PROC		; ATL::CWin32Heap::Reallocate, COMDAT
; _this$ = ecx

; 160  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 161  : 		if( p == NULL )

  0000f	83 7d 08 00	 cmp	 DWORD PTR _p$[ebp], 0
  00013	75 1b		 jne	 SHORT $LN2@Reallocate

; 162  : 		{
; 163  : 			return( Allocate( nBytes ) );

  00015	8b f4		 mov	 esi, esp
  00017	8b 45 0c	 mov	 eax, DWORD PTR _nBytes$[ebp]
  0001a	50		 push	 eax
  0001b	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001e	8b 11		 mov	 edx, DWORD PTR [ecx]
  00020	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00023	8b 02		 mov	 eax, DWORD PTR [edx]
  00025	ff d0		 call	 eax
  00027	3b f4		 cmp	 esi, esp
  00029	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002e	eb 44		 jmp	 SHORT $LN1@Reallocate
$LN2@Reallocate:

; 164  : 		}
; 165  : 
; 166  : 		if (nBytes==0)

  00030	83 7d 0c 00	 cmp	 DWORD PTR _nBytes$[ebp], 0
  00034	75 1e		 jne	 SHORT $LN3@Reallocate

; 167  : 		{
; 168  : 			  Free(p);

  00036	8b f4		 mov	 esi, esp
  00038	8b 4d 08	 mov	 ecx, DWORD PTR _p$[ebp]
  0003b	51		 push	 ecx
  0003c	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  0003f	8b 02		 mov	 eax, DWORD PTR [edx]
  00041	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00044	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  00047	ff d2		 call	 edx
  00049	3b f4		 cmp	 esi, esp
  0004b	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 169  : 			  return NULL;

  00050	33 c0		 xor	 eax, eax
  00052	eb 20		 jmp	 SHORT $LN1@Reallocate
$LN3@Reallocate:

; 170  : 		}
; 171  : 
; 172  : 		return( ::HeapReAlloc( m_hHeap, 0, p, nBytes ) );

  00054	8b f4		 mov	 esi, esp
  00056	8b 45 0c	 mov	 eax, DWORD PTR _nBytes$[ebp]
  00059	50		 push	 eax
  0005a	8b 4d 08	 mov	 ecx, DWORD PTR _p$[ebp]
  0005d	51		 push	 ecx
  0005e	6a 00		 push	 0
  00060	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00063	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  00066	50		 push	 eax
  00067	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__HeapReAlloc@16
  0006d	3b f4		 cmp	 esi, esp
  0006f	e8 00 00 00 00	 call	 __RTC_CheckEsp
$LN1@Reallocate:

; 173  : 	}

  00074	5e		 pop	 esi
  00075	83 c4 04	 add	 esp, 4
  00078	3b ec		 cmp	 ebp, esp
  0007a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0007f	8b e5		 mov	 esp, ebp
  00081	5d		 pop	 ebp
  00082	c2 08 00	 ret	 8
?Reallocate@CWin32Heap@ATL@@UAEPAXPAXI@Z ENDP		; ATL::CWin32Heap::Reallocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlmem.h
;	COMDAT ?GetSize@CWin32Heap@ATL@@UAEIPAX@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_p$ = 8							; size = 4
?GetSize@CWin32Heap@ATL@@UAEIPAX@Z PROC			; ATL::CWin32Heap::GetSize, COMDAT
; _this$ = ecx

; 175  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 176  : 		return( ::HeapSize( m_hHeap, 0, p ) );

  0000f	8b f4		 mov	 esi, esp
  00011	8b 45 08	 mov	 eax, DWORD PTR _p$[ebp]
  00014	50		 push	 eax
  00015	6a 00		 push	 0
  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0001d	52		 push	 edx
  0001e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__HeapSize@12
  00024	3b f4		 cmp	 esi, esp
  00026	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 177  : 	}

  0002b	5e		 pop	 esi
  0002c	83 c4 04	 add	 esp, 4
  0002f	3b ec		 cmp	 ebp, esp
  00031	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00036	8b e5		 mov	 esp, ebp
  00038	5d		 pop	 ebp
  00039	c2 04 00	 ret	 4
?GetSize@CWin32Heap@ATL@@UAEIPAX@Z ENDP			; ATL::CWin32Heap::GetSize
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_GCWin32Heap@ATL@@UAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___flags$ = 8						; size = 4
??_GCWin32Heap@ATL@@UAEPAXI@Z PROC			; ATL::CWin32Heap::`scalar deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??1CWin32Heap@ATL@@UAE@XZ ; ATL::CWin32Heap::~CWin32Heap
  00016	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  00019	83 e0 01	 and	 eax, 1
  0001c	74 0e		 je	 SHORT $LN2@scalar
  0001e	6a 0c		 push	 12			; 0000000cH
  00020	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00023	51		 push	 ecx
  00024	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  00029	83 c4 08	 add	 esp, 8
$LN2@scalar:
  0002c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002f	83 c4 04	 add	 esp, 4
  00032	3b ec		 cmp	 ebp, esp
  00034	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00039	8b e5		 mov	 esp, ebp
  0003b	5d		 pop	 ebp
  0003c	c2 04 00	 ret	 4
??_GCWin32Heap@ATL@@UAEPAXI@Z ENDP			; ATL::CWin32Heap::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpstr.h
;	COMDAT ?AddRef@CStringData@ATL@@QAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?AddRef@CStringData@ATL@@QAEXXZ PROC			; ATL::CStringData::AddRef, COMDAT
; _this$ = ecx

; 63   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 64   : 		ATLASSERT(nRefs > 0);
; 65   : 		_InterlockedIncrement(&nRefs);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	83 c0 0c	 add	 eax, 12			; 0000000cH
  00014	f0 ff 00	 lock	  inc	 DWORD PTR [eax]

; 66   : 	}

  00017	8b e5		 mov	 esp, ebp
  00019	5d		 pop	 ebp
  0001a	c3		 ret	 0
?AddRef@CStringData@ATL@@QAEXXZ ENDP			; ATL::CStringData::AddRef
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpstr.h
;	COMDAT ??0CNilStringData@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0CNilStringData@ATL@@QAE@XZ PROC			; ATL::CNilStringData::CNilStringData, COMDAT
; _this$ = ecx

; 112  : 	CNilStringData() throw()

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 113  : 	{
; 114  : 		pStringMgr = NULL;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0

; 115  : 		nRefs = 2;  // Never gets freed by IAtlStringMgr

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	c7 41 0c 02 00
	00 00		 mov	 DWORD PTR [ecx+12], 2

; 116  : 		nDataLength = 0;

  00021	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00024	c7 42 04 00 00
	00 00		 mov	 DWORD PTR [edx+4], 0

; 117  : 		nAllocLength = 0;

  0002b	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002e	c7 40 08 00 00
	00 00		 mov	 DWORD PTR [eax+8], 0

; 118  : 		achNil[0] = 0;

  00035	b9 02 00 00 00	 mov	 ecx, 2
  0003a	6b d1 00	 imul	 edx, ecx, 0
  0003d	33 c0		 xor	 eax, eax
  0003f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00042	66 89 44 11 10	 mov	 WORD PTR [ecx+edx+16], ax

; 119  : 		achNil[1] = 0;

  00047	ba 02 00 00 00	 mov	 edx, 2
  0004c	c1 e2 00	 shl	 edx, 0
  0004f	33 c0		 xor	 eax, eax
  00051	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00054	66 89 44 11 10	 mov	 WORD PTR [ecx+edx+16], ax

; 120  : 	}

  00059	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0005c	8b e5		 mov	 esp, ebp
  0005e	5d		 pop	 ebp
  0005f	c3		 ret	 0
??0CNilStringData@ATL@@QAE@XZ ENDP			; ATL::CNilStringData::CNilStringData
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpstr.h
;	COMDAT ?SetManager@CNilStringData@ATL@@QAEXPAUIAtlStringMgr@2@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pMgr$ = 8						; size = 4
?SetManager@CNilStringData@ATL@@QAEXPAUIAtlStringMgr@2@@Z PROC ; ATL::CNilStringData::SetManager, COMDAT
; _this$ = ecx

; 123  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 124  : 		ATLASSERT( pStringMgr == NULL );
; 125  : 		pStringMgr = pMgr;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR _pMgr$[ebp]
  00014	89 08		 mov	 DWORD PTR [eax], ecx

; 126  : 	}

  00016	8b e5		 mov	 esp, ebp
  00018	5d		 pop	 ebp
  00019	c2 04 00	 ret	 4
?SetManager@CNilStringData@ATL@@QAEXPAUIAtlStringMgr@2@@Z ENDP ; ATL::CNilStringData::SetManager
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ??0CAtlStringMgr@ATL@@QAE@PAUIAtlMemMgr@1@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pMemMgr$ = 8						; size = 4
??0CAtlStringMgr@ATL@@QAE@PAUIAtlMemMgr@1@@Z PROC	; ATL::CAtlStringMgr::CAtlStringMgr, COMDAT
; _this$ = ecx

; 40   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_7CAtlStringMgr@ATL@@6B@

; 39   : 		m_pMemMgr( pMemMgr )

  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	8b 55 08	 mov	 edx, DWORD PTR _pMemMgr$[ebp]
  0001d	89 51 04	 mov	 DWORD PTR [ecx+4], edx

; 40   : 	{

  00020	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00023	83 c1 08	 add	 ecx, 8
  00026	e8 00 00 00 00	 call	 ??0CNilStringData@ATL@@QAE@XZ ; ATL::CNilStringData::CNilStringData

; 41   : 		m_nil.SetManager( this );

  0002b	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002e	50		 push	 eax
  0002f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00032	83 c1 08	 add	 ecx, 8
  00035	e8 00 00 00 00	 call	 ?SetManager@CNilStringData@ATL@@QAEXPAUIAtlStringMgr@2@@Z ; ATL::CNilStringData::SetManager

; 42   : 	}

  0003a	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0003d	83 c4 04	 add	 esp, 4
  00040	3b ec		 cmp	 ebp, esp
  00042	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00047	8b e5		 mov	 esp, ebp
  00049	5d		 pop	 ebp
  0004a	c2 04 00	 ret	 4
??0CAtlStringMgr@ATL@@QAE@PAUIAtlMemMgr@1@@Z ENDP	; ATL::CAtlStringMgr::CAtlStringMgr
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ
_TEXT	SEGMENT
__$EHRec$ = -12						; size = 12
?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ PROC ; ATL::CAtlStringMgr::GetInstance, COMDAT

; 51   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	56		 push	 esi

; 52   : #pragma warning(push)
; 53   : #pragma warning(disable: 4640) // will always be initialized on entry thread by CImageStaticInitializer
; 54   : 		static CWin32Heap strHeap( ::GetProcessHeap() );

  00019	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:__tls_array
  0001f	8b 08		 mov	 ecx, DWORD PTR [eax]
  00021	8b 15 00 00 00
	00		 mov	 edx, DWORD PTR ?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS0<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  00027	3b 91 00 00 00
	00		 cmp	 edx, DWORD PTR __Init_thread_epoch[ecx]
  0002d	7e 58		 jle	 SHORT $LN2@GetInstanc
  0002f	68 00 00 00 00	 push	 OFFSET ?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS0<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  00034	e8 00 00 00 00	 call	 __Init_thread_header
  00039	83 c4 04	 add	 esp, 4
  0003c	83 3d 00 00 00
	00 ff		 cmp	 DWORD PTR ?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA, -1 ; TSS0<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  00043	75 42		 jne	 SHORT $LN2@GetInstanc
  00045	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0004c	8b f4		 mov	 esi, esp
  0004e	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetProcessHeap@0
  00054	3b f4		 cmp	 esi, esp
  00056	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005b	50		 push	 eax
  0005c	b9 00 00 00 00	 mov	 ecx, OFFSET ?strHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4VCWin32Heap@3@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strHeap
  00061	e8 00 00 00 00	 call	 ??0CWin32Heap@ATL@@QAE@PAX@Z ; ATL::CWin32Heap::CWin32Heap
  00066	68 00 00 00 00	 push	 OFFSET ??__FstrHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strHeap''
  0006b	e8 00 00 00 00	 call	 _atexit
  00070	83 c4 04	 add	 esp, 4
  00073	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0007a	68 00 00 00 00	 push	 OFFSET ?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS0<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  0007f	e8 00 00 00 00	 call	 __Init_thread_footer
  00084	83 c4 04	 add	 esp, 4
$LN2@GetInstanc:

; 55   : 		static CAtlStringMgr strMgr(&strHeap);

  00087	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:__tls_array
  0008d	8b 08		 mov	 ecx, DWORD PTR [eax]
  0008f	8b 15 00 00 00
	00		 mov	 edx, DWORD PTR ?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS1<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  00095	3b 91 00 00 00
	00		 cmp	 edx, DWORD PTR __Init_thread_epoch[ecx]
  0009b	7e 4d		 jle	 SHORT $LN3@GetInstanc
  0009d	68 00 00 00 00	 push	 OFFSET ?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS1<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  000a2	e8 00 00 00 00	 call	 __Init_thread_header
  000a7	83 c4 04	 add	 esp, 4
  000aa	83 3d 00 00 00
	00 ff		 cmp	 DWORD PTR ?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA, -1 ; TSS1<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  000b1	75 37		 jne	 SHORT $LN3@GetInstanc
  000b3	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1
  000ba	68 00 00 00 00	 push	 OFFSET ?strHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4VCWin32Heap@3@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strHeap
  000bf	b9 00 00 00 00	 mov	 ecx, OFFSET ?strMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4V23@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strMgr
  000c4	e8 00 00 00 00	 call	 ??0CAtlStringMgr@ATL@@QAE@PAUIAtlMemMgr@1@@Z ; ATL::CAtlStringMgr::CAtlStringMgr
  000c9	68 00 00 00 00	 push	 OFFSET ??__FstrMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strMgr''
  000ce	e8 00 00 00 00	 call	 _atexit
  000d3	83 c4 04	 add	 esp, 4
  000d6	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000dd	68 00 00 00 00	 push	 OFFSET ?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS1<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  000e2	e8 00 00 00 00	 call	 __Init_thread_footer
  000e7	83 c4 04	 add	 esp, 4
$LN3@GetInstanc:

; 56   : #pragma warning(pop)
; 57   : 
; 58   : 		return &strMgr;

  000ea	b8 00 00 00 00	 mov	 eax, OFFSET ?strMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4V23@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strMgr

; 59   : 	}

  000ef	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000f2	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000f9	5e		 pop	 esi
  000fa	83 c4 0c	 add	 esp, 12			; 0000000cH
  000fd	3b ec		 cmp	 ebp, esp
  000ff	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00104	8b e5		 mov	 esp, ebp
  00106	5d		 pop	 ebp
  00107	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ$0:
  00000	68 00 00 00 00	 push	 OFFSET ?$TSS0@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS0<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  00005	e8 00 00 00 00	 call	 __Init_thread_abort
  0000a	59		 pop	 ecx
  0000b	c3		 ret	 0
__unwindfunclet$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ$1:
  0000c	68 00 00 00 00	 push	 OFFSET ?$TSS1@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4HA ; TSS1<`template-parameter-2',ATL::CAtlStringMgr::tInstance,ATL::IAtlStringMgr * * const volatile,void,int, ?? &>
  00011	e8 00 00 00 00	 call	 __Init_thread_abort
  00016	59		 pop	 ecx
  00017	c3		 ret	 0
__ehhandler$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ:
  00018	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ
  0001d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ ENDP ; ATL::CAtlStringMgr::GetInstance
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ?Allocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@HH@Z
_TEXT	SEGMENT
tv77 = -52						; size = 4
tv65 = -48						; size = 4
___atl_condVal$1 = -44					; size = 4
_nAlignedChars$ = -40					; size = 4
_nDataBytes$ = -32					; size = 4
_pData$ = -24						; size = 4
_nTotalSize$ = -16					; size = 4
___atl_condVal$2 = -8					; size = 4
_this$ = -4						; size = 4
_nChars$ = 8						; size = 4
_nCharSize$ = 12					; size = 4
?Allocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@HH@Z PROC ; ATL::CAtlStringMgr::Allocate, COMDAT
; _this$ = ecx

; 65   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 34	 sub	 esp, 52			; 00000034H
  00006	56		 push	 esi
  00007	57		 push	 edi
  00008	51		 push	 ecx
  00009	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  0000c	b9 0d 00 00 00	 mov	 ecx, 13			; 0000000dH
  00011	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00016	f3 ab		 rep stosd
  00018	59		 pop	 ecx
  00019	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
$LN4@Allocate:

; 66   : 		ATLENSURE_RETURN_VAL( nChars>=0, NULL );

  0001c	83 7d 08 00	 cmp	 DWORD PTR _nChars$[ebp], 0
  00020	7c 09		 jl	 SHORT $LN15@Allocate
  00022	c7 45 d0 01 00
	00 00		 mov	 DWORD PTR tv65[ebp], 1
  00029	eb 07		 jmp	 SHORT $LN16@Allocate
$LN15@Allocate:
  0002b	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR tv65[ebp], 0
$LN16@Allocate:
  00032	8b 45 d0	 mov	 eax, DWORD PTR tv65[ebp]
  00035	89 45 f8	 mov	 DWORD PTR ___atl_condVal$2[ebp], eax
  00038	83 7d f8 00	 cmp	 DWORD PTR ___atl_condVal$2[ebp], 0
  0003c	75 07		 jne	 SHORT $LN2@Allocate
  0003e	33 c0		 xor	 eax, eax
  00040	e9 e7 00 00 00	 jmp	 $LN1@Allocate
$LN2@Allocate:
  00045	33 c9		 xor	 ecx, ecx
  00047	75 d3		 jne	 SHORT $LN4@Allocate

; 67   : 
; 68   : 		size_t nTotalSize;
; 69   : 		CStringData* pData;
; 70   : 		size_t nDataBytes;
; 71   : 
; 72   : 		if( FAILED(::ATL::AtlAdd(&nChars, nChars, 1)) )

  00049	6a 01		 push	 1
  0004b	8b 55 08	 mov	 edx, DWORD PTR _nChars$[ebp]
  0004e	52		 push	 edx
  0004f	8d 45 08	 lea	 eax, DWORD PTR _nChars$[ebp]
  00052	50		 push	 eax
  00053	e8 00 00 00 00	 call	 ??$AtlAdd@H@ATL@@YAJPAHHH@Z ; ATL::AtlAdd<int>
  00058	83 c4 0c	 add	 esp, 12			; 0000000cH
  0005b	85 c0		 test	 eax, eax
  0005d	7d 07		 jge	 SHORT $LN9@Allocate

; 73   : 		{
; 74   : 			return NULL;

  0005f	33 c0		 xor	 eax, eax
  00061	e9 c6 00 00 00	 jmp	 $LN1@Allocate
$LN9@Allocate:

; 75   : 		}
; 76   : 
; 77   : 		int nAlignedChars = ::ATL::AtlAlignUp( nChars, 8 );  // Prevent excessive reallocation.  The heap will usually round up anyway.

  00066	6a 08		 push	 8
  00068	8b 4d 08	 mov	 ecx, DWORD PTR _nChars$[ebp]
  0006b	51		 push	 ecx
  0006c	e8 00 00 00 00	 call	 ??$AtlAlignUp@H@ATL@@YGHHK@Z ; ATL::AtlAlignUp<int>
  00071	89 45 d8	 mov	 DWORD PTR _nAlignedChars$[ebp], eax
$LN7@Allocate:

; 78   : 		ATLENSURE_RETURN_VAL( nChars<=nAlignedChars, NULL );

  00074	8b 55 08	 mov	 edx, DWORD PTR _nChars$[ebp]
  00077	3b 55 d8	 cmp	 edx, DWORD PTR _nAlignedChars$[ebp]
  0007a	7f 09		 jg	 SHORT $LN17@Allocate
  0007c	c7 45 cc 01 00
	00 00		 mov	 DWORD PTR tv77[ebp], 1
  00083	eb 07		 jmp	 SHORT $LN18@Allocate
$LN17@Allocate:
  00085	c7 45 cc 00 00
	00 00		 mov	 DWORD PTR tv77[ebp], 0
$LN18@Allocate:
  0008c	8b 45 cc	 mov	 eax, DWORD PTR tv77[ebp]
  0008f	89 45 d4	 mov	 DWORD PTR ___atl_condVal$1[ebp], eax
  00092	83 7d d4 00	 cmp	 DWORD PTR ___atl_condVal$1[ebp], 0
  00096	75 07		 jne	 SHORT $LN5@Allocate
  00098	33 c0		 xor	 eax, eax
  0009a	e9 8d 00 00 00	 jmp	 $LN1@Allocate
$LN5@Allocate:
  0009f	33 c9		 xor	 ecx, ecx
  000a1	75 d1		 jne	 SHORT $LN7@Allocate

; 79   : 
; 80   : 		if(	FAILED(::ATL::AtlMultiply(&nDataBytes, static_cast<size_t>(nAlignedChars), static_cast<size_t>(nCharSize))) ||

  000a3	8b 55 0c	 mov	 edx, DWORD PTR _nCharSize$[ebp]
  000a6	52		 push	 edx
  000a7	8b 45 d8	 mov	 eax, DWORD PTR _nAlignedChars$[ebp]
  000aa	50		 push	 eax
  000ab	8d 4d e0	 lea	 ecx, DWORD PTR _nDataBytes$[ebp]
  000ae	51		 push	 ecx
  000af	e8 00 00 00 00	 call	 ??$AtlMultiply@I@ATL@@YAJPAIII@Z ; ATL::AtlMultiply<unsigned int>
  000b4	83 c4 0c	 add	 esp, 12			; 0000000cH
  000b7	85 c0		 test	 eax, eax
  000b9	7c 16		 jl	 SHORT $LN12@Allocate
  000bb	8b 55 e0	 mov	 edx, DWORD PTR _nDataBytes$[ebp]
  000be	52		 push	 edx
  000bf	6a 10		 push	 16			; 00000010H
  000c1	8d 45 f0	 lea	 eax, DWORD PTR _nTotalSize$[ebp]
  000c4	50		 push	 eax
  000c5	e8 00 00 00 00	 call	 ??$AtlAdd@I@ATL@@YAJPAIII@Z ; ATL::AtlAdd<unsigned int>
  000ca	83 c4 0c	 add	 esp, 12			; 0000000cH
  000cd	85 c0		 test	 eax, eax
  000cf	7d 04		 jge	 SHORT $LN11@Allocate
$LN12@Allocate:

; 81   : 			FAILED(::ATL::AtlAdd(&nTotalSize, static_cast<size_t>(sizeof( CStringData )), nDataBytes)))
; 82   : 		{
; 83   : 			return NULL;

  000d1	33 c0		 xor	 eax, eax
  000d3	eb 57		 jmp	 SHORT $LN1@Allocate
$LN11@Allocate:

; 84   : 		}
; 85   : 		pData = static_cast< CStringData* >( m_pMemMgr->Allocate( nTotalSize ) );

  000d5	8b f4		 mov	 esi, esp
  000d7	8b 4d f0	 mov	 ecx, DWORD PTR _nTotalSize$[ebp]
  000da	51		 push	 ecx
  000db	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  000de	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  000e1	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  000e4	8b 10		 mov	 edx, DWORD PTR [eax]
  000e6	8b 49 04	 mov	 ecx, DWORD PTR [ecx+4]
  000e9	8b 02		 mov	 eax, DWORD PTR [edx]
  000eb	ff d0		 call	 eax
  000ed	3b f4		 cmp	 esi, esp
  000ef	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000f4	89 45 e8	 mov	 DWORD PTR _pData$[ebp], eax

; 86   : 		if( pData == NULL )

  000f7	83 7d e8 00	 cmp	 DWORD PTR _pData$[ebp], 0
  000fb	75 04		 jne	 SHORT $LN13@Allocate

; 87   : 		{
; 88   : 			return( NULL );

  000fd	33 c0		 xor	 eax, eax
  000ff	eb 2b		 jmp	 SHORT $LN1@Allocate
$LN13@Allocate:

; 89   : 		}
; 90   : 		pData->pStringMgr = this;

  00101	8b 4d e8	 mov	 ecx, DWORD PTR _pData$[ebp]
  00104	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00107	89 11		 mov	 DWORD PTR [ecx], edx

; 91   : 		pData->nRefs = 1;

  00109	8b 45 e8	 mov	 eax, DWORD PTR _pData$[ebp]
  0010c	c7 40 0c 01 00
	00 00		 mov	 DWORD PTR [eax+12], 1

; 92   : 		pData->nAllocLength = nAlignedChars - 1;

  00113	8b 4d d8	 mov	 ecx, DWORD PTR _nAlignedChars$[ebp]
  00116	83 e9 01	 sub	 ecx, 1
  00119	8b 55 e8	 mov	 edx, DWORD PTR _pData$[ebp]
  0011c	89 4a 08	 mov	 DWORD PTR [edx+8], ecx

; 93   : 		pData->nDataLength = 0;

  0011f	8b 45 e8	 mov	 eax, DWORD PTR _pData$[ebp]
  00122	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [eax+4], 0

; 94   : 
; 95   : 		return( pData );

  00129	8b 45 e8	 mov	 eax, DWORD PTR _pData$[ebp]
$LN1@Allocate:

; 96   : 	}

  0012c	52		 push	 edx
  0012d	8b cd		 mov	 ecx, ebp
  0012f	50		 push	 eax
  00130	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN22@Allocate
  00136	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  0013b	58		 pop	 eax
  0013c	5a		 pop	 edx
  0013d	5f		 pop	 edi
  0013e	5e		 pop	 esi
  0013f	83 c4 34	 add	 esp, 52			; 00000034H
  00142	3b ec		 cmp	 ebp, esp
  00144	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00149	8b e5		 mov	 esp, ebp
  0014b	5d		 pop	 ebp
  0014c	c2 08 00	 ret	 8
  0014f	90		 npad	 1
$LN22@Allocate:
  00150	02 00 00 00	 DD	 2
  00154	00 00 00 00	 DD	 $LN21@Allocate
$LN21@Allocate:
  00158	f0 ff ff ff	 DD	 -16			; fffffff0H
  0015c	04 00 00 00	 DD	 4
  00160	00 00 00 00	 DD	 $LN19@Allocate
  00164	e0 ff ff ff	 DD	 -32			; ffffffe0H
  00168	04 00 00 00	 DD	 4
  0016c	00 00 00 00	 DD	 $LN20@Allocate
$LN20@Allocate:
  00170	6e		 DB	 110			; 0000006eH
  00171	44		 DB	 68			; 00000044H
  00172	61		 DB	 97			; 00000061H
  00173	74		 DB	 116			; 00000074H
  00174	61		 DB	 97			; 00000061H
  00175	42		 DB	 66			; 00000042H
  00176	79		 DB	 121			; 00000079H
  00177	74		 DB	 116			; 00000074H
  00178	65		 DB	 101			; 00000065H
  00179	73		 DB	 115			; 00000073H
  0017a	00		 DB	 0
$LN19@Allocate:
  0017b	6e		 DB	 110			; 0000006eH
  0017c	54		 DB	 84			; 00000054H
  0017d	6f		 DB	 111			; 0000006fH
  0017e	74		 DB	 116			; 00000074H
  0017f	61		 DB	 97			; 00000061H
  00180	6c		 DB	 108			; 0000006cH
  00181	53		 DB	 83			; 00000053H
  00182	69		 DB	 105			; 00000069H
  00183	7a		 DB	 122			; 0000007aH
  00184	65		 DB	 101			; 00000065H
  00185	00		 DB	 0
?Allocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@HH@Z ENDP ; ATL::CAtlStringMgr::Allocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ?Free@CAtlStringMgr@ATL@@UAEXPAUCStringData@2@@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pData$ = 8						; size = 4
?Free@CAtlStringMgr@ATL@@UAEXPAUCStringData@2@@Z PROC	; ATL::CAtlStringMgr::Free, COMDAT
; _this$ = ecx

; 98   : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	56		 push	 esi
  00005	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000c	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
$LN2@Free:

; 99   : 		ATLASSUME(pData != NULL);

  0000f	33 c0		 xor	 eax, eax
  00011	75 fc		 jne	 SHORT $LN2@Free

; 100  : 		ATLASSERT(pData->pStringMgr == this);
; 101  : 
; 102  : 		m_pMemMgr->Free( pData );

  00013	8b f4		 mov	 esi, esp
  00015	8b 4d 08	 mov	 ecx, DWORD PTR _pData$[ebp]
  00018	51		 push	 ecx
  00019	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  0001c	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  0001f	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00022	8b 10		 mov	 edx, DWORD PTR [eax]
  00024	8b 49 04	 mov	 ecx, DWORD PTR [ecx+4]
  00027	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  0002a	ff d0		 call	 eax
  0002c	3b f4		 cmp	 esi, esp
  0002e	e8 00 00 00 00	 call	 __RTC_CheckEsp

; 103  : 	}

  00033	5e		 pop	 esi
  00034	83 c4 04	 add	 esp, 4
  00037	3b ec		 cmp	 ebp, esp
  00039	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003e	8b e5		 mov	 esp, ebp
  00040	5d		 pop	 ebp
  00041	c2 04 00	 ret	 4
?Free@CAtlStringMgr@ATL@@UAEXPAUCStringData@2@@Z ENDP	; ATL::CAtlStringMgr::Free
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ?Reallocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@PAU32@HH@Z
_TEXT	SEGMENT
tv77 = -52						; size = 4
tv65 = -48						; size = 4
___atl_condVal$1 = -44					; size = 4
_nAlignedChars$ = -40					; size = 4
_nDataBytes$ = -32					; size = 4
_nTotalSize$ = -20					; size = 4
_pNewData$ = -12					; size = 4
___atl_condVal$2 = -8					; size = 4
_this$ = -4						; size = 4
_pData$ = 8						; size = 4
_nChars$ = 12						; size = 4
_nCharSize$ = 16					; size = 4
?Reallocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@PAU32@HH@Z PROC ; ATL::CAtlStringMgr::Reallocate, COMDAT
; _this$ = ecx

; 108  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 34	 sub	 esp, 52			; 00000034H
  00006	56		 push	 esi
  00007	57		 push	 edi
  00008	51		 push	 ecx
  00009	8d 7d cc	 lea	 edi, DWORD PTR [ebp-52]
  0000c	b9 0d 00 00 00	 mov	 ecx, 13			; 0000000dH
  00011	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00016	f3 ab		 rep stosd
  00018	59		 pop	 ecx
  00019	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
$LN4@Reallocate:

; 109  : 		ATLENSURE_RETURN_VAL( nChars>=0, NULL );

  0001c	83 7d 0c 00	 cmp	 DWORD PTR _nChars$[ebp], 0
  00020	7c 09		 jl	 SHORT $LN15@Reallocate
  00022	c7 45 d0 01 00
	00 00		 mov	 DWORD PTR tv65[ebp], 1
  00029	eb 07		 jmp	 SHORT $LN16@Reallocate
$LN15@Reallocate:
  0002b	c7 45 d0 00 00
	00 00		 mov	 DWORD PTR tv65[ebp], 0
$LN16@Reallocate:
  00032	8b 45 d0	 mov	 eax, DWORD PTR tv65[ebp]
  00035	89 45 f8	 mov	 DWORD PTR ___atl_condVal$2[ebp], eax
  00038	83 7d f8 00	 cmp	 DWORD PTR ___atl_condVal$2[ebp], 0
  0003c	75 07		 jne	 SHORT $LN2@Reallocate
  0003e	33 c0		 xor	 eax, eax
  00040	e9 cd 00 00 00	 jmp	 $LN1@Reallocate
$LN2@Reallocate:
  00045	33 c9		 xor	 ecx, ecx
  00047	75 d3		 jne	 SHORT $LN4@Reallocate

; 110  : 		ATLASSERT( pData->pStringMgr == this );
; 111  : 
; 112  : 		CStringData* pNewData;
; 113  : 		ULONG nTotalSize;
; 114  : 		ULONG nDataBytes;
; 115  : 
; 116  : 		if( FAILED(::ATL::AtlAdd(&nChars, nChars, 1)) )

  00049	6a 01		 push	 1
  0004b	8b 55 0c	 mov	 edx, DWORD PTR _nChars$[ebp]
  0004e	52		 push	 edx
  0004f	8d 45 0c	 lea	 eax, DWORD PTR _nChars$[ebp]
  00052	50		 push	 eax
  00053	e8 00 00 00 00	 call	 ??$AtlAdd@H@ATL@@YAJPAHHH@Z ; ATL::AtlAdd<int>
  00058	83 c4 0c	 add	 esp, 12			; 0000000cH
  0005b	85 c0		 test	 eax, eax
  0005d	7d 07		 jge	 SHORT $LN9@Reallocate

; 117  : 		{
; 118  : 			return NULL;

  0005f	33 c0		 xor	 eax, eax
  00061	e9 ac 00 00 00	 jmp	 $LN1@Reallocate
$LN9@Reallocate:

; 119  : 		}
; 120  : 
; 121  : 		int nAlignedChars = ::ATL::AtlAlignUp( nChars, 8 );  // Prevent excessive reallocation.  The heap will usually round up anyway.

  00066	6a 08		 push	 8
  00068	8b 4d 0c	 mov	 ecx, DWORD PTR _nChars$[ebp]
  0006b	51		 push	 ecx
  0006c	e8 00 00 00 00	 call	 ??$AtlAlignUp@H@ATL@@YGHHK@Z ; ATL::AtlAlignUp<int>
  00071	89 45 d8	 mov	 DWORD PTR _nAlignedChars$[ebp], eax
$LN7@Reallocate:

; 122  : 		ATLENSURE_RETURN_VAL( nChars<=nAlignedChars, NULL );

  00074	8b 55 0c	 mov	 edx, DWORD PTR _nChars$[ebp]
  00077	3b 55 d8	 cmp	 edx, DWORD PTR _nAlignedChars$[ebp]
  0007a	7f 09		 jg	 SHORT $LN17@Reallocate
  0007c	c7 45 cc 01 00
	00 00		 mov	 DWORD PTR tv77[ebp], 1
  00083	eb 07		 jmp	 SHORT $LN18@Reallocate
$LN17@Reallocate:
  00085	c7 45 cc 00 00
	00 00		 mov	 DWORD PTR tv77[ebp], 0
$LN18@Reallocate:
  0008c	8b 45 cc	 mov	 eax, DWORD PTR tv77[ebp]
  0008f	89 45 d4	 mov	 DWORD PTR ___atl_condVal$1[ebp], eax
  00092	83 7d d4 00	 cmp	 DWORD PTR ___atl_condVal$1[ebp], 0
  00096	75 04		 jne	 SHORT $LN5@Reallocate
  00098	33 c0		 xor	 eax, eax
  0009a	eb 76		 jmp	 SHORT $LN1@Reallocate
$LN5@Reallocate:
  0009c	33 c9		 xor	 ecx, ecx
  0009e	75 d4		 jne	 SHORT $LN7@Reallocate

; 123  : 
; 124  : 		if(	FAILED(::ATL::AtlMultiply(&nDataBytes, static_cast<ULONG>(nAlignedChars), static_cast<ULONG>(nCharSize))) ||

  000a0	8b 55 10	 mov	 edx, DWORD PTR _nCharSize$[ebp]
  000a3	52		 push	 edx
  000a4	8b 45 d8	 mov	 eax, DWORD PTR _nAlignedChars$[ebp]
  000a7	50		 push	 eax
  000a8	8d 4d e0	 lea	 ecx, DWORD PTR _nDataBytes$[ebp]
  000ab	51		 push	 ecx
  000ac	e8 00 00 00 00	 call	 ??$AtlMultiply@K@ATL@@YAJPAKKK@Z ; ATL::AtlMultiply<unsigned long>
  000b1	83 c4 0c	 add	 esp, 12			; 0000000cH
  000b4	85 c0		 test	 eax, eax
  000b6	7c 16		 jl	 SHORT $LN12@Reallocate
  000b8	8b 55 e0	 mov	 edx, DWORD PTR _nDataBytes$[ebp]
  000bb	52		 push	 edx
  000bc	6a 10		 push	 16			; 00000010H
  000be	8d 45 ec	 lea	 eax, DWORD PTR _nTotalSize$[ebp]
  000c1	50		 push	 eax
  000c2	e8 00 00 00 00	 call	 ??$AtlAdd@K@ATL@@YAJPAKKK@Z ; ATL::AtlAdd<unsigned long>
  000c7	83 c4 0c	 add	 esp, 12			; 0000000cH
  000ca	85 c0		 test	 eax, eax
  000cc	7d 04		 jge	 SHORT $LN11@Reallocate
$LN12@Reallocate:

; 125  : 			FAILED(::ATL::AtlAdd(&nTotalSize, static_cast<ULONG>(sizeof( CStringData )), nDataBytes)))
; 126  : 		{
; 127  : 			return NULL;

  000ce	33 c0		 xor	 eax, eax
  000d0	eb 40		 jmp	 SHORT $LN1@Reallocate
$LN11@Reallocate:

; 128  : 		}
; 129  : 		pNewData = static_cast< CStringData* >( m_pMemMgr->Reallocate( pData, nTotalSize ) );

  000d2	8b f4		 mov	 esi, esp
  000d4	8b 4d ec	 mov	 ecx, DWORD PTR _nTotalSize$[ebp]
  000d7	51		 push	 ecx
  000d8	8b 55 08	 mov	 edx, DWORD PTR _pData$[ebp]
  000db	52		 push	 edx
  000dc	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  000df	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  000e2	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  000e5	8b 01		 mov	 eax, DWORD PTR [ecx]
  000e7	8b 4a 04	 mov	 ecx, DWORD PTR [edx+4]
  000ea	8b 50 08	 mov	 edx, DWORD PTR [eax+8]
  000ed	ff d2		 call	 edx
  000ef	3b f4		 cmp	 esi, esp
  000f1	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000f6	89 45 f4	 mov	 DWORD PTR _pNewData$[ebp], eax

; 130  : 		if( pNewData == NULL )

  000f9	83 7d f4 00	 cmp	 DWORD PTR _pNewData$[ebp], 0
  000fd	75 04		 jne	 SHORT $LN13@Reallocate

; 131  : 		{
; 132  : 			return NULL;

  000ff	33 c0		 xor	 eax, eax
  00101	eb 0f		 jmp	 SHORT $LN1@Reallocate
$LN13@Reallocate:

; 133  : 		}
; 134  : 		pNewData->nAllocLength = nAlignedChars - 1;

  00103	8b 45 d8	 mov	 eax, DWORD PTR _nAlignedChars$[ebp]
  00106	83 e8 01	 sub	 eax, 1
  00109	8b 4d f4	 mov	 ecx, DWORD PTR _pNewData$[ebp]
  0010c	89 41 08	 mov	 DWORD PTR [ecx+8], eax

; 135  : 
; 136  : 		return pNewData;

  0010f	8b 45 f4	 mov	 eax, DWORD PTR _pNewData$[ebp]
$LN1@Reallocate:

; 137  : 	}

  00112	52		 push	 edx
  00113	8b cd		 mov	 ecx, ebp
  00115	50		 push	 eax
  00116	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN22@Reallocate
  0011c	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  00121	58		 pop	 eax
  00122	5a		 pop	 edx
  00123	5f		 pop	 edi
  00124	5e		 pop	 esi
  00125	83 c4 34	 add	 esp, 52			; 00000034H
  00128	3b ec		 cmp	 ebp, esp
  0012a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0012f	8b e5		 mov	 esp, ebp
  00131	5d		 pop	 ebp
  00132	c2 0c 00	 ret	 12			; 0000000cH
  00135	0f 1f 00	 npad	 3
$LN22@Reallocate:
  00138	02 00 00 00	 DD	 2
  0013c	00 00 00 00	 DD	 $LN21@Reallocate
$LN21@Reallocate:
  00140	ec ff ff ff	 DD	 -20			; ffffffecH
  00144	04 00 00 00	 DD	 4
  00148	00 00 00 00	 DD	 $LN19@Reallocate
  0014c	e0 ff ff ff	 DD	 -32			; ffffffe0H
  00150	04 00 00 00	 DD	 4
  00154	00 00 00 00	 DD	 $LN20@Reallocate
$LN20@Reallocate:
  00158	6e		 DB	 110			; 0000006eH
  00159	44		 DB	 68			; 00000044H
  0015a	61		 DB	 97			; 00000061H
  0015b	74		 DB	 116			; 00000074H
  0015c	61		 DB	 97			; 00000061H
  0015d	42		 DB	 66			; 00000042H
  0015e	79		 DB	 121			; 00000079H
  0015f	74		 DB	 116			; 00000074H
  00160	65		 DB	 101			; 00000065H
  00161	73		 DB	 115			; 00000073H
  00162	00		 DB	 0
$LN19@Reallocate:
  00163	6e		 DB	 110			; 0000006eH
  00164	54		 DB	 84			; 00000054H
  00165	6f		 DB	 111			; 0000006fH
  00166	74		 DB	 116			; 00000074H
  00167	61		 DB	 97			; 00000061H
  00168	6c		 DB	 108			; 0000006cH
  00169	53		 DB	 83			; 00000053H
  0016a	69		 DB	 105			; 00000069H
  0016b	7a		 DB	 122			; 0000007aH
  0016c	65		 DB	 101			; 00000065H
  0016d	00		 DB	 0
?Reallocate@CAtlStringMgr@ATL@@UAEPAUCStringData@2@PAU32@HH@Z ENDP ; ATL::CAtlStringMgr::Reallocate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ?GetNilString@CAtlStringMgr@ATL@@UAEPAUCStringData@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetNilString@CAtlStringMgr@ATL@@UAEPAUCStringData@2@XZ PROC ; ATL::CAtlStringMgr::GetNilString, COMDAT
; _this$ = ecx

; 139  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 140  : 		m_nil.AddRef();

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	83 c1 08	 add	 ecx, 8
  00014	e8 00 00 00 00	 call	 ?AddRef@CStringData@ATL@@QAEXXZ ; ATL::CStringData::AddRef

; 141  : 		return &m_nil;

  00019	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001c	83 c0 08	 add	 eax, 8

; 142  : 	}

  0001f	83 c4 04	 add	 esp, 4
  00022	3b ec		 cmp	 ebp, esp
  00024	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00029	8b e5		 mov	 esp, ebp
  0002b	5d		 pop	 ebp
  0002c	c3		 ret	 0
?GetNilString@CAtlStringMgr@ATL@@UAEPAUCStringData@2@XZ ENDP ; ATL::CAtlStringMgr::GetNilString
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ?Clone@CAtlStringMgr@ATL@@UAEPAUIAtlStringMgr@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?Clone@CAtlStringMgr@ATL@@UAEPAUIAtlStringMgr@2@XZ PROC	; ATL::CAtlStringMgr::Clone, COMDAT
; _this$ = ecx

; 144  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 145  : 		return this;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]

; 146  : 	}

  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c3		 ret	 0
?Clone@CAtlStringMgr@ATL@@UAEPAUIAtlStringMgr@2@XZ ENDP	; ATL::CAtlStringMgr::Clone
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__FstrMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ
text$yd	SEGMENT
??__FstrMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ PROC ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strMgr'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?strMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4V23@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strMgr
  00008	e8 00 00 00 00	 call	 ??1CAtlStringMgr@ATL@@UAE@XZ ; ATL::CAtlStringMgr::~CAtlStringMgr
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__FstrMgr@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ ENDP ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strMgr''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??__FstrHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ
text$yd	SEGMENT
??__FstrHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ PROC ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strHeap'', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?strHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@3@XZ@4VCWin32Heap@3@A ; `ATL::CAtlStringMgr::GetInstance'::`2'::strHeap
  00008	e8 00 00 00 00	 call	 ??1CWin32Heap@ATL@@UAE@XZ ; ATL::CWin32Heap::~CWin32Heap
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__FstrHeap@?1??GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ@YAXXZ ENDP ; `ATL::CAtlStringMgr::GetInstance'::`2'::`dynamic atexit destructor for 'strHeap''
text$yd	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_GCAtlStringMgr@ATL@@UAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___flags$ = 8						; size = 4
??_GCAtlStringMgr@ATL@@UAEPAXI@Z PROC			; ATL::CAtlStringMgr::`scalar deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ??1CAtlStringMgr@ATL@@UAE@XZ ; ATL::CAtlStringMgr::~CAtlStringMgr
  00016	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  00019	83 e0 01	 and	 eax, 1
  0001c	74 0e		 je	 SHORT $LN2@scalar
  0001e	6a 1c		 push	 28			; 0000001cH
  00020	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00023	51		 push	 ecx
  00024	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  00029	83 c4 08	 add	 esp, 8
$LN2@scalar:
  0002c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002f	83 c4 04	 add	 esp, 4
  00032	3b ec		 cmp	 ebp, esp
  00034	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00039	8b e5		 mov	 esp, ebp
  0003b	5d		 pop	 ebp
  0003c	c2 04 00	 ret	 4
??_GCAtlStringMgr@ATL@@UAEPAXI@Z ENDP			; ATL::CAtlStringMgr::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ??1CAtlStringMgr@ATL@@UAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??1CAtlStringMgr@ATL@@UAE@XZ PROC			; ATL::CAtlStringMgr::~CAtlStringMgr, COMDAT
; _this$ = ecx

; 151  : };

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b e5		 mov	 esp, ebp
  00010	5d		 pop	 ebp
  00011	c3		 ret	 0
??1CAtlStringMgr@ATL@@UAE@XZ ENDP			; ATL::CAtlStringMgr::~CAtlStringMgr
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ??0CAtlStringMgrStaticInitializer@ATLImplementationDetails@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0CAtlStringMgrStaticInitializer@ATLImplementationDetails@ATL@@QAE@XZ PROC ; ATL::ATLImplementationDetails::CAtlStringMgrStaticInitializer::CAtlStringMgrStaticInitializer, COMDAT
; _this$ = ecx

; 157  : 	CAtlStringMgrStaticInitializer() { (void)CAtlStringMgr::GetInstance(); }

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	e8 00 00 00 00	 call	 ?GetInstance@CAtlStringMgr@ATL@@SAPAUIAtlStringMgr@2@XZ ; ATL::CAtlStringMgr::GetInstance
  00013	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
??0CAtlStringMgrStaticInitializer@ATLImplementationDetails@ATL@@QAE@XZ ENDP ; ATL::ATLImplementationDetails::CAtlStringMgrStaticInitializer::CAtlStringMgrStaticInitializer
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlstr.h
;	COMDAT ??__EInitializeCAtlStringMgr@ATLImplementationDetails@ATL@@YAXXZ
text$di	SEGMENT
??__EInitializeCAtlStringMgr@ATLImplementationDetails@ATL@@YAXXZ PROC ; ATL::ATLImplementationDetails::`dynamic initializer for 'InitializeCAtlStringMgr'', COMDAT

; 160  : __declspec(selectany) CAtlStringMgrStaticInitializer InitializeCAtlStringMgr;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	b9 00 00 00 00	 mov	 ecx, OFFSET ?InitializeCAtlStringMgr@ATLImplementationDetails@ATL@@3UCAtlStringMgrStaticInitializer@12@A ; ATL::ATLImplementationDetails::InitializeCAtlStringMgr
  00008	e8 00 00 00 00	 call	 ??0CAtlStringMgrStaticInitializer@ATLImplementationDetails@ATL@@QAE@XZ ; ATL::ATLImplementationDetails::CAtlStringMgrStaticInitializer::CAtlStringMgrStaticInitializer
  0000d	3b ec		 cmp	 ebp, esp
  0000f	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00014	5d		 pop	 ebp
  00015	c3		 ret	 0
??__EInitializeCAtlStringMgr@ATLImplementationDetails@ATL@@YAXXZ ENDP ; ATL::ATLImplementationDetails::`dynamic initializer for 'InitializeCAtlStringMgr''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
___flags$ = 8						; size = 4
??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z PROC	; ATL::CComTypeInfoHolder::stringdispid::`vector deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  00026	83 e0 02	 and	 eax, 2
  00029	74 41		 je	 SHORT $LN2@vector
  0002b	68 00 00 00 00	 push	 OFFSET ??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ
  00030	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00033	8b 51 fc	 mov	 edx, DWORD PTR [ecx-4]
  00036	52		 push	 edx
  00037	6a 0c		 push	 12			; 0000000cH
  00039	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003c	50		 push	 eax
  0003d	e8 00 00 00 00	 call	 ??_M@YGXPAXIIP6EX0@Z@Z
  00042	8b 4d 08	 mov	 ecx, DWORD PTR ___flags$[ebp]
  00045	83 e1 01	 and	 ecx, 1
  00048	74 1a		 je	 SHORT $LN3@vector
  0004a	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0004d	6b 42 fc 0c	 imul	 eax, DWORD PTR [edx-4], 12
  00051	83 c0 04	 add	 eax, 4
  00054	50		 push	 eax
  00055	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00058	83 e9 04	 sub	 ecx, 4
  0005b	51		 push	 ecx
  0005c	e8 00 00 00 00	 call	 ??_V@YAXPAXI@Z		; operator delete[]
  00061	83 c4 08	 add	 esp, 8
$LN3@vector:
  00064	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00067	83 e8 04	 sub	 eax, 4
  0006a	eb 21		 jmp	 SHORT $LN1@vector
$LN2@vector:
  0006c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0006f	e8 00 00 00 00	 call	 ??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ
  00074	8b 55 08	 mov	 edx, DWORD PTR ___flags$[ebp]
  00077	83 e2 01	 and	 edx, 1
  0007a	74 0e		 je	 SHORT $LN4@vector
  0007c	6a 0c		 push	 12			; 0000000cH
  0007e	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00081	50		 push	 eax
  00082	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  00087	83 c4 08	 add	 esp, 8
$LN4@vector:
  0008a	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
$LN1@vector:
  0008d	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00090	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00097	83 c4 10	 add	 esp, 16			; 00000010H
  0009a	3b ec		 cmp	 ebp, esp
  0009c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  000a1	8b e5		 mov	 esp, ebp
  000a3	5d		 pop	 ebp
  000a4	c2 04 00	 ret	 4
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??_Estringdispid@CComTypeInfoHolder@ATL@@QAEPAXI@Z ENDP	; ATL::CComTypeInfoHolder::stringdispid::`vector deleting destructor'
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ PROC	; ATL::CComTypeInfoHolder::stringdispid::~stringdispid, COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0002a	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00031	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00034	e8 00 00 00 00	 call	 ??1CComBSTR@ATL@@QAE@XZ	; ATL::CComBSTR::~CComBSTR
  00039	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0003c	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00043	83 c4 10	 add	 esp, 16			; 00000010H
  00046	3b ec		 cmp	 ebp, esp
  00048	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004d	8b e5		 mov	 esp, ebp
  0004f	5d		 pop	 ebp
  00050	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1CComBSTR@ATL@@QAE@XZ	; ATL::CComBSTR::~CComBSTR
__ehhandler$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1stringdispid@CComTypeInfoHolder@ATL@@QAE@XZ ENDP	; ATL::CComTypeInfoHolder::stringdispid::~stringdispid
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_H@YGXPAXIIP6EPAX0@Z@Z
_TEXT	SEGMENT
tv67 = -8						; size = 4
tv66 = -4						; size = 4
___t$ = 8						; size = 4
___s$ = 12						; size = 4
___n$ = 16						; size = 4
___f$ = 20						; size = 4
??_H@YGXPAXIIP6EPAX0@Z@Z PROC				; `vector constructor iterator', COMDAT
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	56		 push	 esi
  00007	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000e	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
$LN2@vector:
  00015	8b 45 10	 mov	 eax, DWORD PTR ___n$[ebp]
  00018	89 45 fc	 mov	 DWORD PTR tv66[ebp], eax
  0001b	8b 4d 10	 mov	 ecx, DWORD PTR ___n$[ebp]
  0001e	83 e9 01	 sub	 ecx, 1
  00021	89 4d 10	 mov	 DWORD PTR ___n$[ebp], ecx
  00024	83 7d fc 00	 cmp	 DWORD PTR tv66[ebp], 0
  00028	76 09		 jbe	 SHORT $LN5@vector
  0002a	c7 45 f8 01 00
	00 00		 mov	 DWORD PTR tv67[ebp], 1
  00031	eb 07		 jmp	 SHORT $LN6@vector
$LN5@vector:
  00033	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR tv67[ebp], 0
$LN6@vector:
  0003a	83 7d f8 00	 cmp	 DWORD PTR tv67[ebp], 0
  0003e	74 1a		 je	 SHORT $LN1@vector
  00040	8b f4		 mov	 esi, esp
  00042	8b 4d 08	 mov	 ecx, DWORD PTR ___t$[ebp]
  00045	ff 55 14	 call	 DWORD PTR ___f$[ebp]
  00048	3b f4		 cmp	 esi, esp
  0004a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0004f	8b 55 08	 mov	 edx, DWORD PTR ___t$[ebp]
  00052	03 55 0c	 add	 edx, DWORD PTR ___s$[ebp]
  00055	89 55 08	 mov	 DWORD PTR ___t$[ebp], edx
  00058	eb bb		 jmp	 SHORT $LN2@vector
$LN1@vector:
  0005a	5e		 pop	 esi
  0005b	83 c4 08	 add	 esp, 8
  0005e	3b ec		 cmp	 ebp, esp
  00060	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00065	8b e5		 mov	 esp, ebp
  00067	5d		 pop	 ebp
  00068	c2 10 00	 ret	 16			; 00000010H
??_H@YGXPAXIIP6EPAX0@Z@Z ENDP				; `vector constructor iterator'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xlocale
;	COMDAT ??0id@locale@std@@QAE@I@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
__Val$ = 8						; size = 4
??0id@locale@std@@QAE@I@Z PROC				; std::locale::id::id, COMDAT
; _this$ = ecx

; 111  : 			{	// construct with specified stamp value

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 110  : 			: _Id(_Val)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 4d 08	 mov	 ecx, DWORD PTR __Val$[ebp]
  00014	89 08		 mov	 DWORD PTR [eax], ecx

; 112  : 			}

  00016	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00019	8b e5		 mov	 esp, ebp
  0001b	5d		 pop	 ebp
  0001c	c2 04 00	 ret	 4
??0id@locale@std@@QAE@I@Z ENDP				; std::locale::id::id
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnBossKey@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnBossKey
; _this$ = ecx

; 6    : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 7    : 	if (m_pMainFrm != NULL)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	83 38 00	 cmp	 DWORD PTR [eax], 0
  00014	74 0c		 je	 SHORT $LN2@OnBossKey

; 8    : 	{
; 9    : 		m_pMainFrm->OnShowOrHideMainWnd(TRUE);

  00016	6a 01		 push	 1
  00018	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001b	8b 09		 mov	 ecx, DWORD PTR [ecx]
  0001d	e8 00 00 00 00	 call	 ?OnShowOrHideMainWnd@CMainFrame@@QAEXH@Z ; CMainFrame::OnShowOrHideMainWnd
$LN2@OnBossKey:

; 10   : 	}
; 11   : 	return 0;

  00022	33 c0		 xor	 eax, eax

; 12   : }

  00024	83 c4 04	 add	 esp, 4
  00027	3b ec		 cmp	 ebp, esp
  00029	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002e	8b e5		 mov	 esp, ebp
  00030	5d		 pop	 ebp
  00031	c2 10 00	 ret	 16			; 00000010H
?OnBossKey@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnBossKey
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnStop@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC	; CAcceleratorlImp::OnStop
; _this$ = ecx

; 15   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 16   : 	if (m_pMainFrm != NULL)
; 17   : 	{
; 18   : //		m_pMainFrm->OnHotKey(PlayStop);
; 19   : 	}
; 20   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 21   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnStop@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP	; CAcceleratorlImp::OnStop
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnFullScreen@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnFullScreen
; _this$ = ecx

; 24   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 25   : 	if (m_pMainFrm != NULL)
; 26   : 	{
; 27   : //		m_pMainFrm->OnHotKey(FullScreen);
; 28   : 	}
; 29   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 30   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnFullScreen@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnFullScreen
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnExitFullScreen@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnExitFullScreen
; _this$ = ecx

; 33   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 34   : 	if (m_pMainFrm != NULL)
; 35   : 	{
; 36   : //		m_pMainFrm->OnHotKey(ExitFullScreen);
; 37   : 	}
; 38   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 39   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnExitFullScreen@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnExitFullScreen
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnAudioUp@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnAudioUp
; _this$ = ecx

; 42   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 43   : 	if (m_pMainFrm != NULL)
; 44   : 	{
; 45   : //		m_pMainFrm->OnHotKey(AudioUP);
; 46   : 	}
; 47   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 48   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnAudioUp@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnAudioUp
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnAudioDown@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnAudioDown
; _this$ = ecx

; 51   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 52   : 	if (m_pMainFrm != NULL)
; 53   : 	{
; 54   : //		m_pMainFrm->OnHotKey(AudioDown);
; 55   : 	}
; 56   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 57   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnAudioDown@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnAudioDown
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnAudioZero@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnAudioZero
; _this$ = ecx

; 60   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 61   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 62   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnAudioZero@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnAudioZero
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?On5Forward@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::On5Forward
; _this$ = ecx

; 65   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 66   : 	if (m_pMainFrm != NULL)
; 67   : 	{
; 68   : //		m_pMainFrm->OnHotKey(Forward);
; 69   : 	}
; 70   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 71   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?On5Forward@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::On5Forward
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?On5Rewind@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::On5Rewind
; _this$ = ecx

; 74   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 75   : 	if (m_pMainFrm != NULL)
; 76   : 	{
; 77   : //		m_pMainFrm->OnHotKey(Rewind);
; 78   : 	}
; 79   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 80   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?On5Rewind@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::On5Rewind
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnRateUp@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC	; CAcceleratorlImp::OnRateUp
; _this$ = ecx

; 83   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 84   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 85   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnRateUp@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP	; CAcceleratorlImp::OnRateUp
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnRateDown@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnRateDown
; _this$ = ecx

; 88   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 89   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 90   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnRateDown@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnRateDown
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnDefRate@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnDefRate
; _this$ = ecx

; 93   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 94   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 95   : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnDefRate@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnDefRate
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnLeftChannel@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnLeftChannel
; _this$ = ecx

; 98   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 99   : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 100  : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnLeftChannel@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnLeftChannel
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnRightChannel@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC ; CAcceleratorlImp::OnRightChannel
; _this$ = ecx

; 103  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 104  : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 105  : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnRightChannel@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP ; CAcceleratorlImp::OnRightChannel
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
_TEXT	SEGMENT
_this$ = -4						; size = 4
___formal$ = 8						; size = 2
_wID$ = 12						; size = 2
___formal$ = 16						; size = 4
___formal$ = 20						; size = 4
?OnStereo@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z PROC	; CAcceleratorlImp::OnStereo
; _this$ = ecx

; 108  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 109  : 	return 0;

  0000e	33 c0		 xor	 eax, eax

; 110  : }

  00010	8b e5		 mov	 esp, ebp
  00012	5d		 pop	 ebp
  00013	c2 10 00	 ret	 16			; 00000010H
?OnStereo@CAcceleratorlImp@@QAEJGGPAUHWND__@@AAH@Z ENDP	; CAcceleratorlImp::OnStereo
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
;	COMDAT ?__empty_global_delete@@YAXPAX@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
?__empty_global_delete@@YAXPAX@Z PROC			; __empty_global_delete, COMDAT

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	5d		 pop	 ebp
  00004	c3		 ret	 0
?__empty_global_delete@@YAXPAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\accelerator\acceleratorimpl.cpp
;	COMDAT ?__empty_global_delete@@YAXPAXI@Z
_TEXT	SEGMENT
___formal$ = 8						; size = 4
___formal$ = 12						; size = 4
?__empty_global_delete@@YAXPAXI@Z PROC			; __empty_global_delete, COMDAT

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	5d		 pop	 ebp
  00004	c3		 ret	 0
?__empty_global_delete@@YAXPAXI@Z ENDP			; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpcoll.h
;	COMDAT ??A?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEAAGH@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_nIndex$ = 8						; size = 4
??A?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEAAGH@Z PROC ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::operator[], COMDAT
; _this$ = ecx

; 236  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 237  : 		ATLASSERT(nIndex >= 0 && nIndex < m_nSize);
; 238  : 		if(nIndex < 0 || nIndex >= m_nSize)

  0000e	83 7d 08 00	 cmp	 DWORD PTR _nIndex$[ebp], 0
  00012	7c 0b		 jl	 SHORT $LN3@operator
  00014	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00017	8b 4d 08	 mov	 ecx, DWORD PTR _nIndex$[ebp]
  0001a	3b 48 04	 cmp	 ecx, DWORD PTR [eax+4]
  0001d	7c 0c		 jl	 SHORT $LN2@operator
$LN3@operator:

; 239  : 		{
; 240  : 			_AtlRaiseException((DWORD)EXCEPTION_ARRAY_BOUNDS_EXCEEDED);

  0001f	6a 01		 push	 1
  00021	68 8c 00 00 c0	 push	 -1073741684		; c000008cH
  00026	e8 00 00 00 00	 call	 ?_AtlRaiseException@ATL@@YAXKK@Z ; ATL::_AtlRaiseException
$LN2@operator:

; 241  : 		}
; 242  : 		return m_aT[nIndex];

  0002b	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  0002e	8b 02		 mov	 eax, DWORD PTR [edx]
  00030	8b 4d 08	 mov	 ecx, DWORD PTR _nIndex$[ebp]
  00033	8d 04 48	 lea	 eax, DWORD PTR [eax+ecx*2]
$LN4@operator:

; 243  : 	}

  00036	83 c4 04	 add	 esp, 4
  00039	3b ec		 cmp	 ebp, esp
  0003b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00040	8b e5		 mov	 esp, ebp
  00042	5d		 pop	 ebp
  00043	c2 04 00	 ret	 4
??A?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEAAGH@Z ENDP ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::operator[]
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpcoll.h
;	COMDAT ?RemoveAll@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEXXZ
_TEXT	SEGMENT
_i$1 = -8						; size = 4
_this$ = -4						; size = 4
?RemoveAll@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEXXZ PROC ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::RemoveAll, COMDAT
; _this$ = ecx

; 215  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	83 ec 08	 sub	 esp, 8
  00006	c7 45 f8 cc cc
	cc cc		 mov	 DWORD PTR [ebp-8], -858993460 ; ccccccccH
  0000d	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  00014	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 216  : 		if(m_aT != NULL)

  00017	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0001a	83 38 00	 cmp	 DWORD PTR [eax], 0
  0001d	74 36		 je	 SHORT $LN5@RemoveAll

; 217  : 		{
; 218  : 			for(int i = 0; i < m_nSize; i++)

  0001f	c7 45 f8 00 00
	00 00		 mov	 DWORD PTR _i$1[ebp], 0
  00026	eb 09		 jmp	 SHORT $LN4@RemoveAll
$LN2@RemoveAll:
  00028	8b 4d f8	 mov	 ecx, DWORD PTR _i$1[ebp]
  0002b	83 c1 01	 add	 ecx, 1
  0002e	89 4d f8	 mov	 DWORD PTR _i$1[ebp], ecx
$LN4@RemoveAll:
  00031	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00034	8b 45 f8	 mov	 eax, DWORD PTR _i$1[ebp]
  00037	3b 42 04	 cmp	 eax, DWORD PTR [edx+4]
  0003a	7d 02		 jge	 SHORT $LN3@RemoveAll

; 219  : 				m_aT[i].~T();

  0003c	eb ea		 jmp	 SHORT $LN2@RemoveAll
$LN3@RemoveAll:

; 220  : 			free(m_aT);

  0003e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00041	8b 11		 mov	 edx, DWORD PTR [ecx]
  00043	52		 push	 edx
  00044	e8 00 00 00 00	 call	 _free
  00049	83 c4 04	 add	 esp, 4

; 221  : 			m_aT = NULL;

  0004c	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0004f	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0
$LN5@RemoveAll:

; 222  : 		}
; 223  : 		m_nSize = 0;

  00055	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00058	c7 41 04 00 00
	00 00		 mov	 DWORD PTR [ecx+4], 0

; 224  : 		m_nAllocSize = 0;

  0005f	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00062	c7 42 08 00 00
	00 00		 mov	 DWORD PTR [edx+8], 0

; 225  :     }

  00069	83 c4 08	 add	 esp, 8
  0006c	3b ec		 cmp	 ebp, esp
  0006e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00073	8b e5		 mov	 esp, ebp
  00075	5d		 pop	 ebp
  00076	c3		 ret	 0
?RemoveAll@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEXXZ ENDP ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::RemoveAll
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpcoll.h
;	COMDAT ?GetSize@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QBEHXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?GetSize@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QBEHXZ PROC ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::GetSize, COMDAT
; _this$ = ecx

; 160  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 161  : 		return m_nSize;

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b 40 04	 mov	 eax, DWORD PTR [eax+4]

; 162  : 	}

  00014	8b e5		 mov	 esp, ebp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?GetSize@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QBEHXZ ENDP ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::GetSize
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpcoll.h
;	COMDAT ??0?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??0?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ PROC ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >, COMDAT
; _this$ = ecx

; 120  : 	{

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 119  : 		m_aT(NULL), m_nSize(0), m_nAllocSize(0)

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], 0
  00017	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001a	c7 41 04 00 00
	00 00		 mov	 DWORD PTR [ecx+4], 0
  00021	8b 55 fc	 mov	 edx, DWORD PTR _this$[ebp]
  00024	c7 42 08 00 00
	00 00		 mov	 DWORD PTR [edx+8], 0

; 121  : 	}

  0002b	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  0002e	8b e5		 mov	 esp, ebp
  00030	5d		 pop	 ebp
  00031	c3		 ret	 0
??0?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ENDP ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlalloc.h
;	COMDAT ??$AtlAdd@K@ATL@@YAJPAKKK@Z
_TEXT	SEGMENT
_ptResult$ = 8						; size = 4
_tLeft$ = 12						; size = 4
_tRight$ = 16						; size = 4
??$AtlAdd@K@ATL@@YAJPAKKK@Z PROC			; ATL::AtlAdd<unsigned long>, COMDAT

; 84   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 85   : 	if(::ATL::AtlLimits<T>::_Max-tLeft < tRight)

  00003	83 c8 ff	 or	 eax, -1
  00006	2b 45 0c	 sub	 eax, DWORD PTR _tLeft$[ebp]
  00009	3b 45 10	 cmp	 eax, DWORD PTR _tRight$[ebp]
  0000c	73 0f		 jae	 SHORT $LN2@AtlAdd

; 86   : 	{
; 87   : 		return HRESULT_FROM_WIN32(ERROR_ARITHMETIC_OVERFLOW);

  0000e	68 16 02 00 00	 push	 534			; 00000216H
  00013	e8 00 00 00 00	 call	 _HRESULT_FROM_WIN32
  00018	83 c4 04	 add	 esp, 4
  0001b	eb 0d		 jmp	 SHORT $LN1@AtlAdd
$LN2@AtlAdd:

; 88   : 	}
; 89   : 	*ptResult= tLeft + tRight;

  0001d	8b 4d 0c	 mov	 ecx, DWORD PTR _tLeft$[ebp]
  00020	03 4d 10	 add	 ecx, DWORD PTR _tRight$[ebp]
  00023	8b 55 08	 mov	 edx, DWORD PTR _ptResult$[ebp]
  00026	89 0a		 mov	 DWORD PTR [edx], ecx

; 90   : 	return S_OK;

  00028	33 c0		 xor	 eax, eax
$LN1@AtlAdd:

; 91   : }

  0002a	3b ec		 cmp	 ebp, esp
  0002c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00031	5d		 pop	 ebp
  00032	c3		 ret	 0
??$AtlAdd@K@ATL@@YAJPAKKK@Z ENDP			; ATL::AtlAdd<unsigned long>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0IAAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0IAAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<524288,0>::CTraceCategoryEx<524288,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0IAAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<524288,0>::CTraceCategoryEx<524288,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$00$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$00$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<1,0>::CTraceCategoryEx<1,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$00$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<1,0>::CTraceCategoryEx<1,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$01$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$01$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<2,0>::CTraceCategoryEx<2,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$01$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<2,0>::CTraceCategoryEx<2,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$03$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$03$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<4,0>::CTraceCategoryEx<4,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$03$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<4,0>::CTraceCategoryEx<4,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$07$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$07$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<8,0>::CTraceCategoryEx<8,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$07$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<8,0>::CTraceCategoryEx<8,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0BA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0BA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<16,0>::CTraceCategoryEx<16,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0BA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<16,0>::CTraceCategoryEx<16,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0CA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0CA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<32,0>::CTraceCategoryEx<32,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0CA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<32,0>::CTraceCategoryEx<32,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0EA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0EA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<64,0>::CTraceCategoryEx<64,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0EA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<64,0>::CTraceCategoryEx<64,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0IA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0IA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<128,0>::CTraceCategoryEx<128,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0IA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<128,0>::CTraceCategoryEx<128,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0BAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0BAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<256,0>::CTraceCategoryEx<256,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0BAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<256,0>::CTraceCategoryEx<256,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0CAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0CAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<512,0>::CTraceCategoryEx<512,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0CAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<512,0>::CTraceCategoryEx<512,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0EAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0EAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<1024,0>::CTraceCategoryEx<1024,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0EAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<1024,0>::CTraceCategoryEx<1024,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0IAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0IAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<2048,0>::CTraceCategoryEx<2048,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0IAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<2048,0>::CTraceCategoryEx<2048,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0BAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0BAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<4096,0>::CTraceCategoryEx<4096,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0BAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<4096,0>::CTraceCategoryEx<4096,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0CAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0CAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<8192,0>::CTraceCategoryEx<8192,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0CAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<8192,0>::CTraceCategoryEx<8192,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0EAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0EAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<16384,0>::CTraceCategoryEx<16384,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0EAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<16384,0>::CTraceCategoryEx<16384,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0IAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0IAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<32768,0>::CTraceCategoryEx<32768,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0IAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<32768,0>::CTraceCategoryEx<32768,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0BAAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0BAAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<65536,0>::CTraceCategoryEx<65536,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0BAAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<65536,0>::CTraceCategoryEx<65536,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0CAAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0CAAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<131072,0>::CTraceCategoryEx<131072,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0CAAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<131072,0>::CTraceCategoryEx<131072,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0EAAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0EAAAA@$0A@@ATL@@QAE@PB_W@Z PROC	; ATL::CTraceCategoryEx<262144,0>::CTraceCategoryEx<262144,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0EAAAA@$0A@@ATL@@QAE@PB_W@Z ENDP	; ATL::CTraceCategoryEx<262144,0>::CTraceCategoryEx<262144,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0BAAAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0BAAAAA@$0A@@ATL@@QAE@PB_W@Z PROC ; ATL::CTraceCategoryEx<1048576,0>::CTraceCategoryEx<1048576,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0BAAAAA@$0A@@ATL@@QAE@PB_W@Z ENDP ; ATL::CTraceCategoryEx<1048576,0>::CTraceCategoryEx<1048576,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0CAAAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0CAAAAA@$0A@@ATL@@QAE@PB_W@Z PROC ; ATL::CTraceCategoryEx<2097152,0>::CTraceCategoryEx<2097152,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0CAAAAA@$0A@@ATL@@QAE@PB_W@Z ENDP ; ATL::CTraceCategoryEx<2097152,0>::CTraceCategoryEx<2097152,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atltrace.h
;	COMDAT ??0?$CTraceCategoryEx@$0EAAAAA@$0A@@ATL@@QAE@PB_W@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
_pszCategoryName$ = 8					; size = 4
??0?$CTraceCategoryEx@$0EAAAAA@$0A@@ATL@@QAE@PB_W@Z PROC ; ATL::CTraceCategoryEx<4194304,0>::CTraceCategoryEx<4194304,0>, COMDAT
; _this$ = ecx

; 495  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 496  : 	(void)pszCategoryName;
; 497  : }

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	8b e5		 mov	 esp, ebp
  00013	5d		 pop	 ebp
  00014	c2 04 00	 ret	 4
??0?$CTraceCategoryEx@$0EAAAAA@$0A@@ATL@@QAE@PB_W@Z ENDP ; ATL::CTraceCategoryEx<4194304,0>::CTraceCategoryEx<4194304,0>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlalloc.h
;	COMDAT ??$AtlAdd@I@ATL@@YAJPAIII@Z
_TEXT	SEGMENT
_ptResult$ = 8						; size = 4
_tLeft$ = 12						; size = 4
_tRight$ = 16						; size = 4
??$AtlAdd@I@ATL@@YAJPAIII@Z PROC			; ATL::AtlAdd<unsigned int>, COMDAT

; 84   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 85   : 	if(::ATL::AtlLimits<T>::_Max-tLeft < tRight)

  00003	83 c8 ff	 or	 eax, -1
  00006	2b 45 0c	 sub	 eax, DWORD PTR _tLeft$[ebp]
  00009	3b 45 10	 cmp	 eax, DWORD PTR _tRight$[ebp]
  0000c	73 0f		 jae	 SHORT $LN2@AtlAdd

; 86   : 	{
; 87   : 		return HRESULT_FROM_WIN32(ERROR_ARITHMETIC_OVERFLOW);

  0000e	68 16 02 00 00	 push	 534			; 00000216H
  00013	e8 00 00 00 00	 call	 _HRESULT_FROM_WIN32
  00018	83 c4 04	 add	 esp, 4
  0001b	eb 0d		 jmp	 SHORT $LN1@AtlAdd
$LN2@AtlAdd:

; 88   : 	}
; 89   : 	*ptResult= tLeft + tRight;

  0001d	8b 4d 0c	 mov	 ecx, DWORD PTR _tLeft$[ebp]
  00020	03 4d 10	 add	 ecx, DWORD PTR _tRight$[ebp]
  00023	8b 55 08	 mov	 edx, DWORD PTR _ptResult$[ebp]
  00026	89 0a		 mov	 DWORD PTR [edx], ecx

; 90   : 	return S_OK;

  00028	33 c0		 xor	 eax, eax
$LN1@AtlAdd:

; 91   : }

  0002a	3b ec		 cmp	 ebp, esp
  0002c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00031	5d		 pop	 ebp
  00032	c3		 ret	 0
??$AtlAdd@I@ATL@@YAJPAIII@Z ENDP			; ATL::AtlAdd<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlsimpcoll.h
;	COMDAT ??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ PROC ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::~CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >, COMDAT
; _this$ = ecx

; 310  : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 311  : 	RemoveAll();

  00023	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00026	e8 00 00 00 00	 call	 ?RemoveAll@?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAEXXZ ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::RemoveAll

; 312  : }

  0002b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0002e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00035	83 c4 10	 add	 esp, 16			; 00000010H
  00038	3b ec		 cmp	 ebp, esp
  0003a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0003f	8b e5		 mov	 esp, ebp
  00041	5d		 pop	 ebp
  00042	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__ehhandler$??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ:
  00000	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ
  00005	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$CSimpleArray@GV?$CSimpleArrayEqualHelper@G@ATL@@@ATL@@QAE@XZ ENDP ; ATL::CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >::~CSimpleArray<unsigned short,ATL::CSimpleArrayEqualHelper<unsigned short> >
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlalloc.h
;	COMDAT ??$AtlAdd@H@ATL@@YAJPAHHH@Z
_TEXT	SEGMENT
_ptResult$ = 8						; size = 4
_tLeft$ = 12						; size = 4
_tRight$ = 16						; size = 4
??$AtlAdd@H@ATL@@YAJPAHHH@Z PROC			; ATL::AtlAdd<int>, COMDAT

; 84   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 85   : 	if(::ATL::AtlLimits<T>::_Max-tLeft < tRight)

  00003	b8 ff ff ff 7f	 mov	 eax, 2147483647		; 7fffffffH
  00008	2b 45 0c	 sub	 eax, DWORD PTR _tLeft$[ebp]
  0000b	3b 45 10	 cmp	 eax, DWORD PTR _tRight$[ebp]
  0000e	7d 0f		 jge	 SHORT $LN2@AtlAdd

; 86   : 	{
; 87   : 		return HRESULT_FROM_WIN32(ERROR_ARITHMETIC_OVERFLOW);

  00010	68 16 02 00 00	 push	 534			; 00000216H
  00015	e8 00 00 00 00	 call	 _HRESULT_FROM_WIN32
  0001a	83 c4 04	 add	 esp, 4
  0001d	eb 0d		 jmp	 SHORT $LN1@AtlAdd
$LN2@AtlAdd:

; 88   : 	}
; 89   : 	*ptResult= tLeft + tRight;

  0001f	8b 4d 0c	 mov	 ecx, DWORD PTR _tLeft$[ebp]
  00022	03 4d 10	 add	 ecx, DWORD PTR _tRight$[ebp]
  00025	8b 55 08	 mov	 edx, DWORD PTR _ptResult$[ebp]
  00028	89 0a		 mov	 DWORD PTR [edx], ecx

; 90   : 	return S_OK;

  0002a	33 c0		 xor	 eax, eax
$LN1@AtlAdd:

; 91   : }

  0002c	3b ec		 cmp	 ebp, esp
  0002e	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00033	5d		 pop	 ebp
  00034	c3		 ret	 0
??$AtlAdd@H@ATL@@YAJPAHHH@Z ENDP			; ATL::AtlAdd<int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\atlmfc\include\atlmem.h
;	COMDAT ??$AtlAlignUp@H@ATL@@YGHHK@Z
_TEXT	SEGMENT
_n$ = 8							; size = 4
_nAlign$ = 12						; size = 4
??$AtlAlignUp@H@ATL@@YGHHK@Z PROC			; ATL::AtlAlignUp<int>, COMDAT

; 28   : {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp

; 29   : 	return( N( (n+(nAlign-1))&~(N( nAlign )-1) ) );

  00003	8b 45 0c	 mov	 eax, DWORD PTR _nAlign$[ebp]
  00006	8b 4d 08	 mov	 ecx, DWORD PTR _n$[ebp]
  00009	8d 44 01 ff	 lea	 eax, DWORD PTR [ecx+eax-1]
  0000d	8b 55 0c	 mov	 edx, DWORD PTR _nAlign$[ebp]
  00010	83 ea 01	 sub	 edx, 1
  00013	f7 d2		 not	 edx
  00015	23 c2		 and	 eax, edx

; 30   : }

  00017	5d		 pop	 ebp
  00018	c2 08 00	 ret	 8
??$AtlAlignUp@H@ATL@@YGHHK@Z ENDP			; ATL::AtlAlignUp<int>
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xlocale
;	COMDAT ??__E?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A@@YAXXZ
text$di	SEGMENT
??__E?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A@@YAXXZ PROC ; `dynamic initializer for 'std::codecvt<char,char,_Mbstatet>::id'', COMDAT

; 954  : 	__PURE_APPDOMAIN_GLOBAL locale::id codecvt<_Elem, _Byte, _Statype>::id;

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A ; std::codecvt<char,char,_Mbstatet>::id
  0000a	e8 00 00 00 00	 call	 ??0id@locale@std@@QAE@I@Z ; std::locale::id::id
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__E?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A@@YAXXZ ENDP ; `dynamic initializer for 'std::codecvt<char,char,_Mbstatet>::id''
text$di	ENDS
END
