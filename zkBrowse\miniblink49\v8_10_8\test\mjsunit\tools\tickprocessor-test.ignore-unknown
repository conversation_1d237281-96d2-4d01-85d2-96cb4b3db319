Statistical profiling result from v8.log, (13 ticks, 2 unaccounted, 0 excluded).

 [Shared libraries]:
   ticks  total  nonlib   name
      3   27.3%          /lib32/libm-2.7.so
      1    9.1%          ffffe000-fffff000

 [JavaScript]:
   ticks  total  nonlib   name

 [C++]:
   ticks  total  nonlib   name
      2   18.2%   28.6%  v8::internal::Runtime_Math_exp(v8::internal::Arguments)
      1    9.1%   14.3%  v8::internal::JSObject::LookupOwnRealNamedProperty(v8::internal::String*, v8::internal::LookupResult*)
      1    9.1%   14.3%  v8::internal::HashTable<v8::internal::StringDictionaryShape, v8::internal::String*>::FindEntry(v8::internal::String*)
      1    9.1%   14.3%  exp

 [Summary]:
   ticks  total  nonlib   name
      0    0.0%    0.0%  JavaScript
      5   45.5%   71.4%  C++
      0    0.0%    0.0%  GC
      4   36.4%          Shared libraries

 [C++ entry points]:
   ticks    cpp   total   name
      2   40.0%   18.2%  v8::internal::Runtime_Math_exp(v8::internal::Arguments)
      1   20.0%    9.1%  v8::internal::JSObject::LookupOwnRealNamedProperty(v8::internal::String*, v8::internal::LookupResult*)
      1   20.0%    9.1%  v8::internal::HashTable<v8::internal::StringDictionaryShape, v8::internal::String*>::FindEntry(v8::internal::String*)
      1   20.0%    9.1%  exp

 [Bottom up (heavy) profile]:
  Note: percentage shows a share of a particular caller in the total
  amount of its parent calls.
  Callers occupying less than 1.0% are not shown.

   ticks parent  name
      3   27.3%  /lib32/libm-2.7.so
      3  100.0%    LazyCompile: exp native math.js:41
      3  100.0%      Script: exp.js

      2   18.2%  v8::internal::Runtime_Math_exp(v8::internal::Arguments)
      2  100.0%    LazyCompile: exp native math.js:41
      2  100.0%      Script: exp.js

      2   18.2%  UNKNOWN
      1   50.0%    LazyCompile: exp native math.js:41
      1  100.0%      Script: exp.js

      1    9.1%  v8::internal::JSObject::LookupOwnRealNamedProperty(v8::internal::String*, v8::internal::LookupResult*)
      1  100.0%    Script: exp.js

      1    9.1%  v8::internal::HashTable<v8::internal::StringDictionaryShape, v8::internal::String*>::FindEntry(v8::internal::String*)
      1  100.0%    Script: exp.js

      1    9.1%  ffffe000-fffff000

      1    9.1%  exp
      1  100.0%    LazyCompile: exp native math.js:41
      1  100.0%      Script: exp.js

