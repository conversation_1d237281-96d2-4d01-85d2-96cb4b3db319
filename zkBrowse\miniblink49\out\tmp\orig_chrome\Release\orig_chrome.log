﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  ffmpeg_common.cc
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\base\time\time.h(230): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(281): warning C4244: “参数”: 从“double”转换到“int64”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(835): error C2653: “ScopedPtrAVFree”: 不是类或命名空间名称
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(835): error C2270: “()”: 非成员函数上不允许修饰符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(839): error C2653: “ScopedPtrAVFreePacket”: 不是类或命名空间名称
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(839): error C2270: “()”: 非成员函数上不允许修饰符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(849): error C2653: “ScopedPtrAVFreeContext”: 不是类或命名空间名称
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(849): error C2270: “()”: 非成员函数上不允许修饰符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(859): error C2653: “ScopedPtrAVFreeFrame”: 不是类或命名空间名称
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\orig_chrome\media\ffmpeg\ffmpeg_common.cc(859): error C2270: “()”: 非成员函数上不允许修饰符
