// Copyright 2008 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

var x;

// Converts a number to string respecting -0.
function stringify(n) {
  if ((1 / n) === -Infinity) return "-0";
  return String(n);
}

function f(expected, y) {
  function testEval(string, x, y) {
    var mulFunction = Function("x, y", "return " + string);
    return mulFunction(x, y);
  }
  function mulTest(expected, x, y) {
    assertEquals(expected, x * y);
    assertEquals(expected, testEval(stringify(x) + " * y", x, y));
    assertEquals(expected, testEval("x * " + stringify(y), x, y));
    assertEquals(expected, testEval(stringify(x) + " * " + stringify(y), x, y));
  }
  mulTest(expected, x, y);
  mulTest(-expected, -x, y);
  mulTest(-expected, x, -y);
  mulTest(expected, -x, -y);
  if (x === y) return;  // Symmetric cases not necessary.
  mulTest(expected, y, x);
  mulTest(-expected, -y, x);
  mulTest(-expected, y, -x);
  mulTest(expected, -y, -x);
}

x = 16385;
f(0, 0);
f(16385, 1);
f(32770, 2);
f(49155, 3);
f(65540, 4);
f(81925, 5);
f(114695, 7);
f(131080, 8);
f(147465, 9);
f(245775, 15);
f(262160, 16);
f(278545, 17);
f(507935, 31);
f(524320, 32);
f(540705, 33);
f(1032255, 63);
f(1048640, 64);
f(1065025, 65);
f(2080895, 127);
f(2097280, 128);
f(2113665, 129);
f(4178175, 255);
f(4194560, 256);
f(4210945, 257);
f(8372735, 511);
f(8389120, 512);
f(8405505, 513);
f(16761855, 1023);
f(16778240, 1024);
f(16794625, 1025);
f(33540095, 2047);
f(33556480, 2048);
f(33572865, 2049);
f(67096575, 4095);
f(67112960, 4096);
f(67129345, 4097);
f(134209535, 8191);
f(134225920, 8192);
f(134242305, 8193);
f(268435455, 16383);
f(268451840, 16384);
f(268468225, 16385);
x = 32767;
f(0, 0);
f(32767, 1);
f(65534, 2);
f(98301, 3);
f(131068, 4);
f(163835, 5);
f(229369, 7);
f(262136, 8);
f(294903, 9);
f(491505, 15);
f(524272, 16);
f(557039, 17);
f(1015777, 31);
f(1048544, 32);
f(1081311, 33);
f(2064321, 63);
f(2097088, 64);
f(2129855, 65);
f(4161409, 127);
f(4194176, 128);
f(4226943, 129);
f(8355585, 255);
f(8388352, 256);
f(8421119, 257);
f(16743937, 511);
f(16776704, 512);
f(16809471, 513);
f(33520641, 1023);
f(33553408, 1024);
f(33586175, 1025);
f(67074049, 2047);
f(67106816, 2048);
f(67139583, 2049);
f(134180865, 4095);
f(134213632, 4096);
f(134246399, 4097);
f(268394497, 8191);
f(268427264, 8192);
f(268460031, 8193);
f(536821761, 16383);
f(536854528, 16384);
f(536887295, 16385);
f(1073676289, 32767);
x = 32768;
f(0, 0);
f(32768, 1);
f(65536, 2);
f(98304, 3);
f(131072, 4);
f(163840, 5);
f(229376, 7);
f(262144, 8);
f(294912, 9);
f(491520, 15);
f(524288, 16);
f(557056, 17);
f(1015808, 31);
f(1048576, 32);
f(1081344, 33);
f(2064384, 63);
f(2097152, 64);
f(2129920, 65);
f(4161536, 127);
f(4194304, 128);
f(4227072, 129);
f(8355840, 255);
f(8388608, 256);
f(8421376, 257);
f(16744448, 511);
f(16777216, 512);
f(16809984, 513);
f(33521664, 1023);
f(33554432, 1024);
f(33587200, 1025);
f(67076096, 2047);
f(67108864, 2048);
f(67141632, 2049);
f(134184960, 4095);
f(134217728, 4096);
f(134250496, 4097);
f(268402688, 8191);
f(268435456, 8192);
f(268468224, 8193);
f(536838144, 16383);
f(536870912, 16384);
f(536903680, 16385);
f(1073709056, 32767);
f(1073741824, 32768);
x = 32769;
f(0, 0);
f(32769, 1);
f(65538, 2);
f(98307, 3);
f(131076, 4);
f(163845, 5);
f(229383, 7);
f(262152, 8);
f(294921, 9);
f(491535, 15);
f(524304, 16);
f(557073, 17);
f(1015839, 31);
f(1048608, 32);
f(1081377, 33);
f(2064447, 63);
f(2097216, 64);
f(2129985, 65);
f(4161663, 127);
f(4194432, 128);
f(4227201, 129);
f(8356095, 255);
f(8388864, 256);
f(8421633, 257);
f(16744959, 511);
f(16777728, 512);
f(16810497, 513);
f(33522687, 1023);
f(33555456, 1024);
f(33588225, 1025);
f(67078143, 2047);
f(67110912, 2048);
f(67143681, 2049);
f(134189055, 4095);
f(134221824, 4096);
f(134254593, 4097);
f(268410879, 8191);
f(268443648, 8192);
f(268476417, 8193);
f(536854527, 16383);
f(536887296, 16384);
f(536920065, 16385);
f(1073741823, 32767);
f(1073774592, 32768);
f(1073807361, 32769);
x = 65535;
f(0, 0);
f(65535, 1);
f(131070, 2);
f(196605, 3);
f(262140, 4);
f(327675, 5);
f(458745, 7);
f(524280, 8);
f(589815, 9);
f(983025, 15);
f(1048560, 16);
f(1114095, 17);
f(2031585, 31);
f(2097120, 32);
f(2162655, 33);
f(4128705, 63);
f(4194240, 64);
f(4259775, 65);
f(8322945, 127);
f(8388480, 128);
f(8454015, 129);
f(16711425, 255);
f(16776960, 256);
f(16842495, 257);
f(33488385, 511);
f(33553920, 512);
f(33619455, 513);
f(67042305, 1023);
f(67107840, 1024);
f(67173375, 1025);
f(134150145, 2047);
f(134215680, 2048);
f(134281215, 2049);
f(268365825, 4095);
f(268431360, 4096);
f(268496895, 4097);
f(536797185, 8191);
f(536862720, 8192);
f(536928255, 8193);
f(1073659905, 16383);
f(1073725440, 16384);
f(1073790975, 16385);
f(2147385345, 32767);
f(2147450880, 32768);
f(2147516415, 32769);
f(4294836225, 65535);
x = 65536;
f(0, 0);
f(65536, 1);
f(131072, 2);
f(196608, 3);
f(262144, 4);
f(327680, 5);
f(458752, 7);
f(524288, 8);
f(589824, 9);
f(983040, 15);
f(1048576, 16);
f(1114112, 17);
f(2031616, 31);
f(2097152, 32);
f(2162688, 33);
f(4128768, 63);
f(4194304, 64);
f(4259840, 65);
f(8323072, 127);
f(8388608, 128);
f(8454144, 129);
f(16711680, 255);
f(16777216, 256);
f(16842752, 257);
f(33488896, 511);
f(33554432, 512);
f(33619968, 513);
f(67043328, 1023);
f(67108864, 1024);
f(67174400, 1025);
f(134152192, 2047);
f(134217728, 2048);
f(134283264, 2049);
f(268369920, 4095);
f(268435456, 4096);
f(268500992, 4097);
f(536805376, 8191);
f(536870912, 8192);
f(536936448, 8193);
f(1073676288, 16383);
f(1073741824, 16384);
f(1073807360, 16385);
f(2147418112, 32767);
f(2147483648, 32768);
f(2147549184, 32769);
f(4294901760, 65535);
f(4294967296, 65536);
x = 65537;
f(0, 0);
f(65537, 1);
f(131074, 2);
f(196611, 3);
f(262148, 4);
f(327685, 5);
f(458759, 7);
f(524296, 8);
f(589833, 9);
f(983055, 15);
f(1048592, 16);
f(1114129, 17);
f(2031647, 31);
f(2097184, 32);
f(2162721, 33);
f(4128831, 63);
f(4194368, 64);
f(4259905, 65);
f(8323199, 127);
f(8388736, 128);
f(8454273, 129);
f(16711935, 255);
f(16777472, 256);
f(16843009, 257);
f(33489407, 511);
f(33554944, 512);
f(33620481, 513);
f(67044351, 1023);
f(67109888, 1024);
f(67175425, 1025);
f(134154239, 2047);
f(134219776, 2048);
f(134285313, 2049);
f(268374015, 4095);
f(268439552, 4096);
f(268505089, 4097);
f(536813567, 8191);
f(536879104, 8192);
f(536944641, 8193);
f(1073692671, 16383);
f(1073758208, 16384);
f(1073823745, 16385);
f(2147450879, 32767);
f(2147516416, 32768);
f(2147581953, 32769);
f(4294967295, 65535);
f(4295032832, 65536);
f(4295098369, 65537);
x = 131071;
f(0, 0);
f(131071, 1);
f(262142, 2);
f(393213, 3);
f(524284, 4);
f(655355, 5);
f(917497, 7);
f(1048568, 8);
f(1179639, 9);
f(1966065, 15);
f(2097136, 16);
f(2228207, 17);
f(4063201, 31);
f(4194272, 32);
f(4325343, 33);
f(8257473, 63);
f(8388544, 64);
f(8519615, 65);
f(16646017, 127);
f(16777088, 128);
f(16908159, 129);
f(33423105, 255);
f(33554176, 256);
f(33685247, 257);
f(66977281, 511);
f(67108352, 512);
f(67239423, 513);
f(134085633, 1023);
f(134216704, 1024);
f(134347775, 1025);
f(268302337, 2047);
f(268433408, 2048);
f(268564479, 2049);
f(536735745, 4095);
f(536866816, 4096);
f(536997887, 4097);
f(1073602561, 8191);
f(1073733632, 8192);
f(1073864703, 8193);
f(2147336193, 16383);
f(2147467264, 16384);
f(2147598335, 16385);
f(4294803457, 32767);
f(4294934528, 32768);
f(4295065599, 32769);
f(8589737985, 65535);
f(8589869056, 65536);
f(8590000127, 65537);
f(17179607041, 131071);
x = 131072;
f(0, 0);
f(131072, 1);
f(262144, 2);
f(393216, 3);
f(524288, 4);
f(655360, 5);
f(917504, 7);
f(1048576, 8);
f(1179648, 9);
f(1966080, 15);
f(2097152, 16);
f(2228224, 17);
f(4063232, 31);
f(4194304, 32);
f(4325376, 33);
f(8257536, 63);
f(8388608, 64);
f(8519680, 65);
f(16646144, 127);
f(16777216, 128);
f(16908288, 129);
f(33423360, 255);
f(33554432, 256);
f(33685504, 257);
f(66977792, 511);
f(67108864, 512);
f(67239936, 513);
f(134086656, 1023);
f(134217728, 1024);
f(134348800, 1025);
f(268304384, 2047);
f(268435456, 2048);
f(268566528, 2049);
f(536739840, 4095);
f(536870912, 4096);
f(537001984, 4097);
f(1073610752, 8191);
f(1073741824, 8192);
f(1073872896, 8193);
f(2147352576, 16383);
f(2147483648, 16384);
f(2147614720, 16385);
f(4294836224, 32767);
f(4294967296, 32768);
f(4295098368, 32769);
f(8589803520, 65535);
f(8589934592, 65536);
f(8590065664, 65537);
f(17179738112, 131071);
f(17179869184, 131072);
x = 131073;
f(0, 0);
f(131073, 1);
f(262146, 2);
f(393219, 3);
f(524292, 4);
f(655365, 5);
f(917511, 7);
f(1048584, 8);
f(1179657, 9);
f(1966095, 15);
f(2097168, 16);
f(2228241, 17);
f(4063263, 31);
f(4194336, 32);
f(4325409, 33);
f(8257599, 63);
f(8388672, 64);
f(8519745, 65);
f(16646271, 127);
f(16777344, 128);
f(16908417, 129);
f(33423615, 255);
f(33554688, 256);
f(33685761, 257);
f(66978303, 511);
f(67109376, 512);
f(67240449, 513);
f(134087679, 1023);
f(134218752, 1024);
f(134349825, 1025);
f(268306431, 2047);
f(268437504, 2048);
f(268568577, 2049);
f(536743935, 4095);
f(536875008, 4096);
f(537006081, 4097);
f(1073618943, 8191);
f(1073750016, 8192);
f(1073881089, 8193);
f(2147368959, 16383);
f(2147500032, 16384);
f(2147631105, 16385);
f(4294868991, 32767);
f(4295000064, 32768);
f(4295131137, 32769);
f(8589869055, 65535);
f(8590000128, 65536);
f(8590131201, 65537);
f(17179869183, 131071);
f(17180000256, 131072);
f(17180131329, 131073);
