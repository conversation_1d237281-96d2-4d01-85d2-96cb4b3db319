# Copyright 2016 the V8 project authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/gclient_args.gni")
import("//build/config/sanitizers/sanitizers.gni")
import("//build/config/v8_target_cpu.gni")
import("release_branch_toggle.gni")
import("split_static_library.gni")

declare_args() {
  # Set flags for tracking code coverage. Uses gcov with gcc and sanitizer
  # coverage with clang.
  v8_code_coverage = false

  # Includes files needed for correctness fuzzing.
  v8_correctness_fuzzer = false

  # Adds additional compile target for building multiple architectures at once.
  v8_multi_arch_build = false

  # Indicate if valgrind was fetched as a custom deps to make it available on
  # swarming.
  v8_has_valgrind = false

  # Indicate if gcmole was fetched as a hook to make it available on swarming.
  v8_gcmole = false

  # Turns on compiler optimizations in V8 in Debug build.
  v8_optimized_debug = true

  # Support for backtrace_symbols on linux.
  v8_enable_backtrace = ""

  # Use external files for startup data blobs:
  # the JS builtins sources and the start snapshot.
  v8_use_external_startup_data = ""

  # Enable ECMAScript Internationalization API. Enabling this feature will
  # add a dependency on the ICU library.
  v8_enable_i18n_support = true

  # Use static libraries instead of source_sets.
  v8_static_library = false

  # Enable monolithic static library for embedders.
  v8_monolithic = false

  # Expose symbols for dynamic linking.
  v8_expose_symbols = false

  # Implement tracing using Perfetto (https://perfetto.dev).
  v8_use_perfetto = false

  # Override global symbol level setting for v8.
  v8_symbol_level = symbol_level

  # Enable WebAssembly debugging via GDB-remote protocol.
  v8_enable_wasm_gdb_remote_debugging = false

  # Lite mode disables a number of performance optimizations to reduce memory
  # at the cost of performance.
  # Sets -DV8_LITE_MODE.
  v8_enable_lite_mode = false

  # Include support for WebAssembly. If disabled, the 'WebAssembly' global
  # will not be available, and embedder APIs to generate WebAssembly modules
  # will fail. Also, asm.js will not be translated to WebAssembly and will be
  # executed as standard JavaScript instead.
  v8_enable_webassembly = ""

  # Enable 256-bit long vector re-vectorization pass in WASM compilation pipeline.
  v8_enable_wasm_simd256_revec = false

  # Enable runtime call stats.
  v8_enable_runtime_call_stats = !is_on_release_branch

  # Add fuzzilli fuzzer support.
  v8_fuzzilli = false

  # Scan the call stack conservatively during garbage collection.
  v8_enable_conservative_stack_scanning = false

  # Use the object start bitmap for inner pointer resolution.
  v8_enable_inner_pointer_resolution_osb = false

  # Use the marking bitmap for inner pointer resolution.
  v8_enable_inner_pointer_resolution_mb = false

  v8_enable_google_benchmark = false

  cppgc_is_standalone = false

  # Enable object names in cppgc for debug purposes.
  cppgc_enable_object_names = false

  # Enable young generation in cppgc.
  cppgc_enable_young_generation = false

  # Enable pointer compression in cppgc.
  cppgc_enable_pointer_compression = false

  # Enable 2gb cage for fast compression/decompression. Currently disabled
  # due to an increased number of OOMs.
  cppgc_enable_2gb_cage = false

  # Enable advanced BigInt algorithms, costing about 10-30 KB binary size
  # depending on platform. Disabled on Android to save binary size.
  v8_advanced_bigint_algorithms = !is_android
}

if (v8_use_external_startup_data == "") {
  # If not specified as a gn arg, use external startup data by default if
  # we're not on ios.
  v8_use_external_startup_data = !is_ios
}

if (v8_enable_backtrace == "") {
  v8_enable_backtrace = is_debug && !v8_optimized_debug
}

# If chromium is configured to use the perfetto client library, v8 should also
# use perfetto for tracing.
if (build_with_chromium && use_perfetto_client_library) {
  v8_use_perfetto = true
}

# WebAssembly is enabled by default, except in lite mode.
if (v8_enable_webassembly == "") {
  v8_enable_webassembly = !v8_enable_lite_mode
}
assert(!(v8_enable_webassembly && v8_enable_lite_mode),
       "Webassembly is not available in lite mode.")

# Points to // in v8 stand-alone or to //v8/ in chromium. We need absolute
# paths for all configs in templates as they are shared in different
# subdirectories.
v8_path_prefix = get_path_info("../", "abspath")

v8_inspector_js_protocol = v8_path_prefix + "/include/js_protocol.pdl"

###############################################################################
# Templates
#

# Common configs to remove or add in all v8 targets.
v8_remove_configs = []
v8_add_configs = [
  v8_path_prefix + ":features",
  v8_path_prefix + ":toolchain",
]

if (is_debug && !v8_optimized_debug) {
  v8_remove_configs += [ "//build/config/compiler:default_optimization" ]
  v8_add_configs += [ "//build/config/compiler:no_optimize" ]
} else {
  v8_remove_configs += [ "//build/config/compiler:default_optimization" ]

  # TODO(crbug.com/621335) Rework this so that we don't have the confusion
  # between "optimize_speed" and "optimize_max".
  if (is_posix && !is_android && !using_sanitizer) {
    v8_add_configs += [ "//build/config/compiler:optimize_speed" ]
  } else {
    v8_add_configs += [ "//build/config/compiler:optimize_max" ]
  }
}

if (!is_debug) {
  v8_remove_configs += [
    # Too much performance impact, unclear security benefit.
    "//build/config/compiler:default_init_stack_vars",
  ]
}

if (v8_code_coverage && !is_clang) {
  v8_add_configs += [
    v8_path_prefix + ":v8_gcov_coverage_cflags",
    v8_path_prefix + ":v8_gcov_coverage_ldflags",
  ]
}

if (v8_symbol_level != symbol_level) {
  v8_remove_configs += [ "//build/config/compiler:default_symbols" ]
  if (v8_symbol_level == 0) {
    v8_add_configs += [ "//build/config/compiler:no_symbols" ]
  } else if (v8_symbol_level == 1) {
    v8_add_configs += [ "//build/config/compiler:minimal_symbols" ]
  } else if (v8_symbol_level == 2) {
    v8_add_configs += [ "//build/config/compiler:symbols" ]
  } else {
    assert(false)
  }
}

if ((is_posix || is_fuchsia) &&
    (v8_enable_backtrace || v8_monolithic || v8_expose_symbols)) {
  v8_remove_configs += [ "//build/config/gcc:symbol_visibility_hidden" ]
  v8_add_configs += [ "//build/config/gcc:symbol_visibility_default" ]
}

# On MIPS gcc_target_rpath and ldso_path might be needed for all builds.
if (target_cpu == "mips64el" || target_cpu == "mips64") {
  v8_add_configs += [ "//build/config/gcc:rpath_for_built_shared_libraries" ]
}

if (!build_with_chromium && is_clang) {
  v8_remove_configs += [ "//build/config/clang:find_bad_constructs" ]
}

# All templates should be kept in sync.
template("v8_source_set") {
  if (defined(invoker.split_count) && invoker.split_count > 1 &&
      defined(v8_static_library) && v8_static_library && is_win) {
    link_target_type = "split_static_library"
  } else if (defined(v8_static_library) && v8_static_library) {
    link_target_type = "static_library"
  } else {
    link_target_type = "source_set"
  }
  target(link_target_type, target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "configs",
                             "remove_configs",
                           ])
    configs -= v8_remove_configs
    configs += v8_add_configs
    if (defined(invoker.remove_configs)) {
      configs -= invoker.remove_configs
    }
    configs += invoker.configs
  }
}

template("v8_header_set") {
  source_set(target_name) {
    forward_variables_from(invoker, "*", [ "configs" ])
    configs -= v8_remove_configs
    configs += v8_add_configs
    configs += invoker.configs
  }
}

template("v8_executable") {
  executable(target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "configs",
                             "remove_configs",
                           ])
    configs -= v8_remove_configs
    configs += v8_add_configs
    if (defined(invoker.remove_configs)) {
      configs -= invoker.remove_configs
    }
    configs += invoker.configs
    if (is_linux || is_chromeos) {
      # For enabling ASLR.
      ldflags = [ "-pie" ]
    }
    if (defined(testonly) && testonly && v8_code_coverage) {
      # Only add code coverage cflags for non-test files for performance
      # reasons.
      if (is_clang) {
        configs -= [ "//build/config/sanitizers:default_sanitizer_flags" ]
        configs +=
            [ "//build/config/sanitizers:default_sanitizer_flags_but_coverage" ]
      } else {
        configs -= [ v8_path_prefix + ":v8_gcov_coverage_cflags" ]
      }
    }
    deps += [ v8_path_prefix + ":v8_dump_build_config" ]
  }
}

template("v8_component") {
  component(target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "configs",
                             "remove_configs",
                           ])
    configs -= v8_remove_configs
    configs += v8_add_configs
    if (defined(invoker.remove_configs)) {
      configs -= invoker.remove_configs
    }
    configs += invoker.configs
  }
}

template("v8_shared_library") {
  shared_library(target_name) {
    forward_variables_from(invoker,
                           "*",
                           [
                             "configs",
                             "remove_configs",
                           ])
    configs -= v8_remove_configs
    configs += v8_add_configs
    if (defined(invoker.remove_configs)) {
      configs -= invoker.remove_configs
    }
    if (defined(invoker.configs)) {
      configs += invoker.configs
    }
  }
}

template("v8_static_library") {
  static_library(target_name) {
    complete_static_lib = true
    forward_variables_from(invoker, "*", [ "configs" ])
    configs -= v8_remove_configs
    configs -= [ "//build/config/compiler:thin_archive" ]
    configs += v8_add_configs
    configs += invoker.configs
  }
}
