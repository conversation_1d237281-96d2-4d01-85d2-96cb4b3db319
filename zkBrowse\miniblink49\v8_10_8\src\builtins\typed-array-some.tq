// Copyright 2019 the V8 project authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include 'src/builtins/builtins-typed-array-gen.h'

namespace typed_array {
const kBuiltinNameSome: constexpr string = '%TypedArray%.prototype.some';

// https://tc39.es/ecma262/#sec-%typedarray%.prototype.some
transitioning macro SomeAllElements(implicit context: Context)(
    attachedArrayAndLength: typed_array::AttachedJSTypedArrayAndLength,
    callbackfn: Callable, thisArg: JSAny): Bo<PERSON>an {
  let witness =
      typed_array::NewAttachedJSTypedArrayWitness(attachedArrayAndLength.array);

  // 5. Let k be 0.
  // 6. Repeat, while k < len
  for (let k: uintptr = 0; k < attachedArrayAndLength.length; k++) {
    // 6a. Let Pk be ! ToString(𝔽(k)).
    // There is no need to cast ToString to load elements.

    // 6b. Let kValue be ! Get(O, Pk).
    // kValue must be undefined when the buffer is detached.
    let value: JSAny;
    try {
      witness.RecheckIndex(k) otherwise goto IsDetachedOrOutOfBounds;
      value = witness.Load(k);
    } label IsDetachedOrOutOfBounds deferred {
      value = Undefined;
    }

    // 6c. Let testResult be ! ToBoolean(? Call(callbackfn, thisArg, « kValue,
    // 𝔽(k), O »)).
    // TODO(v8:4153): Consider versioning this loop for Smi and non-Smi
    // indices to optimize Convert<Number>(k) for the most common case.
    const result = Call(
        context, callbackfn, thisArg, value, Convert<Number>(k),
        witness.GetStable());

    // 6d. If testResult is true, return true.
    if (ToBoolean(result)) {
      return True;
    }

    // 6e. Set k to k + 1. (done by the loop).
  }

  // 7. Return false.
  return False;
}

// https://tc39.github.io/ecma262/#sec-%typedarray%.prototype.some
transitioning javascript builtin
TypedArrayPrototypeSome(
    js-implicit context: NativeContext, receiver: JSAny)(...arguments): JSAny {
  // arguments[0] = callback
  // arguments[1] = thisArg
  try {
    // 1. Let O be the this value.
    // 2. Perform ? ValidateTypedArray(O).
    // 3. Let len be IntegerIndexedObjectLength(O).
    const array: JSTypedArray = Cast<JSTypedArray>(receiver)
        otherwise NotTypedArray;
    const attachedArrayAndLength = EnsureAttachedAndReadLength(array)
        otherwise IsDetachedOrOutOfBounds;
    // 4. If IsCallable(callbackfn) is false, throw a TypeError exception.
    const callbackfn = Cast<Callable>(arguments[0]) otherwise NotCallable;
    const thisArg = arguments[1];
    return SomeAllElements(attachedArrayAndLength, callbackfn, thisArg);
  } label NotTypedArray deferred {
    ThrowTypeError(MessageTemplate::kNotTypedArray, kBuiltinNameSome);
  } label IsDetachedOrOutOfBounds deferred {
    ThrowTypeError(MessageTemplate::kDetachedOperation, kBuiltinNameSome);
  } label NotCallable deferred {
    ThrowTypeError(MessageTemplate::kCalledNonCallable, arguments[0]);
  }
}
}
