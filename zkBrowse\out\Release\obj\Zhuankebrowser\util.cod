; Listing generated by Microsoft (R) Optimizing Compiler Version 19.16.27034.0 

	TITLE	e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\util.cpp
	.686P
	.XMM
	include listing.inc
	.model	flat

INCLUDELIB OLDNAMES

PUBLIC	??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_ostream@DU?$char_traits@D@std@@@1@@ ; std::basic_fstream<char,std::char_traits<char> >::`vbtable'
PUBLIC	??_7?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@ ; std::basic_fstream<char,std::char_traits<char> >::`vftable'
PUBLIC	??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_istream@DU?$char_traits@D@std@@@1@@ ; std::basic_fstream<char,std::char_traits<char> >::`vbtable'
?GenericMonospaceFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericMonospaceFontFamily
?GenericSansSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSansSerifFontFamily
?GenericSerifFontFamily@Gdiplus@@3PAVFontFamily@1@A DD 01H DUP (?) ; Gdiplus::GenericSerifFontFamily
_BSS	ENDS
;	COMDAT ??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_istream@DU?$char_traits@D@std@@@1@@
CONST	SEGMENT
??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_istream@DU?$char_traits@D@std@@@1@@ DD 00H ; std::basic_fstream<char,std::char_traits<char> >::`vbtable'
	DD	078H
CONST	ENDS
;	COMDAT ??_7?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@
CONST	SEGMENT
??_7?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@ DD FLAT:??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@$4PPPPPPPM@A@AEPAXI@Z ; std::basic_fstream<char,std::char_traits<char> >::`vftable'
CONST	ENDS
;	COMDAT ??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_ostream@DU?$char_traits@D@std@@@1@@
CONST	SEGMENT
??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_ostream@DU?$char_traits@D@std@@@1@@ DD 00H ; std::basic_fstream<char,std::char_traits<char> >::`vbtable'
	DD	068H
$SG4294430966 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'C', 00H, 'l', 00H, 'i'
	DB	00H, 'e', 00H, 'n', 00H, 't', 00H, 00H, 00H
$SG4294430961 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'E', 00H, 'x', 00H, 'c', 00H, 'e', 00H, 'p'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
$SG4294430960 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'T', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H
	DB	00H
$SG4294430971 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'g', 00H, 'i', 00H, 's'
	DB	00H, 't', 00H, 'r', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294430970 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'R', 00H, 'e', 00H, 'f', 00H, 'c', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, 00H, 00H
$SG4294430969 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'W', 00H, 'i', 00H, 'n', 00H, 'd', 00H, 'o'
	DB	00H, 'w', 00H, 'i', 00H, 'n', 00H, 'g', 00H, 00H, 00H
?PADDING@@3PAEA DB 080H					; PADDING
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
	DB	00H
$SG4294430967 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'H', 00H, 'o', 00H, 's', 00H, 't', 00H, 'i'
	DB	00H, 'n', 00H, 'g', 00H, 00H, 00H
$SG4294430965 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'D', 00H, 'B', 00H, 'P', 00H, 'r', 00H, 'o'
	DB	00H, 'v', 00H, 'i', 00H, 'd', 00H, 'e', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430964 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'n', 00H, 'a', 00H, 'p', 00H, 'i'
	DB	00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430963 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'N', 00H, 'o', 00H, 't', 00H, 'I', 00H, 'm'
	DB	00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294430962 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'A', 00H, 'l', 00H, 'l', 00H, 'o', 00H, 'c'
	DB	00H, 'a', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430974 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'G', 00H, 'e', 00H, 'n', 00H, 'e', 00H, 'r'
	DB	00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294430973 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294430972 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'Q', 00H, 'I', 00H, 00H, 00H
	ORG $+2
$SG4294430968 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'o', 00H, 'n', 00H, 't', 00H, 'r'
	DB	00H, 'o', 00H, 'l', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430951 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430950 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294430949 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430948 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'S', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430947 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430946 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430945 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430944 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430959 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'C', 00H, 'a', 00H, 'c', 00H, 'h', 00H, 'e'
	DB	00H, 00H, 00H
$SG4294430958 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'e', 00H, 'n', 00H, 'c'
	DB	00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294430957 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 't', 00H, 'r', 00H, 'i', 00H, 'n'
	DB	00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294430956 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'M', 00H, 'a', 00H, 'p', 00H, 00H, 00H
$SG4294430955 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'U', 00H, 't', 00H, 'i', 00H, 'l', 00H, 00H
	DB	00H
	ORG $+2
$SG4294430954 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'e', 00H, 'c', 00H, 'u', 00H, 'r'
	DB	00H, 'i', 00H, 't', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294430953 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'S', 00H, 'y', 00H, 'n', 00H, 'c', 00H, 00H
	DB	00H
	ORG $+2
$SG4294430952 DB 'a', 00H, 't', 00H, 'l', 00H, 'T', 00H, 'r', 00H, 'a', 00H
	DB	'c', 00H, 'e', 00H, 'I', 00H, 'S', 00H, 'A', 00H, 'P', 00H, 'I'
	DB	00H, 00H, 00H
$SG4294430935 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430934 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430933 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430932 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430931 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430930 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430929 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430928 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430943 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430942 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430941 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294430940 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430939 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430938 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430937 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430936 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430919 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430918 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430917 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430916 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430915 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294430914 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430913 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430912 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294430927 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430926 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430925 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294430924 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294430923 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294430922 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430921 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430920 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294430903 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430902 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430901 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294430900 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294430899 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294430898 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430897 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430896 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430911 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294430910 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294430909 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430908 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430907 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294430906 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430905 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430904 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294430887 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294430886 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294430885 DB 00H, 00H
	ORG $+2
$SG4294430884 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'A', 00H, 00H, 00H
$SG4294430883 DB 'b', 00H, 'g', 00H, '-', 00H, 'B', 00H, 'G', 00H, 00H, 00H
$SG4294430882 DB 'c', 00H, 'a', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294430881 DB 'z', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'W', 00H, 00H, 00H
$SG4294430880 DB 'c', 00H, 's', 00H, '-', 00H, 'C', 00H, 'Z', 00H, 00H, 00H
$SG4294430895 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430894 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430893 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430892 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430891 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430890 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430889 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430888 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294430871 DB 'i', 00H, 's', 00H, '-', 00H, 'I', 00H, 'S', 00H, 00H, 00H
$SG4294430870 DB 'i', 00H, 't', 00H, '-', 00H, 'I', 00H, 'T', 00H, 00H, 00H
$SG4294430869 DB 'j', 00H, 'a', 00H, '-', 00H, 'J', 00H, 'P', 00H, 00H, 00H
$SG4294430868 DB 'k', 00H, 'o', 00H, '-', 00H, 'K', 00H, 'R', 00H, 00H, 00H
$SG4294430867 DB 'n', 00H, 'l', 00H, '-', 00H, 'N', 00H, 'L', 00H, 00H, 00H
$SG4294430866 DB 'n', 00H, 'b', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294430865 DB 'p', 00H, 'l', 00H, '-', 00H, 'P', 00H, 'L', 00H, 00H, 00H
$SG4294430864 DB 'p', 00H, 't', 00H, '-', 00H, 'B', 00H, 'R', 00H, 00H, 00H
$SG4294430879 DB 'd', 00H, 'a', 00H, '-', 00H, 'D', 00H, 'K', 00H, 00H, 00H
$SG4294430878 DB 'd', 00H, 'e', 00H, '-', 00H, 'D', 00H, 'E', 00H, 00H, 00H
$SG4294430877 DB 'e', 00H, 'l', 00H, '-', 00H, 'G', 00H, 'R', 00H, 00H, 00H
$SG4294430876 DB 'e', 00H, 'n', 00H, '-', 00H, 'U', 00H, 'S', 00H, 00H, 00H
$SG4294430875 DB 'f', 00H, 'i', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294430874 DB 'f', 00H, 'r', 00H, '-', 00H, 'F', 00H, 'R', 00H, 00H, 00H
$SG4294430873 DB 'h', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'L', 00H, 00H, 00H
$SG4294430872 DB 'h', 00H, 'u', 00H, '-', 00H, 'H', 00H, 'U', 00H, 00H, 00H
$SG4294430855 DB 'u', 00H, 'r', 00H, '-', 00H, 'P', 00H, 'K', 00H, 00H, 00H
$SG4294430854 DB 'i', 00H, 'd', 00H, '-', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294430853 DB 'u', 00H, 'k', 00H, '-', 00H, 'U', 00H, 'A', 00H, 00H, 00H
$SG4294430852 DB 'b', 00H, 'e', 00H, '-', 00H, 'B', 00H, 'Y', 00H, 00H, 00H
$SG4294430851 DB 's', 00H, 'l', 00H, '-', 00H, 'S', 00H, 'I', 00H, 00H, 00H
$SG4294430850 DB 'e', 00H, 't', 00H, '-', 00H, 'E', 00H, 'E', 00H, 00H, 00H
$SG4294430849 DB 'l', 00H, 'v', 00H, '-', 00H, 'L', 00H, 'V', 00H, 00H, 00H
$SG4294430848 DB 'l', 00H, 't', 00H, '-', 00H, 'L', 00H, 'T', 00H, 00H, 00H
$SG4294430863 DB 'r', 00H, 'o', 00H, '-', 00H, 'R', 00H, 'O', 00H, 00H, 00H
$SG4294430862 DB 'r', 00H, 'u', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294430861 DB 'h', 00H, 'r', 00H, '-', 00H, 'H', 00H, 'R', 00H, 00H, 00H
$SG4294430860 DB 's', 00H, 'k', 00H, '-', 00H, 'S', 00H, 'K', 00H, 00H, 00H
$SG4294430859 DB 's', 00H, 'q', 00H, '-', 00H, 'A', 00H, 'L', 00H, 00H, 00H
$SG4294430858 DB 's', 00H, 'v', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294430857 DB 't', 00H, 'h', 00H, '-', 00H, 'T', 00H, 'H', 00H, 00H, 00H
$SG4294430856 DB 't', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'R', 00H, 00H, 00H
$SG4294430839 DB 'z', 00H, 'u', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294430838 DB 'a', 00H, 'f', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294430837 DB 'k', 00H, 'a', 00H, '-', 00H, 'G', 00H, 'E', 00H, 00H, 00H
$SG4294430836 DB 'f', 00H, 'o', 00H, '-', 00H, 'F', 00H, 'O', 00H, 00H, 00H
$SG4294430835 DB 'h', 00H, 'i', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430834 DB 'm', 00H, 't', 00H, '-', 00H, 'M', 00H, 'T', 00H, 00H, 00H
$SG4294430833 DB 's', 00H, 'e', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294430832 DB 'm', 00H, 's', 00H, '-', 00H, 'M', 00H, 'Y', 00H, 00H, 00H
$SG4294430847 DB 'f', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'R', 00H, 00H, 00H
$SG4294430846 DB 'v', 00H, 'i', 00H, '-', 00H, 'V', 00H, 'N', 00H, 00H, 00H
$SG4294430845 DB 'h', 00H, 'y', 00H, '-', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294430844 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430843 DB 'e', 00H, 'u', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294430842 DB 'm', 00H, 'k', 00H, '-', 00H, 'M', 00H, 'K', 00H, 00H, 00H
$SG4294430841 DB 't', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294430840 DB 'x', 00H, 'h', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294430823 DB 't', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430822 DB 't', 00H, 'e', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430821 DB 'k', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430820 DB 'm', 00H, 'l', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430819 DB 'm', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430818 DB 's', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430817 DB 'm', 00H, 'n', 00H, '-', 00H, 'M', 00H, 'N', 00H, 00H, 00H
$SG4294430816 DB 'c', 00H, 'y', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294430831 DB 'k', 00H, 'k', 00H, '-', 00H, 'K', 00H, 'Z', 00H, 00H, 00H
$SG4294430830 DB 'k', 00H, 'y', 00H, '-', 00H, 'K', 00H, 'G', 00H, 00H, 00H
$SG4294430829 DB 's', 00H, 'w', 00H, '-', 00H, 'K', 00H, 'E', 00H, 00H, 00H
$SG4294430828 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430827 DB 't', 00H, 't', 00H, '-', 00H, 'R', 00H, 'U', 00H, 00H, 00H
$SG4294430826 DB 'b', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430825 DB 'p', 00H, 'a', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430824 DB 'g', 00H, 'u', 00H, '-', 00H, 'I', 00H, 'N', 00H, 00H, 00H
$SG4294430807 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'N', 00H, 00H, 00H
$SG4294430806 DB 'd', 00H, 'e', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294430805 DB 'e', 00H, 'n', 00H, '-', 00H, 'G', 00H, 'B', 00H, 00H, 00H
$SG4294430804 DB 'e', 00H, 's', 00H, '-', 00H, 'M', 00H, 'X', 00H, 00H, 00H
$SG4294430803 DB 'f', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294430802 DB 'i', 00H, 't', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294430801 DB 'n', 00H, 'l', 00H, '-', 00H, 'B', 00H, 'E', 00H, 00H, 00H
$SG4294430800 DB 'n', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'O', 00H, 00H, 00H
$SG4294430815 DB 'g', 00H, 'l', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294430814 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'I', 00H, 'N', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430813 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430812 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'M', 00H, 'V', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430811 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'B', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430810 DB 'n', 00H, 's', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294430809 DB 'm', 00H, 'i', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294430808 DB 'a', 00H, 'r', 00H, '-', 00H, 'I', 00H, 'Q', 00H, 00H, 00H
$SG4294430791 DB 'a', 00H, 'r', 00H, '-', 00H, 'E', 00H, 'G', 00H, 00H, 00H
$SG4294430790 DB 'z', 00H, 'h', 00H, '-', 00H, 'H', 00H, 'K', 00H, 00H, 00H
$SG4294430789 DB 'd', 00H, 'e', 00H, '-', 00H, 'A', 00H, 'T', 00H, 00H, 00H
$SG4294430788 DB 'e', 00H, 'n', 00H, '-', 00H, 'A', 00H, 'U', 00H, 00H, 00H
$SG4294430787 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'S', 00H, 00H, 00H
$SG4294430786 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294430785 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430784 DB 's', 00H, 'e', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294430799 DB 'p', 00H, 't', 00H, '-', 00H, 'P', 00H, 'T', 00H, 00H, 00H
$SG4294430798 DB 's', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'P', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430797 DB 's', 00H, 'v', 00H, '-', 00H, 'F', 00H, 'I', 00H, 00H, 00H
$SG4294430796 DB 'a', 00H, 'z', 00H, '-', 00H, 'A', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430795 DB 's', 00H, 'e', 00H, '-', 00H, 'S', 00H, 'E', 00H, 00H, 00H
$SG4294430794 DB 'm', 00H, 's', 00H, '-', 00H, 'B', 00H, 'N', 00H, 00H, 00H
$SG4294430793 DB 'u', 00H, 'z', 00H, '-', 00H, 'U', 00H, 'Z', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430792 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'E', 00H, 'C', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430775 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430774 DB 'a', 00H, 'r', 00H, '-', 00H, 'D', 00H, 'Z', 00H, 00H, 00H
$SG4294430773 DB 'z', 00H, 'h', 00H, '-', 00H, 'M', 00H, 'O', 00H, 00H, 00H
$SG4294430772 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'I', 00H, 00H, 00H
$SG4294430771 DB 'e', 00H, 'n', 00H, '-', 00H, 'N', 00H, 'Z', 00H, 00H, 00H
$SG4294430770 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'R', 00H, 00H, 00H
$SG4294430769 DB 'f', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294430768 DB 'b', 00H, 's', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430783 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'P', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430782 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'Y', 00H, 00H, 00H
$SG4294430781 DB 'z', 00H, 'h', 00H, '-', 00H, 'S', 00H, 'G', 00H, 00H, 00H
$SG4294430780 DB 'd', 00H, 'e', 00H, '-', 00H, 'L', 00H, 'U', 00H, 00H, 00H
$SG4294430779 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'A', 00H, 00H, 00H
$SG4294430778 DB 'e', 00H, 's', 00H, '-', 00H, 'G', 00H, 'T', 00H, 00H, 00H
$SG4294430777 DB 'f', 00H, 'r', 00H, '-', 00H, 'C', 00H, 'H', 00H, 00H, 00H
$SG4294430776 DB 'h', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, 00H, 00H
$SG4294430759 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'A', 00H, 00H, 00H
$SG4294430758 DB 'e', 00H, 's', 00H, '-', 00H, 'D', 00H, 'O', 00H, 00H, 00H
$SG4294430757 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'C', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430756 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430755 DB 'a', 00H, 'r', 00H, '-', 00H, 'O', 00H, 'M', 00H, 00H, 00H
$SG4294430754 DB 'e', 00H, 'n', 00H, '-', 00H, 'J', 00H, 'M', 00H, 00H, 00H
$SG4294430753 DB 'e', 00H, 's', 00H, '-', 00H, 'V', 00H, 'E', 00H, 00H, 00H
$SG4294430752 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430767 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'S', 00H, 'E', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430766 DB 'a', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'A', 00H, 00H, 00H
$SG4294430765 DB 'e', 00H, 'n', 00H, '-', 00H, 'I', 00H, 'E', 00H, 00H, 00H
$SG4294430764 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'A', 00H, 00H, 00H
$SG4294430763 DB 'f', 00H, 'r', 00H, '-', 00H, 'M', 00H, 'C', 00H, 00H, 00H
$SG4294430762 DB 's', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'A', 00H, '-', 00H
	DB	'L', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430761 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'N', 00H, 'O', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430760 DB 'a', 00H, 'r', 00H, '-', 00H, 'T', 00H, 'N', 00H, 00H, 00H
$SG4294430743 DB 'e', 00H, 'n', 00H, '-', 00H, 'T', 00H, 'T', 00H, 00H, 00H
$SG4294430742 DB 'e', 00H, 's', 00H, '-', 00H, 'A', 00H, 'R', 00H, 00H, 00H
$SG4294430741 DB 'a', 00H, 'r', 00H, '-', 00H, 'L', 00H, 'B', 00H, 00H, 00H
$SG4294430740 DB 'e', 00H, 'n', 00H, '-', 00H, 'Z', 00H, 'W', 00H, 00H, 00H
$SG4294430739 DB 'e', 00H, 's', 00H, '-', 00H, 'E', 00H, 'C', 00H, 00H, 00H
$SG4294430738 DB 'a', 00H, 'r', 00H, '-', 00H, 'K', 00H, 'W', 00H, 00H, 00H
$SG4294430737 DB 'e', 00H, 'n', 00H, '-', 00H, 'P', 00H, 'H', 00H, 00H, 00H
$SG4294430736 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'L', 00H, 00H, 00H
$SG4294430751 DB 'a', 00H, 'r', 00H, '-', 00H, 'Y', 00H, 'E', 00H, 00H, 00H
$SG4294430750 DB 'e', 00H, 'n', 00H, '-', 00H, 'C', 00H, 'B', 00H, 00H, 00H
$SG4294430749 DB 'e', 00H, 's', 00H, '-', 00H, 'C', 00H, 'O', 00H, 00H, 00H
$SG4294430748 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'F', 00H, 'I', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430747 DB 'a', 00H, 'r', 00H, '-', 00H, 'S', 00H, 'Y', 00H, 00H, 00H
$SG4294430746 DB 'e', 00H, 'n', 00H, '-', 00H, 'B', 00H, 'Z', 00H, 00H, 00H
$SG4294430745 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'E', 00H, 00H, 00H
$SG4294430744 DB 'a', 00H, 'r', 00H, '-', 00H, 'J', 00H, 'O', 00H, 00H, 00H
$SG4294430727 DB 'e', 00H, 's', 00H, '-', 00H, 'N', 00H, 'I', 00H, 00H, 00H
$SG4294430726 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'R', 00H, 00H, 00H
$SG4294430725 DB 'z', 00H, 'h', 00H, '-', 00H, 'C', 00H, 'H', 00H, 'T', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430724 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430723 DB 00H, 00H
	ORG $+2
$SG4294430722 DB 'a', 00H, 'f', 00H, 00H, 00H
	ORG $+2
$SG4294430721 DB 'a', 00H, 'f', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294430720 DB 'a', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430735 DB 'a', 00H, 'r', 00H, '-', 00H, 'A', 00H, 'E', 00H, 00H, 00H
$SG4294430734 DB 'e', 00H, 's', 00H, '-', 00H, 'U', 00H, 'Y', 00H, 00H, 00H
$SG4294430733 DB 'a', 00H, 'r', 00H, '-', 00H, 'B', 00H, 'H', 00H, 00H, 00H
$SG4294430732 DB 'e', 00H, 's', 00H, '-', 00H, 'P', 00H, 'Y', 00H, 00H, 00H
$SG4294430731 DB 'a', 00H, 'r', 00H, '-', 00H, 'Q', 00H, 'A', 00H, 00H, 00H
$SG4294430730 DB 'e', 00H, 's', 00H, '-', 00H, 'B', 00H, 'O', 00H, 00H, 00H
$SG4294430729 DB 'e', 00H, 's', 00H, '-', 00H, 'S', 00H, 'V', 00H, 00H, 00H
$SG4294430728 DB 'e', 00H, 's', 00H, '-', 00H, 'H', 00H, 'N', 00H, 00H, 00H
$SG4294430711 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'y', 00H, 00H, 00H
$SG4294430710 DB 'a', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'a', 00H, 00H, 00H
$SG4294430709 DB 'a', 00H, 'r', 00H, '-', 00H, 'o', 00H, 'm', 00H, 00H, 00H
$SG4294430708 DB 'a', 00H, 'r', 00H, '-', 00H, 'q', 00H, 'a', 00H, 00H, 00H
$SG4294430707 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'a', 00H, 00H, 00H
$SG4294430706 DB 'a', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H, 00H, 00H
$SG4294430705 DB 'a', 00H, 'r', 00H, '-', 00H, 't', 00H, 'n', 00H, 00H, 00H
$SG4294430704 DB 'a', 00H, 'r', 00H, '-', 00H, 'y', 00H, 'e', 00H, 00H, 00H
$SG4294430719 DB 'a', 00H, 'r', 00H, '-', 00H, 'a', 00H, 'e', 00H, 00H, 00H
$SG4294430718 DB 'a', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'h', 00H, 00H, 00H
$SG4294430717 DB 'a', 00H, 'r', 00H, '-', 00H, 'd', 00H, 'z', 00H, 00H, 00H
$SG4294430716 DB 'a', 00H, 'r', 00H, '-', 00H, 'e', 00H, 'g', 00H, 00H, 00H
$SG4294430715 DB 'a', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'q', 00H, 00H, 00H
$SG4294430714 DB 'a', 00H, 'r', 00H, '-', 00H, 'j', 00H, 'o', 00H, 00H, 00H
$SG4294430713 DB 'a', 00H, 'r', 00H, '-', 00H, 'k', 00H, 'w', 00H, 00H, 00H
$SG4294430712 DB 'a', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'b', 00H, 00H, 00H
$SG4294430695 DB 'b', 00H, 's', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430694 DB 'c', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430693 DB 'c', 00H, 'a', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294430692 DB 'c', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430691 DB 'c', 00H, 's', 00H, '-', 00H, 'c', 00H, 'z', 00H, 00H, 00H
$SG4294430690 DB 'c', 00H, 'y', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294430689 DB 'd', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430688 DB 'd', 00H, 'a', 00H, '-', 00H, 'd', 00H, 'k', 00H, 00H, 00H
$SG4294430703 DB 'a', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294430702 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430701 DB 'a', 00H, 'z', 00H, '-', 00H, 'a', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430700 DB 'b', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430699 DB 'b', 00H, 'e', 00H, '-', 00H, 'b', 00H, 'y', 00H, 00H, 00H
$SG4294430698 DB 'b', 00H, 'g', 00H, 00H, 00H
	ORG $+2
$SG4294430697 DB 'b', 00H, 'g', 00H, '-', 00H, 'b', 00H, 'g', 00H, 00H, 00H
$SG4294430696 DB 'b', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430679 DB 'e', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430678 DB 'e', 00H, 'l', 00H, '-', 00H, 'g', 00H, 'r', 00H, 00H, 00H
$SG4294430677 DB 'e', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430676 DB 'e', 00H, 'n', 00H, '-', 00H, 'a', 00H, 'u', 00H, 00H, 00H
$SG4294430675 DB 'e', 00H, 'n', 00H, '-', 00H, 'b', 00H, 'z', 00H, 00H, 00H
$SG4294430674 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294430673 DB 'e', 00H, 'n', 00H, '-', 00H, 'c', 00H, 'b', 00H, 00H, 00H
$SG4294430672 DB 'e', 00H, 'n', 00H, '-', 00H, 'g', 00H, 'b', 00H, 00H, 00H
$SG4294430687 DB 'd', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430686 DB 'd', 00H, 'e', 00H, '-', 00H, 'a', 00H, 't', 00H, 00H, 00H
$SG4294430685 DB 'd', 00H, 'e', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294430684 DB 'd', 00H, 'e', 00H, '-', 00H, 'd', 00H, 'e', 00H, 00H, 00H
$SG4294430683 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'i', 00H, 00H, 00H
$SG4294430682 DB 'd', 00H, 'e', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294430681 DB 'd', 00H, 'i', 00H, 'v', 00H, 00H, 00H
$SG4294430680 DB 'd', 00H, 'i', 00H, 'v', 00H, '-', 00H, 'm', 00H, 'v', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430663 DB 'e', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430662 DB 'e', 00H, 's', 00H, '-', 00H, 'a', 00H, 'r', 00H, 00H, 00H
$SG4294430661 DB 'e', 00H, 's', 00H, '-', 00H, 'b', 00H, 'o', 00H, 00H, 00H
$SG4294430660 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'l', 00H, 00H, 00H
$SG4294430659 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'o', 00H, 00H, 00H
$SG4294430658 DB 'e', 00H, 's', 00H, '-', 00H, 'c', 00H, 'r', 00H, 00H, 00H
$SG4294430657 DB 'e', 00H, 's', 00H, '-', 00H, 'd', 00H, 'o', 00H, 00H, 00H
$SG4294430656 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 'c', 00H, 00H, 00H
$SG4294430671 DB 'e', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'e', 00H, 00H, 00H
$SG4294430670 DB 'e', 00H, 'n', 00H, '-', 00H, 'j', 00H, 'm', 00H, 00H, 00H
$SG4294430669 DB 'e', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294430668 DB 'e', 00H, 'n', 00H, '-', 00H, 'p', 00H, 'h', 00H, 00H, 00H
$SG4294430667 DB 'e', 00H, 'n', 00H, '-', 00H, 't', 00H, 't', 00H, 00H, 00H
$SG4294430666 DB 'e', 00H, 'n', 00H, '-', 00H, 'u', 00H, 's', 00H, 00H, 00H
$SG4294430665 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294430664 DB 'e', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'w', 00H, 00H, 00H
$SG4294430647 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'y', 00H, 00H, 00H
$SG4294430646 DB 'e', 00H, 's', 00H, '-', 00H, 's', 00H, 'v', 00H, 00H, 00H
$SG4294430645 DB 'e', 00H, 's', 00H, '-', 00H, 'u', 00H, 'y', 00H, 00H, 00H
$SG4294430644 DB 'e', 00H, 's', 00H, '-', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294430643 DB 'e', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430642 DB 'e', 00H, 't', 00H, '-', 00H, 'e', 00H, 'e', 00H, 00H, 00H
$SG4294430641 DB 'e', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430640 DB 'e', 00H, 'u', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294430655 DB 'e', 00H, 's', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294430654 DB 'e', 00H, 's', 00H, '-', 00H, 'g', 00H, 't', 00H, 00H, 00H
$SG4294430653 DB 'e', 00H, 's', 00H, '-', 00H, 'h', 00H, 'n', 00H, 00H, 00H
$SG4294430652 DB 'e', 00H, 's', 00H, '-', 00H, 'm', 00H, 'x', 00H, 00H, 00H
$SG4294430651 DB 'e', 00H, 's', 00H, '-', 00H, 'n', 00H, 'i', 00H, 00H, 00H
$SG4294430650 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'a', 00H, 00H, 00H
$SG4294430649 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'e', 00H, 00H, 00H
$SG4294430648 DB 'e', 00H, 's', 00H, '-', 00H, 'p', 00H, 'r', 00H, 00H, 00H
$SG4294430631 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'a', 00H, 00H, 00H
$SG4294430630 DB 'f', 00H, 'r', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294430629 DB 'f', 00H, 'r', 00H, '-', 00H, 'f', 00H, 'r', 00H, 00H, 00H
$SG4294430628 DB 'f', 00H, 'r', 00H, '-', 00H, 'l', 00H, 'u', 00H, 00H, 00H
$SG4294430627 DB 'f', 00H, 'r', 00H, '-', 00H, 'm', 00H, 'c', 00H, 00H, 00H
$SG4294430626 DB 'g', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430625 DB 'g', 00H, 'l', 00H, '-', 00H, 'e', 00H, 's', 00H, 00H, 00H
$SG4294430624 DB 'g', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430639 DB 'f', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430638 DB 'f', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'r', 00H, 00H, 00H
$SG4294430637 DB 'f', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294430636 DB 'f', 00H, 'i', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294430635 DB 'f', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430634 DB 'f', 00H, 'o', 00H, '-', 00H, 'f', 00H, 'o', 00H, 00H, 00H
$SG4294430633 DB 'f', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430632 DB 'f', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294430615 DB 'h', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430614 DB 'h', 00H, 'u', 00H, '-', 00H, 'h', 00H, 'u', 00H, 00H, 00H
$SG4294430613 DB 'h', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294430612 DB 'h', 00H, 'y', 00H, '-', 00H, 'a', 00H, 'm', 00H, 00H, 00H
$SG4294430611 DB 'i', 00H, 'd', 00H, 00H, 00H
	ORG $+2
$SG4294430610 DB 'i', 00H, 'd', 00H, '-', 00H, 'i', 00H, 'd', 00H, 00H, 00H
$SG4294430609 DB 'i', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430608 DB 'i', 00H, 's', 00H, '-', 00H, 'i', 00H, 's', 00H, 00H, 00H
$SG4294430623 DB 'g', 00H, 'u', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430622 DB 'h', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430621 DB 'h', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'l', 00H, 00H, 00H
$SG4294430620 DB 'h', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294430619 DB 'h', 00H, 'i', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430618 DB 'h', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430617 DB 'h', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, 00H, 00H
$SG4294430616 DB 'h', 00H, 'r', 00H, '-', 00H, 'h', 00H, 'r', 00H, 00H, 00H
$SG4294430599 DB 'k', 00H, 'k', 00H, '-', 00H, 'k', 00H, 'z', 00H, 00H, 00H
$SG4294430598 DB 'k', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430597 DB 'k', 00H, 'n', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430596 DB 'k', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430595 DB 'k', 00H, 'o', 00H, 'k', 00H, 00H, 00H
$SG4294430594 DB 'k', 00H, 'o', 00H, 'k', 00H, '-', 00H, 'i', 00H, 'n', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430593 DB 'k', 00H, 'o', 00H, '-', 00H, 'k', 00H, 'r', 00H, 00H, 00H
$SG4294430592 DB 'k', 00H, 'y', 00H, 00H, 00H
	ORG $+2
$SG4294430607 DB 'i', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430606 DB 'i', 00H, 't', 00H, '-', 00H, 'c', 00H, 'h', 00H, 00H, 00H
$SG4294430605 DB 'i', 00H, 't', 00H, '-', 00H, 'i', 00H, 't', 00H, 00H, 00H
$SG4294430604 DB 'j', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430603 DB 'j', 00H, 'a', 00H, '-', 00H, 'j', 00H, 'p', 00H, 00H, 00H
$SG4294430602 DB 'k', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430601 DB 'k', 00H, 'a', 00H, '-', 00H, 'g', 00H, 'e', 00H, 00H, 00H
$SG4294430600 DB 'k', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430583 DB 'm', 00H, 'l', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430582 DB 'm', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430581 DB 'm', 00H, 'n', 00H, '-', 00H, 'm', 00H, 'n', 00H, 00H, 00H
$SG4294430580 DB 'm', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430579 DB 'm', 00H, 'r', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430578 DB 'm', 00H, 's', 00H, 00H, 00H
	ORG $+2
$SG4294430577 DB 'm', 00H, 's', 00H, '-', 00H, 'b', 00H, 'n', 00H, 00H, 00H
$SG4294430576 DB 'm', 00H, 's', 00H, '-', 00H, 'm', 00H, 'y', 00H, 00H, 00H
$SG4294430591 DB 'k', 00H, 'y', 00H, '-', 00H, 'k', 00H, 'g', 00H, 00H, 00H
$SG4294430590 DB 'l', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430589 DB 'l', 00H, 't', 00H, '-', 00H, 'l', 00H, 't', 00H, 00H, 00H
$SG4294430588 DB 'l', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294430587 DB 'l', 00H, 'v', 00H, '-', 00H, 'l', 00H, 'v', 00H, 00H, 00H
$SG4294430586 DB 'm', 00H, 'i', 00H, '-', 00H, 'n', 00H, 'z', 00H, 00H, 00H
$SG4294430585 DB 'm', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430584 DB 'm', 00H, 'k', 00H, '-', 00H, 'm', 00H, 'k', 00H, 00H, 00H
$SG4294430567 DB 'p', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430566 DB 'p', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430565 DB 'p', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430564 DB 'p', 00H, 'l', 00H, '-', 00H, 'p', 00H, 'l', 00H, 00H, 00H
$SG4294430563 DB 'p', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430562 DB 'p', 00H, 't', 00H, '-', 00H, 'b', 00H, 'r', 00H, 00H, 00H
$SG4294430561 DB 'p', 00H, 't', 00H, '-', 00H, 'p', 00H, 't', 00H, 00H, 00H
$SG4294430560 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'b', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430575 DB 'm', 00H, 't', 00H, '-', 00H, 'm', 00H, 't', 00H, 00H, 00H
$SG4294430574 DB 'n', 00H, 'b', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294430573 DB 'n', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430572 DB 'n', 00H, 'l', 00H, '-', 00H, 'b', 00H, 'e', 00H, 00H, 00H
$SG4294430571 DB 'n', 00H, 'l', 00H, '-', 00H, 'n', 00H, 'l', 00H, 00H, 00H
$SG4294430570 DB 'n', 00H, 'n', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294430569 DB 'n', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430568 DB 'n', 00H, 's', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294430551 DB 's', 00H, 'e', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294430550 DB 's', 00H, 'e', 00H, '-', 00H, 'n', 00H, 'o', 00H, 00H, 00H
$SG4294430549 DB 's', 00H, 'e', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294430548 DB 's', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430547 DB 's', 00H, 'k', 00H, '-', 00H, 's', 00H, 'k', 00H, 00H, 00H
$SG4294430546 DB 's', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430545 DB 's', 00H, 'l', 00H, '-', 00H, 's', 00H, 'i', 00H, 00H, 00H
$SG4294430544 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430559 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'e', 00H, 'c', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430558 DB 'q', 00H, 'u', 00H, 'z', 00H, '-', 00H, 'p', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430557 DB 'r', 00H, 'o', 00H, 00H, 00H
	ORG $+2
$SG4294430556 DB 'r', 00H, 'o', 00H, '-', 00H, 'r', 00H, 'o', 00H, 00H, 00H
$SG4294430555 DB 'r', 00H, 'u', 00H, 00H, 00H
	ORG $+2
$SG4294430554 DB 'r', 00H, 'u', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294430553 DB 's', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430552 DB 's', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430535 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430534 DB 's', 00H, 'r', 00H, '-', 00H, 'b', 00H, 'a', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430533 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430532 DB 's', 00H, 'r', 00H, '-', 00H, 's', 00H, 'p', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430531 DB 's', 00H, 'v', 00H, 00H, 00H
	ORG $+2
$SG4294430530 DB 's', 00H, 'v', 00H, '-', 00H, 'f', 00H, 'i', 00H, 00H, 00H
$SG4294430529 DB 's', 00H, 'v', 00H, '-', 00H, 's', 00H, 'e', 00H, 00H, 00H
$SG4294430528 DB 's', 00H, 'w', 00H, 00H, 00H
	ORG $+2
$SG4294430543 DB 's', 00H, 'm', 00H, 'a', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430542 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 'n', 00H, 'o', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430541 DB 's', 00H, 'm', 00H, 'j', 00H, '-', 00H, 's', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430540 DB 's', 00H, 'm', 00H, 'n', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430539 DB 's', 00H, 'm', 00H, 's', 00H, '-', 00H, 'f', 00H, 'i', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430538 DB 's', 00H, 'q', 00H, 00H, 00H
	ORG $+2
$SG4294430537 DB 's', 00H, 'q', 00H, '-', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294430536 DB 's', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430519 DB 't', 00H, 'h', 00H, '-', 00H, 't', 00H, 'h', 00H, 00H, 00H
$SG4294430518 DB 't', 00H, 'n', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294430517 DB 't', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430516 DB 't', 00H, 'r', 00H, '-', 00H, 't', 00H, 'r', 00H, 00H, 00H
$SG4294430515 DB 't', 00H, 't', 00H, 00H, 00H
	ORG $+2
$SG4294430514 DB 't', 00H, 't', 00H, '-', 00H, 'r', 00H, 'u', 00H, 00H, 00H
$SG4294430513 DB 'u', 00H, 'k', 00H, 00H, 00H
	ORG $+2
$SG4294430512 DB 'u', 00H, 'k', 00H, '-', 00H, 'u', 00H, 'a', 00H, 00H, 00H
$SG4294430527 DB 's', 00H, 'w', 00H, '-', 00H, 'k', 00H, 'e', 00H, 00H, 00H
$SG4294430526 DB 's', 00H, 'y', 00H, 'r', 00H, 00H, 00H
$SG4294430525 DB 's', 00H, 'y', 00H, 'r', 00H, '-', 00H, 's', 00H, 'y', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430524 DB 't', 00H, 'a', 00H, 00H, 00H
	ORG $+2
$SG4294430523 DB 't', 00H, 'a', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430522 DB 't', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430521 DB 't', 00H, 'e', 00H, '-', 00H, 'i', 00H, 'n', 00H, 00H, 00H
$SG4294430520 DB 't', 00H, 'h', 00H, 00H, 00H
	ORG $+2
$SG4294430503 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 's', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430502 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'h', 00H, 't', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430501 DB 'z', 00H, 'h', 00H, '-', 00H, 'c', 00H, 'n', 00H, 00H, 00H
$SG4294430500 DB 'z', 00H, 'h', 00H, '-', 00H, 'h', 00H, 'k', 00H, 00H, 00H
$SG4294430499 DB 'z', 00H, 'h', 00H, '-', 00H, 'm', 00H, 'o', 00H, 00H, 00H
$SG4294430498 DB 'z', 00H, 'h', 00H, '-', 00H, 's', 00H, 'g', 00H, 00H, 00H
$SG4294430497 DB 'z', 00H, 'h', 00H, '-', 00H, 't', 00H, 'w', 00H, 00H, 00H
$SG4294430496 DB 'z', 00H, 'u', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294430511 DB 'u', 00H, 'r', 00H, 00H, 00H
	ORG $+2
$SG4294430510 DB 'u', 00H, 'r', 00H, '-', 00H, 'p', 00H, 'k', 00H, 00H, 00H
$SG4294430509 DB 'u', 00H, 'z', 00H, 00H, 00H
	ORG $+2
$SG4294430508 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'c', 00H, 'y', 00H, 'r', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294430507 DB 'u', 00H, 'z', 00H, '-', 00H, 'u', 00H, 'z', 00H, '-', 00H
	DB	'l', 00H, 'a', 00H, 't', 00H, 'n', 00H, 00H, 00H
	ORG $+2
$SG4294430506 DB 'v', 00H, 'i', 00H, 00H, 00H
	ORG $+2
$SG4294430505 DB 'v', 00H, 'i', 00H, '-', 00H, 'v', 00H, 'n', 00H, 00H, 00H
$SG4294430504 DB 'x', 00H, 'h', 00H, '-', 00H, 'z', 00H, 'a', 00H, 00H, 00H
$SG4294430455 DB 'A', 00H, 'p', 00H, 'p', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294430454 DB 'C', 00H, 'L', 00H, 'S', 00H, 'I', 00H, 'D', 00H, 00H, 00H
$SG4294430453 DB 'C', 00H, 'o', 00H, 'm', 00H, 'p', 00H, 'o', 00H, 'n', 00H
	DB	'e', 00H, 'n', 00H, 't', 00H, ' ', 00H, 'C', 00H, 'a', 00H, 't'
	DB	00H, 'e', 00H, 'g', 00H, 'o', 00H, 'r', 00H, 'i', 00H, 'e', 00H
	DB	's', 00H, 00H, 00H
	ORG $+2
$SG4294430452 DB 'F', 00H, 'i', 00H, 'l', 00H, 'e', 00H, 'T', 00H, 'y', 00H
	DB	'p', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430451 DB 'I', 00H, 'n', 00H, 't', 00H, 'e', 00H, 'r', 00H, 'f', 00H
	DB	'a', 00H, 'c', 00H, 'e', 00H, 00H, 00H
$SG4294430450 DB 'H', 00H, 'a', 00H, 'r', 00H, 'd', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430449 DB 'M', 00H, 'i', 00H, 'm', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430448 DB 'S', 00H, 'A', 00H, 'M', 00H, 00H, 00H
$SG4294430463 DB 'S', 00H, 00H, 00H
$SG4294430462 DB 'M', 00H, 00H, 00H
$SG4294430461 DB 'D', 00H, 00H, 00H
$SG4294430460 DB 'B', 00H, 00H, 00H
$SG4294430459 DB 'V', 00H, 'a', 00H, 'l', 00H, 00H, 00H
$SG4294430458 DB 'F', 00H, 'o', 00H, 'r', 00H, 'c', 00H, 'e', 00H, 'R', 00H
	DB	'e', 00H, 'm', 00H, 'o', 00H, 'v', 00H, 'e', 00H, 00H, 00H
$SG4294430457 DB 'N', 00H, 'o', 00H, 'R', 00H, 'e', 00H, 'm', 00H, 'o', 00H
	DB	'v', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430456 DB 'D', 00H, 'e', 00H, 'l', 00H, 'e', 00H, 't', 00H, 'e', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430447 DB 'S', 00H, 'E', 00H, 'C', 00H, 'U', 00H, 'R', 00H, 'I', 00H
	DB	'T', 00H, 'Y', 00H, 00H, 00H
	ORG $+2
$SG4294430446 DB 'S', 00H, 'Y', 00H, 'S', 00H, 'T', 00H, 'E', 00H, 'M', 00H
	DB	00H, 00H
	ORG $+2
$SG4294430445 DB 'S', 00H, 'o', 00H, 'f', 00H, 't', 00H, 'w', 00H, 'a', 00H
	DB	'r', 00H, 'e', 00H, 00H, 00H
	ORG $+2
$SG4294430444 DB 'T', 00H, 'y', 00H, 'p', 00H, 'e', 00H, 'L', 00H, 'i', 00H
	DB	'b', 00H, 00H, 00H
$SG4294430413 DB ':', 00H, 00H, 00H
$SG4294430412 DB 00H, 00H
	ORG $+2
$SG4294430411 DB 00H, 00H
	ORG $+2
$SG4294430320 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294429607 DB 'm', 00H, 'b', 00H, '.', 00H, 'd', 00H, 'l', 00H, 'l', 00H
	DB	00H, 00H
	ORG $+2
$SG4294429606 DB 'n', 00H, 'o', 00H, 'd', 00H, 'e', 00H, '.', 00H, 'd', 00H
	DB	'l', 00H, 'l', 00H, 00H, 00H
	ORG $+2
$SG4294429155 DB ':', 00H, '/', 00H, '/', 00H, 00H, 00H
$SG4294429154 DB 'f', 00H, 'i', 00H, 'l', 00H, 'e', 00H, ':', 00H, '/', 00H
	DB	'/', 00H, '/', 00H, 00H, 00H
PUBLIC	??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@$4PPPPPPPM@A@AEPAXI@Z ; std::basic_fstream<char,std::char_traits<char> >::`vector deleting destructor'
PUBLIC	??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >
PUBLIC	??_G?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z ; std::basic_fstream<char,std::char_traits<char> >::`scalar deleting destructor'
PUBLIC	??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >
PUBLIC	??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_fstream<char,std::char_traits<char> >::~basic_fstream<char,std::char_traits<char> >
PUBLIC	?rdbuf@?$basic_fstream@DU?$char_traits@D@std@@@std@@QBEPAV?$basic_filebuf@DU?$char_traits@D@std@@@2@XZ ; std::basic_fstream<char,std::char_traits<char> >::rdbuf
PUBLIC	??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ ; std::basic_fstream<char,std::char_traits<char> >::`vbase destructor'
PUBLIC	?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; GetScript
PUBLIC	?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ ; GetUrl
PUBLIC	??7ios_base@std@@QBE_NXZ			; std::ios_base::operator!
EXTRN	??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z:PROC ; std::basic_fstream<char,std::char_traits<char> >::`vector deleting destructor'
?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B DD 01H DUP (?)	; WTL::atlTraceUI
_BSS	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ DD 019930522H
	DD	02H
	DD	FLAT:__unwindtable$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	04H
__unwindtable$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ$0
	DD	00H
	DD	FLAT:__unwindfunclet$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ$1
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z DD 019930522H
	DD	01H
	DD	FLAT:__unwindtable$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z$0
xdata$x	ENDS
;	COMDAT xdata$x
xdata$x	SEGMENT
__ehfuncinfo$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z DD 019930522H
	DD	03H
	DD	FLAT:__unwindtable$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z DD 0ffffffffH
	DD	FLAT:__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$0
	DD	00H
	DD	FLAT:__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$1
	DD	01H
	DD	FLAT:__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$2
__ehfuncinfo$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ DD 019930522H
	DD	0aH
	DD	FLAT:__unwindtable$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__ehfuncinfo$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ DD 019930522H
	DD	08H
	DD	FLAT:__unwindtable$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
	DD	2 DUP(00H)
	DD	2 DUP(00H)
	DD	00H
	DD	00H
__unwindtable$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$5
	DD	00H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$0
	DD	01H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$1
	DD	02H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$2
	DD	03H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$3
	DD	02H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$3
	DD	05H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$4
	DD	06H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$6
	DD	07H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$7
	DD	08H
	DD	FLAT:__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$8
__unwindtable$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ DD 0ffffffffH
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$4
	DD	00H
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$0
	DD	01H
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$1
	DD	02H
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$2
	DD	01H
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$2
	DD	04H
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$3
	DD	05H
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$5
	DD	06H
	DD	FLAT:__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$6
?atlTraceUI$initializer$@WTL@@3P6AXXZA DD FLAT:??__EatlTraceUI@WTL@@YAXXZ ; WTL::atlTraceUI$initializer$
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wtl\atlapp.h
;	COMDAT ??__EatlTraceUI@WTL@@YAXXZ
text$di	SEGMENT
??__EatlTraceUI@WTL@@YAXXZ PROC				; WTL::`dynamic initializer for 'atlTraceUI'', COMDAT

; 151  : DECLARE_TRACE_CATEGORY(atlTraceUI);

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a 00		 push	 0
  00005	b9 00 00 00 00	 mov	 ecx, OFFSET ?atlTraceUI@WTL@@3VCTraceCategory@ATL@@B
  0000a	e8 00 00 00 00	 call	 ??0CTraceCategory@ATL@@QAE@PB_W@Z ; ATL::CTraceCategory::CTraceCategory
  0000f	3b ec		 cmp	 ebp, esp
  00011	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
??__EatlTraceUI@WTL@@YAXXZ ENDP				; WTL::`dynamic initializer for 'atlTraceUI''
text$di	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\xiosbase
;	COMDAT ??7ios_base@std@@QBE_NXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??7ios_base@std@@QBE_NXZ PROC				; std::ios_base::operator!, COMDAT
; _this$ = ecx

; 282  : 		{	// test if any stream operation has failed

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 283  : 		return (fail());

  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	e8 00 00 00 00	 call	 ?fail@ios_base@std@@QBE_NXZ ; std::ios_base::fail

; 284  : 		}

  00016	83 c4 04	 add	 esp, 4
  00019	3b ec		 cmp	 ebp, esp
  0001b	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00020	8b e5		 mov	 esp, ebp
  00022	5d		 pop	 ebp
  00023	c3		 ret	 0
??7ios_base@std@@QBE_NXZ ENDP				; std::ios_base::operator!
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\util.cpp
_TEXT	SEGMENT
tv162 = -212						; size = 4
tv65 = -208						; size = 4
tv160 = -204						; size = 4
tv70 = -200						; size = 4
$T2 = -196						; size = 4
$T3 = -192						; size = 24
$T4 = -168						; size = 24
$T5 = -144						; size = 24
_vArgs$ = -116						; size = 12
_ocmdline$ = -96					; size = 48
_surl$ = -40						; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ PROC ; GetUrl

; 9    : std::wstring GetUrl() {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	81 ec c8 00 00
	00		 sub	 esp, 200		; 000000c8H
  0001e	56		 push	 esi
  0001f	57		 push	 edi
  00020	8d bd 2c ff ff
	ff		 lea	 edi, DWORD PTR [ebp-212]
  00026	b9 32 00 00 00	 mov	 ecx, 50			; 00000032H
  0002b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00030	f3 ab		 rep stosd
  00032	c7 85 3c ff ff
	ff 00 00 00 00	 mov	 DWORD PTR $T2[ebp], 0

; 10   : 	using namespace std;
; 11   : 	wstring surl;

  0003c	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  0003f	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00044	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 12   : 	CCmdLine ocmdline(GetCommandLineW());

  0004b	8b f4		 mov	 esi, esp
  0004d	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetCommandLineW@0
  00053	3b f4		 cmp	 esi, esp
  00055	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0005a	50		 push	 eax
  0005b	8d 8d 70 ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  00061	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@QB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00066	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  0006a	8d 85 70 ff ff
	ff		 lea	 eax, DWORD PTR $T5[ebp]
  00070	50		 push	 eax
  00071	8d 4d a0	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  00074	e8 00 00 00 00	 call	 ??0CCmdLine@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; CCmdLine::CCmdLine
  00079	c6 45 fc 04	 mov	 BYTE PTR __$EHRec$[ebp+8], 4
  0007d	8d 8d 70 ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  00083	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 13   : 	auto vArgs = ocmdline.GetArgs();

  00088	6a 01		 push	 1
  0008a	6a ff		 push	 -1
  0008c	8d 4d 8c	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  0008f	51		 push	 ecx
  00090	8d 4d a0	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  00093	e8 00 00 00 00	 call	 ?GetArgs@CCmdLine@@QAE?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@I_N@Z ; CCmdLine::GetArgs
  00098	c6 45 fc 05	 mov	 BYTE PTR __$EHRec$[ebp+8], 5

; 14   : 	if (vArgs.size() < 3) {

  0009c	8d 4d 8c	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  0009f	e8 00 00 00 00	 call	 ?size@?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEIXZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::size
  000a4	83 f8 03	 cmp	 eax, 3
  000a7	73 47		 jae	 SHORT $LN2@GetUrl

; 15   : 		return surl;

  000a9	8d 55 d8	 lea	 edx, DWORD PTR _surl$[ebp]
  000ac	52		 push	 edx
  000ad	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  000b0	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000b5	8b 85 3c ff ff
	ff		 mov	 eax, DWORD PTR $T2[ebp]
  000bb	83 c8 01	 or	 eax, 1
  000be	89 85 3c ff ff
	ff		 mov	 DWORD PTR $T2[ebp], eax
  000c4	c6 45 fc 04	 mov	 BYTE PTR __$EHRec$[ebp+8], 4
  000c8	8d 4d 8c	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  000cb	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  000d0	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  000d4	8d 4d a0	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  000d7	e8 00 00 00 00	 call	 ??1CCmdLine@@QAE@XZ
  000dc	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  000e0	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  000e3	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000e8	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  000eb	e9 f0 00 00 00	 jmp	 $LN1@GetUrl
$LN2@GetUrl:

; 16   : 	}
; 17   : 	surl = vArgs[2];

  000f0	6a 02		 push	 2
  000f2	8d 4d 8c	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  000f5	e8 00 00 00 00	 call	 ??A?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAEAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@I@Z ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::operator[]
  000fa	50		 push	 eax
  000fb	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  000fe	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=

; 18   : 	if (surl.find(L"://") == surl.npos) {

  00103	6a 00		 push	 0
  00105	68 00 00 00 00	 push	 OFFSET $SG4294429155
  0010a	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  0010d	e8 00 00 00 00	 call	 ?find@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEIQB_WI@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::find
  00112	83 f8 ff	 cmp	 eax, -1
  00115	0f 85 83 00 00
	00		 jne	 $LN3@GetUrl

; 19   : 		//if (surl.find(L"\\") == surl.npos) surl = __GetModuleFileNameW(TRUE) + surl;
; 20   : 		surl = wstring(L"file:///") + surl;

  0011b	68 00 00 00 00	 push	 OFFSET $SG4294429154
  00120	8d 8d 58 ff ff
	ff		 lea	 ecx, DWORD PTR $T4[ebp]
  00126	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@QB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0012b	89 85 38 ff ff
	ff		 mov	 DWORD PTR tv70[ebp], eax
  00131	8b 8d 38 ff ff
	ff		 mov	 ecx, DWORD PTR tv70[ebp]
  00137	89 8d 34 ff ff
	ff		 mov	 DWORD PTR tv160[ebp], ecx
  0013d	c6 45 fc 06	 mov	 BYTE PTR __$EHRec$[ebp+8], 6
  00141	8d 55 d8	 lea	 edx, DWORD PTR _surl$[ebp]
  00144	52		 push	 edx
  00145	8b 85 34 ff ff
	ff		 mov	 eax, DWORD PTR tv160[ebp]
  0014b	50		 push	 eax
  0014c	8d 8d 40 ff ff
	ff		 lea	 ecx, DWORD PTR $T3[ebp]
  00152	51		 push	 ecx
  00153	e8 00 00 00 00	 call	 ??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@$$QAV10@ABV10@@Z ; std::operator+<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00158	83 c4 0c	 add	 esp, 12			; 0000000cH
  0015b	89 85 30 ff ff
	ff		 mov	 DWORD PTR tv65[ebp], eax
  00161	8b 95 30 ff ff
	ff		 mov	 edx, DWORD PTR tv65[ebp]
  00167	89 95 2c ff ff
	ff		 mov	 DWORD PTR tv162[ebp], edx
  0016d	c6 45 fc 07	 mov	 BYTE PTR __$EHRec$[ebp+8], 7
  00171	8b 85 2c ff ff
	ff		 mov	 eax, DWORD PTR tv162[ebp]
  00177	50		 push	 eax
  00178	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  0017b	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV01@$$QAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00180	c6 45 fc 06	 mov	 BYTE PTR __$EHRec$[ebp+8], 6
  00184	8d 8d 40 ff ff
	ff		 lea	 ecx, DWORD PTR $T3[ebp]
  0018a	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0018f	c6 45 fc 05	 mov	 BYTE PTR __$EHRec$[ebp+8], 5
  00193	8d 8d 58 ff ff
	ff		 lea	 ecx, DWORD PTR $T4[ebp]
  00199	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
$LN3@GetUrl:

; 21   : 	}
; 22   : 
; 23   : 	return surl;

  0019e	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  001a1	51		 push	 ecx
  001a2	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  001a5	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  001aa	8b 95 3c ff ff
	ff		 mov	 edx, DWORD PTR $T2[ebp]
  001b0	83 ca 01	 or	 edx, 1
  001b3	89 95 3c ff ff
	ff		 mov	 DWORD PTR $T2[ebp], edx
  001b9	c6 45 fc 04	 mov	 BYTE PTR __$EHRec$[ebp+8], 4
  001bd	8d 4d 8c	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  001c0	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  001c5	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  001c9	8d 4d a0	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  001cc	e8 00 00 00 00	 call	 ??1CCmdLine@@QAE@XZ
  001d1	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  001d5	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  001d8	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  001dd	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
$LN1@GetUrl:

; 24   : }

  001e0	52		 push	 edx
  001e1	8b cd		 mov	 ecx, ebp
  001e3	50		 push	 eax
  001e4	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN18@GetUrl
  001ea	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  001ef	58		 pop	 eax
  001f0	5a		 pop	 edx
  001f1	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  001f4	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  001fb	5f		 pop	 edi
  001fc	5e		 pop	 esi
  001fd	81 c4 d4 00 00
	00		 add	 esp, 212		; 000000d4H
  00203	3b ec		 cmp	 ebp, esp
  00205	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0020a	8b e5		 mov	 esp, ebp
  0020c	5d		 pop	 ebp
  0020d	c3		 ret	 0
  0020e	66 90		 npad	 2
$LN18@GetUrl:
  00210	03 00 00 00	 DD	 3
  00214	00 00 00 00	 DD	 $LN17@GetUrl
$LN17@GetUrl:
  00218	d8 ff ff ff	 DD	 -40			; ffffffd8H
  0021c	18 00 00 00	 DD	 24			; 00000018H
  00220	00 00 00 00	 DD	 $LN13@GetUrl
  00224	a0 ff ff ff	 DD	 -96			; ffffffa0H
  00228	30 00 00 00	 DD	 48			; 00000030H
  0022c	00 00 00 00	 DD	 $LN14@GetUrl
  00230	8c ff ff ff	 DD	 -116			; ffffff8cH
  00234	0c 00 00 00	 DD	 12			; 0000000cH
  00238	00 00 00 00	 DD	 $LN15@GetUrl
$LN15@GetUrl:
  0023c	76		 DB	 118			; 00000076H
  0023d	41		 DB	 65			; 00000041H
  0023e	72		 DB	 114			; 00000072H
  0023f	67		 DB	 103			; 00000067H
  00240	73		 DB	 115			; 00000073H
  00241	00		 DB	 0
$LN14@GetUrl:
  00242	6f		 DB	 111			; 0000006fH
  00243	63		 DB	 99			; 00000063H
  00244	6d		 DB	 109			; 0000006dH
  00245	64		 DB	 100			; 00000064H
  00246	6c		 DB	 108			; 0000006cH
  00247	69		 DB	 105			; 00000069H
  00248	6e		 DB	 110			; 0000006eH
  00249	65		 DB	 101			; 00000065H
  0024a	00		 DB	 0
$LN13@GetUrl:
  0024b	73		 DB	 115			; 00000073H
  0024c	75		 DB	 117			; 00000075H
  0024d	72		 DB	 114			; 00000072H
  0024e	6c		 DB	 108			; 0000006cH
  0024f	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _surl$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$1:
  00008	8d 8d 70 ff ff
	ff		 lea	 ecx, DWORD PTR $T5[ebp]
  0000e	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$2:
  00013	8d 4d a0	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  00016	e9 00 00 00 00	 jmp	 ??1CCmdLine@@QAE@XZ
__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$3:
  0001b	8d 4d 8c	 lea	 ecx, DWORD PTR _vArgs$[ebp]
  0001e	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$4:
  00023	8b 85 3c ff ff
	ff		 mov	 eax, DWORD PTR $T2[ebp]
  00029	83 e0 01	 and	 eax, 1
  0002c	0f 84 0f 00 00
	00		 je	 $LN10@GetUrl
  00032	83 a5 3c ff ff
	ff fe		 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00039	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  0003c	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
$LN10@GetUrl:
  00041	c3		 ret	 0
__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$5:
  00042	8d 8d 58 ff ff
	ff		 lea	 ecx, DWORD PTR $T4[ebp]
  00048	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ$6:
  0004d	8d 8d 40 ff ff
	ff		 lea	 ecx, DWORD PTR $T3[ebp]
  00053	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__ehhandler$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ:
  00058	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
  0005d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?GetUrl@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ ENDP ; GetUrl
; Function compile flags: /Odtp /RTCsu
; File e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\wkeapi\include\util.cpp
_TEXT	SEGMENT
tv203 = -596						; size = 4
tv154 = -592						; size = 4
$T2 = -588						; size = 4
$T3 = -584						; size = 24
$T4 = -560						; size = 24
_wss$ = -532						; size = 176
_fs$ = -348						; size = 192
_vArgs$ = -148						; size = 12
_ocmdline$ = -128					; size = 48
_sfile$ = -72						; size = 24
_sjs$ = -40						; size = 24
__$EHRec$ = -12						; size = 12
___$ReturnUdt$ = 8					; size = 4
?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ PROC ; GetScript

; 26   : std::string GetScript() {

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	81 ec 48 02 00
	00		 sub	 esp, 584		; 00000248H
  0001e	56		 push	 esi
  0001f	57		 push	 edi
  00020	8d bd ac fd ff
	ff		 lea	 edi, DWORD PTR [ebp-596]
  00026	b9 92 00 00 00	 mov	 ecx, 146		; 00000092H
  0002b	b8 cc cc cc cc	 mov	 eax, -858993460		; ccccccccH
  00030	f3 ab		 rep stosd
  00032	c7 85 b4 fd ff
	ff 00 00 00 00	 mov	 DWORD PTR $T2[ebp], 0

; 27   : 	// ";external.alert(123);external.CreateActiveXObject('WScript.shell').Popup('acd');'Hello' + ', World!'"
; 28   : 	using namespace std;
; 29   : 	using namespace std;
; 30   : 	string sjs;

  0003c	8d 4d d8	 lea	 ecx, DWORD PTR _sjs$[ebp]
  0003f	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00044	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 31   : 	wstring sfile;

  0004b	8d 4d b8	 lea	 ecx, DWORD PTR _sfile$[ebp]
  0004e	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00053	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2

; 32   : 	CCmdLine ocmdline(GetCommandLineW());

  00057	8b f4		 mov	 esi, esp
  00059	ff 15 00 00 00
	00		 call	 DWORD PTR __imp__GetCommandLineW@0
  0005f	3b f4		 cmp	 esi, esp
  00061	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00066	50		 push	 eax
  00067	8d 8d d0 fd ff
	ff		 lea	 ecx, DWORD PTR $T4[ebp]
  0006d	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@QB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00072	c6 45 fc 03	 mov	 BYTE PTR __$EHRec$[ebp+8], 3
  00076	8d 85 d0 fd ff
	ff		 lea	 eax, DWORD PTR $T4[ebp]
  0007c	50		 push	 eax
  0007d	8d 4d 80	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  00080	e8 00 00 00 00	 call	 ??0CCmdLine@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; CCmdLine::CCmdLine
  00085	c6 45 fc 05	 mov	 BYTE PTR __$EHRec$[ebp+8], 5
  00089	8d 8d d0 fd ff
	ff		 lea	 ecx, DWORD PTR $T4[ebp]
  0008f	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 33   : 	auto vArgs = ocmdline.GetArgs();

  00094	6a 01		 push	 1
  00096	6a ff		 push	 -1
  00098	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR _vArgs$[ebp]
  0009e	51		 push	 ecx
  0009f	8d 4d 80	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  000a2	e8 00 00 00 00	 call	 ?GetArgs@CCmdLine@@QAE?AV?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@I_N@Z ; CCmdLine::GetArgs
  000a7	c6 45 fc 06	 mov	 BYTE PTR __$EHRec$[ebp+8], 6

; 34   : 	if (vArgs.size() < 2) {

  000ab	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR _vArgs$[ebp]
  000b1	e8 00 00 00 00	 call	 ?size@?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QBEIXZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::size
  000b6	83 f8 02	 cmp	 eax, 2
  000b9	73 56		 jae	 SHORT $LN2@GetScript

; 35   : 		return sjs;

  000bb	8d 55 d8	 lea	 edx, DWORD PTR _sjs$[ebp]
  000be	52		 push	 edx
  000bf	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  000c2	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  000c7	8b 85 b4 fd ff
	ff		 mov	 eax, DWORD PTR $T2[ebp]
  000cd	83 c8 01	 or	 eax, 1
  000d0	89 85 b4 fd ff
	ff		 mov	 DWORD PTR $T2[ebp], eax
  000d6	c6 45 fc 05	 mov	 BYTE PTR __$EHRec$[ebp+8], 5
  000da	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR _vArgs$[ebp]
  000e0	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  000e5	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  000e9	8d 4d 80	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  000ec	e8 00 00 00 00	 call	 ??1CCmdLine@@QAE@XZ
  000f1	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  000f5	8d 4d b8	 lea	 ecx, DWORD PTR _sfile$[ebp]
  000f8	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000fd	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00101	8d 4d d8	 lea	 ecx, DWORD PTR _sjs$[ebp]
  00104	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00109	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  0010c	e9 8f 01 00 00	 jmp	 $LN1@GetScript
$LN2@GetScript:

; 36   : 	}
; 37   : 	sfile = vArgs[1];

  00111	6a 01		 push	 1
  00113	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR _vArgs$[ebp]
  00119	e8 00 00 00 00	 call	 ??A?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAEAAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@I@Z ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::operator[]
  0011e	50		 push	 eax
  0011f	8d 4d b8	 lea	 ecx, DWORD PTR _sfile$[ebp]
  00122	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAEAAV01@ABV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=

; 38   : 	fstream fs(sfile, ios::in | ios::binary);

  00127	6a 01		 push	 1
  00129	6a 40		 push	 64			; 00000040H
  0012b	6a 21		 push	 33			; 00000021H
  0012d	8d 4d b8	 lea	 ecx, DWORD PTR _sfile$[ebp]
  00130	51		 push	 ecx
  00131	8d 8d a4 fe ff
	ff		 lea	 ecx, DWORD PTR _fs$[ebp]
  00137	e8 00 00 00 00	 call	 ??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >
  0013c	c6 45 fc 07	 mov	 BYTE PTR __$EHRec$[ebp+8], 7

; 39   : 	if (!fs) return sjs;

  00140	8b 95 a4 fe ff
	ff		 mov	 edx, DWORD PTR _fs$[ebp]
  00146	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  00149	8d 8c 05 a4 fe
	ff ff		 lea	 ecx, DWORD PTR _fs$[ebp+eax]
  00150	e8 00 00 00 00	 call	 ??7ios_base@std@@QBE_NXZ ; std::ios_base::operator!
  00155	0f b6 c8	 movzx	 ecx, al
  00158	85 c9		 test	 ecx, ecx
  0015a	74 65		 je	 SHORT $LN3@GetScript
  0015c	8d 55 d8	 lea	 edx, DWORD PTR _sjs$[ebp]
  0015f	52		 push	 edx
  00160	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00163	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00168	8b 85 b4 fd ff
	ff		 mov	 eax, DWORD PTR $T2[ebp]
  0016e	83 c8 01	 or	 eax, 1
  00171	89 85 b4 fd ff
	ff		 mov	 DWORD PTR $T2[ebp], eax
  00177	c6 45 fc 06	 mov	 BYTE PTR __$EHRec$[ebp+8], 6
  0017b	8d 8d a4 fe ff
	ff		 lea	 ecx, DWORD PTR _fs$[ebp]
  00181	e8 00 00 00 00	 call	 ??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ
  00186	c6 45 fc 05	 mov	 BYTE PTR __$EHRec$[ebp+8], 5
  0018a	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR _vArgs$[ebp]
  00190	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  00195	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  00199	8d 4d 80	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  0019c	e8 00 00 00 00	 call	 ??1CCmdLine@@QAE@XZ
  001a1	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  001a5	8d 4d b8	 lea	 ecx, DWORD PTR _sfile$[ebp]
  001a8	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  001ad	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  001b1	8d 4d d8	 lea	 ecx, DWORD PTR _sjs$[ebp]
  001b4	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  001b9	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
  001bc	e9 df 00 00 00	 jmp	 $LN1@GetScript
$LN3@GetScript:

; 40   : 	stringstream wss;

  001c1	6a 01		 push	 1
  001c3	6a 03		 push	 3
  001c5	8d 8d ec fd ff
	ff		 lea	 ecx, DWORD PTR _wss$[ebp]
  001cb	e8 00 00 00 00	 call	 ??0?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@H@Z ; std::basic_stringstream<char,std::char_traits<char>,std::allocator<char> >::basic_stringstream<char,std::char_traits<char>,std::allocator<char> >
  001d0	c6 45 fc 08	 mov	 BYTE PTR __$EHRec$[ebp+8], 8

; 41   : 	wss << fs.rdbuf();

  001d4	8d 8d a4 fe ff
	ff		 lea	 ecx, DWORD PTR _fs$[ebp]
  001da	e8 00 00 00 00	 call	 ?rdbuf@?$basic_fstream@DU?$char_traits@D@std@@@std@@QBEPAV?$basic_filebuf@DU?$char_traits@D@std@@@2@XZ ; std::basic_fstream<char,std::char_traits<char> >::rdbuf
  001df	50		 push	 eax
  001e0	8d 8d fc fd ff
	ff		 lea	 ecx, DWORD PTR _wss$[ebp+16]
  001e6	e8 00 00 00 00	 call	 ??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QAEAAV01@PAV?$basic_streambuf@DU?$char_traits@D@std@@@1@@Z ; std::basic_ostream<char,std::char_traits<char> >::operator<<

; 42   : 	sjs = wss.str();

  001eb	8d 8d b8 fd ff
	ff		 lea	 ecx, DWORD PTR $T3[ebp]
  001f1	51		 push	 ecx
  001f2	8d 8d ec fd ff
	ff		 lea	 ecx, DWORD PTR _wss$[ebp]
  001f8	e8 00 00 00 00	 call	 ?str@?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QBE?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@XZ ; std::basic_stringstream<char,std::char_traits<char>,std::allocator<char> >::str
  001fd	89 85 b0 fd ff
	ff		 mov	 DWORD PTR tv154[ebp], eax
  00203	8b 95 b0 fd ff
	ff		 mov	 edx, DWORD PTR tv154[ebp]
  00209	89 95 ac fd ff
	ff		 mov	 DWORD PTR tv203[ebp], edx
  0020f	c6 45 fc 09	 mov	 BYTE PTR __$EHRec$[ebp+8], 9
  00213	8b 85 ac fd ff
	ff		 mov	 eax, DWORD PTR tv203[ebp]
  00219	50		 push	 eax
  0021a	8d 4d d8	 lea	 ecx, DWORD PTR _sjs$[ebp]
  0021d	e8 00 00 00 00	 call	 ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEAAV01@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator=
  00222	c6 45 fc 08	 mov	 BYTE PTR __$EHRec$[ebp+8], 8
  00226	8d 8d b8 fd ff
	ff		 lea	 ecx, DWORD PTR $T3[ebp]
  0022c	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >

; 43   : 	return sjs;

  00231	8d 4d d8	 lea	 ecx, DWORD PTR _sjs$[ebp]
  00234	51		 push	 ecx
  00235	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00238	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@$$QAV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0023d	8b 95 b4 fd ff
	ff		 mov	 edx, DWORD PTR $T2[ebp]
  00243	83 ca 01	 or	 edx, 1
  00246	89 95 b4 fd ff
	ff		 mov	 DWORD PTR $T2[ebp], edx
  0024c	c6 45 fc 07	 mov	 BYTE PTR __$EHRec$[ebp+8], 7
  00250	8d 8d ec fd ff
	ff		 lea	 ecx, DWORD PTR _wss$[ebp]
  00256	e8 00 00 00 00	 call	 ??_D?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXXZ
  0025b	c6 45 fc 06	 mov	 BYTE PTR __$EHRec$[ebp+8], 6
  0025f	8d 8d a4 fe ff
	ff		 lea	 ecx, DWORD PTR _fs$[ebp]
  00265	e8 00 00 00 00	 call	 ??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ
  0026a	c6 45 fc 05	 mov	 BYTE PTR __$EHRec$[ebp+8], 5
  0026e	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR _vArgs$[ebp]
  00274	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
  00279	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2
  0027d	8d 4d 80	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  00280	e8 00 00 00 00	 call	 ??1CCmdLine@@QAE@XZ
  00285	c6 45 fc 01	 mov	 BYTE PTR __$EHRec$[ebp+8], 1
  00289	8d 4d b8	 lea	 ecx, DWORD PTR _sfile$[ebp]
  0028c	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00291	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  00295	8d 4d d8	 lea	 ecx, DWORD PTR _sjs$[ebp]
  00298	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  0029d	8b 45 08	 mov	 eax, DWORD PTR ___$ReturnUdt$[ebp]
$LN1@GetScript:

; 44   : }

  002a0	52		 push	 edx
  002a1	8b cd		 mov	 ecx, ebp
  002a3	50		 push	 eax
  002a4	8d 15 00 00 00
	00		 lea	 edx, DWORD PTR $LN23@GetScript
  002aa	e8 00 00 00 00	 call	 @_RTC_CheckStackVars@8
  002af	58		 pop	 eax
  002b0	5a		 pop	 edx
  002b1	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  002b4	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  002bb	5f		 pop	 edi
  002bc	5e		 pop	 esi
  002bd	81 c4 54 02 00
	00		 add	 esp, 596		; 00000254H
  002c3	3b ec		 cmp	 ebp, esp
  002c5	e8 00 00 00 00	 call	 __RTC_CheckEsp
  002ca	8b e5		 mov	 esp, ebp
  002cc	5d		 pop	 ebp
  002cd	c3		 ret	 0
  002ce	66 90		 npad	 2
$LN23@GetScript:
  002d0	06 00 00 00	 DD	 6
  002d4	00 00 00 00	 DD	 $LN22@GetScript
$LN22@GetScript:
  002d8	d8 ff ff ff	 DD	 -40			; ffffffd8H
  002dc	18 00 00 00	 DD	 24			; 00000018H
  002e0	00 00 00 00	 DD	 $LN15@GetScript
  002e4	b8 ff ff ff	 DD	 -72			; ffffffb8H
  002e8	18 00 00 00	 DD	 24			; 00000018H
  002ec	00 00 00 00	 DD	 $LN16@GetScript
  002f0	80 ff ff ff	 DD	 -128			; ffffff80H
  002f4	30 00 00 00	 DD	 48			; 00000030H
  002f8	00 00 00 00	 DD	 $LN17@GetScript
  002fc	6c ff ff ff	 DD	 -148			; ffffff6cH
  00300	0c 00 00 00	 DD	 12			; 0000000cH
  00304	00 00 00 00	 DD	 $LN18@GetScript
  00308	a4 fe ff ff	 DD	 -348			; fffffea4H
  0030c	c0 00 00 00	 DD	 192			; 000000c0H
  00310	00 00 00 00	 DD	 $LN19@GetScript
  00314	ec fd ff ff	 DD	 -532			; fffffdecH
  00318	b0 00 00 00	 DD	 176			; 000000b0H
  0031c	00 00 00 00	 DD	 $LN20@GetScript
$LN20@GetScript:
  00320	77		 DB	 119			; 00000077H
  00321	73		 DB	 115			; 00000073H
  00322	73		 DB	 115			; 00000073H
  00323	00		 DB	 0
$LN19@GetScript:
  00324	66		 DB	 102			; 00000066H
  00325	73		 DB	 115			; 00000073H
  00326	00		 DB	 0
$LN18@GetScript:
  00327	76		 DB	 118			; 00000076H
  00328	41		 DB	 65			; 00000041H
  00329	72		 DB	 114			; 00000072H
  0032a	67		 DB	 103			; 00000067H
  0032b	73		 DB	 115			; 00000073H
  0032c	00		 DB	 0
$LN17@GetScript:
  0032d	6f		 DB	 111			; 0000006fH
  0032e	63		 DB	 99			; 00000063H
  0032f	6d		 DB	 109			; 0000006dH
  00330	64		 DB	 100			; 00000064H
  00331	6c		 DB	 108			; 0000006cH
  00332	69		 DB	 105			; 00000069H
  00333	6e		 DB	 110			; 0000006eH
  00334	65		 DB	 101			; 00000065H
  00335	00		 DB	 0
$LN16@GetScript:
  00336	73		 DB	 115			; 00000073H
  00337	66		 DB	 102			; 00000066H
  00338	69		 DB	 105			; 00000069H
  00339	6c		 DB	 108			; 0000006cH
  0033a	65		 DB	 101			; 00000065H
  0033b	00		 DB	 0
$LN15@GetScript:
  0033c	73		 DB	 115			; 00000073H
  0033d	6a		 DB	 106			; 0000006aH
  0033e	73		 DB	 115			; 00000073H
  0033f	00		 DB	 0
_TEXT	ENDS
text$x	SEGMENT
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$0:
  00000	8d 4d d8	 lea	 ecx, DWORD PTR _sjs$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$1:
  00008	8d 4d b8	 lea	 ecx, DWORD PTR _sfile$[ebp]
  0000b	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$2:
  00010	8d 8d d0 fd ff
	ff		 lea	 ecx, DWORD PTR $T4[ebp]
  00016	e9 00 00 00 00	 jmp	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QAE@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$3:
  0001b	8d 4d 80	 lea	 ecx, DWORD PTR _ocmdline$[ebp]
  0001e	e9 00 00 00 00	 jmp	 ??1CCmdLine@@QAE@XZ
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$4:
  00023	8d 8d 6c ff ff
	ff		 lea	 ecx, DWORD PTR _vArgs$[ebp]
  00029	e9 00 00 00 00	 jmp	 ??1?$vector@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@V?$allocator@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@2@@std@@QAE@XZ ; std::vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >::~vector<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >,std::allocator<std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > > >
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$5:
  0002e	8b 85 b4 fd ff
	ff		 mov	 eax, DWORD PTR $T2[ebp]
  00034	83 e0 01	 and	 eax, 1
  00037	0f 84 0f 00 00
	00		 je	 $LN11@GetScript
  0003d	83 a5 b4 fd ff
	ff fe		 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00044	8b 4d 08	 mov	 ecx, DWORD PTR ___$ReturnUdt$[ebp]
  00047	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
$LN11@GetScript:
  0004c	c3		 ret	 0
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$6:
  0004d	8d 8d a4 fe ff
	ff		 lea	 ecx, DWORD PTR _fs$[ebp]
  00053	e9 00 00 00 00	 jmp	 ??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$7:
  00058	8d 8d ec fd ff
	ff		 lea	 ecx, DWORD PTR _wss$[ebp]
  0005e	e9 00 00 00 00	 jmp	 ??_D?$basic_stringstream@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAEXXZ
__unwindfunclet$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ$8:
  00063	8d 8d b8 fd ff
	ff		 lea	 ecx, DWORD PTR $T3[ebp]
  00069	e9 00 00 00 00	 jmp	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QAE@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
__ehhandler$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:
  0006e	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
  00073	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
?GetScript@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ENDP ; GetScript
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ PROC ; std::basic_fstream<char,std::char_traits<char> >::`vbase destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	83 c1 78	 add	 ecx, 120		; 00000078H
  00014	e8 00 00 00 00	 call	 ??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_fstream<char,std::char_traits<char> >::~basic_fstream<char,std::char_traits<char> >
  00019	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  0001c	83 c1 78	 add	 ecx, 120		; 00000078H
  0001f	e8 00 00 00 00	 call	 ??1?$basic_ios@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_ios<char,std::char_traits<char> >::~basic_ios<char,std::char_traits<char> >
  00024	83 c4 04	 add	 esp, 4
  00027	3b ec		 cmp	 ebp, esp
  00029	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0002e	8b e5		 mov	 esp, ebp
  00030	5d		 pop	 ebp
  00031	c3		 ret	 0
??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ ENDP ; std::basic_fstream<char,std::char_traits<char> >::`vbase destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\fstream
;	COMDAT ?rdbuf@?$basic_fstream@DU?$char_traits@D@std@@@std@@QBEPAV?$basic_filebuf@DU?$char_traits@D@std@@@2@XZ
_TEXT	SEGMENT
_this$ = -4						; size = 4
?rdbuf@?$basic_fstream@DU?$char_traits@D@std@@@std@@QBEPAV?$basic_filebuf@DU?$char_traits@D@std@@@2@XZ PROC ; std::basic_fstream<char,std::char_traits<char> >::rdbuf, COMDAT
; _this$ = ecx

; 1492 : 		{	// return pointer to file buffer

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx

; 1493 : 		return ((_Myfb *)&_Filebuffer);

  0000e	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00011	83 c0 18	 add	 eax, 24			; 00000018H

; 1494 : 		}

  00014	8b e5		 mov	 esp, ebp
  00016	5d		 pop	 ebp
  00017	c3		 ret	 0
?rdbuf@?$basic_fstream@DU?$char_traits@D@std@@@std@@QBEPAV?$basic_filebuf@DU?$char_traits@D@std@@@2@XZ ENDP ; std::basic_fstream<char,std::char_traits<char> >::rdbuf
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\fstream
;	COMDAT ??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ PROC ; std::basic_fstream<char,std::char_traits<char> >::~basic_fstream<char,std::char_traits<char> >, COMDAT
; _this$ = ecx

; 1488 : 		{	// destroy the object

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  00023	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00026	8b 48 88	 mov	 ecx, DWORD PTR [eax-120]
  00029	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0002c	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0002f	c7 44 10 88 00
	00 00 00	 mov	 DWORD PTR [eax+edx-120], OFFSET ??_7?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@
  00037	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003a	8b 51 88	 mov	 edx, DWORD PTR [ecx-120]
  0003d	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  00040	83 e8 78	 sub	 eax, 120		; 00000078H
  00043	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00046	8b 51 88	 mov	 edx, DWORD PTR [ecx-120]
  00049	8b 4a 04	 mov	 ecx, DWORD PTR [edx+4]
  0004c	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0004f	89 44 0a 84	 mov	 DWORD PTR [edx+ecx-124], eax
  00053	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 1489 : 		}

  0005a	c6 45 fc 00	 mov	 BYTE PTR __$EHRec$[ebp+8], 0
  0005e	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00061	83 e9 60	 sub	 ecx, 96			; 00000060H
  00064	e8 00 00 00 00	 call	 ??1?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_filebuf<char,std::char_traits<char> >::~basic_filebuf<char,std::char_traits<char> >
  00069	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  00070	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00073	83 e9 58	 sub	 ecx, 88			; 00000058H
  00076	e8 00 00 00 00	 call	 ??1?$basic_iostream@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_iostream<char,std::char_traits<char> >::~basic_iostream<char,std::char_traits<char> >
  0007b	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  0007e	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00085	83 c4 10	 add	 esp, 16			; 00000010H
  00088	3b ec		 cmp	 ebp, esp
  0008a	e8 00 00 00 00	 call	 __RTC_CheckEsp
  0008f	8b e5		 mov	 esp, ebp
  00091	5d		 pop	 ebp
  00092	c3		 ret	 0
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	83 e9 58	 sub	 ecx, 88			; 00000058H
  00006	e9 00 00 00 00	 jmp	 ??1?$basic_iostream@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_iostream<char,std::char_traits<char> >::~basic_iostream<char,std::char_traits<char> >
__unwindfunclet$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ$1:
  0000b	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0000e	83 e9 60	 sub	 ecx, 96			; 00000060H
  00011	e9 00 00 00 00	 jmp	 ??1?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_filebuf<char,std::char_traits<char> >::~basic_filebuf<char,std::char_traits<char> >
__ehhandler$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ:
  00016	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ
  0001b	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??1?$basic_fstream@DU?$char_traits@D@std@@@std@@UAE@XZ ENDP ; std::basic_fstream<char,std::char_traits<char> >::~basic_fstream<char,std::char_traits<char> >
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\fstream
;	COMDAT ??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z
_TEXT	SEGMENT
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
__Str$ = 8						; size = 4
__Mode$ = 12						; size = 4
__Prot$ = 16						; size = 4
_$initVBases$ = 20					; size = 4
??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z PROC ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >, COMDAT
; _this$ = ecx

; 1352 : 		{	// construct with wide-named file -- EXTENSION

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	51		 push	 ecx
  00019	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00020	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx

; 1351 : 		: basic_fstream(_Str.c_str(), _Mode, _Prot)

  00023	8b 45 14	 mov	 eax, DWORD PTR _$initVBases$[ebp]
  00026	50		 push	 eax
  00027	8b 4d 10	 mov	 ecx, DWORD PTR __Prot$[ebp]
  0002a	51		 push	 ecx
  0002b	8b 55 0c	 mov	 edx, DWORD PTR __Mode$[ebp]
  0002e	52		 push	 edx
  0002f	8b 4d 08	 mov	 ecx, DWORD PTR __Str$[ebp]
  00032	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QBEPB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  00037	50		 push	 eax
  00038	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0003b	e8 00 00 00 00	 call	 ??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >
  00040	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0

; 1352 : 		{	// construct with wide-named file -- EXTENSION

  00047	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0004a	8b 08		 mov	 ecx, DWORD PTR [eax]
  0004c	8b 51 04	 mov	 edx, DWORD PTR [ecx+4]
  0004f	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  00052	c7 04 10 00 00
	00 00		 mov	 DWORD PTR [eax+edx], OFFSET ??_7?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@
  00059	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0005c	8b 11		 mov	 edx, DWORD PTR [ecx]
  0005e	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  00061	83 e8 78	 sub	 eax, 120		; 00000078H
  00064	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00067	8b 11		 mov	 edx, DWORD PTR [ecx]
  00069	8b 4a 04	 mov	 ecx, DWORD PTR [edx+4]
  0006c	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0006f	89 44 0a fc	 mov	 DWORD PTR [edx+ecx-4], eax

; 1353 : 		}

  00073	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  0007a	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0007d	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  00080	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  00087	83 c4 10	 add	 esp, 16			; 00000010H
  0008a	3b ec		 cmp	 ebp, esp
  0008c	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00091	8b e5		 mov	 esp, ebp
  00093	5d		 pop	 ebp
  00094	c2 10 00	 ret	 16			; 00000010H
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z$0:
  00000	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00003	e9 00 00 00 00	 jmp	 ??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ
__ehhandler$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z:
  00008	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z
  0000d	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@ABV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@HH@Z ENDP ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >
; Function compile flags: /Odtp /RTCsu
;	COMDAT ??_G?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z
_TEXT	SEGMENT
_this$ = -4						; size = 4
___flags$ = 8						; size = 4
??_G?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z PROC ; std::basic_fstream<char,std::char_traits<char> >::`scalar deleting destructor', COMDAT
; _this$ = ecx
  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	51		 push	 ecx
  00004	c7 45 fc cc cc
	cc cc		 mov	 DWORD PTR [ebp-4], -858993460 ; ccccccccH
  0000b	89 4d fc	 mov	 DWORD PTR _this$[ebp], ecx
  0000e	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00011	83 e9 78	 sub	 ecx, 120		; 00000078H
  00014	e8 00 00 00 00	 call	 ??_D?$basic_fstream@DU?$char_traits@D@std@@@std@@QAEXXZ
  00019	8b 45 08	 mov	 eax, DWORD PTR ___flags$[ebp]
  0001c	83 e0 01	 and	 eax, 1
  0001f	74 14		 je	 SHORT $LN2@scalar
  00021	68 c0 00 00 00	 push	 192			; 000000c0H
  00026	8b 4d fc	 mov	 ecx, DWORD PTR _this$[ebp]
  00029	83 e9 78	 sub	 ecx, 120		; 00000078H
  0002c	51		 push	 ecx
  0002d	e8 00 00 00 00	 call	 ??3@YAXPAXI@Z		; operator delete
  00032	83 c4 08	 add	 esp, 8
$LN2@scalar:
  00035	8b 45 fc	 mov	 eax, DWORD PTR _this$[ebp]
  00038	83 e8 78	 sub	 eax, 120		; 00000078H
  0003b	83 c4 04	 add	 esp, 4
  0003e	3b ec		 cmp	 ebp, esp
  00040	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00045	8b e5		 mov	 esp, ebp
  00047	5d		 pop	 ebp
  00048	c2 04 00	 ret	 4
??_G?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z ENDP ; std::basic_fstream<char,std::char_traits<char> >::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp /RTCsu
; File c:\program files (x86)\microsoft visual studio\2017\enterprise\vc\tools\msvc\14.16.27023\include\fstream
;	COMDAT ??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z
_TEXT	SEGMENT
$T2 = -20						; size = 4
_this$ = -16						; size = 4
__$EHRec$ = -12						; size = 12
__Filename$ = 8						; size = 4
__Mode$ = 12						; size = 4
__Prot$ = 16						; size = 4
_$initVBases$ = 20					; size = 4
??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z PROC ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >, COMDAT
; _this$ = ecx

; 1343 : 		{	// construct with wide-named file (in standard as const std::filesystem::path::value_type *)

  00000	55		 push	 ebp
  00001	8b ec		 mov	 ebp, esp
  00003	6a ff		 push	 -1
  00005	68 00 00 00 00	 push	 __ehhandler$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z
  0000a	64 a1 00 00 00
	00		 mov	 eax, DWORD PTR fs:0
  00010	50		 push	 eax
  00011	64 89 25 00 00
	00 00		 mov	 DWORD PTR fs:0, esp
  00018	83 ec 08	 sub	 esp, 8
  0001b	c7 45 ec cc cc
	cc cc		 mov	 DWORD PTR [ebp-20], -858993460 ; ccccccccH
  00022	c7 45 f0 cc cc
	cc cc		 mov	 DWORD PTR [ebp-16], -858993460 ; ccccccccH
  00029	89 4d f0	 mov	 DWORD PTR _this$[ebp], ecx
  0002c	c7 45 ec 00 00
	00 00		 mov	 DWORD PTR $T2[ebp], 0
  00033	83 7d 14 00	 cmp	 DWORD PTR _$initVBases$[ebp], 0
  00037	74 2e		 je	 SHORT $LN2@basic_fstr
  00039	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0003c	c7 00 00 00 00
	00		 mov	 DWORD PTR [eax], OFFSET ??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_istream@DU?$char_traits@D@std@@@1@@
  00042	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00045	c7 41 10 00 00
	00 00		 mov	 DWORD PTR [ecx+16], OFFSET ??_8?$basic_fstream@DU?$char_traits@D@std@@@std@@7B?$basic_ostream@DU?$char_traits@D@std@@@1@@
  0004c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0004f	83 c1 78	 add	 ecx, 120		; 00000078H
  00052	e8 00 00 00 00	 call	 ??0?$basic_ios@DU?$char_traits@D@std@@@std@@IAE@XZ ; std::basic_ios<char,std::char_traits<char> >::basic_ios<char,std::char_traits<char> >
  00057	c7 45 fc 00 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 0
  0005e	8b 55 ec	 mov	 edx, DWORD PTR $T2[ebp]
  00061	83 ca 01	 or	 edx, 1
  00064	89 55 ec	 mov	 DWORD PTR $T2[ebp], edx
$LN2@basic_fstr:

; 1342 : 		: _Mybase(&_Filebuffer)

  00067	6a 00		 push	 0
  00069	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  0006c	83 c0 18	 add	 eax, 24			; 00000018H
  0006f	50		 push	 eax
  00070	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00073	e8 00 00 00 00	 call	 ??0?$basic_iostream@DU?$char_traits@D@std@@@std@@QAE@PAV?$basic_streambuf@DU?$char_traits@D@std@@@1@@Z ; std::basic_iostream<char,std::char_traits<char> >::basic_iostream<char,std::char_traits<char> >
  00078	c7 45 fc 01 00
	00 00		 mov	 DWORD PTR __$EHRec$[ebp+8], 1

; 1343 : 		{	// construct with wide-named file (in standard as const std::filesystem::path::value_type *)

  0007f	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00082	8b 11		 mov	 edx, DWORD PTR [ecx]
  00084	8b 42 04	 mov	 eax, DWORD PTR [edx+4]
  00087	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0008a	c7 04 01 00 00
	00 00		 mov	 DWORD PTR [ecx+eax], OFFSET ??_7?$basic_fstream@DU?$char_traits@D@std@@@std@@6B@
  00091	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  00094	8b 02		 mov	 eax, DWORD PTR [edx]
  00096	8b 48 04	 mov	 ecx, DWORD PTR [eax+4]
  00099	83 e9 78	 sub	 ecx, 120		; 00000078H
  0009c	8b 55 f0	 mov	 edx, DWORD PTR _this$[ebp]
  0009f	8b 02		 mov	 eax, DWORD PTR [edx]
  000a1	8b 50 04	 mov	 edx, DWORD PTR [eax+4]
  000a4	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  000a7	89 4c 10 fc	 mov	 DWORD PTR [eax+edx-4], ecx
  000ab	6a 00		 push	 0
  000ad	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000b0	83 c1 18	 add	 ecx, 24			; 00000018H
  000b3	e8 00 00 00 00	 call	 ??0?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAE@PAU_iobuf@@@Z ; std::basic_filebuf<char,std::char_traits<char> >::basic_filebuf<char,std::char_traits<char> >
  000b8	c6 45 fc 02	 mov	 BYTE PTR __$EHRec$[ebp+8], 2

; 1344 : 		if (_Filebuffer.open(_Filename, _Mode, _Prot) == nullptr)

  000bc	8b 4d 10	 mov	 ecx, DWORD PTR __Prot$[ebp]
  000bf	51		 push	 ecx
  000c0	8b 55 0c	 mov	 edx, DWORD PTR __Mode$[ebp]
  000c3	52		 push	 edx
  000c4	8b 45 08	 mov	 eax, DWORD PTR __Filename$[ebp]
  000c7	50		 push	 eax
  000c8	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000cb	83 c1 18	 add	 ecx, 24			; 00000018H
  000ce	e8 00 00 00 00	 call	 ?open@?$basic_filebuf@DU?$char_traits@D@std@@@std@@QAEPAV12@PB_WHH@Z ; std::basic_filebuf<char,std::char_traits<char> >::open
  000d3	85 c0		 test	 eax, eax
  000d5	75 14		 jne	 SHORT $LN1@basic_fstr

; 1345 : 			_Myios::setstate(ios_base::failbit);

  000d7	6a 00		 push	 0
  000d9	6a 02		 push	 2
  000db	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000de	8b 11		 mov	 edx, DWORD PTR [ecx]
  000e0	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  000e3	03 4a 04	 add	 ecx, DWORD PTR [edx+4]
  000e6	e8 00 00 00 00	 call	 ?setstate@?$basic_ios@DU?$char_traits@D@std@@@std@@QAEXH_N@Z ; std::basic_ios<char,std::char_traits<char> >::setstate
$LN1@basic_fstr:

; 1346 : 		}

  000eb	c7 45 fc ff ff
	ff ff		 mov	 DWORD PTR __$EHRec$[ebp+8], -1
  000f2	8b 45 f0	 mov	 eax, DWORD PTR _this$[ebp]
  000f5	8b 4d f4	 mov	 ecx, DWORD PTR __$EHRec$[ebp]
  000f8	64 89 0d 00 00
	00 00		 mov	 DWORD PTR fs:0, ecx
  000ff	83 c4 14	 add	 esp, 20			; 00000014H
  00102	3b ec		 cmp	 ebp, esp
  00104	e8 00 00 00 00	 call	 __RTC_CheckEsp
  00109	8b e5		 mov	 esp, ebp
  0010b	5d		 pop	 ebp
  0010c	c2 10 00	 ret	 16			; 00000010H
_TEXT	ENDS
;	COMDAT text$x
text$x	SEGMENT
__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$0:
  00000	8b 45 ec	 mov	 eax, DWORD PTR $T2[ebp]
  00003	83 e0 01	 and	 eax, 1
  00006	0f 84 0f 00 00
	00		 je	 $LN6@basic_fstr
  0000c	83 65 ec fe	 and	 DWORD PTR $T2[ebp], -2	; fffffffeH
  00010	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  00013	83 c1 78	 add	 ecx, 120		; 00000078H
  00016	e9 00 00 00 00	 jmp	 ??1?$basic_ios@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_ios<char,std::char_traits<char> >::~basic_ios<char,std::char_traits<char> >
$LN6@basic_fstr:
  0001b	c3		 ret	 0
__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$1:
  0001c	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0001f	83 c1 20	 add	 ecx, 32			; 00000020H
  00022	e9 00 00 00 00	 jmp	 ??1?$basic_iostream@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_iostream<char,std::char_traits<char> >::~basic_iostream<char,std::char_traits<char> >
__unwindfunclet$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z$2:
  00027	8b 4d f0	 mov	 ecx, DWORD PTR _this$[ebp]
  0002a	83 c1 18	 add	 ecx, 24			; 00000018H
  0002d	e9 00 00 00 00	 jmp	 ??1?$basic_filebuf@DU?$char_traits@D@std@@@std@@UAE@XZ ; std::basic_filebuf<char,std::char_traits<char> >::~basic_filebuf<char,std::char_traits<char> >
__ehhandler$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z:
  00032	b8 00 00 00 00	 mov	 eax, OFFSET __ehfuncinfo$??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z
  00037	e9 00 00 00 00	 jmp	 ___CxxFrameHandler3
text$x	ENDS
??0?$basic_fstream@DU?$char_traits@D@std@@@std@@QAE@PB_WHH@Z ENDP ; std::basic_fstream<char,std::char_traits<char> >::basic_fstream<char,std::char_traits<char> >
; Function compile flags: /Odsp /RTCsu
;	COMDAT ??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@$4PPPPPPPM@A@AEPAXI@Z
_TEXT	SEGMENT
??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@$4PPPPPPPM@A@AEPAXI@Z PROC ; std::basic_fstream<char,std::char_traits<char> >::`vector deleting destructor', COMDAT
  00000	2b 49 fc	 sub	 ecx, DWORD PTR [ecx-4]
  00003	e9 00 00 00 00	 jmp	 ??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@UAEPAXI@Z
??_E?$basic_fstream@DU?$char_traits@D@std@@@std@@$4PPPPPPPM@A@AEPAXI@Z ENDP ; std::basic_fstream<char,std::char_traits<char> >::`vector deleting destructor'
_TEXT	ENDS
END
