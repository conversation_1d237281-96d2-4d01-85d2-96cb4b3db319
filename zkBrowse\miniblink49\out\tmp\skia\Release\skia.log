﻿E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\skia\skia.vcxproj(82,5): warning MSB4011: 无法再次导入“C:\Users\<USER>\AppData\Local\Microsoft\MSBuild\v4.0\Microsoft.Cpp.Win32.user.props”。可能已在“E:\ParaPlay_svn\NewWebbrowser\pc_chrome_src\zkBrowse\miniblink49\build\skia\skia.vcxproj (76,5)”处导入过它。这很可能是生成创作错误。将忽略此后续导入。
C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
  SkAAClip.cpp
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
  SkAdvancedTypefaceMetrics.cpp
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
  SkAlphaRuns.cpp
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
cl : 命令行 warning D9025: 正在重写“/sdl”(用“/GS-”)
  SkAnnotation.cpp
  SkBBHFactory.cpp
  SkBigPicture.cpp
  SkBitmap.cpp
  SkBitmapCache.cpp
  SkBitmapController.cpp
  SkBitmapDevice.cpp
  SkBitmapFilter.cpp
  SkBitmapHeap.cpp
  SkBitmapProcShader.cpp
  SkBitmapProcState.cpp
  SkBitmapProcState_matrixProcs.cpp
  SkBitmapScaler.cpp
  SkBlitMask_D32.cpp
  SkBlitRow_D16.cpp
  SkBlitRow_D32.cpp
  SkBlitter.cpp
  SkBlitter_A8.cpp
  SkBlitter_ARGB32.cpp
  SkBlitter_RGB16.cpp
  SkBlitter_Sprite.cpp
  SkBuffer.cpp
  SkCachedData.cpp
  SkCanvas.cpp
  SkChunkAlloc.cpp
  SkClipStack.cpp
  SkColor.cpp
  SkColorFilter.cpp
  SkColorTable.cpp
  SkComposeShader.cpp
  SkConfig8888.cpp
  SkConvolver.cpp
  SkCubicClipper.cpp
  SkData.cpp
  SkDataTable.cpp
  SkDebug.cpp
  SkDeque.cpp
  SkDevice.cpp
  SkDeviceLooper.cpp
  SkDeviceProfile.cpp
  SkDistanceFieldGen.cpp
  SkDither.cpp
  SkDraw.cpp
  SkDrawLooper.cpp
  SkDrawable.cpp
  SkEdge.cpp
  SkEdgeBuilder.cpp
  SkEdgeClipper.cpp
  SkError.cpp
  SkFilterProc.cpp
  SkFilterShader.cpp
  SkFlate.cpp
  SkFlattenable.cpp
  SkFlattenableSerialization.cpp
  SkFloatBits.cpp
  SkFont.cpp
  SkFontDescriptor.cpp
  SkFontHost.cpp
  SkFontMgr.cpp
  SkFontStream.cpp
  SkFontStyle.cpp
  SkGeometry.cpp
  SkGlyphCache.cpp
  SkGraphics.cpp
  SkHalf.cpp
  SkImageFilter.cpp
  SkImageGenerator.cpp
  SkImageInfo.cpp
  SkLineClipper.cpp
  SkLocalMatrixShader.cpp
  SkMallocPixelRef.cpp
  SkMask.cpp
  SkMaskCache.cpp
  SkMaskFilter.cpp
  SkMaskGamma.cpp
  SkMath.cpp
  SkMatrix.cpp
  SkMatrixImageFilter.cpp
  SkMetaData.cpp
  SkMiniRecorder.cpp
  SkMipMap.cpp
  SkMultiPictureDraw.cpp
  SkNinePatchIter.cpp
  SkPackBits.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skimagefilter.cpp(343): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skmatrix.cpp(1720): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skmatrix.cpp(1720): warning C4244: “初始化”: 从“int”转换到“const float”，可能丢失数据
  SkPaint.cpp
  SkPaintPriv.cpp
  SkPath.cpp
  SkPathEffect.cpp
  SkPathMeasure.cpp
  SkPathRef.cpp
  SkPicture.cpp
  SkPictureContentInfo.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skmallocpixelref.cpp(72): warning C4244: “=”: 从“int64_t”转换到“int32_t”，可能丢失数据
  SkPictureData.cpp
  SkPictureFlat.cpp
  SkPicturePlayback.cpp
  SkPictureRecord.cpp
  SkPictureRecorder.cpp
  SkPictureShader.cpp
  SkPixelRef.cpp
  SkPixmap.cpp
  SkPoint.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpath.cpp(2523): warning C4244: “=”: 从“int”转换到“SkScalar”，可能丢失数据
  SkPtrRecorder.cpp
  SkQuadClipper.cpp
  SkRRect.cpp
  SkRTree.cpp
  SkRWBuffer.cpp
  SkRasterClip.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpicture.cpp(138): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpicture.cpp(137): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpicture.cpp(163): warning C4244: “参数”: 从“SkScalar”转换到“T”，可能丢失数据
          with
          [
              T=int32_t
          ]
  SkRasterizer.cpp
  SkReadBuffer.cpp
  SkRecord.cpp
  SkRecordDraw.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrrect.cpp(194): warning C4244: “*=”: 从“double”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrrect.cpp(195): warning C4244: “*=”: 从“double”转换到“SkScalar”，可能丢失数据
  SkRecordOpts.cpp
  SkRecorder.cpp
  SkRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpictureshader.cpp(206): warning C4244: “参数”: 从“T”转换到“int32_t”，可能丢失数据
          with
          [
              T=SkScalar
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skpictureshader.cpp(206): warning C4244: “初始化”: 从“int32_t”转换到“SkScalar”，可能丢失数据
  SkRefDict.cpp
  SkRegion.cpp
  SkRegion_path.cpp
  SkResourceCache.cpp
  SkScalar.cpp
  SkScalerContext.cpp
  SkScan.cpp
  SkScan_AntiPath.cpp
  SkScan_Antihair.cpp
  SkScan_Hairline.cpp
  SkScan_Path.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(395): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(395): warning C4244: “参数”: 从“const int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(412): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skrecorddraw.cpp(436): warning C4244: “参数”: 从“int”转换到“SkScalar”，可能丢失数据
  SkSemaphore.cpp
  SkShader.cpp
  SkSharedMutex.cpp
  SkSpinlock.cpp
  SkSpriteBlitter_ARGB32.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skregionpriv.h(71): warning C4244: “参数”: 从“const int64_t”转换到“size_t”，可能丢失数据 (编译源文件 ..\..\third_party\skia\src\core\SkRegion_path.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skregionpriv.h(71): warning C4244: “参数”: 从“const int64_t”转换到“size_t”，可能丢失数据 (编译源文件 ..\..\third_party\skia\src\core\SkRegion.cpp)
  SkSpriteBlitter_RGB16.cpp
  SkStream.cpp
  SkString.cpp
  SkStringUtils.cpp
  SkStroke.cpp
  SkStrokeRec.cpp
  SkStrokerPriv.cpp
  SkTLS.cpp
  SkTSearch.cpp
  SkTaskGroup.cpp
  SkTextBlob.cpp
  SkTime.cpp
  SkTypeface.cpp
  SkTypefaceCache.cpp
  SkUnPreMultiply.cpp
  SkUtils.cpp
  SkUtilsArm.cpp
  SkValidatingReadBuffer.cpp
  SkVarAlloc.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skstream.cpp(147): warning C4267: “=”: 从“size_t”转换到“uint8_t”，可能丢失数据
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\core\skstream.cpp(150): warning C4267: “初始化”: 从“size_t”转换到“uint16_t”，可能丢失数据
  SkVertState.cpp
  SkWriteBuffer.cpp
  SkWriter32.cpp
  SkXfermode.cpp
  SkXfermodeInterpretation.cpp
  SkYUVPlanesCache.cpp
  SkDocument.cpp
  SkDocument_PDF.cpp
  Sk1DPathEffect.cpp
  Sk2DPathEffect.cpp
  SkAlphaThresholdFilter.cpp
  SkArcToPathEffect.cpp
  SkArithmeticMode.cpp
  SkArithmeticMode_gpu.cpp
  SkBitmapSource.cpp
  SkBlurDrawLooper.cpp
  SkBlurImageFilter.cpp
  SkBlurMask.cpp
  SkBlurMaskFilter.cpp
  SkColorCubeFilter.cpp
  SkColorFilterImageFilter.cpp
  SkColorFilters.cpp
  SkColorMatrix.cpp
  SkColorMatrixFilter.cpp
  SkComposeImageFilter.cpp
  SkCornerPathEffect.cpp
  SkDashPathEffect.cpp
  SkDiscretePathEffect.cpp
  SkDisplacementMapEffect.cpp
  SkDropShadowImageFilter.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\skia\src\effects\skdisplacementmapeffect.cpp : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  SkEmbossMask.cpp
  SkEmbossMaskFilter.cpp
  SkGpuBlurUtils.cpp
  SkLayerDrawLooper.cpp
  SkLayerRasterizer.cpp
  SkLerpXfermode.cpp
  SkLightingImageFilter.cpp
  SkLumaColorFilter.cpp
  SkMagnifierImageFilter.cpp
  SkMatrixConvolutionImageFilter.cpp
  SkMergeImageFilter.cpp
  SkMorphologyImageFilter.cpp
  SkOffsetImageFilter.cpp
  SkPaintFlagsDrawFilter.cpp
  SkPerlinNoiseShader.cpp
  SkPictureImageFilter.cpp
  SkPixelXorXfermode.cpp
  SkRectShaderImageFilter.cpp
  SkTableColorFilter.cpp
  SkTableMaskFilter.cpp
  SkTestImageFilters.cpp
  SkTileImageFilter.cpp
  SkXfermodeImageFilter.cpp
  SkClampRange.cpp
  SkGradientBitmapCache.cpp
  SkGradientShader.cpp
  SkLinearGradient.cpp
  SkRadialGradient.cpp
  SkSweepGradient.cpp
  SkTwoPointConicalGradient.cpp
  SkTwoPointConicalGradient_gpu.cpp
  SkFontMgr_indirect.cpp
  SkGScalerContext.cpp
  SkRandomScalerContext.cpp
  SkRemotableFontMgr.cpp
  SkTestScalerContext.cpp
  GrAAConvexPathRenderer.cpp
  GrAAConvexTessellator.cpp
  GrAADistanceFieldPathRenderer.cpp
  GrAAHairLinePathRenderer.cpp
  GrAALinearizingConvexPathRenderer.cpp
  GrAARectRenderer.cpp
  GrAddPathRenderers_default.cpp
  GrAtlas.cpp
  GrAtlasTextContext.cpp
  GrBatch.cpp
  GrBatchAtlas.cpp
  GrBatchFontCache.cpp
  GrBatchTarget.cpp
  GrBatchTest.cpp
  GrBlurUtils.cpp
  GrBufferAllocPool.cpp
  GrCaps.cpp
  GrClip.cpp
  GrClipMaskCache.cpp
  GrClipMaskManager.cpp
  GrCommandBuilder.cpp
  GrContext.cpp
  GrContextFactory.cpp
  GrCoordTransform.cpp
  GrDashLinePathRenderer.cpp
  GrDefaultGeoProcFactory.cpp
  GrDefaultPathRenderer.cpp
  GrDrawContext.cpp
  GrDrawTarget.cpp
  GrFontScaler.cpp
  GrGpu.cpp
  GrGpuFactory.cpp
  GrGpuResource.cpp
  GrGpuResourceRef.cpp
  GrImmediateDrawTarget.cpp
  GrInOrderCommandBuilder.cpp
  GrInOrderDrawBuffer.cpp
  GrInvariantOutput.cpp
  GrLayerCache.cpp
  GrLayerHoister.cpp
  GrMemoryPool.cpp
  GrOvalRenderer.cpp
  GrPaint.cpp
  GrPath.cpp
  GrPathProcessor.cpp
  GrPathRange.cpp
  GrPathRenderer.cpp
  GrPathRendererChain.cpp
  GrPathRendering.cpp
  GrPathUtils.cpp
  GrPipeline.cpp
  GrPipelineBuilder.cpp
  GrPrimitiveProcessor.cpp
  GrProcOptInfo.cpp
  GrProcessor.cpp
  GrProgramElement.cpp
  GrRecordReplaceDraw.cpp
  GrRectBatch.cpp
  GrRectanizer_pow2.cpp
  GrRectanizer_skyline.cpp
  GrReducedClip.cpp
  GrRenderTarget.cpp
  GrReorderCommandBuilder.cpp
  GrResourceCache.cpp
  GrResourceProvider.cpp
  GrSWMaskHelper.cpp
  GrSoftwarePathRenderer.cpp
  GrStencil.cpp
  GrStencilAndCoverPathRenderer.cpp
  GrStencilAndCoverTextContext.cpp
  GrStencilAttachment.cpp
  GrStrokeInfo.cpp
  GrSurface.cpp
  GrTargetCommands.cpp
