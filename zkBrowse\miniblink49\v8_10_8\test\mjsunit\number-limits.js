// Copyright 2008 the V8 project authors. All rights reserved.
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
//       copyright notice, this list of conditions and the following
//       disclaimer in the documentation and/or other materials provided
//       with the distribution.
//     * Neither the name of Google Inc. nor the names of its
//       contributors may be used to endorse or promote products derived
//       from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Ensure that Number.MAX_VALUE and Number.MIN_VALUE are extreme.
function testLimits() {
  var i; var eps;
  for (i = 0, eps = 1; i < 1100; i++, eps /= 2) {
    var mulAboveMax = Number.MAX_VALUE * (1 + eps);
    var addAboveMax = Number.MAX_VALUE + 1/eps;
    var mulBelowMin = Number.MIN_VALUE * (1 - eps);
    var addBelowMin = Number.MIN_VALUE - eps;
    assertTrue(mulAboveMax == Number.MAX_VALUE ||
               mulAboveMax == Infinity, "mul" + i);
    assertTrue(addAboveMax == Number.MAX_VALUE ||
               addAboveMax == Infinity, "add" + i);
    assertTrue(mulBelowMin == Number.MIN_VALUE ||
               mulBelowMin <= 0, "mul2" + i);
    assertTrue(addBelowMin == Number.MIN_VALUE ||
               addBelowMin <= 0, "add2" + i);
  }
}

testLimits();
