﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  DefaultMediaPlayerFactory.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\content\web_impl_win\defaultmediaplayerfactory.cpp(29): error C3668: “content::SimpleWkeMediaPlayer::load”: 包含重写说明符“override”的方法没有重写任何基类方法
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\content\web_impl_win\defaultmediaplayerfactory.cpp(51): error C2555: “content::SimpleWkeMediaPlayer::seekable”: 重写虚函数返回类型有差异，且不是来自“wke::WkeMediaPlayer::seekable”的协变
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\wke\wkemediaplayer.h(226): note: 参见“wke::WkeMediaPlayer::seekable”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\content\web_impl_win\defaultmediaplayerfactory.cpp(126): error C2259: “content::SimpleWkeMediaPlayer”: 不能实例化抽象类
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\content\web_impl_win\defaultmediaplayerfactory.cpp(126): note: 由于下列成员:
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\content\web_impl_win\defaultmediaplayerfactory.cpp(126): note: “void wke::WkeMediaPlayer::load(wke::WkeMediaPlayer::LoadType,const char *,wke::WkeMediaPlayer::CORSMode,bool)”: 是抽象的
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\wke\wkemediaplayer.h(212): note: 参见“wke::WkeMediaPlayer::load”的声明
